#!/bin/bash

# Enhanced Search MCP服务器测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🧪 测试Enhanced Search MCP服务器${NC}"
echo -e "${CYAN}=================================${NC}"

# 测试健康检查
echo -e "${YELLOW}🏥 测试健康检查端点...${NC}"
if curl -s --connect-timeout 5 http://localhost:8881/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 健康检查端点可访问${NC}"
    echo -e "${BLUE}健康检查响应:${NC}"
    curl -s http://localhost:8881/health | python -m json.tool
else
    echo -e "${RED}❌ 健康检查端点不可访问${NC}"
    exit 1
fi

echo ""

# 测试工具列表
echo -e "${YELLOW}🛠️  测试工具列表端点...${NC}"
if curl -s --connect-timeout 5 http://localhost:8881/tools > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 工具列表端点可访问${NC}"
    echo -e "${BLUE}可用工具:${NC}"
    curl -s http://localhost:8881/tools | python -m json.tool
else
    echo -e "${RED}❌ 工具列表端点不可访问${NC}"
fi

echo ""

# 测试SSE端点
echo -e "${YELLOW}📡 测试SSE端点...${NC}"
if curl -s --connect-timeout 5 http://localhost:8881/mcp/sse > /dev/null 2>&1; then
    echo -e "${GREEN}✅ SSE端点可访问${NC}"
else
    echo -e "${RED}❌ SSE端点不可访问${NC}"
fi

echo ""
echo -e "${CYAN}🎉 测试完成！${NC}"
echo -e "${CYAN}============${NC}"
echo -e "${GREEN}📡 SSE端点: http://localhost:8881/mcp/sse${NC}"
echo -e "${GREEN}🏥 健康检查: http://localhost:8881/health${NC}"
echo -e "${GREEN}🛠️  工具列表: http://localhost:8881/tools${NC}"
echo ""
echo -e "${BLUE}📋 Agent-Zero MCP配置:${NC}"
echo -e '   {
     "name": "enhanced-search",
     "type": "sse", 
     "url": "http://localhost:8881/mcp/sse",
     "description": "增强搜索引擎MCP服务器"
   }'
