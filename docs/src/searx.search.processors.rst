.. _searx.search.processors:

=================
Search processors
=================

.. contents::
   :depth: 2
   :local:
   :backlinks: entry


Abstract processor class
========================

.. automodule:: searx.search.processors.abstract
  :members:

Offline processor
=================

.. automodule:: searx.search.processors.offline
  :members:

Online processor
================

.. automodule:: searx.search.processors.online
  :members:

Online currency processor
=========================

.. automodule:: searx.search.processors.online_currency
  :members:

Online dictionary processor
===========================

.. automodule:: searx.search.processors.online_dictionary
  :members:

Online URL search processor
===========================

.. automodule:: searx.search.processors.online_url_search
  :members:
