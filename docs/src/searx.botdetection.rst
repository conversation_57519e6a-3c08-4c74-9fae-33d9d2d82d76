.. _botdetection:

=============
Bot Detection
=============

.. contents::
   :depth: 2
   :local:
   :backlinks: entry

.. automodule:: searx.botdetection
  :members:

.. _botdetection ip_lists:

IP lists
========

.. automodule:: searx.botdetection.ip_lists
  :members:


.. _botdetection rate limit:

Rate limit
==========

.. automodule:: searx.botdetection.ip_limit
  :members:

.. automodule:: searx.botdetection.link_token
  :members:


.. _botdetection probe headers:

Probe HTTP headers
==================

.. automodule:: searx.botdetection.http_accept
  :members:

.. automodule:: searx.botdetection.http_accept_encoding
  :members:

.. automodule:: searx.botdetection.http_accept_language
  :members:

.. automodule:: searx.botdetection.http_connection
  :members:

.. automodule:: searx.botdetection.http_user_agent
  :members:

.. automodule:: searx.botdetection.http_sec_fetch
  :members:

.. _botdetection config:

Config
======

.. automodule:: searx.botdetection.config
  :members:
