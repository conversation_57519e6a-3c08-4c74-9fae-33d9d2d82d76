.. _favicons source:

=================
Favicons (source)
=================

.. contents::
   :depth: 2
   :local:
   :backlinks: entry

.. automodule:: searx.favicons
   :members:

.. _favicons.config:

Favicons Config
===============

.. automodule:: searx.favicons.config
   :members:

.. _favicons.proxy:

Favicons Proxy
==============

.. automodule:: searx.favicons.proxy
   :members:

.. _favicons.resolver:

Favicons Resolver
=================

.. automodule:: searx.favicons.resolvers
   :members:

.. _favicons.cache:

Favicons Cache
==============

.. automodule:: searx.favicons.cache
   :members:



