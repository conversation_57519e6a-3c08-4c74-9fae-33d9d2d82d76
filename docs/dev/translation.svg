<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="999px" preserveAspectRatio="none" style="width:1018px;height:999px;background:#FFFFFF;" version="1.1" viewBox="0 0 1018 999" width="1018px" zoomAndPan="magnify"><defs><filter height="300%" id="flwgy9xrajsm" width="300%" x="-1" y="-1"><feGaussianBlur result="blurOut" stdDeviation="2.0"/><feColorMatrix in="blurOut" result="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 .4 0"/><feOffset dx="4.0" dy="4.0" in="blurOut2" result="blurOut3"/><feBlend in="SourceGraphic" in2="blurOut3" mode="normal"/></filter></defs><g><rect fill="#FFFFFF" filter="url(#flwgy9xrajsm)" height="351.8594" style="stroke:#000000;stroke-width:2.0;" width="991" x="10" y="116.7266"/><rect fill="#FFFFFF" filter="url(#flwgy9xrajsm)" height="320.7266" style="stroke:#000000;stroke-width:2.0;" width="971" x="20" y="140.8594"/><rect fill="#FFFFFF" filter="url(#flwgy9xrajsm)" height="245.3281" style="stroke:#000000;stroke-width:2.0;" width="555" x="426" y="209.2578"/><rect fill="#FFFFFF" filter="url(#flwgy9xrajsm)" height="324.7266" style="stroke:#000000;stroke-width:2.0;" width="848" x="143" y="525.7188"/><rect fill="#FFFFFF" filter="url(#flwgy9xrajsm)" height="245.3281" style="stroke:#000000;stroke-width:2.0;" width="828" x="153" y="549.8516"/><line style="stroke:#A80036;stroke-width:1.0;stroke-dasharray:5.0,5.0;" x1="91" x2="91" y1="56.5938" y2="939.7109"/><line style="stroke:#A80036;stroke-width:1.0;stroke-dasharray:5.0,5.0;" x1="270" x2="270" y1="56.5938" y2="939.7109"/><line style="stroke:#A80036;stroke-width:1.0;stroke-dasharray:5.0,5.0;" x1="514" x2="514" y1="56.5938" y2="939.7109"/><line style="stroke:#A80036;stroke-width:1.0;stroke-dasharray:5.0,5.0;" x1="738" x2="738" y1="56.5938" y2="939.7109"/><line style="stroke:#A80036;stroke-width:1.0;stroke-dasharray:5.0,5.0;" x1="899" x2="899" y1="56.5938" y2="939.7109"/><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="30.2969" style="stroke:#A80036;stroke-width:1.5;" width="119" x="30" y="21.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="105" x="37" y="41.292">master branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="30.2969" style="stroke:#A80036;stroke-width:1.5;" width="119" x="30" y="938.7109"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="105" x="37" y="958.7061">master branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="30.2969" style="stroke:#A80036;stroke-width:1.5;" width="211" x="163" y="21.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="197" x="170" y="41.292">translations_update branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="30.2969" style="stroke:#A80036;stroke-width:1.5;" width="211" x="163" y="938.7109"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="197" x="170" y="958.7061">translations_update branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="30.2969" style="stroke:#A80036;stroke-width:1.5;" width="153" x="436" y="21.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="139" x="443" y="41.292">translations branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="30.2969" style="stroke:#A80036;stroke-width:1.5;" width="153" x="436" y="938.7109"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="139" x="443" y="958.7061">translations branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="46.5938" style="stroke:#A80036;stroke-width:1.5;" width="153" x="660" y="5"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="120" x="676.5" y="24.9951">weblate clone of</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="139" x="667" y="41.292">translations branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="46.5938" style="stroke:#A80036;stroke-width:1.5;" width="153" x="660" y="938.7109"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="120" x="676.5" y="958.7061">weblate clone of</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="139" x="667" y="975.0029">translations branch</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="46.5938" style="stroke:#A80036;stroke-width:1.5;" width="140" x="827" y="5"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="58" x="868" y="24.9951">weblate</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="122" x="834" y="41.292">pending changes</text><rect fill="#FEFECE" filter="url(#flwgy9xrajsm)" height="46.5938" style="stroke:#A80036;stroke-width:1.5;" width="140" x="827" y="938.7109"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="58" x="868" y="958.7061">weblate</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="122" x="834" y="975.0029">pending changes</text><rect fill="#EEEEEE" filter="url(#flwgy9xrajsm)" height="3" style="stroke:#EEEEEE;stroke-width:1.0;" width="1011" x="0" y="87.1602"/><line style="stroke:#000000;stroke-width:1.0;" x1="0" x2="1011" y1="87.1602" y2="87.1602"/><line style="stroke:#000000;stroke-width:1.0;" x1="0" x2="1011" y1="90.1602" y2="90.1602"/><rect fill="#EEEEEE" filter="url(#flwgy9xrajsm)" height="23.1328" style="stroke:#000000;stroke-width:2.0;" width="230" x="390.5" y="76.5938"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="211" x="396.5" y="92.6606">for each commit on master</text><path d="M10,116.7266 L314,116.7266 L314,123.7266 L304,133.7266 L10,133.7266 L10,116.7266 " fill="#EEEEEE" style="stroke:#000000;stroke-width:1.0;"/><rect fill="none" height="351.8594" style="stroke:#000000;stroke-width:2.0;" width="991" x="10" y="116.7266"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="259" x="25" y="129.7935">.github/workflows/integration.yml</text><path d="M20,140.8594 L309,140.8594 L309,147.8594 L299,157.8594 L20,157.8594 L20,140.8594 " fill="#EEEEEE" style="stroke:#000000;stroke-width:1.0;"/><rect fill="none" height="320.7266" style="stroke:#000000;stroke-width:2.0;" width="971" x="20" y="140.8594"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="244" x="35" y="153.9263">make weblate.push.translations</text><polygon fill="#A80036" points="502.5,190.2578,512.5,194.2578,502.5,198.2578,506.5,194.2578" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="91.5" x2="508.5" y1="194.2578" y2="194.2578"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="101" x="98.5" y="174.0591">pybabel extract</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="399" x="98.5" y="189.1919">extract messages, store messages.pot on translations branch</text><path d="M426,209.2578 L492,209.2578 L492,216.2578 L482,226.2578 L426,226.2578 L426,209.2578 " fill="#EEEEEE" style="stroke:#000000;stroke-width:1.0;"/><rect fill="none" height="245.3281" style="stroke:#000000;stroke-width:2.0;" width="555" x="426" y="209.2578"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="21" x="441" y="222.3247">alt</text><text fill="#000000" font-family="sans-serif" font-size="11" font-weight="bold" lengthAdjust="spacing" textLength="299" x="507" y="221.4683">[if there are some changes in messages.pot]</text><polygon fill="#FBFB77" filter="url(#flwgy9xrajsm)" points="711,231.3906,765,231.3906,775,242.3906,765,254.3906,711,254.3906,701,242.3906,711,231.3906" style="stroke:#A80036;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="50" x="713" y="247.4575">wlc lock</text><polygon fill="#A80036" points="726.5,276.6563,736.5,280.6563,726.5,284.6563,730.5,280.6563" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="514.5" x2="732.5" y1="280.6563" y2="280.6563"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="47" x="521.5" y="275.5903">wlc pull</text><polygon fill="#A80036" points="749.5,305.7891,739.5,309.7891,749.5,313.7891,745.5,309.7891" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="743.5" x2="898" y1="309.7891" y2="309.7891"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="74" x="755.5" y="304.7231">wlc commit</text><polygon fill="#A80036" points="525.5,334.9219,515.5,338.9219,525.5,342.9219,521.5,338.9219" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="519.5" x2="737.5" y1="338.9219" y2="338.9219"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="196" x="531.5" y="333.856">git merge weblate/translations</text><polygon fill="#A80036" points="726.5,409.4531,736.5,413.4531,726.5,417.4531,730.5,413.4531" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="514.5" x2="732.5" y1="413.4531" y2="413.4531"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="200" x="521.5" y="362.9888">pybabel update (messages.po)</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="163" x="521.5" y="378.1216">git add searx/translations</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="69" x="521.5" y="393.2544">git commit</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="51" x="521.5" y="408.3872">git push</text><polygon fill="#FBFB77" filter="url(#flwgy9xrajsm)" points="703,426.4531,773,426.4531,783,437.4531,773,449.4531,703,449.4531,693,437.4531,703,426.4531" style="stroke:#A80036;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="66" x="705" y="442.52">wlc unlock</text><rect fill="#EEEEEE" filter="url(#flwgy9xrajsm)" height="3" style="stroke:#EEEEEE;stroke-width:1.0;" width="1011" x="0" y="496.1523"/><line style="stroke:#000000;stroke-width:1.0;" x1="0" x2="1011" y1="496.1523" y2="496.1523"/><line style="stroke:#000000;stroke-width:1.0;" x1="0" x2="1011" y1="499.1523" y2="499.1523"/><rect fill="#EEEEEE" filter="url(#flwgy9xrajsm)" height="23.1328" style="stroke:#000000;stroke-width:2.0;" width="111" x="450" y="485.5859"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="92" x="456" y="501.6528">every Friday</text><path d="M143,525.7188 L513,525.7188 L513,532.7188 L503,542.7188 L143,542.7188 L143,525.7188 " fill="#EEEEEE" style="stroke:#000000;stroke-width:1.0;"/><rect fill="none" height="324.7266" style="stroke:#000000;stroke-width:2.0;" width="848" x="143" y="525.7188"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="325" x="158" y="538.7856">.github/workflows/translations-update.yml</text><path d="M153,549.8516 L465,549.8516 L465,556.8516 L455,566.8516 L153,566.8516 L153,549.8516 " fill="#EEEEEE" style="stroke:#000000;stroke-width:1.0;"/><rect fill="none" height="245.3281" style="stroke:#000000;stroke-width:2.0;" width="828" x="153" y="549.8516"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="267" x="168" y="562.9185">make weblate.translations.commit</text><polygon fill="#FBFB77" filter="url(#flwgy9xrajsm)" points="711,571.9844,765,571.9844,775,582.9844,765,594.9844,711,594.9844,701,582.9844,711,571.9844" style="stroke:#A80036;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="50" x="713" y="588.0513">wlc lock</text><polygon fill="#A80036" points="726.5,617.25,736.5,621.25,726.5,625.25,730.5,621.25" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="514.5" x2="732.5" y1="621.25" y2="621.25"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="47" x="521.5" y="616.1841">wlc pull</text><polygon fill="#A80036" points="749.5,646.3828,739.5,650.3828,749.5,654.3828,745.5,650.3828" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="743.5" x2="898" y1="650.3828" y2="650.3828"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="74" x="755.5" y="645.3169">wlc commit</text><polygon fill="#A80036" points="525.5,675.5156,515.5,679.5156,525.5,683.5156,521.5,679.5156" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="519.5" x2="737.5" y1="679.5156" y2="679.5156"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="196" x="531.5" y="674.4497">git merge weblate/translations</text><polygon fill="#A80036" points="281.5,750.0469,271.5,754.0469,281.5,758.0469,277.5,754.0469" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="275.5" x2="513.5" y1="754.0469" y2="754.0469"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="105" x="287.5" y="703.5825">pybabel compile</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="134" x="287.5" y="718.7153">cp searx/translations</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="44" x="287.5" y="733.8481">git add</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="69" x="287.5" y="748.981">git commit</text><polygon fill="#FBFB77" filter="url(#flwgy9xrajsm)" points="703,767.0469,773,767.0469,783,778.0469,773,790.0469,703,790.0469,693,778.0469,703,767.0469" style="stroke:#A80036;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="66" x="705" y="783.1138">wlc unlock</text><polygon fill="#FBFB77" filter="url(#flwgy9xrajsm)" points="175,807.1797,365,807.1797,375,826.1797,365,845.1797,175,845.1797,165,826.1797,175,807.1797" style="stroke:#A80036;stroke-width:1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="186" x="177" y="823.2466">create or update pull request</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="135" x="177" y="838.3794">"Update translations"</text><rect fill="#EEEEEE" filter="url(#flwgy9xrajsm)" height="3" style="stroke:#EEEEEE;stroke-width:1.0;" width="1011" x="0" y="878.0117"/><line style="stroke:#000000;stroke-width:1.0;" x1="0" x2="1011" y1="878.0117" y2="878.0117"/><line style="stroke:#000000;stroke-width:1.0;" x1="0" x2="1011" y1="881.0117" y2="881.0117"/><rect fill="#EEEEEE" filter="url(#flwgy9xrajsm)" height="23.1328" style="stroke:#000000;stroke-width:2.0;" width="168" x="421.5" y="867.4453"/><text fill="#000000" font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="149" x="427.5" y="883.5122">developper's review</text><polygon fill="#A80036" points="102.5,917.7109,92.5,921.7109,102.5,925.7109,98.5,921.7109" style="stroke:#A80036;stroke-width:1.0;"/><line style="stroke:#A80036;stroke-width:1.0;" x1="96.5" x2="269.5" y1="921.7109" y2="921.7109"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="121" x="108.5" y="916.645">merge pull request</text><!--MD5=[5c6c15370b11f873d07f34ae239ca6ad]
@startuml
participant "master branch" as master
participant "translations_update branch" as translations_update
participant "translations branch" as translations
participant "weblate clone of\ntranslations branch" as weblate
participant "weblate\npending changes " as weblate_change

== for each commit on master ==

group .github/workflows/integration.yml
  group make weblate.push.translations
    master -> translations: pybabel extract\nextract messages, store messages.pot on translations branch
    alt if there are some changes in messages.pot
      hnote over weblate : wlc lock
      translations -> weblate: wlc pull
      weblate_change -> weblate: wlc commit
      weblate -> translations: git merge weblate/translations
      translations -> weblate: pybabel update (messages.po)\ngit add searx/translations\ngit commit\ngit push
      hnote over weblate : wlc unlock
    end
  end  
end

== every Friday ==

group .github/workflows/translations-update.yml
  group make weblate.translations.commit
    hnote over weblate : wlc lock
    translations -> weblate: wlc pull
    weblate_change -> weblate: wlc commit
    weblate -> translations: git merge weblate/translations
    translations -> translations_update: pybabel compile\ncp searx/translations\ngit add\ngit commit
    hnote over weblate : wlc unlock
  end
  hnote over translations_update : create or update pull request\n"Update translations"
end

==  developper's review ==

translations_update -> master: merge pull request
@enduml

See
https://plantuml.com/en/sequence-diagram
https://www.planttext.com
--></g></svg>