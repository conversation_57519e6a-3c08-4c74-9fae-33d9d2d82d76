=========================
``searxng_extra/update/``
=========================

:origin:`[source] <searxng_extra/update/__init__.py>`

Scripts to update static data in :origin:`searx/data/`

.. _update_ahmia_blacklist.py:

``update_ahmia_blacklist.py``
=============================

:origin:`[source] <searxng_extra/update/update_ahmia_blacklist.py>`

.. automodule:: searxng_extra.update.update_ahmia_blacklist
  :members:


``update_currencies.py``
========================

:origin:`[source] <searxng_extra/update/update_currencies.py>`

.. automodule:: searxng_extra.update.update_currencies
  :members:

``update_engine_descriptions.py``
=================================

:origin:`[source] <searxng_extra/update/update_engine_descriptions.py>`

.. automodule:: searxng_extra.update.update_engine_descriptions
  :members:


``update_external_bangs.py``
============================

:origin:`[source] <searxng_extra/update/update_external_bangs.py>`

.. automodule:: searxng_extra.update.update_external_bangs
  :members:


``update_firefox_version.py``
=============================

:origin:`[source] <searxng_extra/update/update_firefox_version.py>`

.. automodule:: searxng_extra.update.update_firefox_version
  :members:


``update_engine_traits.py``
===========================

:origin:`[source] <searxng_extra/update/update_engine_traits.py>`

.. automodule:: searxng_extra.update.update_engine_traits
  :members:

.. _update_osm_keys_tags.py:

``update_osm_keys_tags.py``
===========================

:origin:`[source] <searxng_extra/update/update_osm_keys_tags.py>`

.. automodule:: searxng_extra.update.update_osm_keys_tags
  :members:


``update_pygments.py``
======================

:origin:`[source] <searxng_extra/update/update_pygments.py>`

.. automodule:: searxng_extra.update.update_pygments
  :members:

.. _update_locales.py:

``update_locales.py``
=====================

:origin:`[source] <searxng_extra/update/update_locales.py>`

.. automodule:: searxng_extra.update.update_locales
  :members:


``update_wikidata_units.py``
============================

:origin:`[source] <searxng_extra/update/update_wikidata_units.py>`

.. automodule:: searxng_extra.update.update_wikidata_units
  :members:
