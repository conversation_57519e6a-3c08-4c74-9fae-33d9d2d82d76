.. _main search results:

===================
Main Search Results
===================

In the :ref:`area main results` the results that a search engine has found for
the search term are displayed.

There is still no typing for all items in the :ref:`main result list`.  The
following types have been implemented so far ..

.. toctree::
   :maxdepth: 2

   main/mainresult
   main/keyvalue

The :ref:`LegacyResult <LegacyResult>` is used internally for the results that
have not yet been typed.  The templates can be used as orientation until the
final typing is complete.

- :ref:`template default` / :py:obj:`Result`
- :ref:`template images`
- :ref:`template videos`
- :ref:`template torrent`
- :ref:`template map`
- :ref:`template paper`
- :ref:`template packages`
- :ref:`template code`
- :ref:`template files`
- :ref:`template products`
