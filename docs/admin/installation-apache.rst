.. _installation apache:

======
Apache
======

.. _Apache: https://httpd.apache.org/
.. _Apache Debian:
    https://cwiki.apache.org/confluence/display/HTTPD/DistrosDefaultLayout#DistrosDefaultLayout-Debian,Ubuntu(Apachehttpd2.x):
.. _apache2.README.Debian:
    https://salsa.debian.org/apache-team/apache2/raw/master/debian/apache2.README.Debian
.. _Apache Arch Linux:
    https://wiki.archlinux.org/index.php/Apache_HTTP_Server
.. _Apache Fedora:
    https://docs.fedoraproject.org/en-US/quick-docs/getting-started-with-apache-http-server/index.html
.. _Apache directives:
    https://httpd.apache.org/docs/trunk/mod/directives.html
.. _Getting Started:
    https://httpd.apache.org/docs/current/en/getting-started.html
.. _Terms Used to Describe Directives:
    https://httpd.apache.org/docs/current/en/mod/directive-dict.html
.. _Configuration Files:
    https://httpd.apache.org/docs/current/en/configuring.html
.. _ProxyPreserveHost: https://httpd.apache.org/docs/trunk/mod/mod_proxy.html#proxypreservehost
.. _LoadModule:
    https://httpd.apache.org/docs/mod/mod_so.html#loadmodule
.. _IncludeOptional:
    https://httpd.apache.org/docs/mod/core.html#includeoptional
.. _DocumentRoot:
    https://httpd.apache.org/docs/trunk/mod/core.html#documentroot
.. _Location:
    https://httpd.apache.org/docs/trunk/mod/core.html#location
.. _uWSGI Apache support:
    https://uwsgi-docs.readthedocs.io/en/latest/Apache.html
.. _mod_proxy_uwsgi:
    https://uwsgi-docs.readthedocs.io/en/latest/Apache.html#mod-proxy-uwsgi
.. _mod_proxy_http:
    https://httpd.apache.org/docs/current/mod/mod_proxy_http.html
.. _mod_proxy:
    https://httpd.apache.org/docs/current/mod/mod_proxy.html


This section explains how to set up a SearXNG instance using the HTTP server Apache_.
If you did use the :ref:`installation scripts` and do not have any special preferences
you can install the :ref:`SearXNG site <apache searxng site>` using
:ref:`searxng.sh <searxng.sh overview>`:

.. code:: bash

   $ sudo -H ./utils/searxng.sh install apache

If you have special interests or problems with setting up Apache, the following
section might give you some guidance.


.. sidebar:: further read

   - `Apache Arch Linux`_
   - `Apache Debian`_
   - `apache2.README.Debian`_
   - `Apache Fedora`_
   - `Apache directives`_

.. contents::
   :depth: 2
   :local:
   :backlinks: entry


The Apache HTTP server
======================

If Apache_ is not installed, install it now. If apache_ is new to you, the
`Getting Started`_, `Configuration Files`_ and `Terms Used to Describe
Directives`_ documentation gives first orientation.  There is also a list of
`Apache directives`_ *to keep in the pocket*.

.. tabs::

   .. group-tab:: Ubuntu / debian

      .. code:: bash

         sudo -H apt-get install apache2

   .. group-tab:: Arch Linux

      .. code:: bash

         sudo -H pacman -S apache
         sudo -H systemctl enable httpd
         sudo -H systemctl start http

   .. group-tab::  Fedora / RHEL

      .. code:: bash

         sudo -H dnf install httpd
         sudo -H systemctl enable httpd
         sudo -H systemctl start httpd

Now at http://localhost you should see some kind of *Welcome* or *Test* page.
How this default site is configured, depends on the linux distribution
(compare `Apache directives`_).

.. tabs::

   .. group-tab:: Ubuntu / debian

      .. code:: bash

         less /etc/apache2/sites-enabled/000-default.conf

      In this file, there is a line setting the `DocumentRoot`_ directive:

      .. code:: apache

         DocumentRoot /var/www/html

      And the *welcome* page is the HTML file at ``/var/www/html/index.html``.

   .. group-tab:: Arch Linux

      .. code:: bash

         less /etc/httpd/conf/httpd.conf

      In this file, there is a line setting the `DocumentRoot`_ directive:

      .. code:: apache

         DocumentRoot "/srv/http"
         <Directory "/srv/http">
             Options Indexes FollowSymLinks
             AllowOverride None
             Require all granted
         </Directory>

      The *welcome* page of Arch Linux is a page showing the directory located
      at ``DocumentRoot``.  This *directory* page is generated by the Module
      `mod_autoindex <https://httpd.apache.org/docs/2.4/mod/mod_autoindex.html>`_:

      .. code:: apache

         LoadModule autoindex_module modules/mod_autoindex.so
         ...
         Include conf/extra/httpd-autoindex.conf

   .. group-tab::  Fedora / RHEL

      .. code:: bash

         less /etc/httpd/conf/httpd.conf

      In this file, there is a line setting the ``DocumentRoot`` directive:

      .. code:: apache

          DocumentRoot "/var/www/html"
          ...
          <Directory "/var/www">
              AllowOverride None
              # Allow open access:
              Require all granted
          </Directory>

      On fresh installations, the ``/var/www`` is empty and the *default
      welcome page* is shown, the configuration is located at::

        less /etc/httpd/conf.d/welcome.conf


.. _Debian's Apache layout:

Debian's Apache layout
----------------------

Be aware, Debian's Apache layout is quite different from the standard Apache
configuration.  For details look at the apache2.README.Debian_
(``/usr/share/doc/apache2/README.Debian.gz``).  Some commands you should know on
Debian:

* :man:`apache2ctl`:  Apache HTTP server control interface
* :man:`a2enmod`, :man:`a2dismod`: switch on/off modules
* :man:`a2enconf`, :man:`a2disconf`: switch on/off configurations
* :man:`a2ensite`, :man:`a2dissite`: switch on/off sites

.. _apache modules:

Apache modules
--------------

To load additional modules, in most distributions you have to uncomment the
lines with the corresponding LoadModule_ directive, except in :ref:`Debian's
Apache layout`.

.. tabs::

   .. group-tab:: Ubuntu / debian

      :ref:`Debian's Apache layout` uses :man:`a2enmod` and :man:`a2dismod` to
      activate or disable modules:

      .. code:: bash

         sudo -H a2enmod ssl
         sudo -H a2enmod headers
         sudo -H a2enmod proxy
         sudo -H a2enmod proxy_http
         sudo -H a2enmod proxy_uwsgi

   .. group-tab:: Arch Linux

      In the ``/etc/httpd/conf/httpd.conf`` file, activate LoadModule_
      directives:

      .. code:: apache

         LoadModule ssl_module           modules/mod_ssl.so
         LoadModule headers_module       modules/mod_headers.so
         LoadModule proxy_module         modules/mod_proxy.so
         LoadModule proxy_http_module    modules/mod_proxy_http.so
         LoadModule proxy_uwsgi_module   modules/mod_proxy_uwsgi.so

   .. group-tab::  Fedora / RHEL

      In the ``/etc/httpd/conf/httpd.conf`` file, activate LoadModule_
      directives:

      .. code:: apache

         LoadModule ssl_module           modules/mod_ssl.so
         LoadModule headers_module       modules/mod_headers.so
         LoadModule proxy_module         modules/mod_proxy.so
         LoadModule proxy_http_module    modules/mod_proxy_http.so
         LoadModule proxy_uwsgi_module   modules/mod_proxy_uwsgi.so


.. _apache sites:

Apache sites
------------

.. tabs::

   .. group-tab:: Ubuntu / debian

      In :ref:`Debian's Apache layout` you create a ``searxng.conf`` with the
      ``<Location /searxng >`` directive and save this file in the *sites
      available* folder at ``/etc/apache2/sites-available``.  To enable the
      ``searxng.conf`` use :man:`a2ensite`:

      .. code:: bash

         sudo -H a2ensite searxng.conf

   .. group-tab:: Arch Linux

      In the ``/etc/httpd/conf/httpd.conf`` file add a IncludeOptional_
      directive:

      .. code:: apache

         IncludeOptional sites-enabled/*.conf

      Create two folders, one for the *available sites* and one for the *enabled sites*:

      .. code:: bash

         mkdir -p /etc/httpd/sites-available
         mkdir -p /etc/httpd/sites-enabled

      Create configuration at ``/etc/httpd/sites-available`` and place a
      symlink to ``sites-enabled``:

      .. code:: bash

         sudo -H ln -s /etc/httpd/sites-available/searxng.conf \
                       /etc/httpd/sites-enabled/searxng.conf

   .. group-tab::  Fedora / RHEL

      In the ``/etc/httpd/conf/httpd.conf`` file add a IncludeOptional_
      directive:

      .. code:: apache

         IncludeOptional sites-enabled/*.conf

      Create two folders, one for the *available sites* and one for the *enabled sites*:

      .. code:: bash

         mkdir -p /etc/httpd/sites-available
         mkdir -p /etc/httpd/sites-enabled

      Create configuration at ``/etc/httpd/sites-available`` and place a
      symlink to ``sites-enabled``:

      .. code:: bash

         sudo -H ln -s /etc/httpd/sites-available/searxng.conf \
                       /etc/httpd/sites-enabled/searxng.conf


.. _apache searxng site:

Apache's SearXNG site
=====================

.. _mod_uwsgi: https://uwsgi-docs.readthedocs.io/en/latest/Apache.html#mod-uwsgi

.. sidebar:: uWSGI

   Use mod_proxy_uwsgi_ / don't use the old mod_uwsgi_ anymore.

To proxy the incoming requests to the SearXNG instance Apache needs the
mod_proxy_ module (:ref:`apache modules`).

.. sidebar:: HTTP headers

   With ProxyPreserveHost_ the incoming ``Host`` header is passed to the proxied
   host.

Depending on what your SearXNG installation is listening on, you need a http
mod_proxy_http_) or socket (mod_proxy_uwsgi_) communication to upstream.

The :ref:`installation scripts` installs the :ref:`reference setup
<use_default_settings.yml>` and a :ref:`uwsgi setup` that listens on a socket by default.
You can install and activate your own ``searxng.conf`` like shown in
:ref:`apache sites`.

.. tabs::

   .. group-tab:: socket

      .. kernel-include:: $DOCS_BUILD/includes/searxng.rst
         :start-after: START apache socket
         :end-before: END apache socket

   .. group-tab:: http

      .. kernel-include:: $DOCS_BUILD/includes/searxng.rst
         :start-after: START apache http
         :end-before: END apache http

.. _restart apache:

Restart service:

.. tabs::

   .. group-tab:: Ubuntu / debian

      .. code:: bash

         sudo -H systemctl restart apache2
         sudo -H service uwsgi restart searxng

   .. group-tab:: Arch Linux

      .. code:: bash

         sudo -H systemctl restart httpd
         sudo -H systemctl restart uwsgi@searxng

   .. group-tab::  Fedora / RHEL

      .. code:: bash

         sudo -H systemctl restart httpd
         sudo -H touch /etc/uwsgi.d/searxng.ini


disable logs
============

For better privacy you can disable Apache logs.  In the examples above activate
one of the lines and `restart apache`_:

.. code:: apache

   SetEnvIf Request_URI "/searxng" dontlog
   # CustomLog /dev/null combined env=dontlog

The ``CustomLog`` directive disables logs for the entire (virtual) server, use it
when the URL of the service does not have a path component (``/searxng``), so when
SearXNG is located at root (``/``).
