digraph G {

  node [style=filled, shape=box, fillcolor="#ffffcc", fontname=San<PERSON>];
  edge [fontname="Sans"];

  browser [label="browser", shape=tab, fillcolor=aliceblue];
  rp      [label="reverse proxy"];
  static  [label="static files", shape=folder, href="url to configure static files", fillcolor=lightgray];
  uwsgi   [label="uwsgi", shape=parallelogram href="https://docs.searxng.org/utils/searxng.sh.html"]
  redis     [label="redis DB", shape=cylinder];
  searxng1  [label="SearXNG #1", fontcolor=blue3];
  searxng2  [label="SearXNG #2", fontcolor=blue3];
  searxng3  [label="SearXNG #3", fontcolor=blue3];
  searxng4  [label="SearXNG #4", fontcolor=blue3];

  browser -> rp [label="HTTPS"]

  subgraph cluster_searxng {
      label = "SearXNG instance" fontname=Sans;
      bgcolor="#fafafa";
      { rank=same; static rp };
      rp -> static  [label="optional: reverse proxy serves static files", fillcolor=slategray, fontcolor=slategray];
      rp -> uwsgi [label="http:// (tcp) or unix:// (socket)"];
      uwsgi -> searxng1 -> redis;
      uwsgi -> searxng2 -> redis;
      uwsgi -> searxng3 -> redis;
      uwsgi -> searxng4 -> redis;
  }

}
