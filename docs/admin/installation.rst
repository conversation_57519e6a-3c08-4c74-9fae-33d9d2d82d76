.. _installation:

============
Installation
============

*You're spoilt for choice*, choose your preferred method of installation.

- :ref:`installation docker`
- :ref:`installation scripts`
- :ref:`installation basic`

The :ref:`installation basic` is an excellent illustration of *how a SearXNG
instance is build up* (see :ref:`architecture uWSGI`).  If you do not have any
special preferences, it's recommended to use the :ref:`installation docker` or the
:ref:`installation scripts`.

.. attention::

   SearXNG is growing rapidly, you should regularly read our :ref:`migrate and
   stay tuned` section.  If you want to upgrade an existing instance or migrate
   from searx to SearXNG, you should read this section first!
