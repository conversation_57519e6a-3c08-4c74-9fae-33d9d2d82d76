#!/bin/bash

# SearXNG + Enhanced Search MCP 综合启动脚本
# 同时启动SearXNG搜索引擎和增强搜索MCP服务器

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本信息
echo -e "${CYAN}🚀 SearXNG + Enhanced Search MCP 综合启动脚本${NC}"
echo -e "${CYAN}================================================${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
MCP_DIR="$PROJECT_DIR/enhanced_search_mcp"

echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}MCP目录: $MCP_DIR${NC}"

# 检查必要目录
if [ ! -d "$MCP_DIR" ]; then
    echo -e "${RED}❌ MCP目录不存在: $MCP_DIR${NC}"
    exit 1
fi

# 检查conda是否安装
echo -e "${YELLOW}🐍 检查conda环境...${NC}"
if ! command -v conda &> /dev/null; then
    echo -e "${RED}❌ conda未安装或未在PATH中${NC}"
    exit 1
fi

# 初始化conda
source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh || {
    echo -e "${RED}❌ 无法初始化conda${NC}"
    exit 1
}

echo -e "${GREEN}✅ conda环境初始化成功${NC}"

# 检查searxng conda环境
echo -e "${YELLOW}🔍 检查searxng conda环境...${NC}"
if conda env list | grep -q "searxng"; then
    echo -e "${GREEN}✅ searxng conda环境存在${NC}"
else
    echo -e "${RED}❌ searxng conda环境不存在${NC}"
    echo -e "${YELLOW}正在创建searxng环境...${NC}"
    conda create -n searxng python=3.11 -y
fi

# 激活searxng环境
echo -e "${YELLOW}🔄 激活searxng环境...${NC}"
conda activate searxng
echo -e "${GREEN}✅ 已激活conda环境: $(conda info --envs | grep '*' | awk '{print $1}')${NC}"

# 检查SearXNG依赖
echo -e "${YELLOW}📦 检查SearXNG依赖...${NC}"
cd "$PROJECT_DIR"

if [ -f "requirements.txt" ]; then
    echo -e "${BLUE}安装SearXNG依赖...${NC}"
    pip install -r requirements.txt
else
    echo -e "${YELLOW}⚠️  未找到SearXNG requirements.txt${NC}"
fi

# 检查MCP服务器依赖
echo -e "${YELLOW}📦 检查MCP服务器依赖...${NC}"
cd "$MCP_DIR"

if [ -f "requirements.txt" ]; then
    echo -e "${BLUE}安装MCP服务器依赖...${NC}"
    pip install -r requirements.txt
else
    echo -e "${YELLOW}⚠️  未找到MCP requirements.txt，安装基础依赖...${NC}"
    pip install fastmcp fastapi uvicorn aiohttp python-dotenv
fi

# 依赖检查函数
check_python_package() {
    local package=$1
    if python -c "import $package" 2>/dev/null; then
        echo -e "${GREEN}✅ $package${NC}"
        return 0
    else
        echo -e "${RED}❌ $package${NC}"
        return 1
    fi
}

# 检查关键依赖
echo -e "${YELLOW}🔍 检查关键Python包...${NC}"
MISSING_PACKAGES=()

# SearXNG依赖
for pkg in "flask" "werkzeug" "babel" "lxml" "httpx"; do
    if ! check_python_package "$pkg"; then
        MISSING_PACKAGES+=("$pkg")
    fi
done

# MCP服务器依赖
for pkg in "fastapi" "uvicorn" "aiohttp"; do
    if ! check_python_package "$pkg"; then
        MISSING_PACKAGES+=("$pkg")
    fi
done

# 安装缺失的包
if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
    echo -e "${YELLOW}📦 安装缺失的包: ${MISSING_PACKAGES[*]}${NC}"
    pip install "${MISSING_PACKAGES[@]}"
fi

# 检查端口占用
check_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $port ($service) 已被占用${NC}"
        echo -e "${YELLOW}正在尝试终止占用进程...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# 检查端口
echo -e "${YELLOW}🔌 检查端口占用...${NC}"
check_port 8888 "SearXNG"
check_port 8881 "MCP服务器"

# 创建日志目录
mkdir -p "$PROJECT_DIR/logs"
mkdir -p "$MCP_DIR/logs"

# 设置环境变量
export SEARXNG_URL="http://localhost:8888"
export MCP_HOST="0.0.0.0"
export MCP_PORT="8881"
export LOG_LEVEL="INFO"

# 启动函数
start_searxng() {
    echo -e "${PURPLE}🔍 启动SearXNG搜索引擎...${NC}"
    cd "$PROJECT_DIR"
    
    # 检查SearXNG配置
    if [ ! -f "searx/settings.yml" ]; then
        echo -e "${YELLOW}⚠️  SearXNG配置文件不存在，使用默认配置${NC}"
        if [ -f "searx/settings_robot.yml" ]; then
            cp searx/settings_robot.yml searx/settings.yml
        fi
    fi
    
    # 启动SearXNG (后台运行)
    nohup python -m searx.webapp > logs/searxng.log 2>&1 &
    SEARXNG_PID=$!
    echo -e "${GREEN}✅ SearXNG已启动 (PID: $SEARXNG_PID)${NC}"
    echo -e "${BLUE}📡 SearXNG地址: http://localhost:8888${NC}"
    
    # 等待SearXNG启动
    echo -e "${YELLOW}⏳ 等待SearXNG启动...${NC}"
    for i in {1..30}; do
        if curl -s --connect-timeout 2 http://localhost:8888 > /dev/null; then
            echo -e "${GREEN}✅ SearXNG启动成功${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo -e "${RED}❌ SearXNG启动超时${NC}"
    return 1
}

start_mcp_server() {
    echo -e "${PURPLE}🤖 启动Enhanced Search MCP服务器...${NC}"
    cd "$MCP_DIR"
    
    # 检查必要文件
    REQUIRED_FILES=("server.py" "enhanced_search_engine.py" "searxng_adapter.py" "config.py")
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            echo -e "${RED}❌ 缺少必要文件: $file${NC}"
            return 1
        fi
    done
    
    # 启动MCP服务器 (后台运行)
    nohup python server.py > logs/mcp_server.log 2>&1 &
    MCP_PID=$!
    echo -e "${GREEN}✅ MCP服务器已启动 (PID: $MCP_PID)${NC}"
    echo -e "${BLUE}📡 MCP SSE端点: http://localhost:8881/mcp/sse${NC}"
    echo -e "${BLUE}🏥 健康检查: http://localhost:8881/health${NC}"
    
    # 等待MCP服务器启动
    echo -e "${YELLOW}⏳ 等待MCP服务器启动...${NC}"
    for i in {1..20}; do
        if curl -s --connect-timeout 2 http://localhost:8881/health > /dev/null; then
            echo -e "${GREEN}✅ MCP服务器启动成功${NC}"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo -e "${RED}❌ MCP服务器启动超时${NC}"
    return 1
}

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🛑 正在停止服务...${NC}"
    
    if [ ! -z "$SEARXNG_PID" ]; then
        kill $SEARXNG_PID 2>/dev/null || true
        echo -e "${GREEN}✅ SearXNG已停止${NC}"
    fi
    
    if [ ! -z "$MCP_PID" ]; then
        kill $MCP_PID 2>/dev/null || true
        echo -e "${GREEN}✅ MCP服务器已停止${NC}"
    fi
    
    # 强制清理端口
    lsof -ti:8888 | xargs kill -9 2>/dev/null || true
    lsof -ti:8881 | xargs kill -9 2>/dev/null || true
    
    echo -e "${CYAN}👋 服务已全部停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主启动流程
echo -e "${CYAN}🚀 开始启动服务...${NC}"

# 启动SearXNG
if start_searxng; then
    echo -e "${GREEN}✅ SearXNG启动完成${NC}"
else
    echo -e "${RED}❌ SearXNG启动失败${NC}"
    cleanup
    exit 1
fi

# 启动MCP服务器
if start_mcp_server; then
    echo -e "${GREEN}✅ MCP服务器启动完成${NC}"
else
    echo -e "${RED}❌ MCP服务器启动失败${NC}"
    cleanup
    exit 1
fi

# 显示服务状态
echo -e "\n${CYAN}🎉 所有服务启动成功！${NC}"
echo -e "${CYAN}================================${NC}"
echo -e "${GREEN}🔍 SearXNG搜索引擎:${NC}"
echo -e "   📡 Web界面: http://localhost:8888"
echo -e "   📊 API端点: http://localhost:8888/search"
echo -e ""
echo -e "${GREEN}🤖 Enhanced Search MCP服务器:${NC}"
echo -e "   📡 SSE端点: http://localhost:8881/mcp/sse"
echo -e "   🏥 健康检查: http://localhost:8881/health"
echo -e "   🛠️  工具列表: http://localhost:8881/tools"
echo -e ""
echo -e "${BLUE}📋 Agent-Zero MCP配置:${NC}"
echo -e '   {
     "name": "enhanced-search",
     "type": "sse", 
     "url": "http://localhost:8881/mcp/sse"
   }'
echo -e ""
echo -e "${YELLOW}📝 日志文件:${NC}"
echo -e "   SearXNG: $PROJECT_DIR/logs/searxng.log"
echo -e "   MCP服务器: $MCP_DIR/logs/mcp_server.log"
echo -e ""
echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"

# 保持脚本运行
while true; do
    sleep 10
    
    # 检查服务状态
    if ! kill -0 $SEARXNG_PID 2>/dev/null; then
        echo -e "${RED}❌ SearXNG进程已停止${NC}"
        cleanup
        exit 1
    fi
    
    if ! kill -0 $MCP_PID 2>/dev/null; then
        echo -e "${RED}❌ MCP服务器进程已停止${NC}"
        cleanup
        exit 1
    fi
done
