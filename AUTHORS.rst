searxng is a fork from `searx <https://github.com/searx/searx>`_ and is
maintained by <PERSON> (`@return42 <https://github.com/return42>`_)

People who have submitted patches/translations, reported bugs, consulted
features or generally made searx better:

- <PERSON> `@asciimoo <https://github.com/asciimoo>`_
- <PERSON><PERSON> `@matejc <https://github.com/matejc>`_
- <PERSON><PERSON><PERSON><PERSON> `@unixfox <https://github.com/unixfox>`_
- <PERSON> `pointhi <https://github.com/pointhi>`_
- <PERSON><PERSON><PERSON> `@kvch <https://github.com/kvch>`_
- `@Cqoicebordel <https://github.com/Cqoicebordel>`_
- <PERSON> `@MarcAbonce <https://github.com/MarcAbonce>`_
- `@pofilo <https://github.com/pofilo>`_

- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>
- @pw3t
- @rhapsodhy
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
- <PERSON>
- @HLFH
- @TheRadialActive
- @<PERSON><PERSON>
- <PERSON>
- <PERSON>
- rike
- dp
- <PERSON>mann
- @courgette
- @kernc
- @Reventl0v
- Caner Başaran
- Benjamin Sonntag
- @opi
- @dimqua
- Giorgos Logiotatidis
- Luc Didry
- Niklas Haas
- @underr
- Emmanuel Benazera
- @GreenLunar
- Kang-min Liu
- Kirill Isakov
- Guilhem Bonnefille
- @jibe-b
- Christian Pietsch @pietsch
- @Maxqia
- Ashutosh Das @pyprism
- YuLun Shih @imZack
- Dmitry Mikhirev @mikhirev
- David A Roberts `@davidar <https://github.com/davidar>`_
- Jan Verbeek @blyxxyz
- Ammar Najjar @ammarnajjar
- @stepshal
- François Revol @mmuman
- Harry Wood @harry-wood
- Thomas Renard @threnard
- Pydo `<https://github.com/pydo>`_
- Athemis `<https://github.com/Athemis>`_
- Stefan Antoni `<http://stefan.antoni.io>`
- @firebovine
- Lorenzo J. Lucchini @luccoj
- @eig8phei
- @maxigas
- Jannik Winkel @kiney
- @juanitobananas
- Vache Asatryan @vachi
- Luca CPZ @lcpz
- @nikaiw
- Thirnearez
- Hypolite Petovan @MrPetovan
- @woorst
- @Apply55gx
- @pyrrh0n1c
- @cclauss
- QGW @moon2l
- Pierre-Alain Toret @daftaupe
- Matthew Olmsted @icegiant
- Michael Tran @trankmichael
- Joseph Nuthalapati @josephkiranbabu
- @maiki
- Richard Didier @zeph33
- Michael Vieria @Themimitoof
- Richard Nespithal @rndevfx
- Stanislas @angristan
- @rinpatch
- g. s. @usernameisntallowed
- Léo Bourrel @bourrel
- @cy8aer
- @Popolon
- Alice Ferrazzi @aliceinwire
- @LiquidLemon
- @dadosch
- Václav Zouzalík @Venca24
- @ZEROF
- Ivan Skytte Jørgensen @isj-privacore
- @miicha
- Étienne Deparis @milouse
- @pelag0s
- Denis Wernert @d-tux
- Robin Hallabro-Kokko @hallabro
- Jonas Zohren @jfowl
- Elias Ojala @theel0ja
- @brunob
- Nick Espig @nachtalb
- Rachmadani Haryono @rachmadaniHaryono
- Frank de Lange @yetangitu
- Nicolas Gelot @nfk
- @volth
- Mathieu Brunot @madmath03
- @lorddavidiii
- @x250
- Robby O'Connor @robbyoconnor
- Finn @0xhtml
- @tmikaeld
- @hobbestigrou
- Vipul @finn0
- @CaffeinatedTech
- Robin Schneider @ypid
- @splintah
- Lukas van den Berk @lukasvdberk
- @piplongrun
- Jason Kaltsikis @jjasonkal
- Sion Kazama @KazamaSion
- @resynth1943
- Mostafa Ahangarha @ahangarha
- @gordon-quad
- Sophie Tauchert @999eagle
- @bauruine
- Michael Ilsaas `<https://mikeri.net>`_
- @renyhp
- rachmadani haryono @rachmadaniHaryono
- Mohamad Safadieh @msafadieh
- @gardouille
- @resynth1943
- @Eliesemoule
- @gardouille
- @GazoilKerozen
- Lukáš Kucharczyk @KucharczykL
- Lynda Lopez @lyndalopez544
- M. Efe Çetin @efectn
- Nícholas Kegler @nicholasks
- @pierrechtux
- Scott Wallace @scottwallacesh
- @Singustromo
- @TheEvilSkeleton
- @Wonderfall
- @mrwormo
- Xiaoyu WEI @xywei
- @joshu9h
- Daniel Hones
- @cyclaero
- @thezeroalpha
- @Tobi823
- @archiecodes
- @BBaoVanC
- @datagram1
- @lucky13820
- @jhigginbotham
- @xenrox
- @OliveiraHermogenes
- Paul Alcock @Guilvareux
- Sam A. `<https://samsapti.dev>`_
- @XavierHorwood
- Ahmad Alkadri `<https://github.com/ahmad-alkadri>`_
- Milad Laly @Milad-Laly
- @llmII
- @blob42 `<https://blob42.xyz>`_
- Paolo Basso `<https://github.com/paolobasso99>`
- Bernie Huang `<https://github.com/BernieHuang2008>`
- Austin Olacsi `<https://github.com/Austin-Olacsi>`
- @micsthepick
- Daniel Kukula `<https://github.com/dkuku>`
- Patrick Evans `https://github.com/holysoles`
- Daniel Mowitz `<https://daniel.mowitz.rocks>`
- `Bearz314 <https://github.com/bearz314>`_
- Tommaso Colella `<https://github.com/gioleppe>`
