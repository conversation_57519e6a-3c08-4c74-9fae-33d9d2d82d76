#!/usr/bin/env python
# SPDX-License-Identifier: AGPL-3.0-or-later
"""Script that implements some prebuild tasks needed by target docs.prebuild
"""

import sys
import os.path
import time
from contextlib import contextmanager

from searx import settings, get_setting, locales
from searx.infopage import InfoPageSet, InfoPage

_doc_user = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'docs', 'user'))


def main():
    locales.locales_initialize()
    base_url = get_setting('server.base_url', None)
    if base_url:
        infopageset_ctx = _instance_infosetset_ctx(base_url)
    else:
        infopageset_ctx = _offline_infosetset_ctx()

    with infopageset_ctx as infopageset:
        for _, _, page in infopageset.iter_pages('en'):
            fname = os.path.join(_doc_user, os.path.basename(page.fname))
            with open(fname, 'w', encoding='utf-8') as f:
                f.write(page.content)


class OfflinePage(InfoPage):  # pylint: disable=missing-class-docstring

    def get_ctx(self):
        """Jinja context to render :py:obj:`DocPage.content` for offline purpose (no
        links to SearXNG instance)"""

        ctx = super().get_ctx()
        ctx['link'] = lambda name, url: '`%s`' % name
        ctx['search'] = lambda query: '`%s`' % query

        return ctx


@contextmanager
def _offline_infosetset_ctx():
    yield InfoPageSet(OfflinePage)


@contextmanager
def _instance_infosetset_ctx(base_url):
    # The url_for functions in the jinja templates need all routes to be
    # registered in the Flask app.

    settings['server']['secret_key'] = ''
    from searx.webapp import app  # pylint: disable=import-outside-toplevel

    # Specify base_url so that url_for() works for base_urls.  If base_url is
    # specified, then these values from are given preference over any Flask's
    # generics (see flaskfix.py).

    with app.test_request_context(base_url=base_url):
        yield InfoPageSet()

    # The searx.webapp import from above fires some HTTP requests, that's
    # why we get a RuntimeError::
    #
    #     RuntimeError: The connection pool was closed while 1 HTTP \
    #       requests/responses were still in-flight.
    #
    # Closing network won't help ..
    #   from searx.network import network
    #   network.done()

    # waiting some seconds before ending the command line was the only solution I
    # found ..

    time.sleep(3)


if __name__ == '__main__':
    sys.exit(main())
