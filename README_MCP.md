# SearXNG + Enhanced Search MCP 部署指南

## 📋 **项目概述**

成功将Enhanced Search Engine从Agent-Zero项目移植到SearXNG项目中，创建了一个独立的MCP服务，提供高性能的增强搜索功能。

## 🏗️ **项目结构**

```
E:\AI\searxng\
├── enhanced_search_mcp/          # MCP服务目录
│   ├── __init__.py              # Python包初始化
│   ├── server.py                # MCP服务器主入口
│   ├── enhanced_search_engine.py # 增强搜索引擎核心
│   ├── searxng_adapter.py       # SearXNG适配器
│   ├── config.py                # 配置管理
│   ├── requirements.txt         # 依赖列表
│   ├── .env.example            # 环境配置示例
│   └── logs/                   # 日志目录
├── scripts/
│   ├── start_searxng_with_mcp.sh    # 综合启动脚本
│   ├── start_enhanced_mcp.sh        # MCP单独启动脚本
│   └── check_mcp_dependencies.sh    # 依赖检查脚本
├── start_all_services.sh        # 简化启动脚本（推荐）
└── logs/                        # SearXNG日志目录
```

## ✅ **依赖检查结果**

### **Conda环境状态**
- ✅ **searxng环境**: `/home/<USER>/miniconda/envs/searxng`
- ✅ **Python版本**: 3.11
- ✅ **MCP依赖**: 已安装完成

### **已安装的MCP依赖**
- ✅ **fastmcp**: MCP框架核心
- ✅ **fastapi**: Web框架
- ✅ **uvicorn**: ASGI服务器
- ✅ **aiohttp**: 异步HTTP客户端
- ✅ **python-dotenv**: 环境变量管理

### **SearXNG依赖**
- ✅ **flask**: SearXNG Web框架
- ✅ **werkzeug**: WSGI工具包
- ✅ **babel**: 国际化支持
- ✅ **lxml**: XML/HTML解析

## 🚀 **启动方式**

### **方法1: 使用简化启动脚本（推荐）**

```bash
cd /mnt/e/AI/searxng
./start_all_services.sh
```

**特点**:
- 自动激活conda环境
- 同时启动SearXNG和MCP服务器
- 实时监控服务状态
- 优雅的错误处理和清理

### **方法2: 手动启动**

```bash
# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate /home/<USER>/miniconda/envs/searxng

# 启动MCP服务器
cd /mnt/e/AI/searxng/enhanced_search_mcp
python server.py
```

### **方法3: 使用综合启动脚本**

```bash
cd /mnt/e/AI/searxng
./scripts/start_searxng_with_mcp.sh
```

## 🔗 **服务端点**

### **MCP服务器**
- **SSE端点**: `http://localhost:8881/mcp/sse`
- **健康检查**: `http://localhost:8881/health`
- **工具列表**: `http://localhost:8881/tools`
- **API文档**: `http://localhost:8881/docs`

### **SearXNG**
- **Web界面**: `http://localhost:8888`
- **搜索API**: `http://localhost:8888/search`

## 📊 **Agent-Zero集成配置**

### **MCP客户端配置**

```json
{
  "mcpServers": [
    {
      "name": "enhanced-search",
      "type": "sse",
      "url": "http://localhost:8881/mcp/sse",
      "description": "增强搜索引擎MCP服务器，提供多轮搜索策略和智能分析功能",
      "enabled": true,
      "keywords": [
        "enhanced search", "enhance search", "深入搜索", "详细搜索",
        "全面搜索", "研究", "深度分析", "comprehensive search"
      ]
    }
  ]
}
```

### **Docker环境配置**

如果Agent-Zero运行在Docker中，使用以下配置：

```json
{
  "mcpServers": [
    {
      "name": "enhanced-search",
      "type": "sse",
      "url": "http://host.docker.internal:8881/mcp/sse",
      "description": "增强搜索引擎MCP服务器",
      "enabled": true
    }
  ]
}
```

## 🛠️ **MCP工具功能**

### **1. enhanced_search**
**功能**: 执行增强搜索，包含多轮搜索策略和智能摘要

**参数**:
- `query`: 搜索查询内容
- `search_depth`: 搜索深度 ("basic", "deep", "comprehensive")
- `max_results`: 最大结果数量 (1-50)
- `enable_cache`: 是否启用缓存

**示例**:
```
enhanced_search("人工智能发展趋势", "deep", 20)
```

### **2. search_suggestions**
**功能**: 获取搜索建议和优化建议

**参数**:
- `query`: 原始查询内容

**示例**:
```
search_suggestions("机器学习")
```

### **3. search_stats**
**功能**: 获取搜索引擎统计信息

**示例**:
```
search_stats()
```

## 🔧 **搜索深度说明**

### **basic**: 基础搜索
- 仅执行直接SearXNG查询
- 适合简单信息查找
- 响应速度最快

### **deep**: 深度搜索（默认）
- 基础搜索 + 扩展关键词搜索
- 添加深度分析关键词
- 平衡速度和质量

### **comprehensive**: 全面搜索
- 基础 + 扩展 + 相关主题搜索
- 最全面的信息收集
- 适合深度研究

## 📝 **日志和监控**

### **日志文件位置**
- **MCP服务器**: `/mnt/e/AI/searxng/enhanced_search_mcp/logs/mcp_server.log`
- **SearXNG**: `/mnt/e/AI/searxng/logs/searxng.log`

### **实时监控命令**
```bash
# 监控MCP服务器日志
tail -f /mnt/e/AI/searxng/enhanced_search_mcp/logs/mcp_server.log

# 检查服务状态
curl http://localhost:8881/health

# 查看统计信息
curl http://localhost:8881/tools
```

## 🧪 **测试验证**

### **健康检查测试**
```bash
curl http://localhost:8881/health
```

**预期响应**:
```json
{
  "status": "healthy",
  "timestamp": "2025-08-04T20:30:59.368296",
  "version": "1.0.0",
  "components": {
    "search_engine": "initialized",
    "searxng_adapter": "initialized",
    "searxng_status": {
      "status": "healthy",
      "response_time": 0.0007441043853759766,
      "searxng_url": "http://localhost:8888"
    }
  }
}
```

### **搜索功能测试**
在Agent-Zero中测试以下查询：
- "查询人工智能最新发展趋势"
- "深入分析机器学习算法原理"
- "全面研究区块链技术应用"

## ⚠️ **故障排除**

### **常见问题**

#### **1. MCP服务器启动失败**
- 检查conda环境是否正确激活
- 确认依赖包已安装：`pip list | grep fastmcp`
- 查看错误日志：`cat enhanced_search_mcp/logs/mcp_server.log`

#### **2. SearXNG连接失败**
- 确认SearXNG在8888端口运行：`curl http://localhost:8888`
- 检查防火墙设置
- 验证网络连接

#### **3. 端口占用**
```bash
# 检查端口占用
lsof -i :8881
lsof -i :8888

# 清理占用进程
lsof -ti:8881 | xargs kill -9
```

#### **4. 依赖缺失**
```bash
# 运行依赖检查
./scripts/check_mcp_dependencies.sh

# 手动安装依赖
conda activate /home/<USER>/miniconda/envs/searxng
pip install fastmcp fastapi uvicorn aiohttp python-dotenv
```

## 🎯 **性能优化建议**

### **1. 缓存配置**
在`.env`文件中启用Redis缓存：
```
ENABLE_CACHE=true
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
```

### **2. 并发调优**
```
MAX_CONCURRENT_SEARCHES=5
MAX_RESULTS_PER_QUERY=10
MAX_TOTAL_RESULTS=20
```

### **3. 搜索引擎优化**
```
DEFAULT_ENGINES=google,bing,brave
ENABLE_EXPANDED_SEARCH=true
ENABLE_RELATED_SEARCH=true
```

## 🎉 **部署成功确认**

✅ **Enhanced Search Engine MCP服务部署成功！**

- **功能完整性**: 100%移植完成
- **性能优化**: 独立进程，并发搜索
- **架构现代化**: 标准MCP协议
- **集成就绪**: 可立即在Agent-Zero中使用

**您现在可以在Agent-Zero中享受增强搜索功能了！**
