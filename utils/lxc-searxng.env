# -*- coding: utf-8; mode: sh indent-tabs-mode: nil -*-
# SPDX-License-Identifier: AGPL-3.0-or-later
# shellcheck shell=bash

# This file is a setup of a LXC suite.  It is sourced from different context, do
# not manipulate the environment directly, implement functions and manipulate
# environment only in subshells.

lxc_set_suite_env() {

    export LXC_SUITE_NAME="searxng"

    # name of https://images.linuxcontainers.org
    export LINUXCONTAINERS_ORG_NAME="${LINUXCONTAINERS_ORG_NAME:-images}"
    export LXC_HOST_PREFIX="${LXC_SUITE_NAME:-searx}"
    export LXC_SUITE=(

        # end of standard support see https://wiki.ubuntu.com/Releases
        "$LINUXCONTAINERS_ORG_NAME:ubuntu/20.04"  "ubu2004" # LTS EOSS April 2025
        "$LINUXCONTAINERS_ORG_NAME:ubuntu/22.04"  "ubu2204" # LTS EOSS April 2027

        # EOL see https://fedoraproject.org/wiki/Releases
        "$LINUXCONTAINERS_ORG_NAME:fedora/35"     "fedora35"

        # rolling releases see https://www.archlinux.org/releng/releases/
        "$LINUXCONTAINERS_ORG_NAME:archlinux"     "archlinux"
    )
}

lxc_suite_install_info() {
    (
        lxc_set_suite_env
        cat <<EOF
LXC suite: ${LXC_SUITE_NAME}
  Suite includes installation of SearXNG
  images:     ${LOCAL_IMAGES[*]}
  containers: ${CONTAINERS[*]}
EOF
    )
}

lxc_suite_install() {
    (
        lxc_set_suite_env
        FORCE_TIMEOUT=0 "${LXC_REPO_ROOT}/utils/searxng.sh" install all
        rst_title "Suite installation finished ($(hostname))" part
        if ask_yn "Developer install? (wraps source from HOST into the running instance)" Yn; then
            "${LXC_REPO_ROOT}/utils/searxng.sh" searxng.install.link_src "$(pwd)"
        fi
        lxc_suite_info
        echo
    )
}

lxc_suite_info() {
    (
        lxc_set_suite_env
        for ip in $(global_IPs) ; do
            if [[ $ip =~ .*:.* ]]; then
                info_msg "(${ip%|*}) IPv6:       http://[${ip#*|}]"
            else
                # IPv4:
                # shellcheck disable=SC2034,SC2031
                info_msg "(${ip%|*}) docs-live:  http://${ip#*|}:8080/"
            fi
        done
        "${LXC_REPO_ROOT}/utils/searxng.sh" searxng.instance.env
    )
}
