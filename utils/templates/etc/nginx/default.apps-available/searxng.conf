location ${SEARXNG_URL_PATH} {

    proxy_pass http://${SEARXNG_INTERNAL_HTTP};

    proxy_set_header   Host             \$host;
    proxy_set_header   Connection       \$http_connection;

    # see flaskfix.py
    proxy_set_header   X-Scheme         \$scheme;
    proxy_set_header   X-Script-Name    ${SEARXNG_URL_PATH};

    # see limiter.py
    proxy_set_header   X-Real-IP        \$remote_addr;
    proxy_set_header   X-Forwarded-For  \$proxy_add_x_forwarded_for;

    # proxy_buffering  off;
    # proxy_request_buffering off;
    # proxy_buffer_size 8k;

}

# uWSGI serves the static files and in settings.yml we use::
#
#   ui:
#     static_use_hash: true
#
# location ${SEARXNG_URL_PATH}/static/ {
#     alias ${SEARXNG_STATIC}/;
# }
