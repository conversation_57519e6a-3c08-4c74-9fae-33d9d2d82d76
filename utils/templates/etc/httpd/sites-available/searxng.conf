# -*- coding: utf-8; mode: apache -*-

LoadModule ssl_module           ${APACHE_MODULES}/mod_ssl.so
LoadModule headers_module       ${APACHE_MODULES}/mod_headers.so
LoadModule proxy_module         ${APACHE_MODULES}/mod_proxy.so
LoadModule proxy_http_module    ${APACHE_MODULES}/mod_proxy_http.so
# LoadModule setenvif_module      ${APACHE_MODULES}/mod_setenvif.so
#
# SetEnvIf Request_URI "${SEARXNG_URL_PATH}" dontlog
# CustomLog /dev/null combined env=dontlog

<Location ${SEARXNG_URL_PATH}>

    Require all granted
    Order deny,allow
    Deny from all
    # Allow from fd00::/8 ***********/16 fe80::/10 *********/8 ::1
    Allow from all

    # add the trailing slash
    RedirectMatch  308 ${SEARXNG_URL_PATH}\$ ${SEARXNG_URL_PATH}/

    ProxyPreserveHost On
    ProxyPass http://${SEARXNG_INTERNAL_HTTP}

    # see flaskfix.py
    RequestHeader set X-Scheme %{REQUEST_SCHEME}s
    RequestHeader set X-Script-Name ${SEARXNG_URL_PATH}

    # see limiter.py
    RequestHeader set X-Real-IP %{REMOTE_ADDR}s
    RequestHeader append X-Forwarded-For %{REMOTE_ADDR}s

</Location>

# uWSGI serves the static files and in settings.yml we use::
#
#   ui:
#     static_use_hash: true
#
# Alias ${SEARXNG_URL_PATH}/static/ ${SEARXNG_STATIC}/
