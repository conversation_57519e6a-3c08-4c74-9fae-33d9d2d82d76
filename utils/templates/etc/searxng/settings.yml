# SearXNG settings

use_default_settings: true

general:
  debug: false
  instance_name: "SearXNG"

search:
  safe_search: 2
  autocomplete: 'duckduckgo'
  formats:
    - html

server:
  # Is overwritten by ${SEARXNG_SECRET}
  secret_key: "ultrasecretkey"
  limiter: true
  image_proxy: true
  # public URL of the instance, to ensure correct inbound links. Is overwritten
  # by ${SEARXNG_BASE_URL}.
  # base_url: http://example.com/location

redis:
  # URL to connect redis database. Is overwritten by ${SEARXNG_REDIS_URL}.
  url: unix:///usr/local/searxng-redis/run/redis.sock?db=0

ui:
  static_use_hash: true

# preferences:
#   lock:
#     - autocomplete
#     - method

# engines:
#
#   - name: fdroid
#     disabled: false
#
#   - name: apk mirror
#     disabled: false
#
#   - name: mediathekviewweb
#     categories: TV
#     disabled: false
