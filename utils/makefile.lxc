# -*- coding: utf-8; mode: makefile-gmake -*-
#
# LXC environment
# ===============
#
# To activate/deactivate LXC makefile environment in a container, set/unset link
# from root '/.lxcenv.mk' to *this* file::
#
#   sudo make ./utils/makefile.lxc lxc-activate
#   sudo make ./utils/makefile.lxc lxc-deactivate

LXC_ENV_FOLDER=lxc-env/$(shell hostname)/

lxc-help::
	@echo  '  LXC: running in container LXC_ENV_FOLDER=$(LXC_ENV_FOLDER)'

# If not activated, serve target 'lxc-activate' ..
ifeq (,$(wildcard /.lxcenv.mk))
PHONY += lxc-activate
lxc-activate:
	ln -s "$(abspath $(lastword $(MAKEFILE_LIST)))" "/.lxcenv.mk"
else
# .. and if activated, serve target 'lxc-deactivate'.
PHONY += lxc-deactivate
lxc-deactivate:
	rm /.lxcenv.mk
$(LXC_ENV_FOLDER):
	$(Q)mkdir -p $(LXC_ENV_FOLDER)
	$(Q)echo placeholder > $(LXC_ENV_FOLDER).placeholder
endif

.PHONY: $(PHONY)
