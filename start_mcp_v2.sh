#!/bin/bash

# Enhanced Search MCP服务器 v2.0 启动脚本
# 使用FastMCP 2.0最佳实践

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 启动Enhanced Search MCP服务器 v2.0${NC}"
echo -e "${CYAN}===========================================${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
MCP_DIR="$PROJECT_DIR/enhanced_search_mcp"

echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}MCP目录: $MCP_DIR${NC}"

# 初始化conda
echo -e "${YELLOW}🐍 初始化conda环境...${NC}"
source ~/miniconda3/etc/profile.d/conda.sh

# 激活searxng环境
echo -e "${YELLOW}🔄 激活searxng环境...${NC}"
conda activate /home/<USER>/miniconda/envs/searxng
echo -e "${GREEN}✅ 已激活searxng环境${NC}"

# 检查并升级FastMCP到最新版本
echo -e "${YELLOW}📦 检查FastMCP版本...${NC}"
pip install --upgrade fastmcp>=2.11.0
echo -e "${GREEN}✅ FastMCP已更新到最新版本${NC}"

# 检查关键依赖
echo -e "${YELLOW}📦 检查关键依赖...${NC}"
python -c "import fastmcp; print(f'FastMCP版本: {fastmcp.__version__}')" 2>/dev/null && echo -e "${GREEN}✅ fastmcp${NC}" || echo -e "${RED}❌ fastmcp${NC}"
python -c "import fastapi" 2>/dev/null && echo -e "${GREEN}✅ fastapi${NC}" || echo -e "${RED}❌ fastapi${NC}"
python -c "import uvicorn" 2>/dev/null && echo -e "${GREEN}✅ uvicorn${NC}" || echo -e "${RED}❌ uvicorn${NC}"
python -c "import aiohttp" 2>/dev/null && echo -e "${GREEN}✅ aiohttp${NC}" || echo -e "${RED}❌ aiohttp${NC}"

# 检查端口占用并清理
cleanup_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  清理端口 $port ($service)...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

cleanup_port 8881 "MCP服务器"

# 创建日志目录
mkdir -p "$MCP_DIR/logs"

# 设置环境变量
export SEARXNG_URL="http://localhost:8888"
export MCP_HOST="0.0.0.0"
export MCP_PORT="8881"
export LOG_LEVEL="INFO"

# 检查SearXNG是否可访问
echo -e "${CYAN}🔍 检查SearXNG连接...${NC}"
if curl -s --connect-timeout 5 http://localhost:8888 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ SearXNG服务可访问${NC}"
else
    echo -e "${YELLOW}⚠️  SearXNG服务不可访问，MCP服务器将尝试连接${NC}"
fi

# 启动MCP服务器 v2.0
echo -e "${CYAN}🤖 启动Enhanced Search MCP服务器 v2.0...${NC}"
cd "$MCP_DIR"

# 检查必要文件
if [ -f "server_v2.py" ] && [ -f "enhanced_search_engine.py" ]; then
    echo -e "${GREEN}✅ MCP服务器v2.0文件存在${NC}"
    
    echo -e "${GREEN}🚀 启动MCP服务器v2.0...${NC}"
    echo -e "${BLUE}📡 MCP端点: http://localhost:8881/mcp${NC}"
    echo -e "${BLUE}🔄 使用Streamable HTTP传输协议${NC}"
    echo -e "${BLUE}✨ 支持FastMCP 2.0所有特性${NC}"
    echo ""
    echo -e "${YELLOW}按 Ctrl+C 停止服务器${NC}"
    echo ""
    
    # 启动服务器 (前台运行)
    python server_v2.py
else
    echo -e "${RED}❌ MCP服务器v2.0文件缺失${NC}"
    echo -e "${YELLOW}💡 尝试使用原版服务器...${NC}"
    if [ -f "server.py" ]; then
        python server.py
    else
        echo -e "${RED}❌ 未找到任何服务器文件${NC}"
        exit 1
    fi
fi
