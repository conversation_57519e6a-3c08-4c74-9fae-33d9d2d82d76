#!/bin/bash

# SearXNG + Enhanced Search MCP 简化启动脚本
# 适配实际的conda环境路径

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 启动SearXNG + Enhanced Search MCP服务${NC}"
echo -e "${CYAN}============================================${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
MCP_DIR="$PROJECT_DIR/enhanced_search_mcp"

echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}MCP目录: $MCP_DIR${NC}"

# 初始化conda
echo -e "${YELLOW}🐍 初始化conda环境...${NC}"
source ~/miniconda3/etc/profile.d/conda.sh

# 激活searxng环境（使用完整路径）
echo -e "${YELLOW}🔄 激活searxng环境...${NC}"
conda activate /home/<USER>/miniconda/envs/searxng
echo -e "${GREEN}✅ 已激活searxng环境${NC}"

# 检查关键依赖
echo -e "${YELLOW}📦 检查关键依赖...${NC}"
python -c "import fastmcp" 2>/dev/null && echo -e "${GREEN}✅ fastmcp${NC}" || echo -e "${RED}❌ fastmcp${NC}"
python -c "import fastapi" 2>/dev/null && echo -e "${GREEN}✅ fastapi${NC}" || echo -e "${RED}❌ fastapi${NC}"
python -c "import uvicorn" 2>/dev/null && echo -e "${GREEN}✅ uvicorn${NC}" || echo -e "${RED}❌ uvicorn${NC}"
python -c "import aiohttp" 2>/dev/null && echo -e "${GREEN}✅ aiohttp${NC}" || echo -e "${RED}❌ aiohttp${NC}"

# 检查端口占用并清理
cleanup_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  清理端口 $port ($service)...${NC}"
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

cleanup_port 8888 "SearXNG"
cleanup_port 8881 "MCP服务器"

# 创建日志目录
mkdir -p "$PROJECT_DIR/logs"
mkdir -p "$MCP_DIR/logs"

# 设置环境变量
export SEARXNG_URL="http://localhost:8888"
export MCP_HOST="0.0.0.0"
export MCP_PORT="8881"
export LOG_LEVEL="INFO"

# 启动SearXNG
echo -e "${CYAN}🔍 启动SearXNG...${NC}"
cd "$PROJECT_DIR"

# 检查SearXNG是否可以启动
if [ -f "searx/webapp.py" ] || [ -f "searx/__init__.py" ]; then
    echo -e "${GREEN}✅ SearXNG文件存在${NC}"
    
    # 启动SearXNG (后台)
    nohup python -m searx.webapp > logs/searxng.log 2>&1 &
    SEARXNG_PID=$!
    echo -e "${GREEN}✅ SearXNG已启动 (PID: $SEARXNG_PID)${NC}"
    
    # 等待SearXNG启动
    echo -e "${YELLOW}⏳ 等待SearXNG启动...${NC}"
    for i in {1..20}; do
        if curl -s --connect-timeout 2 http://localhost:8888 > /dev/null 2>&1; then
            echo -e "${GREEN}✅ SearXNG启动成功${NC}"
            break
        fi
        sleep 2
        echo -n "."
    done
else
    echo -e "${YELLOW}⚠️  SearXNG文件不存在，跳过SearXNG启动${NC}"
    echo -e "${YELLOW}💡 MCP服务器将直接连接到现有的SearXNG实例${NC}"
fi

# 启动MCP服务器
echo -e "${CYAN}🤖 启动Enhanced Search MCP服务器...${NC}"
cd "$MCP_DIR"

# 检查必要文件
if [ -f "server.py" ] && [ -f "enhanced_search_engine.py" ]; then
    echo -e "${GREEN}✅ MCP服务器文件存在${NC}"
    
    # 启动MCP服务器 (后台)
    nohup python server.py > logs/mcp_server.log 2>&1 &
    MCP_PID=$!
    echo -e "${GREEN}✅ MCP服务器已启动 (PID: $MCP_PID)${NC}"
    
    # 等待MCP服务器启动
    echo -e "${YELLOW}⏳ 等待MCP服务器启动...${NC}"
    for i in {1..15}; do
        if curl -s --connect-timeout 2 http://localhost:8881/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ MCP服务器启动成功${NC}"
            break
        fi
        sleep 2
        echo -n "."
    done
else
    echo -e "${RED}❌ MCP服务器文件缺失${NC}"
    exit 1
fi

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🛑 正在停止服务...${NC}"
    
    if [ ! -z "$SEARXNG_PID" ]; then
        kill $SEARXNG_PID 2>/dev/null || true
        echo -e "${GREEN}✅ SearXNG已停止${NC}"
    fi
    
    if [ ! -z "$MCP_PID" ]; then
        kill $MCP_PID 2>/dev/null || true
        echo -e "${GREEN}✅ MCP服务器已停止${NC}"
    fi
    
    # 强制清理端口
    lsof -ti:8888 | xargs kill -9 2>/dev/null || true
    lsof -ti:8881 | xargs kill -9 2>/dev/null || true
    
    echo -e "${CYAN}👋 服务已全部停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 显示服务状态
echo -e "\n${CYAN}🎉 服务启动完成！${NC}"
echo -e "${CYAN}==================${NC}"
echo -e "${GREEN}🔍 SearXNG搜索引擎:${NC}"
echo -e "   📡 Web界面: http://localhost:8888"
echo -e "   📊 API端点: http://localhost:8888/search"
echo -e ""
echo -e "${GREEN}🤖 Enhanced Search MCP服务器:${NC}"
echo -e "   📡 SSE端点: http://localhost:8881/mcp/sse"
echo -e "   🏥 健康检查: http://localhost:8881/health"
echo -e "   🛠️  工具列表: http://localhost:8881/tools"
echo -e ""
echo -e "${BLUE}📋 Agent-Zero MCP配置:${NC}"
echo -e '   {
     "name": "enhanced-search",
     "type": "sse", 
     "url": "http://localhost:8881/mcp/sse",
     "description": "增强搜索引擎MCP服务器"
   }'
echo -e ""
echo -e "${YELLOW}📝 日志文件:${NC}"
echo -e "   SearXNG: $PROJECT_DIR/logs/searxng.log"
echo -e "   MCP服务器: $MCP_DIR/logs/mcp_server.log"
echo -e ""
echo -e "${YELLOW}💡 测试命令:${NC}"
echo -e "   curl http://localhost:8888"
echo -e "   curl http://localhost:8881/health"
echo -e ""
echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"

# 保持脚本运行并监控服务
while true; do
    sleep 10
    
    # 检查MCP服务器状态
    if [ ! -z "$MCP_PID" ] && ! kill -0 $MCP_PID 2>/dev/null; then
        echo -e "${RED}❌ MCP服务器进程已停止${NC}"
        cleanup
        exit 1
    fi
    
    # 检查SearXNG状态（如果启动了的话）
    if [ ! -z "$SEARXNG_PID" ] && ! kill -0 $SEARXNG_PID 2>/dev/null; then
        echo -e "${RED}❌ SearXNG进程已停止${NC}"
        cleanup
        exit 1
    fi
done
