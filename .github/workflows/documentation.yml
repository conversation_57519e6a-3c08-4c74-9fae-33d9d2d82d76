---
name: Documentation

# yamllint disable-line rule:truthy
on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

permissions:
  contents: read

env:
  PYTHON_VERSION: "3.13"

jobs:
  release:
    if: github.repository_owner == 'searxng' || github.event_name == 'workflow_dispatch'
    name: Release
    runs-on: ubuntu-24.04-arm
    permissions:
      # for JamesIves/github-pages-deploy-action to push
      contents: write

    steps:
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "${{ env.PYTHON_VERSION }}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"
          fetch-depth: "0"

      - name: Setup cache Python
        uses: actions/cache@v4
        with:
          key: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-${{ hashFiles('./requirements*.txt') }}"
          restore-keys: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-"
          path: "./local/"

      - name: Setup venv
        run: make V=1 install

      - name: Build documentation
        run: make V=1 docs.clean docs.html

      - if: github.ref_name == 'master'
        name: Release
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: "dist/docs"
          branch: "gh-pages"
          commit-message: "[doc] build from commit ${{ github.sha }}"
          # Automatically remove deleted files from the deploy branch
          clean: "true"
          single-commit: "true"
