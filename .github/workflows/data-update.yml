---
name: Update searx.data

# yamllint disable-line rule:truthy
on:
  workflow_dispatch:
  schedule:
    - cron: "59 23 28 * *"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

permissions:
  contents: read

env:
  PYTHON_VERSION: "3.13"

jobs:
  data:
    if: github.repository_owner == 'searxng'
    name: ${{ matrix.fetch }}
    runs-on: ubuntu-24.04-arm
    strategy:
      fail-fast: false
      matrix:
        fetch:
          - update_ahmia_blacklist.py
          - update_currencies.py
          - update_external_bangs.py
          - update_firefox_version.py
          - update_engine_traits.py
          - update_wikidata_units.py
          - update_engine_descriptions.py

    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "${{ env.PYTHON_VERSION }}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - name: Setup cache Python
        uses: actions/cache@v4
        with:
          key: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-${{ hashFiles('./requirements*.txt') }}"
          restore-keys: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-"
          path: "./local/"

      - name: Setup venv
        run: make V=1 install

      - name: Fetch data
        run: V=1 ./manage pyenv.cmd python "./searxng_extra/update/${{ matrix.fetch }}"

      - name: Create PR
        id: cpr
        uses: peter-evans/create-pull-request@v7
        with:
          author: "searxng-bot <<EMAIL>>"
          committer: "searxng-bot <<EMAIL>>"
          title: "[data] update searx.data - ${{ matrix.fetch }}"
          commit-message: "[data] update searx.data - ${{ matrix.fetch }}"
          branch: "update_data_${{ matrix.fetch }}"
          delete-branch: "true"
          draft: "false"
          signoff: "false"
          body: |
            [data] update searx.data - ${{ matrix.fetch }}
          labels: |
            data

      - name: Display information
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"
