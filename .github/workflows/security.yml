---
name: Security

# yamllint disable-line rule:truthy
on:
  workflow_dispatch:
  schedule:
    - cron: "42 05 * * *"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  container:
    if: github.repository_owner == 'searxng'
    name: Container
    runs-on: ubuntu-24.04-arm
    permissions:
      security-events: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - name: Run Trivy scanner
        uses: aquasecurity/trivy-action@0.31.0
        with:
          image-ref: "ghcr.io/searxng/searxng:latest"
          vuln-type: "os,library"
          severity: "UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL"
          ignore-unfixed: "false"
          format: "sarif"
          output: "./trivy-results.sarif"

      - name: Upload SARIFs
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: "./trivy-results.sarif"
