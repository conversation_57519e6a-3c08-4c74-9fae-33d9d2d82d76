---
name: Container

# yamllint disable-line rule:truthy
on:
  workflow_dispatch:
  workflow_run:
    workflows:
      - Integration
    types:
      - completed
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

permissions:
  contents: read
  # Organization GHCR
  packages: read

env:
  PYTHON_VERSION: "3.13"

jobs:
  build-base:
    if: |
      (github.repository_owner == 'searxng' && github.event.workflow_run.conclusion == 'success')
      || github.event_name == 'workflow_dispatch'
    name: Build base
    runs-on: ubuntu-24.04
    permissions:
      # Organization GHCR
      packages: write

    steps:
      - if: github.repository_owner == 'searxng'
        name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - if: github.repository_owner == 'searxng'
        name: Get date
        id: date
        run: echo "date=$(date +'%Y%m%d')" >>$GITHUB_OUTPUT

      - if: github.repository_owner == 'searxng'
        name: Check cache apko
        id: cache-apko
        uses: actions/cache/restore@v4
        with:
          # yamllint disable-line rule:line-length
          key: "apko-${{ steps.date.outputs.date }}-${{ hashFiles('./container/base.yml', './container/base-builder.yml') }}"
          path: "/tmp/.apko/"
          lookup-only: true

      - if: github.repository_owner == 'searxng' && steps.cache-apko.outputs.cache-hit != 'true'
        name: Setup cache apko
        uses: actions/cache@v4
        with:
          # yamllint disable-line rule:line-length
          key: "apko-${{ steps.date.outputs.date }}-${{ hashFiles('./container/base.yml', './container/base-builder.yml') }}"
          restore-keys: "apko-${{ steps.date.outputs.date }}-"
          path: "/tmp/.apko/"

      - if: github.repository_owner == 'searxng' && steps.cache-apko.outputs.cache-hit != 'true'
        name: Setup apko
        run: |
          eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
          brew install apko

      - if: github.repository_owner == 'searxng' && steps.cache-apko.outputs.cache-hit != 'true'
        name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - if: github.repository_owner == 'searxng' && steps.cache-apko.outputs.cache-hit != 'true'
        name: Build
        run: |
          eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"

          apko publish ./container/base.yml ghcr.io/${{ github.repository_owner }}/base:searxng \
            --cache-dir=/tmp/.apko/ \
            --sbom=false \
            --vcs=false \
            --log-level=debug

          apko publish ./container/base-builder.yml ghcr.io/${{ github.repository_owner }}/base:searxng-builder \
            --cache-dir=/tmp/.apko/ \
            --sbom=false \
            --vcs=false \
            --log-level=debug

  build:
    if: github.repository_owner == 'searxng' || github.event_name == 'workflow_dispatch'
    name: Build (${{ matrix.arch }})
    runs-on: ${{ matrix.os }}
    needs: build-base
    strategy:
      fail-fast: false
      matrix:
        include:
          - arch: amd64
            os: ubuntu-24.04
            emulation: false
          - arch: arm64
            os: ubuntu-24.04-arm
            emulation: false
          - arch: armv7
            os: ubuntu-24.04-arm
            emulation: true

    permissions:
      # Organization GHCR
      packages: write

    outputs:
      version_string: ${{ steps.build.outputs.version_string }}
      version_tag: ${{ steps.build.outputs.version_tag }}
      docker_tag: ${{ steps.build.outputs.docker_tag }}
      git_url: ${{ steps.build.outputs.git_url }}
      git_branch: ${{ steps.build.outputs.git_branch }}

    steps:
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "${{ env.PYTHON_VERSION }}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - name: Setup cache Python
        uses: actions/cache@v4
        with:
          key: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-${{ hashFiles('./requirements*.txt') }}"
          restore-keys: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-"
          path: "./local/"

      - name: Setup cache container mounts
        uses: actions/cache@v4
        with:
          # yamllint disable-line rule:line-length
          key: "container-mounts-${{ matrix.arch }}-${{ hashFiles('./container/Dockerfile') }}"
          restore-keys: "container-mounts-${{ matrix.arch }}-"
          path: |
            /var/tmp/buildah-cache/
            /var/tmp/buildah-cache-*/

      - if: ${{ matrix.emulation }}
        name: Setup QEMU
        uses: docker/setup-qemu-action@v3

      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: Build
        id: build
        env:
          OVERRIDE_ARCH: "${{ matrix.arch }}"
        run: make podman.build

  test:
    name: Test (${{ matrix.arch }})
    runs-on: ${{ matrix.os }}
    needs: build
    strategy:
      fail-fast: false
      matrix:
        include:
          - arch: amd64
            os: ubuntu-24.04
            emulation: false
          - arch: arm64
            os: ubuntu-24.04-arm
            emulation: false
          - arch: armv7
            os: ubuntu-24.04-arm
            emulation: true

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - if: ${{ matrix.emulation }}
        name: Setup QEMU
        uses: docker/setup-qemu-action@v3

      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: Test
        env:
          OVERRIDE_ARCH: "${{ matrix.arch }}"
          GIT_URL: "${{ needs.build.outputs.git_url }}"
        run: make container.test

  release:
    if: github.repository_owner == 'searxng' && github.ref_name == 'master'
    name: Release
    runs-on: ubuntu-24.04-arm
    needs:
      - build
      - test

    permissions:
      # Organization GHCR
      packages: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: "ghcr.io"
          username: "${{ github.repository_owner }}"
          password: "${{ secrets.GITHUB_TOKEN }}"

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          registry: "docker.io"
          username: "${{ secrets.DOCKERHUB_USERNAME }}"
          password: "${{ secrets.DOCKERHUB_TOKEN }}"

      - name: Release
        env:
          GIT_URL: "${{ needs.build.outputs.git_url }}"
          DOCKER_TAG: "${{ needs.build.outputs.docker_tag }}"
        run: make container.push
