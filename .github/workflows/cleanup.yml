---
name: Cleanup

# yamllint disable-line rule:truthy
on:
  workflow_dispatch:
  schedule:
    - cron: "4 4 * * *"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  registry:
    # FIXME: On forks it fails with "Failed to fetch packages: missing field `id` at line 1 column 141"
    if: github.repository_owner == 'searxng' || github.event_name == 'workflow_dispatch'
    name: Registry
    runs-on: ubuntu-24.04
    permissions:
      # Organization GHCR
      packages: write

    steps:
      - name: <PERSON><PERSON><PERSON>
        uses: snok/container-retention-policy@v3.0.0
        with:
          account: "${{ github.repository_owner }}"
          token: "${{ secrets.GITHUB_TOKEN }}"
          # Remove only cache images https://github.com/snok/container-retention-policy/issues/97
          image-names: "cache"
          image-tags: "!searxng*"
          cut-off: "1d"
          keep-n-most-recent: "100"
