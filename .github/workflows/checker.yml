---
name: Checker

# yamllint disable-line rule:truthy
on:
  workflow_dispatch:
  schedule:
    - cron: "0 4 * * 5"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

permissions:
  contents: read

env:
  PYTHON_VERSION: "3.13"

jobs:
  search:
    if: github.repository_owner == 'searxng' || github.event_name == 'workflow_dispatch'
    name: Search
    runs-on: ubuntu-24.04-arm
    steps:
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "${{ env.PYTHON_VERSION }}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - name: Setup cache Python
        uses: actions/cache@v4
        with:
          key: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-${{ hashFiles('./requirements*.txt') }}"
          restore-keys: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-"
          path: "./local"

      - name: Setup venv
        run: make V=1 install

      - name: Search checker
        run: make search.checker
