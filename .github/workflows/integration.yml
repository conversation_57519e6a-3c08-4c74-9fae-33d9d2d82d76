---
name: Integration

# yamllint disable-line rule:truthy
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: false

permissions:
  contents: read

env:
  PYTHON_VERSION: "3.13"

jobs:
  test:
    name: Python ${{ matrix.python-version }}
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        python-version:
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
          - "3.13"

    steps:
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "${{ matrix.python-version }}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - name: Setup cache Python
        uses: actions/cache@v4
        with:
          key: "python-${{ matrix.python-version }}-${{ runner.arch }}-${{ hashFiles('./requirements*.txt') }}"
          restore-keys: "python-${{ matrix.python-version }}-${{ runner.arch }}-"
          path: "./local/"

      - name: Setup venv
        run: make V=1 install

      - name: Run tests
        run: make V=1 ci.test

  theme:
    name: Theme
    runs-on: ubuntu-24.04-arm
    steps:
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "${{ env.PYTHON_VERSION }}"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: "false"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: "./.nvmrc"

      - name: Setup cache Node.js
        uses: actions/cache@v4
        with:
          key: "nodejs-${{ runner.arch }}-${{ hashFiles('./.nvmrc', './package.json') }}"
          path: "./client/simple/node_modules/"

      - name: Setup cache Python
        uses: actions/cache@v4
        with:
          key: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-${{ hashFiles('./requirements*.txt') }}"
          restore-keys: "python-${{ env.PYTHON_VERSION }}-${{ runner.arch }}-"
          path: "./local/"

      - name: Setup venv
        run: make V=1 install

      - name: Build
        run: make themes.all
