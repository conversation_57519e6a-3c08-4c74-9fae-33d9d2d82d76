# https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file
version: 2
updates:
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "friday"
    open-pull-requests-limit: 5
    target-branch: "master"
    commit-message:
      prefix: "[upd] pypi:"
    groups:
      minor:
        applies-to: version-updates
        update-types:
          - "minor"
          - "patch"

  - package-ecosystem: "npm"
    directory: "/client/simple"
    schedule:
      interval: "weekly"
      day: "friday"
    open-pull-requests-limit: 5
    target-branch: "master"
    commit-message:
      prefix: "[upd] web-client (simple):"
    groups:
      minor:
        applies-to: version-updates
        update-types:
          - "minor"
          - "patch"

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "friday"
    target-branch: "master"
    commit-message:
      prefix: "[upd] docker:"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "friday"
    target-branch: "master"
    commit-message:
      prefix: "[upd] github-actions:"
