---
name: Engine request
about: Request a new engine in SearXNG
title: ''
labels: enhancement, engine request
assignees: ''

---
<!-- PLEASE FILL THESE FIELDS, IT REALLY HELPS THE MAINTAINERS OF SearXNG -->

**Working URL to the engine**
<!-- Please check if the engine is responding correctly before submitting it. -->

**Why do you want to add this engine?**
<!-- What's special about this engine? Is it open source or libre? -->

**Features of this engine**
<!-- Features of this engine: Doesn't track its users, fast, easy to integrate, ... -->

**How can SearXNG fetch the information from this engine?**
<!-- List API URL, example code (using the correct markdown) and more
that could be useful for the developers in order to implement this engine.
If you don't know what to write, let this part blank. -->

**Applicable category of this engine**
<!-- Where should this new engine fit in SearXNG? Current categories in SearXNG:
general, files, images, it, map, music, news, science, social media and videos.
You can add multiple categories at the same time. -->

**Additional context**
<!-- Add any other context about this engine here. -->
