#!/bin/bash

# Enhanced Search MCP服务器 v2.0 测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🧪 测试Enhanced Search MCP服务器 v2.0${NC}"
echo -e "${CYAN}=======================================${NC}"

# 测试MCP端点 (Streamable HTTP)
echo -e "${YELLOW}📡 测试MCP端点 (Streamable HTTP)...${NC}"
if curl -s --connect-timeout 5 -X POST http://localhost:8881/mcp \
    -H "Content-Type: application/json" \
    -H "Accept: application/json, text/event-stream" \
    -d '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2025-06-18", "capabilities": {}, "clientInfo": {"name": "test-client", "version": "1.0.0"}}}' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ MCP端点可访问 (支持Streamable HTTP)${NC}"
else
    echo -e "${RED}❌ MCP端点不可访问${NC}"
    exit 1
fi

echo ""

# 测试工具列表
echo -e "${YELLOW}🛠️  测试工具列表...${NC}"
if curl -s --connect-timeout 5 -X POST http://localhost:8881/mcp \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"jsonrpc": "2.0", "id": 2, "method": "tools/list", "params": {}}' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 工具列表端点可访问${NC}"
    echo -e "${BLUE}可用工具:${NC}"
    curl -s -X POST http://localhost:8881/mcp \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{"jsonrpc": "2.0", "id": 2, "method": "tools/list", "params": {}}' | python -m json.tool
else
    echo -e "${RED}❌ 工具列表端点不可访问${NC}"
fi

echo ""

# 测试搜索功能
echo -e "${YELLOW}🔍 测试搜索功能...${NC}"
if curl -s --connect-timeout 10 -X POST http://localhost:8881/mcp \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"jsonrpc": "2.0", "id": 3, "method": "tools/call", "params": {"name": "enhanced_search", "arguments": {"query": "Python编程", "search_depth": "basic", "max_results": 5}}}' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 搜索功能测试成功${NC}"
    echo -e "${BLUE}搜索结果示例:${NC}"
    curl -s -X POST http://localhost:8881/mcp \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d '{"jsonrpc": "2.0", "id": 3, "method": "tools/call", "params": {"name": "enhanced_search", "arguments": {"query": "Python编程", "search_depth": "basic", "max_results": 3}}}' | python -c "
import json, sys
try:
    data = json.load(sys.stdin)
    if 'result' in data and 'content' in data['result']:
        content = data['result']['content']
        if len(content) > 500:
            print(content[:500] + '...')
        else:
            print(content)
    else:
        print(json.dumps(data, indent=2, ensure_ascii=False))
except:
    print('解析响应失败')
"
else
    echo -e "${RED}❌ 搜索功能测试失败${NC}"
fi

echo ""

# 测试统计信息
echo -e "${YELLOW}📊 测试统计信息...${NC}"
if curl -s --connect-timeout 5 -X POST http://localhost:8881/mcp \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d '{"jsonrpc": "2.0", "id": 4, "method": "tools/call", "params": {"name": "search_stats", "arguments": {}}}' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 统计信息功能可用${NC}"
else
    echo -e "${YELLOW}⚠️  统计信息功能可能需要初始化${NC}"
fi

echo ""
echo -e "${CYAN}🎉 测试完成！${NC}"
echo -e "${CYAN}============${NC}"
echo -e "${GREEN}📡 MCP端点: http://localhost:8881/mcp${NC}"
echo -e "${GREEN}🔄 传输协议: Streamable HTTP (MCP 2025-06-18)${NC}"
echo -e "${GREEN}✨ FastMCP版本: 2.11.0+${NC}"
echo ""
echo -e "${BLUE}📋 Agent-Zero MCP配置 (更新版):${NC}"
echo -e '   {
     "name": "enhanced-search-v2",
     "type": "http", 
     "url": "http://localhost:8881/mcp",
     "description": "增强搜索引擎MCP服务器 v2.0 - 支持Streamable HTTP"
   }'
echo ""
echo -e "${BLUE}📋 Claude Desktop MCP配置:${NC}"
echo -e '   {
     "mcpServers": {
       "enhanced-search": {
         "url": "http://localhost:8881/mcp"
       }
     }
   }'
