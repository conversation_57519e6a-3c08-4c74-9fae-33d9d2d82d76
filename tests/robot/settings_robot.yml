general:
  debug: false
  instance_name: "searx_test"

brand:
  git_url: https://github.com/searxng/searxng
  git_branch: master
  issue_url: https://github.com/searxng/searxng/issues
  new_issue_url: https://github.com/searxng/searxng/issues/new
  docs_url: https://docs.searxng.org
  public_instances: https://searx.space
  wiki_url: https://github.com/searxng/searxng/wiki

search:
  language: "all"

server:
  port: 11111
  bind_address: 127.0.0.1
  secret_key: "changedultrasecretkey"
  base_url: false
  http_protocol_version: "1.0"

ui:
  static_path: ""
  templates_path: ""
  default_theme: simple

preferences:
  lock: []

outgoing:
  request_timeout: 1.0  # seconds
  useragent_suffix: ""

categories_as_tabs:
  general:
  dummy:

engines:
  - name: general dummy
    engine: dummy
    categories: general
    shortcut: gd

  - name: dummy dummy
    engine: dummy
    categories: dummy
    shortcut: dd

doi_resolvers:
  oadoi.org: 'https://oadoi.org/'
  doi.org: 'https://doi.org/'
  doai.io: 'https://dissem.in/'
  sci-hub.se: 'https://sci-hub.se/'
  sci-hub.st: 'https://sci-hub.st/'
  sci-hub.ru: 'https://sci-hub.ru/'

default_doi_resolver: 'oadoi.org'
