# This SearXNG setup is used in unit tests

use_default_settings:

  engines:
    # remove all engines
    keep_only: []

search:

  formats: [html, csv, json, rss]

server:

  secret_key: "user_secret_key"

engines:

  - name: dummy engine
    engine: demo_offline
    categories: ["general"]
    shortcut: "gd"
    timeout: 3

  - name: dummy private engine
    engine: demo_offline
    categories: ["general"]
    shortcut: "gdp"
    timeout: 3
    tokens: ["my-token"]
