.loader,
.loader::after {
  border-radius: 50%;
  width: 10em;
  height: 10em;
}

.loader {
  margin: 60px auto;
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 1.1em solid var(--color-loading-indicator);
  border-right: 1.1em solid var(--color-loading-indicator);
  border-bottom: 1.1em solid var(--color-loading-indicator);
  border-left: 1.1em solid var(--color-loading-indicator-gap);
  transform: translateZ(0);
  animation: load8 1.1s infinite linear;
}

@keyframes load8 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
