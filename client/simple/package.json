{"name": "simple", "version": "1.0.0", "type": "module", "scripts": {"clean": "rm -Rf node_modules", "build": "node theme_icons.js && vite build", "fix": "eslint --fix && stylelint --fix strict 'src/**/*.{css,scss,sass,less,styl,vue,svelte}'", "icons.html": "node theme_icons.js"}, "devDependencies": {"@eslint/js": "^9.28.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "edge.js": "^6.2.1", "eslint": "^9.28.0", "filemanager-webpack-plugin": "^8.0.0", "globals": "^16.2.0", "ionicons": "^8.0.9", "leaflet": "^1.9.4", "less": "^4.3.0", "less-loader": "^12.3.0", "normalize.css": "^8.0.1", "sharp": "^0.34.2", "style-loader": "^4.0.0", "stylelint": "^16.20.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-less": "^3.0.1", "stylelint-prettier": "^5.0.3", "svgo": "^3.3.2", "swiped-events": "^1.2.0", "vite": "^6.3.5", "vite-plugin-static-copy": "^3.0.0", "vite-plugin-stylelint": "^6.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}