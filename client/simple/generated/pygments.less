/*
   this file is generated automatically by searxng_extra/update/update_pygments.py
   using pygments version 2.19.1
*/


.code-highlight {

  pre { line-height: 100%; }
  td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
  span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
  td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
  span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
  .hll { background-color: #ffffcc }
  .c { color: #3D7B7B; font-style: italic } /* Comment */
  .err { border: 1px solid #F00 } /* Error */
  .k { color: #008000; font-weight: bold } /* Keyword */
  .o { color: #666 } /* Operator */
  .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */
  .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */
  .cp { color: #9C6500 } /* Comment.Preproc */
  .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */
  .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */
  .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */
  .gd { color: #A00000 } /* Generic.Deleted */
  .ge { font-style: italic } /* Generic.Emph */
  .ges { font-weight: bold; font-style: italic } /* Generic.EmphStrong */
  .gr { color: #E40000 } /* Generic.Error */
  .gh { color: #000080; font-weight: bold } /* Generic.Heading */
  .gi { color: #008400 } /* Generic.Inserted */
  .go { color: #717171 } /* Generic.Output */
  .gp { color: #000080; font-weight: bold } /* Generic.Prompt */
  .gs { font-weight: bold } /* Generic.Strong */
  .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
  .gt { color: #04D } /* Generic.Traceback */
  .kc { color: #008000; font-weight: bold } /* Keyword.Constant */
  .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */
  .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */
  .kp { color: #008000 } /* Keyword.Pseudo */
  .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */
  .kt { color: #B00040 } /* Keyword.Type */
  .m { color: #666 } /* Literal.Number */
  .s { color: #BA2121 } /* Literal.String */
  .na { color: #687822 } /* Name.Attribute */
  .nb { color: #008000 } /* Name.Builtin */
  .nc { color: #00F; font-weight: bold } /* Name.Class */
  .no { color: #800 } /* Name.Constant */
  .nd { color: #A2F } /* Name.Decorator */
  .ni { color: #717171; font-weight: bold } /* Name.Entity */
  .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */
  .nf { color: #00F } /* Name.Function */
  .nl { color: #767600 } /* Name.Label */
  .nn { color: #00F; font-weight: bold } /* Name.Namespace */
  .nt { color: #008000; font-weight: bold } /* Name.Tag */
  .nv { color: #19177C } /* Name.Variable */
  .ow { color: #A2F; font-weight: bold } /* Operator.Word */
  .w { color: #BBB } /* Text.Whitespace */
  .mb { color: #666 } /* Literal.Number.Bin */
  .mf { color: #666 } /* Literal.Number.Float */
  .mh { color: #666 } /* Literal.Number.Hex */
  .mi { color: #666 } /* Literal.Number.Integer */
  .mo { color: #666 } /* Literal.Number.Oct */
  .sa { color: #BA2121 } /* Literal.String.Affix */
  .sb { color: #BA2121 } /* Literal.String.Backtick */
  .sc { color: #BA2121 } /* Literal.String.Char */
  .dl { color: #BA2121 } /* Literal.String.Delimiter */
  .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */
  .s2 { color: #BA2121 } /* Literal.String.Double */
  .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */
  .sh { color: #BA2121 } /* Literal.String.Heredoc */
  .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */
  .sx { color: #008000 } /* Literal.String.Other */
  .sr { color: #A45A77 } /* Literal.String.Regex */
  .s1 { color: #BA2121 } /* Literal.String.Single */
  .ss { color: #19177C } /* Literal.String.Symbol */
  .bp { color: #008000 } /* Name.Builtin.Pseudo */
  .fm { color: #00F } /* Name.Function.Magic */
  .vc { color: #19177C } /* Name.Variable.Class */
  .vg { color: #19177C } /* Name.Variable.Global */
  .vi { color: #19177C } /* Name.Variable.Instance */
  .vm { color: #19177C } /* Name.Variable.Magic */
  .il { color: #666 } /* Literal.Number.Integer.Long */
}

.code-highlight-dark(){
  .code-highlight {

    pre { line-height: 100%; }
    td.linenos .normal { color: #3c4354; background-color: transparent; padding-left: 5px; padding-right: 5px; }
    span.linenos { color: #3c4354; background-color: transparent; padding-left: 5px; padding-right: 5px; }
    td.linenos .special { color: #3c4354; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
    span.linenos.special { color: #3c4354; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
    .hll { background-color: #6e7681 }
    .c { color: #7E8AA1 } /* Comment */
    .err { color: #F88F7F } /* Error */
    .esc { color: #D4D2C8 } /* Escape */
    .g { color: #D4D2C8 } /* Generic */
    .k { color: #FFAD66 } /* Keyword */
    .l { color: #D5FF80 } /* Literal */
    .n { color: #D4D2C8 } /* Name */
    .o { color: #FFAD66 } /* Operator */
    .x { color: #D4D2C8 } /* Other */
    .p { color: #D4D2C8 } /* Punctuation */
    .ch { color: #F88F7F; font-style: italic } /* Comment.Hashbang */
    .cm { color: #7E8AA1 } /* Comment.Multiline */
    .cp { color: #FFAD66; font-weight: bold } /* Comment.Preproc */
    .cpf { color: #7E8AA1 } /* Comment.PreprocFile */
    .c1 { color: #7E8AA1 } /* Comment.Single */
    .cs { color: #7E8AA1; font-style: italic } /* Comment.Special */
    .gd { color: #F88F7F; background-color: #3D1E20 } /* Generic.Deleted */
    .ge { color: #D4D2C8; font-style: italic } /* Generic.Emph */
    .ges { color: #D4D2C8 } /* Generic.EmphStrong */
    .gr { color: #F88F7F } /* Generic.Error */
    .gh { color: #D4D2C8 } /* Generic.Heading */
    .gi { color: #6AD4AF; background-color: #19362C } /* Generic.Inserted */
    .go { color: #7E8AA1 } /* Generic.Output */
    .gp { color: #D4D2C8 } /* Generic.Prompt */
    .gs { color: #D4D2C8; font-weight: bold } /* Generic.Strong */
    .gu { color: #D4D2C8 } /* Generic.Subheading */
    .gt { color: #F88F7F } /* Generic.Traceback */
    .kc { color: #FFAD66 } /* Keyword.Constant */
    .kd { color: #FFAD66 } /* Keyword.Declaration */
    .kn { color: #FFAD66 } /* Keyword.Namespace */
    .kp { color: #FFAD66 } /* Keyword.Pseudo */
    .kr { color: #FFAD66 } /* Keyword.Reserved */
    .kt { color: #73D0FF } /* Keyword.Type */
    .ld { color: #D5FF80 } /* Literal.Date */
    .m { color: #DFBFFF } /* Literal.Number */
    .s { color: #D5FF80 } /* Literal.String */
    .na { color: #FFD173 } /* Name.Attribute */
    .nb { color: #FFD173 } /* Name.Builtin */
    .nc { color: #73D0FF } /* Name.Class */
    .no { color: #FFD173 } /* Name.Constant */
    .nd { color: #7E8AA1; font-weight: bold; font-style: italic } /* Name.Decorator */
    .ni { color: #95E6CB } /* Name.Entity */
    .ne { color: #73D0FF } /* Name.Exception */
    .nf { color: #FFD173 } /* Name.Function */
    .nl { color: #D4D2C8 } /* Name.Label */
    .nn { color: #D4D2C8 } /* Name.Namespace */
    .nx { color: #D4D2C8 } /* Name.Other */
    .py { color: #FFD173 } /* Name.Property */
    .nt { color: #5CCFE6 } /* Name.Tag */
    .nv { color: #D4D2C8 } /* Name.Variable */
    .ow { color: #FFAD66 } /* Operator.Word */
    .pm { color: #D4D2C8 } /* Punctuation.Marker */
    .w { color: #D4D2C8 } /* Text.Whitespace */
    .mb { color: #DFBFFF } /* Literal.Number.Bin */
    .mf { color: #DFBFFF } /* Literal.Number.Float */
    .mh { color: #DFBFFF } /* Literal.Number.Hex */
    .mi { color: #DFBFFF } /* Literal.Number.Integer */
    .mo { color: #DFBFFF } /* Literal.Number.Oct */
    .sa { color: #F29E74 } /* Literal.String.Affix */
    .sb { color: #D5FF80 } /* Literal.String.Backtick */
    .sc { color: #D5FF80 } /* Literal.String.Char */
    .dl { color: #D5FF80 } /* Literal.String.Delimiter */
    .sd { color: #7E8AA1 } /* Literal.String.Doc */
    .s2 { color: #D5FF80 } /* Literal.String.Double */
    .se { color: #95E6CB } /* Literal.String.Escape */
    .sh { color: #D5FF80 } /* Literal.String.Heredoc */
    .si { color: #95E6CB } /* Literal.String.Interpol */
    .sx { color: #95E6CB } /* Literal.String.Other */
    .sr { color: #95E6CB } /* Literal.String.Regex */
    .s1 { color: #D5FF80 } /* Literal.String.Single */
    .ss { color: #DFBFFF } /* Literal.String.Symbol */
    .bp { color: #5CCFE6 } /* Name.Builtin.Pseudo */
    .fm { color: #FFD173 } /* Name.Function.Magic */
    .vc { color: #D4D2C8 } /* Name.Variable.Class */
    .vg { color: #D4D2C8 } /* Name.Variable.Global */
    .vi { color: #D4D2C8 } /* Name.Variable.Instance */
    .vm { color: #D4D2C8 } /* Name.Variable.Magic */
    .il { color: #DFBFFF } /* Literal.Number.Integer.Long */
  }
}
