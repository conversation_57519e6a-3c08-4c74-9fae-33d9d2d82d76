# Indonesian translations for PROJECT.
# Copyright (C) 2021 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2021.
# <AUTHOR> <EMAIL>, 2022, 2023, 2024.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# adriennathaniel1999
# <<EMAIL>>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-06 05:50+0000\n"
"Last-Translator: Linerly <<EMAIL>>\n"
"Language-Team: Indonesian <https://translate.codeberg.org/projects/searxng/"
"searxng/id/>\n"
"Language: id\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "tanpa penyubkelompokkan khusus"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "lainnya"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "Berkas"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "umum"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musik"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "media sosial"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "gambar"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "video"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "TI"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "berita"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "peta"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "sains"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikasi"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "kamus"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "lirik"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paket"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "tanya jawab"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repositori"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wiki perangkat lunak"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "publikasi ilmiah"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "otomatis"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "cerah"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "gelap"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "hitam"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Waktu aktif"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Tentang"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "suhu rata‐rata."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Tutupan awan"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "kondisi"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "kondisi saat ini"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Sore"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "terasa seperti"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "kelembapan"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "suhu maksimum."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "suhu minimum."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Pagi"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Malam"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Siang"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Tekanan"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Fajar"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Senja"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Suhu"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Indeks sinar UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Jarak pandang"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Angin"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Langit cerah"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Berawan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Cerah"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Berkabut"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Hujan lebat dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Hujan lebat disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Hujan lebat"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Hujan lebat"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Hujan es lebat dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Hujan es lebat disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Hujan es lebat"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Hujan es lebat"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Salju lebat dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Hujan salju lebat disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Hujan salju lebat"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Salju lebat"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Hujan ringan dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Hujan ringan disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Hujan ringan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Hujan ringan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Hujan es ringan dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Hujan es ringan disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Hujan es ringan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Hujan es ringan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Salju ringan dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Hujan salju ringan disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Hujan salju ringan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Salju ringan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Berawan sebagian"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Hujan dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Hujan disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Hujan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Hujan"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Hujan es dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Hujan es disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Hujan es"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Hujan es"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Salju dan guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Hujan salju disertai guntur"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Hujan salju"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Salju"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Pelanggan"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "Postingan"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "pengguna aktif"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "komentar"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "pengguna"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "komunitas"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "poin-poin"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "judul"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "penulis"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Buka"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Tertutup"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "dijawab"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Item tidak ditemukan"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Sumber"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Gagal memuat laman berikutnya"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Pengaturan takvalid. Mohon ubah preferensi Anda"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Pengaturan takvalid"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "galat pencarian"

#: searx/webutils.py:35
msgid "timeout"
msgstr "waktu habis"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "kesalahan penguraian"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "kesalahan protokol HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "galat jaringan"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Galat SSL: gagal memvalidasi sertifikat"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "kemogokan takterduga"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "galat HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "galat koneksi HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "galat proksi"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "terlalu banyak permintaan"

#: searx/webutils.py:58
msgid "access denied"
msgstr "akses ditolak"

#: searx/webutils.py:59
msgid "server API error"
msgstr "galat API peladen"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Ditangguhkan"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} menit yang lalu"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} jam, {minutes} menit yang lalu"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Menghasilkan nilai-nilai acak yang berbeda"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Hitung {func} dari argumen"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Tampilkan rute di peta .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (USANG)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Entri ini telah digantikan oleh"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Saluran"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "kecepatan bit"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "suara"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klik"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Bahasa"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} kutipan dari tahun {firstCitationVelocityYear} sampai "
"dengan {lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Tidak dapat membaca URL gambar. Hal ini dapat disebabkan format berkas "
"tidak didukung. TinEye hanya mendukung gambar JPEG, PNG, GIF, BMP, TIFF, "
"atau WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Gambar ini terlalu sederhana untuk dicari kecocokandengan gambar lain. "
"TinEye membutuhkan gambar dengan setidaknya detail mendasar agar "
"kecocokannya dengan gambar lain dapat terdeteksi."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Gambar ini tidak dapat diunduh."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Penilaian buku"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Kualitas berkas"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Daftar hitam Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Saringkan hasil onion yang muncul dalam daftar hitam Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Kalukator dasar"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Hitung ekspresi matematika di bilah pencarian"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Mengubah untaian (string) menjadi pilah digest (hash digest) yang berbeda."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "pilah digest"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Plugin nama hos"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "Tulis ulang nama hos, hapus atau prioritaskan hasil berdasarkan nama hos"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Penulisan ulang Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "Hindari paywall dengan mengalihkan ke versi terbuka jika tersedia"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informasi Diri"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Menampilkan IP Anda jika kueri adalah \"ip\" dan agen pengguna Anda jika "
"kueri adalah \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "IP Anda: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Agen pengguna Anda: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Plugin pemeriksaan Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Plugin ini memeriksa apakah alamat permintaan adalah node keluaran Tor, "
"dan memberi tahu pengguna jika alamat tersebut memang node keluaran Tor; "
"seperti check.torproject.org, tetapi dari SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Tidak dapat mengunduh daftar node keluar Tor"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Anda menggunakan Tor dan sepertinya Anda memiliki alamat IP eksternal"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Anda tidak menggunakan Tor dan Anda memiliki alamat IP eksternal"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Penghapus URL pelacak"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Menghapus argumen pelacak dari URL yang dikembalikan"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Plugin konverter satuan"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konversikan antarsatuan"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Laman tidak ditemukan"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Menuju %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "halaman pencarian"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Berdonasi"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferensi"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Diberdayakan oleh"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "sebuah mesin pencari meta terbuka yang menghormati privasi Anda"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Kode sumber"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Pelacak masalah"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statistika mesin"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instansi umum"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Kebijakan privasi"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Hubungi pengelola instansi"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Klik pembesar untuk melakukan pencarian"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Durasi"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Tampilan"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Penulis"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "tembolok"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Mulai membuat isu baru di GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Mohon periksa kutu pada mesin pencarian ini di GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Saya mengonfirmasi bahwa tidak ada kutu pada masalah yang saya alami"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Jika mesin pencarian yang Anda gunakan merupakan sebuah instansi publik, "
"mohon berikan URL di laporan kutu"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Laporkan sebuah masalah baru di Github yang mengandung informasi di atas"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Tanpa HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Tampilkan log galat dan kirimkan laporan keberadaan kutu"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang pada mesin ini"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang pada kategorinya"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Median"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Tes pemeriksa gagal: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Galat:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Umum"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Kategori bawaan"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Antarmuka pengguna"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privasi"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Mesin"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Mesin pencari yang saat ini digunakan"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Pencarian Khusus"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Kuki"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Jumlah hasil"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informasi"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Kembali ke laman atas"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Laman sebelumnya"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Laman berikutnya"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Tampilkan laman depan"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Cari..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "bersihkan"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "cari"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Saat ini tidak ada data yang tersedia. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nama mesin"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Skor"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Jumlah hasil"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Waktu respons"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Keandalan"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Memproses"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Peringatan"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Galat dan pengecualian"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Pengecualian"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Pesan"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Persentase"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nama berkas"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Fungsi"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kode"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Pemeriksa"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Tes gagal"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komentar"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Contoh"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definisi"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinonim"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Terasa Seperti"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Jawaban"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Unduh hasil"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Coba cari:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Pesan dari mesin pencarian"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "detik"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL pencarian"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Tersalin"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Salin"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Saran"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Bahasa pencarian"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Bahasa bawaan"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Deteksi otomatis"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Pencarian Aman"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Ketat"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Menengah"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Mati"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Rentang waktu"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Kapan saja"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Kemarin"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Minggu kemarin"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Bulan kemarin"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Tahun kemarin"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informasi!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "saat ini, tidak ada kuki yang terdefinisikan."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Maaf!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Hasil tidak ditemukan. Anda dapat:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Tidak ada hasil lagi. Anda dapat coba:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Muat ulang laman ini."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Telusuri pencarianblain atau pilih kategori lain (sebelumnya)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Ubah mesin pencarian yang ditetapkan pada preferensi:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Ganti ke instans lain:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Cari pencarian lain atau pilih kategori lain."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Kembali ke laman sebelumnya dengan menggunakan tombol laman sebelumnya."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Izinkan"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Kata kunci (kata pertama dalam kueri)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nama"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Deskripsi"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Berikut ini adalah daftar modul-penjawab instan SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Berikut ini adalah daftar plugin."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Lengkapi entri pencarian secara otomatis"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Cari hal-hal saat Anda mengetik"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Penjajaran Tengah"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Tampilkan hasil pada bagian tengah halaman (tata letak Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Daftar berikut merupakan daftar kuki berikut nilai-nilai kuki yang "
"disimpan SearXNG di komputer Anda."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Anda dapat menilai transparansi SearXNG dengan daftar tersebut."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nama kuki"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Nilai"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Cari URL preferensi-tersimpan"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Catatan: menampilkan pengaturan khusus pada URL pencarian dapat "
"mengurangi privasi dengan membocorkan data kepada situs hasil yang "
"diklik."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL untuk memulihkan preferensi Anda pada peramban lain"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"URL yang berisi preferensi Anda. URL ini dapat digunakan untuk memulihkan"
" pengaturan Anda pada perangkat yang berbeda."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Salin preferensi pilah"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Sisipkan salinan pilah preferensi (tanpa URL) untuk memulihkan"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "pilah preferensi"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital Object Identifier (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Penyelesaian Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Pilih layanan yang digunakan oleh penulisan ulang DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Tab ini tidak tersedia di antarmuka pengguna, tetapi Anda dapat mencari "
"pada mesin ini dengan memasukkan kata kunci !bang."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "aktifkan semua"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "nonaktifkan semua"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Mendukung bahasa yang dipilih"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Berat"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Waktu maksimum"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Penyelesai Favikon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Tampilkan favikon"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Pengaturan ini disimpan pada kuki Anda, sehingga kami tidak dapat "
"menyimpan data apa pun tentang Anda."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Kuki ini hanya dinyalakan demi kenyamanan Anda semata. Kuki ini tidak "
"kami gunakan untuk melacak Anda."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Simpan"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Atur ulang ke bawaan"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Kembali"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Pintasan"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "lir-Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigasikan hasil pencarian dengan pintasan (perlu JavaScript). Tekan "
"tombol \"h\" di laman utama atau laman hasil agar mendapatkan bantuan."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proksi gambar"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Memproksikan hasil gambar melalui SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Guliran takterbatas"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Secara otomatis memuat laman selanjutnya saat menggulir ke bawah laman "
"saat ini"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Anda ingin mencari dalam bahasa apa?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Pilih Deteksi Otomatis agar SearXNG dapat mendeteksi bahasa pencarian "
"Anda."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Metode HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Ubah cara pengiriman form"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Pencarian pada judul halaman"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Ketika diaktifkan, judul halaman-hasil akan menampilkan pencarian Anda. "
"Peramban Anda dapat menyimpan judul ini"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Hasil pada tab baru"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Buka tautan hasil pada tab-peramban baru"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Tapis konten"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Cari berdasarkan pilihan kategori"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Melakukan pencarian dengan segera jika satu kategori dipilih. Nonaktifkan"
" untuk memilih beberapa kategori"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Ubah tata letak SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Gaya tema"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Pilih otomatis untuk menggunakan pengaturan peramban Anda"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Token mesin"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Token akses untuk mesin pribadi"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Bahasa antarmuka"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Ubah bahasa tata letak"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Pemformatan URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Indah"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Penuh"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Hos"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Ubah pemformatan URL hasil"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repositori"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "tampilkan media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "sembunyikan media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Situs ini tidak memberikan deskripsi apa pun."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Ukuran berkas"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Tanggal"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Jenis"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolusi"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Mesin"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Tampilkan sumber"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "alamat"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "tampilkan peta"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "sembunyikan peta"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versi"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Pemelihara"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Dimutakhirkan pada"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Penanda"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Kepopuleran"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lisensi"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Proyek"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Laman beranda proyek"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Tanggal penerbitan"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Jurnal"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Penyunting"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Penerbit"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "tautan magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "berkas torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Pengumpan"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Jumlah Berkas"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "tampilkan video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "sembunyikan video"

#~ msgid "Method"
#~ msgstr "Metode"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Tab ini tidak ditampilkan untuk hasil"
#~ " pencarian tetapi Anda dapat mencari "
#~ "di mesin-mesin berikut ini melalui "
#~ "fitur bangs."

#~ msgid "Advanced settings"
#~ msgstr "Pengaturan lanjut"

#~ msgid "Close"
#~ msgstr "Tutup"

#~ msgid "Language"
#~ msgstr "Bahasa"

#~ msgid "broken"
#~ msgstr "rusak"

#~ msgid "supported"
#~ msgstr "didukung"

#~ msgid "not supported"
#~ msgstr "tidak didukung"

#~ msgid "about"
#~ msgstr "tentang"

#~ msgid "Avg."
#~ msgstr "Rata-rata"

#~ msgid "User Interface"
#~ msgstr "Antarmuka Pengguna"

#~ msgid "Choose style for this theme"
#~ msgstr "Pilih gaya untuk tema ini"

#~ msgid "Style"
#~ msgstr "Gaya"

#~ msgid "Show advanced settings"
#~ msgstr "Tampilkan pengaturan lanjut"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Tampilkan panel pengaturan lanjut di halaman utama secara default"

#~ msgid "Allow all"
#~ msgstr "Izinkan semua"

#~ msgid "Disable all"
#~ msgstr "Matikan semua"

#~ msgid "Selected language"
#~ msgstr "Bahasa yang dipilih"

#~ msgid "Query"
#~ msgstr "Pencarian"

#~ msgid "save"
#~ msgstr "simpan"

#~ msgid "back"
#~ msgstr "kembali"

#~ msgid "Links"
#~ msgstr "Tautan"

#~ msgid "RSS subscription"
#~ msgstr "Langganan RSS"

#~ msgid "Search results"
#~ msgstr "Hasil pencarian"

#~ msgid "next page"
#~ msgstr "halaman selanjutnya"

#~ msgid "previous page"
#~ msgstr "halaman sebelumnya"

#~ msgid "Start search"
#~ msgstr "Mulai mencari"

#~ msgid "Clear search"
#~ msgstr "Bersihkan pencarian"

#~ msgid "Clear"
#~ msgstr "Bersihkan"

#~ msgid "stats"
#~ msgstr "statistik"

#~ msgid "Heads up!"
#~ msgstr "Perhatian!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Sepertinya Anda menggunakan SearXNG untuk pertama kali."

#~ msgid "Well done!"
#~ msgstr "Selamat!"

#~ msgid "Settings saved successfully."
#~ msgstr "Pengaturan berhasil disimpan."

#~ msgid "Oh snap!"
#~ msgstr "Aduh!"

#~ msgid "Something went wrong."
#~ msgstr "Ada yang salah."

#~ msgid "Date"
#~ msgstr "Tanggal"

#~ msgid "Type"
#~ msgstr "Tipe"

#~ msgid "Get image"
#~ msgstr "Dapatkan gambar"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferensi"

#~ msgid "Scores per result"
#~ msgstr "Skor per hasil"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr ""
#~ "sebuah mesin pencari meta yang "
#~ "menghormati privasi, dan dapat dimodifikasi"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Tidak ada abstrak yang tersedia untuk publikasi ini."

#~ msgid "Self Informations"
#~ msgstr "Informasi Diri"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ubah bagaimana formulir dikirimkan, <a "
#~ "href=\"https://id.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Metode_permintaan\""
#~ " rel=\"external\">pelajari lebih lanjut tentang"
#~ " metode permintaan</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Plugin ini memeriksa jika alamat peminta"
#~ " adalah node keluaran TOR, dan "
#~ "memberitahukan pengguna jika iya, seperti "
#~ "check.torproject.org tetapi dari searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Daftar node keluar TOR "
#~ "(https://check.torproject.org/exit-addresses) tidak "
#~ "dapat dijangkau."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Anda menggunakan TOR. Alamat IP Anda adalah: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""
#~ "Anda tidak menggunakan TOR. Alamat IP"
#~ " Anda terlihat sebagai: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Deteksi bahasa pencarian secara otomatis"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Deteksi bahasa kueri pencarian dan ubah bahasanya secara otomatis."

#~ msgid "others"
#~ msgstr "lain-lain"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Tab ini tidak ditampilkan untuk hasil"
#~ " pencarian, tetapi Anda dapat mencari "
#~ "di mesin-mesin berikut ini melalui "
#~ "fitur bangs."

#~ msgid "Shortcut"
#~ msgstr "Pintasan"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Mesin-mesin tidak dapat mendapatkan hasil."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Mohon coba lagi nanti atau cari instansi SearXNG yang lain."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Mengalihkan ke versi terbuka dari "
#~ "publikasi jika tersedia (plugin dibutuhkan)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ubah bagaimana formulir dikirimkan, <a "
#~ "href=\"https://id.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Metode_permintaan\""
#~ " rel=\"external\">pelajari lebih lanjut tentang"
#~ " metode permintaan</a>"

#~ msgid "On"
#~ msgstr "Aktif"

#~ msgid "Off"
#~ msgstr "Nonaktif"

#~ msgid "Enabled"
#~ msgstr "Diaktifkan"

#~ msgid "Disabled"
#~ msgstr "Dinonaktifkan"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Lakukan pencarian langsung apabila sebuah "
#~ "kategori dipilih. Matikan untuk memilih "
#~ "banyak kategori. (JavaScript dibutuhkan)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Tombol pintas mirip Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navigasikan hasil pencarian dengan tombol "
#~ "pintas mirip Vim (Membutuhkan JavaScript). "
#~ "Tekan tombol \"h\" pada halaman utama"
#~ " atau halaman hasil untuk mendapatkan "
#~ "bantuan."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "kami tidak menemukan hasil apa pun. "
#~ "Mohon menggunakan pencarian lain atau "
#~ "cari dalam kategori lain."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Tulis ulang nama host hasil atau hapus hasil berdasarkan pada nama host"

#~ msgid "Bytes"
#~ msgstr "Bita"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Pengubah nama host"

#~ msgid "Error!"
#~ msgstr "Terjadi kesalahan!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Mesin-mesin tidak dapat mendapatkan hasil"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Mulai mengirimkan sebuah masalah baru di GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Penghasil nilai acak"

#~ msgid "Statistics functions"
#~ msgstr "Fungsi statistika"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Menghitung {functions} dari argumen"

#~ msgid "Get directions"
#~ msgstr "Dapatkan arah"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Menampilkan IP Anda jika pencariannya "
#~ "adalah \"ip\" dan agen pengguna Anda "
#~ "jika pencariannya mengandung \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Tidak dapat mengunduh daftar node-keluar"
#~ " Tor dari: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Anda sedang menggunakan Tor dan "
#~ "sepertinya alamat IP eksternal Anda "
#~ "adalah sebagai berikut: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Anda sedang tidak menggunakan Tor dan"
#~ " alamat IP eksternal Anda adalah "
#~ "sebagai berikut: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Kata kunci"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Menyediakan pengaturan kustom pada URL "
#~ "preferensi agar dapat digunakan untuk "
#~ "menyinkronkan preferensi pada semua perangkat."

#~ msgid "proxied"
#~ msgstr "proksi"
