# Portuguese (Brazil) translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
# C. E., 2020
# C. E., 2018
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2015
# pizzaiolo, 2016
# shizuka, 2018
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <PERSON><PERSON><PERSON><PERSON>_Helper <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>,,
# 2025.
# 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: rodgui <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) <https://translate.codeberg.org/projects/"
"searxng/searxng/pt_BR/>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "sem mais subgrupos"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "outro"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "arquivos"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "geral"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "música"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "redes sociais"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "imagens"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "vídeos"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "rádio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "TI"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "notícias"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "ciência"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplicativos"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "dicionários"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "letras"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pacotes"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "dúvidas"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repositórios"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wikis de programas"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "publicações científicas"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automático"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "claro"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "escuro"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "preto"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Tempo em execução"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Sobre"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Temperatura Média"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Nuvens cobertas"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Condição"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Condição atual"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Tarde"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Sensação térmica"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Umidade"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Temperatura Máxima"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Temperatura Mínima"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Manhã"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noite"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Meio dia"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Pressão"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Nascer do sol"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Pôr do sol"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Índice UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Visibilidade"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vento"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Céu limpo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Nublado"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Tempo bom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Neblina"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Chuva forte e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Chuva forte com trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Chuvas fortes"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Chuva forte"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Neve forte e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Fortes chuvas de granizo e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Chuvas fortes de granizo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Chuva forte de granizo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Neve pesada e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Neves pesadas e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Fortes chuvas de neve"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Neve pesada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Chuva leve e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Chuvas leves e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Chuvas leve"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Chuva leve"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Granizo leve e trovão"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Chuvas leves de granizo e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Chuvas leve de granizo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Neve fraca"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Neve fraca e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Chuvas fraca de neve e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Chuvas leve de neve"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Neve suave"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Parcialmente nublado"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Chuva e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Chuvas e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Chuvas"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Chuva"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Granizo e trovão"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Chuvas de granizo e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Chuvas de granizo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Granizo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Neve e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Chuvas de neve e trovões"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Chuva de neves"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Neve"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Inscritos"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "publicações"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "usuários ativos"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "comentários"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "usuário"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "comunidade"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "pontos"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "título"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Abrir"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Fechado"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "respondido"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nenhum item encontrado"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Fonte"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Erro ao carregar a próxima página"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Configurações inválidas, por favor, edite suas preferências"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Configurações inválidas"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "erro de busca"

#: searx/webutils.py:35
msgid "timeout"
msgstr "tempo esgotado"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "erro de leitura"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "erro de protocolo HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "erro de rede"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Erro de SSL: validação de certificado falhou"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "falha inesperada"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "erro HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "erro de conexão HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "erro de proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "muitas solicitações"

#: searx/webutils.py:58
msgid "access denied"
msgstr "acesso negado"

#: searx/webutils.py:59
msgid "server API error"
msgstr "erro de API do servidor"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspenso"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minuto(s) atrás"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} hora(s), {minutes} minuto(s) atrás"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Gerar diferentes valores aleatórios"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Calcular {func} dos argumentos"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Exibe rota no mapa .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLETO)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Esta entrada foi substituída por"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Canal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "fluxo de transferência de bits"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "vótos"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "cliques"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Idioma"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citações do ano {firstCitationVelocityYear} até "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Não foi possível fazer a leitura desta URL. Isso pode ter ocorrido devido"
" a um formato de arquivo não suportado. Apenas os seguintes tipos de "
"imagem são suportados pelo TinEye: JPEG, PNG, GIF, BMP, TIFF ou WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Esta imagem é simples demais para achar outras correspondências. TinEye "
"necessita de um nível básico de detalhe visual para identificar as "
"correspondências."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Essa imagem não pôde ser baixada."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Avaliação de livro"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Qualidade do arquivo"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Lista de bloqueios do Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrar resultados onion que aparecem na lista de bloqueios do Ahmia"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Calculadora Básica"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Calcular expressões matemáticas pela caixa de pesquisa"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin da hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Converte as sequências em diferentes resultados de hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "resultado de hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Plugin de Hostnames"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Reescrita de hostnames, remova resultados ou priorize-os com base no "
"hostname"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Reescrita DOI de acesso aberto"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Evita \"paywalls\" ao redirecionar para versões de acesso livre de "
"publicações, quando possível"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Autoinformação"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Mostra seu IP se a consulta for \"ip\" e seu agente de usuário se a "
"consulta for \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Seu IP é: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Seu agente de usuário é: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Plugin de verificação Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Este plugin verifica se o endereço do pedido vem de um nó de saída do Tor"
" e informa ao usuário se sim; é semelhante ao check.torproject.org, mas "
"para o SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Não foi possível baixar a lista de nós de saída do Tor de"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Você está usando o Tor e parece que seu endereço de IP externo é"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Você não está usando o Tor e seu endereço de IP externo é"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Removedor de rastreador da URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Remover os argumentos de rastreio da URL recebida"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Plugin conversor de unidades"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Converter entre unidades"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Página não encontrada"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Ir a %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "página de busca"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Doar"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferências"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Distribuído por"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "um mecanismo de metapesquisa aberto e que respeita a privacidade"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Código fonte"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Rastreador de problemas"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Estatísticas de busca"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instâncias públicas"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Política de Privacidade"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Contatar o responsável da instância"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Clique na lupa para realizar a busca"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Duração"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Visualizações"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "em cache"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Submeta um novo problema no Github"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Por favor, cheque bugs existentes sobre essa engine no GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Eu confirmo que não há nenhum bug existente sobre o problema que eu "
"encontrei"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Se essa for uma instância pública, por favor, especifique a URL no "
"relatório do bug"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Envie um novo problema no Github incluindo as informações acima"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Sem HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Ver o registros de erros e enviar um relatório"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang para esse serviço"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang para essa categoria"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediana"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Teste(s) de verificador falhou: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Erros:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Geral"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Categorias padrão"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interface de usuário"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privacidade"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Motores de pesquisa"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Serviço de busca em uso"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Consultas especiais"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Número de resultados"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informações"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "de volta ao topo"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Página anterior"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Próxima página"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Mostrar a página inicial"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Buscar por..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "limpar"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "buscar"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Atualmente, não há dados disponíveis. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nome do motor"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Pontuações"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Contagem de resultados"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Tempo de resposta"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Consistência"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Processando"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Avisos"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Erros e exceções"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Exceção"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mensagem"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Porcentagem"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parâmetro"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nome do arquivo"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Função"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Código"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Verificador"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "O teste falhou"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Comentário(s)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Exemplos"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definições"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinônimos"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Sensação térmica"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Respostas"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Resultados de download"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Tente pesquisar por:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Mensagens dos sítios web de busca"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Buscar URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Copiado"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copiar"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Sugestões"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Idioma de busca"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Idioma padrão"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Auto-detectar"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Busca Segura"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Rigoroso"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderado"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Nenhum"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Intervalo de tempo"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "A qualquer momento"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Ontem"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Semana passada"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Mês passado"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Ano passado"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informação!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "atualmente, não há cookies definidos."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Desculpe!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Nenhum resultado foi encontrado. Você pode tentar:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Sem mais resultados, você pode tentar:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Atualize a página."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Pesquise outro termo ou selecione uma categoria diferente (acima)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Modifique o mecanismo de busca utilizado nas preferências:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Mude para outra instância:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Pesquise outro termo ou selecione uma categoria diferente."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Volte para a página anterior usando o botão 'página anterior'."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Permitir"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Palavras chave (primeira palavra da consulta)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nome"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Descrição"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Esta é a lista de módulos de resposta instantânea do SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Esta é a lista de plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Autocompletar"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Exibir sugestões enquanto você digita"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Alinhamento central"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Exibe os resultados no centro da página (layout Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Esta é a lista de cookies e seus valores que o SearXNG armazena em seu "
"computador."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Com essa lista, você pode avaliar a transparência do SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nome do cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valor"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "A URL de Pesquisa das configurações atuais salvas"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Observe: ao especificar configurações personalizadas na URL de pesquisa "
"você pode reduzir a privacidade vazando dados para os sites clicados nos "
"resultados."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL para restaurar suas preferências em outro navegador"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Uma URL contendo suas preferências. Esta URL pode ser usada para "
"restaurar suas configurações em outro dispositivo."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copiar hash de preferências"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Insira hash de preferência copiado (sem URL) para restaurar"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash's de preferência"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Identificador de Objeto Digital (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Resolvedor DOI de Acesso Aberto"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Selecione o serviço utilizado pelo DOI rewrite"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Essa aba não existe na interface de usuário, mas você pode pesquisar "
"nessas ferramentas/motores pelos seus !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Habilitar tudo"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Desabilitar tudo"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Suporta o idioma selecionado"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Peso"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Tempo máximo"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Resolvedor de Favicons"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Exibir favicons próximo aos resultados da pesquisa"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Essas configurações são armazenadas em seus cookies, nos não armazenamos "
"nenhum dado a seu respeito."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Estes cookies servem ao seu único propósito, nós não usamos esses cookies"
" para rastreá-lo."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Salvar"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Redefinir configurações"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Voltar"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Atalhos"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Estilo-Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navegue os resultados de busca com atalhos (JavaScript é necessário). "
"Pressione a tecla \"h\" na página principal ou de resultados para obter "
"ajuda."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy de imagem"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Usar proxy para resultados da imagem no SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Rolagem infinita"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Automaticamente carregar a próxima página ao rolar até o fim da página "
"atual"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Que idioma você prefere para a busca?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Escolha Auto-detect para permitir que o SearXNG detecte automaticamente o"
" idioma da sua consulta."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Método HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Mude como os formulários são submetidos"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Consultar no título da página"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Quando ativado, o título da página de resultados conterá sua consulta. "
"Seu navegador pode registrar este título"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultados em novas abas"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Abrir resultados em novas abas do navegador"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrar conteúdo"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Pesquisar na categoria selecionada"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Faça a busca imediatamente se existir uma categoria selecionada. "
"Desabilite para selecionar múltiplas categorias"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Mudar a interface do SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Estilo do tema"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Escolha auto para seguir as configurações do seu navegador"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokens de busca"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Acesso a tokens para buscadores privados"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Idioma da interface"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Alterar o idioma da interface"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formatação de URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Lindo"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Completo"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Anfitrião"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Mudar a resultante formatação do URL"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repositório"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "exibir mídia"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "ocultar mídia"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Esse site não disponibilizou uma descrição."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Tamanho do arquivo"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Data"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tipo"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolução"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formato"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motor de busca"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Ver código-fonte"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "endereço"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "exibir mapas"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ocultar mapas"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versão"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Mantenedor"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Atualizado em"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tags"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularidade"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licença"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projeto"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Página inicial do projeto"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Data de publicação"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Jornal"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editor"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Editor(a)"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "IOD"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "NSPI"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "NLPI"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "link magnético"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "arquivo torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Semeador"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Número de Arquivos"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "exibir vídeo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ocultar vídeo"

#~ msgid "Engine time (sec)"
#~ msgstr "Tempo do motor (segundos)"

#~ msgid "Page loads (sec)"
#~ msgstr "Carregamento da página (sec)"

#~ msgid "Errors"
#~ msgstr "Erros"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA requerido"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Redirecionar conexões HTTP para HTTPS, se possível"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Os resultados são abertos na mesma "
#~ "janela por padrão. Este complemento muda"
#~ " o comportamento padrão ao abrir "
#~ "links em novas abas/janelas (JavaScript "
#~ "necessário)."

#~ msgid "Color"
#~ msgstr "Cor"

#~ msgid "Blue (default)"
#~ msgstr "Azul (padrão)"

#~ msgid "Violet"
#~ msgstr "Violeta"

#~ msgid "Green"
#~ msgstr "Verde"

#~ msgid "Cyan"
#~ msgstr "Ciano"

#~ msgid "Orange"
#~ msgstr "Laranja"

#~ msgid "Red"
#~ msgstr "Vermelho"

#~ msgid "Category"
#~ msgstr "Categoria"

#~ msgid "Block"
#~ msgstr "Bloqueado"

#~ msgid "original context"
#~ msgstr "Contexto original"

#~ msgid "Plugins"
#~ msgstr "Complementos"

#~ msgid "Answerers"
#~ msgstr "Operadores de Resposta"

#~ msgid "Avg. time"
#~ msgstr "Tempo médio"

#~ msgid "show details"
#~ msgstr "Exibir detalhes"

#~ msgid "hide details"
#~ msgstr "ocultar detalhes"

#~ msgid "Load more..."
#~ msgstr "Mostrar mais..."

#~ msgid "Loading..."
#~ msgstr "Carregando..."

#~ msgid "Change searx layout"
#~ msgstr "Alterar interface do searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Usar proxy para resultado de imagens exibidas através do searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Esta é a lista do módulos de resposta instantânea do searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Esta é a lista de cookies que "
#~ "o searx está armazenando em seu "
#~ "computador."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Com essa lista, você pode avaliar a transparência do searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Parece que você está usando o searx pela primeira vez."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""
#~ "Por favor, tente novamente mais tarde"
#~ " ou procure outra instância do searx."

#~ msgid "Themes"
#~ msgstr "Temas"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Método"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Configurações avançadas"

#~ msgid "Close"
#~ msgstr "Fechar"

#~ msgid "Language"
#~ msgstr "Idioma"

#~ msgid "broken"
#~ msgstr "quebrado"

#~ msgid "supported"
#~ msgstr "suportado"

#~ msgid "not supported"
#~ msgstr "não suportado"

#~ msgid "about"
#~ msgstr "sobre"

#~ msgid "Avg."
#~ msgstr "Média"

#~ msgid "User Interface"
#~ msgstr "Interface do usuário"

#~ msgid "Choose style for this theme"
#~ msgstr "Escolher um estilo para este tema"

#~ msgid "Style"
#~ msgstr "Estilo"

#~ msgid "Show advanced settings"
#~ msgstr "Mostrar configurações avançadas"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""
#~ "Mostrar por padrão o painel de "
#~ "configurações avançadas na página inicial"

#~ msgid "Allow all"
#~ msgstr "Permitir tudo"

#~ msgid "Disable all"
#~ msgstr "Desativar tudo"

#~ msgid "Selected language"
#~ msgstr "Idioma selecionado"

#~ msgid "Query"
#~ msgstr "Consulta"

#~ msgid "save"
#~ msgstr "salvar"

#~ msgid "back"
#~ msgstr "voltar"

#~ msgid "Links"
#~ msgstr "Links"

#~ msgid "RSS subscription"
#~ msgstr "Assinatura RSS"

#~ msgid "Search results"
#~ msgstr "Procurar resultados"

#~ msgid "next page"
#~ msgstr "próxima página"

#~ msgid "previous page"
#~ msgstr "página anterior"

#~ msgid "Start search"
#~ msgstr "Iniciar busca"

#~ msgid "Clear search"
#~ msgstr "Limpar busca"

#~ msgid "Clear"
#~ msgstr "Limpar"

#~ msgid "stats"
#~ msgstr "estatísticas"

#~ msgid "Heads up!"
#~ msgstr "Atenção!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Parece que você está usando o SearXNG pela primeira vez."

#~ msgid "Well done!"
#~ msgstr "Muito bem!"

#~ msgid "Settings saved successfully."
#~ msgstr "Configurações salvas com sucesso."

#~ msgid "Oh snap!"
#~ msgstr "Oh não!"

#~ msgid "Something went wrong."
#~ msgstr "Algo deu errado."

#~ msgid "Date"
#~ msgstr "Data"

#~ msgid "Type"
#~ msgstr "Tipo"

#~ msgid "Get image"
#~ msgstr "Obter imagem"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferências"

#~ msgid "Scores per result"
#~ msgstr "Pontuações por resultado"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "um mecanismo de metabusca que respeita a sua privacidade"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Nenhum resumo disponível para essa publicação."

#~ msgid "Self Informations"
#~ msgstr "Informações Próprias"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Alterar como os formulários são "
#~ "submetidos<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">saiba mais sobre os "
#~ "métodos de solicitação</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Esse plugin checa se o endereço do"
#~ " requerimento é um nódulo de saída"
#~ " TOR, e informa o usuário se "
#~ "ele realmente for, parecido com "
#~ "check.torproject.org mas para searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "A lista de nódulos de saída TOR"
#~ " (https://check.torproject.org/exit-addresses) é "
#~ "inalcançável."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Você está usando TOR. Seu endereço de IP aparenta ser: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""
#~ "Você não está usando TOR. Seu "
#~ "endereço de IP aparenta ser: "
#~ "{ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Detecção automática de idioma de pesquisa"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""
#~ "Detecte automaticamente o idioma de "
#~ "pesquisa da consulta e mude para "
#~ "ele."

#~ msgid "others"
#~ msgstr "outros"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Esta aba não aparece para resultados "
#~ "de busca, mas você pode buscar os"
#~ " sites listados aqui via bangs."

#~ msgid "Shortcut"
#~ msgstr "Atalhos"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Essa aba não existe na interface "
#~ "de usuário, mas você pode pesquisar "
#~ "nessas ferramentas/motores pelos seus !bangs."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Os motores de busca não podem extrair os resultados."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Por favor, tente novamente mais tarde"
#~ " ou encontre outra instância do "
#~ "SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Quando disponível, redirecionar para as "
#~ "versões de acesso livre das publicações"
#~ " (necessário plugin)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Mude como formulários são enviados, <a"
#~ " "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">aprenda mais sobre métodos "
#~ "de requisição</a>"

#~ msgid "On"
#~ msgstr "Ligado"

#~ msgid "Off"
#~ msgstr "Desligado"

#~ msgid "Enabled"
#~ msgstr "Habilitado"

#~ msgid "Disabled"
#~ msgstr "Desabilitado"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Executar a busca imediatamente se a "
#~ "categoria está selecionada. Desativar para "
#~ "selecionar várias categorias. (Necessário "
#~ "JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Atalhos estilo Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navegar pelos resultados de busca com"
#~ " atalhos semelhantes ao Vim (JavaScript "
#~ "necessário). Aperte \"h\" na página de"
#~ " resultados para obter ajuda."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Não encontramos nenhum resultado. Utilize "
#~ "outra consulta ou pesquisa em mais "
#~ "categorias."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Sobreescreve hosts dos resultados ou remove resultados baseado no host"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Substituir host"

#~ msgid "Error!"
#~ msgstr "Erro!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Os motores de busca não conseguiram obter resultados"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Submeta um novo problema no Github"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Gerador de valor aleatório"

#~ msgid "Statistics functions"
#~ msgstr "Funções estatísticas"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Computar {functions} dos argumentos"

#~ msgid "Get directions"
#~ msgstr "Obter instruções"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Exibe o seu IP se a consulta "
#~ "contiver \"ip\" e seu agente de "
#~ "usuário, se a consulta contiver \"user"
#~ " agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Não foi possível baixar a lista de"
#~ " nós de saída do Tor de: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Você está usando o Tor e parece"
#~ " que tem este endereço IP externo:"
#~ " {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Você não está usando o Tor e tem este endereço IP externo: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Palavras-chave"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Especificar preferências customizadas na URL"
#~ " pode ser usado para sincronizar "
#~ "preferências em outros dispositivos."

#~ msgid "proxied"
#~ msgstr "por proxy"
