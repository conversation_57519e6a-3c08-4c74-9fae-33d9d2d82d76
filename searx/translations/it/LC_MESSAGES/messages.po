# Italian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2018
# caoswave, 2016
# caoswave, 2016-2018
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2014,2017
# <PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2015
# Random_R, 2018-2020
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-03 23:14+0000\n"
"Last-Translator: LinuxWizard <<EMAIL>>\n"
"Language-Team: Italian <https://translate.codeberg.org/projects/searxng/"
"searxng/it/>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "senza altri sottogruppi"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "altro"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "documenti"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "generale"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musica"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "social media"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "immagini"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "video"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "notizie"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mappa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "cipolle"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "scienza"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "applicazioni"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "dizionari"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "testo musicale"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pacchetti"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "d&r"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "reposs"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wiki del software"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "pubblicazioni scientifiche"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatico"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "chiaro"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "scuro"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "nero"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Tempo di attività"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Al riguardo"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Temp. media"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Nuvolosità"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Condizione"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Condizione attuale"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Sera"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Percepita come"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Umidità"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Temp. massima"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Temp. min"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Mattina"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Notte"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Mezzogiorno"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Pressione"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Alba"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Tramonto"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Indice UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Visibilità"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vento"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Cielo sereno"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Nuvoloso"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Sereno"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Nebbia"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Pioggia intensa e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Rovesci di pioggia intensa e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Rovesci di pioggia intensa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Pioggia intensa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Nevischio intenso e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Rovesci di nevischio intenso e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Rovesci di nevischio intenso"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Nevischio intenso"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Neve intensa e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Rovesci di neve intensa e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Rovesci di neve intensa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Neve intensa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Pioggia debole e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Rovesci di pioggia debole e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Rovesci di pioggia debole"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Pioggia debole"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Nevischio debole e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Rovesci di nevischio debole e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Rovesci di nevischio debole"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Nevischio debole"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Neve debole e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Rovesci di neve debole e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Rovesci di neve debole"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Neve debole"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Parzialmente nuvoloso"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Pioggia e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Rovesci di pioggia e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Rovesci di pioggia"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Pioggia"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Nevischio e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Rovesci di nevischio e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Rovesci di nevischio"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Nevischio"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Neve e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Rovesci di neve e tuoni"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Rovesci di neve"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Neve"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "iscritti"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "messaggi"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "utenti attivi"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "commenti"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "utente"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "comunità"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "punti"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "titolo"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autore"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "aperto"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "chiuso"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "risposto"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nessun oggetto trovato"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Sorgente"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Errore di caricamento della pagina successiva"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Impostazioni non valide, modifica le tue preferenze"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Impostazioni non valide"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "errore di ricerca"

#: searx/webutils.py:35
msgid "timeout"
msgstr "tempo scaduto"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "errore di analisi"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "errore protocollo HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "errore di rete"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Errore SSL: verifica del certificato fallita"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "crash inaspettato"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "errore HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "errore di connessione HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "errore del proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "troppe richieste"

#: searx/webutils.py:58
msgid "access denied"
msgstr "accesso negato"

#: searx/webutils.py:59
msgid "server API error"
msgstr "errore server API"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Sospeso"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minuto(i) fa"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "di {hours} ora(e) e {minutes} minuto(i) fa"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Genera più numeri casuali"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Calcola {func} degli argomenti"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Mostra percorso nella mappa .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLETO)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Questa voce è stata sostituita da"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Canale"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "velocità in bit"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "voti"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "clic"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Lingua"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citazioni dall anno {firstCitationVelocityYear} fino al "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Impossibile leggere l'URL dell'immagine. Ciò potrebbe essere dovuto a un "
"formato del file non supportato. TinEye supporta solo immagini JPEG, PNG,"
" GIF, BMP, TIFF o Web."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"L'immagine è troppo semplice per trovare corrispondenze. TinEye richiede "
"un maggiore livello di dettagli visivi per identificare corrispondenze "
"con successo."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "L'immagine non può essere scaricata."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Valutazione del libro"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Qualità del file"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Blacklist Ahima"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Escludi i risultati onion che appaiono nella blacklist Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Calcolatrice base"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Calcola espressioni matematiche nella barra di ricerca"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Converte le stringhe in diversi digest di hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "digest dell'hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Plugin dell'hostname"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Riscrive gli hostname, rimuove i risultati o gli da priorità in base "
"all'hostname"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Reindirizzamento Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Se possibile, evita il paywall di una pubblicazione reindirizzando ad una"
" versione ad accesso libero"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informazioni su di sé"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Mostra il tuo IP se la query è \"ip\" e il tuo user agent se la query è "
"\"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Il tuo IP è: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Il tuo interprete è: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Plugin di verifica tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Questo plugin controlla se l'indirizzo richiesto è un nodo di uscita di "
"Tor e informa l'utente se lo è; come check.torproject.org, ma da SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Impossibile scaricare l’elenco dei nodi di uscita di Tor da"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Stai usando Tor e sembra che tu abbia l’indirizzo IP esterno"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Non stai usando Tor e hai l’indirizzo IP esterno"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Rimuovi URL traccianti"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Rimuovi gli elementi traccianti dall'indirizzo URL riportato"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Plug in convertitore unità"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Converti tra le unità"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Pagina non trovata"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Vai a %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "cerca nella pagina"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Dona"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferenze"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Offerto da"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "un meta-motore di ricerca web, open source e rispettoso della privacy"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Codice sorgente"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Registratore dei problemi"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statistiche dei motori"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Istanze pubbliche"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Politica sulla riservatezza"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Contatta il manutentore dell'istanza"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Premi sull'icona della lente per avviare la ricerca"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Lunghezza"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Visualizzazioni"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autore"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "in cache"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Inizia segnalando un nuovo problema su GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Cerca bug esistenti riguardo questo motore su GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Confermo che non ci sono bug esistenti riguardo il problema che ho "
"riscontrato"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Se questa è un'istanza pubblica, per favore specifica l'URL nella "
"segnalazione del bug"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Segnala un nuovo problema su Github, includendo le informazioni sopra "
"citate"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Nessun HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Visualizza i registri degli errori e invia una segnalazione di bug"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "esegui un !bang per questo motore"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "esegui un !bang per le sue categorie"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediano"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Test di controllo fallito(i): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Errori:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Generale"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Categorie predefinite"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interfaccia utente"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privacy"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Motori"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Motori di ricerca attualmente in uso"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Richieste speciali"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookie"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Numero di risultati"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informazioni"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Torna in cima"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Pagina precedente"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Pagina successiva"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Visualizza la pagina principale"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Cerca..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "pulisci"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "cerca"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Non ci sono dati attualmente disponibili. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nome del motore"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Punteggi"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Conteggio dei risultati"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Tempo di risposta"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Affidabilità"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Totale"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Elaborazione"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Avvisi"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Errori ed eccezioni"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Eccezione"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Messaggio"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Percentuale"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametro"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nome del file"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funzione"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Codice"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Controllore"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Test fallito"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Commento(i)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Esempi"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definizioni"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinonimi"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Temperatura percepita"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Risposte"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Scarica i risultati"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Prova a cercare:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Messaggi dai motori di ricerca"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL della ricerca"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Copiato"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copia"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Suggerimenti"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Lingua di ricerca"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Lingua predefinita"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Rilevamento automatico"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Ricerca Sicura"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Severo"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderata"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Nessuna"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Intervallo di tempo"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Qualsiasi data"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Ultimo giorno"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Ultima settimana"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Ultimo mese"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Ultimo anno"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informazione!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "Attualmente non ci sono cookie definiti."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Scusa!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Non sono stati trovati risultati. Puoi provare a:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Non ci sono più risultati. Puoi provare a:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Aggiorna la pagina."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Cerca un'altra query o seleziona un'altra categoria (sopra)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Modifica il motore di ricerca utilizzato nelle preferenze:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Passa ad un'altra istanza:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Prova con un’altra ricerca o seleziona un’altra categoria."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""
"Torna alla pagina precedente utilizzando il pulsante della pagina "
"precedente."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Autorizza"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Parole chiave (prima parola della richiesta)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nome"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Descrizione"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Questa è la lista dei moduli di risposta istantanea di SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Questa è la lista dei plugin."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Completamento automatico"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Visualizza risultati mentre digiti"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Allinea al centro"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Mostra i risultati al centro della pagina (Oscar layout)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Questa è la lista di cookies e i loro valori che SearXNG sta salvando sul"
" tuo computer."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Con questa lista, puoi valutare la trasparenza di SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nome del cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valore"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL di ricerca delle preferenze attualmente salvate"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Nota: specificando le impostazioni personalizzate nell'URL di ricerca si "
"può ridurre la privacy facendo trapelare dati ai siti cliccati."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL per ripristinare le tue preferenze in un altro browser"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Un URL contenente le tue preferenze. Tale URL può essere utilizzato per "
"ripristinare le tue impostazioni in un altro dispositivo."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copia l’hash delle preferenze"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Inserisci l’hash delle preferenze copiate (senza URL) da ripristinare"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash delle preferenze"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital Object Identifier (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Resolver Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Seleziona il servizio usato dalla riscrittura DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Questa scheda non esiste nell’interfaccia utente, ma puoi effettuare "
"ricerche in questi motori tramite i suoi !bang."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Attiva tutto"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Disattiva tutto"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "La lingua selezionata è supportata"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Peso"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Tempo massimo"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Risolutore Favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Mostra le favicon vicino ai risultati della ricerca"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Le impostazioni vengono salvate nei tuoi cookie, consentendoci di non "
"conservare dati su di te."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Questi cookie servono solo ad offrirti un servizio migliore. Non li "
"usiamo per tracciarti."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Salva"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Ripristina i valori predefiniti"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Indietro"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Tasti di scelta rapida"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Simile a Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Naviga tra i risultati della ricerca con i tasti di scelta rapida (è "
"necessario JavaScript). Premi il tasto \"h\" nella pagina principale o in"
" quella dei risultati per ottenere aiuto."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy immagini"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proxy dei risultati delle immagini attraverso SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Scorrimento infinito"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Carica automaticamente la pagina successiva quando si scorre sino alla "
"fine della pagina attuale"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Che lingua preferisci per eseguire la ricerca?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Scegli la funzione di Auto-rilevamento per far scegliere a SearXNG la "
"lingua da usare nella tua ricerca."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Metodo HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Modifica come vengono inviati i moduli"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Query nel titolo della pagina"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Quando è abilitato, il titolo della pagina dei risultati contiene la tua "
"ricerca. Il tuo browser può registrare questo titolo"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Risultati in una nuova scheda"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Apri i risultati in nuove schede del browser"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtra il contenuto"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Cerca nella categoria selezionata"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Esegue immediatamente la ricerca se è stata selezionata una categoria. "
"Disabilita la selezione di più categorie"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Cambia la disposizione di SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Stile tema"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Seleziona automatico per seguire le impostazioni del tuo browser"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokens del motore"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tokens di accesso per motori privati"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Lingua dell'interfaccia"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Cambia la lingua dell'interfaccia"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formattazione URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Figo"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Pieno"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Oste"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Modifica formattazione URL del risultato"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "ripostiglo"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "mostra media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "nascondi media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Questo sito non fornisce nessuna descrizione."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Dimensioni file"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Data"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tipo"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Risoluzione"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formato"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motore"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Guarda la fonte"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "indirizzo"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "mostra mappa"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "nascondi mappa"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versione"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Manutentore"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Aggiornato alle"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etichette"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popolarità"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licenza"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Progetto"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Pagina iniziale del progetto"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Data di pubblicazione"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Giornale"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redattore"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Editore"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "link magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "file torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeder"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Numero di file"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "mostra video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "nascondi video"

#~ msgid "Engine time (sec)"
#~ msgstr "Tempo del motore (secondi)"

#~ msgid "Page loads (sec)"
#~ msgstr " Caricamento della pagina (secondi)"

#~ msgid "Errors"
#~ msgstr "Errori"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA richiesto"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Se possible, converti gli indirizzi HTTP in HTTPS"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Di base i risultati sono aperti "
#~ "nella stessa finestra. Questa estensione "
#~ "fa sì invece che vengano mostrati "
#~ "in nuove schede/finestre. (Javascript "
#~ "necessario)"

#~ msgid "Color"
#~ msgstr "Colore"

#~ msgid "Blue (default)"
#~ msgstr "Blu (predefinito)"

#~ msgid "Violet"
#~ msgstr "Viola"

#~ msgid "Green"
#~ msgstr "Verde"

#~ msgid "Cyan"
#~ msgstr "Ciano"

#~ msgid "Orange"
#~ msgstr "Arancione"

#~ msgid "Red"
#~ msgstr "Rosso"

#~ msgid "Category"
#~ msgstr "Categoria"

#~ msgid "Block"
#~ msgstr "Blocca"

#~ msgid "original context"
#~ msgstr "contesto originale"

#~ msgid "Plugins"
#~ msgstr "Plugin"

#~ msgid "Answerers"
#~ msgstr "Risponditori"

#~ msgid "Avg. time"
#~ msgstr "Tempo medio"

#~ msgid "show details"
#~ msgstr "mostra dettagli"

#~ msgid "hide details"
#~ msgstr "nascondi dettagli"

#~ msgid "Load more..."
#~ msgstr "Carica altro..."

#~ msgid "Loading..."
#~ msgstr "Caricamento..."

#~ msgid "Change searx layout"
#~ msgstr "Cambia l'aspetto di searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Usa un proxy per le immagini ottenute attraverso searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Questa è la lista dei moduli searx con risposta immediata"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "Qui puoi vedere i cookie che vengono conservati sul tuo computer."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "In questo modo, puoi constatare la trasparenza di searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Sembra che tu stia utilizzando searx per la prima volta."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Riprova nuovamente o cerca un'altra istanza di searx."

#~ msgid "Themes"
#~ msgstr "Temi"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metodo"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Impostazioni avanzate"

#~ msgid "Close"
#~ msgstr "Chiudi"

#~ msgid "Language"
#~ msgstr "Lingua"

#~ msgid "broken"
#~ msgstr "rotto"

#~ msgid "supported"
#~ msgstr "supportato"

#~ msgid "not supported"
#~ msgstr "non supportato"

#~ msgid "about"
#~ msgstr "informazioni"

#~ msgid "Avg."
#~ msgstr "Avg."

#~ msgid "User Interface"
#~ msgstr "Interfaccia utente"

#~ msgid "Choose style for this theme"
#~ msgstr "Scegli lo stile per questo tema"

#~ msgid "Style"
#~ msgstr "Stile"

#~ msgid "Show advanced settings"
#~ msgstr "Mostra le impostazioni avanzate"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""
#~ "Mostra il pannello delle impostazioni "
#~ "avanzate nella pagina iniziale per "
#~ "impostazione predefinita"

#~ msgid "Allow all"
#~ msgstr "Permetti tutto"

#~ msgid "Disable all"
#~ msgstr "Disattiva tutto"

#~ msgid "Selected language"
#~ msgstr "Lingua selezionata"

#~ msgid "Query"
#~ msgstr "Richiesta"

#~ msgid "save"
#~ msgstr "salva"

#~ msgid "back"
#~ msgstr "indietro"

#~ msgid "Links"
#~ msgstr "Collegamenti"

#~ msgid "RSS subscription"
#~ msgstr "Abbonamento RSS"

#~ msgid "Search results"
#~ msgstr "Risultati della ricerca"

#~ msgid "next page"
#~ msgstr "pagina successiva"

#~ msgid "previous page"
#~ msgstr "pagina precedente"

#~ msgid "Start search"
#~ msgstr "Cerca"

#~ msgid "Clear search"
#~ msgstr "Svuota ricerca"

#~ msgid "Clear"
#~ msgstr "Svuota"

#~ msgid "stats"
#~ msgstr "statistiche"

#~ msgid "Heads up!"
#~ msgstr "Avviso!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Sembra che sia la prima volta che usi SearXNG."

#~ msgid "Well done!"
#~ msgstr "Ottimo!"

#~ msgid "Settings saved successfully."
#~ msgstr "Impostazioni salvate con successo."

#~ msgid "Oh snap!"
#~ msgstr "Mannaggia!"

#~ msgid "Something went wrong."
#~ msgstr "Qualcosa è andato storto."

#~ msgid "Date"
#~ msgstr "Data"

#~ msgid "Type"
#~ msgstr "Tipo"

#~ msgid "Get image"
#~ msgstr "Visualizza immagine"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferenze"

#~ msgid "Scores per result"
#~ msgstr "Punteggio per risultato"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "un metamotore di ricerca personalizzabile e rispettoso della privacy"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Nessun sommario disponibile per questa pubblicazione."

#~ msgid "Self Informations"
#~ msgstr "Informazioni su di sé"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Seleziona il metodo di richiesta HTTP"
#~ " (<a "
#~ "href=\"https://it.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Messaggio_di_richiesta\""
#~ " rel=\"external\">Cos'è un metodo di "
#~ "richiesta?</a>)"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Questo plugin controlla se l'indirizzo "
#~ "della richiesta è un nodo di "
#~ "uscita di TOR e informa l'utente "
#~ "se lo è. Simile a check.torproject.org"
#~ " ma fornito da searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "La lista dei nodi d'uscita TOR non"
#~ " è raggiungibile (https://check.torproject.org/exit-"
#~ "addresses)."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Stai usando TOR. Il tuo indirizzo IP risulta essere : {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Non stai usando TOR. Il tuo indirizzo IP sembra essere: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Rileva automaticamente la lingua di ricerca"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""
#~ "Rileva automaticamente la lingua di "
#~ "ricerca della query e passa ad "
#~ "essa."

#~ msgid "others"
#~ msgstr "altri"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Questa scheda non viene mostrata per "
#~ "i risultati di ricerca, ma puoi "
#~ "cercare i motori elencati qui usando "
#~ "i bang."

#~ msgid "Shortcut"
#~ msgstr "Scorciatoia"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Questa scheda non esiste nell’interfaccia "
#~ "utente, ma puoi effettuare ricerche in"
#~ " questi motori tramite i suoi !bang."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "I motori di ricerca non riescono a recuperare risultati."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Riprova più tardi o trova un'altra istanza SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Indirizza a versioni open-access delle"
#~ " pubblicazioni quando disponibili (plugin "
#~ "richiesto)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Seleziona il metodo di richiesta HTTP"
#~ " (<a "
#~ "href=\"https://it.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Messaggio_di_richiesta\""
#~ " rel=\"external\">Cos'è un metodo di "
#~ "richiesta?</a>)"

#~ msgid "On"
#~ msgstr "Attivo"

#~ msgid "Off"
#~ msgstr "Spento"

#~ msgid "Enabled"
#~ msgstr "Attivo"

#~ msgid "Disabled"
#~ msgstr "Disabilitato"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Esegui la ricerca immediatamente se una"
#~ " categoria è selezionata. Disabilita questa"
#~ " opzione se vuoi selezionare più "
#~ "categorie. (Javascript necessario)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Scorciatoie in stile Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Usa comandi in stile Vim per "
#~ "navigare tra i risultati (JavaScript "
#~ "necessario). Premi il tasto \"h\" per"
#~ " visualizzare la finestra d'aiuto."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "non abbiamo trovato alcun risultato. "
#~ "Prova una nuova ricerca, o cerca "
#~ "in più categorie."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Riscrivere gli hostname dei risultati o"
#~ " rimuovere i risultati in base "
#~ "all'hostname"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Sostituzione del nome host"

#~ msgid "Error!"
#~ msgstr "Errore!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "I motori di ricerca non riescono a recuperare risultati"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Segnala un nuovo problema su GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generatore di numeri casuali"

#~ msgid "Statistics functions"
#~ msgstr "Funzioni statistiche"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Calcola {functions} degli argomenti"

#~ msgid "Get directions"
#~ msgstr "Ricevi direzioni"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Mostra il tuo IP se hai cercato"
#~ " \"ip\" ed il tuo user agent se"
#~ " hai cercato \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Non ho potuto scaricare la lista "
#~ "dei nodi di uscita di Tor da: "
#~ "https://check.torproject.org?exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Stai usando Tor e sembra che tu"
#~ " abbia il seguente indirizzo IP: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Non stai usando Tor e il tuo indirizzo IP esterno è: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Parole chiave"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Specificando le impostazioni personalizzate "
#~ "nell'URL delle preferenze è possibile "
#~ "sincronizzare le preferenze tra i vari"
#~ " dispositivi."

#~ msgid "proxied"
#~ msgstr "proxy"
