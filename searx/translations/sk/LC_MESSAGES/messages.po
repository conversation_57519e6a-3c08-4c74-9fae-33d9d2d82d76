# Slovak translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON>, 2017
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON>y<PERSON>eaN <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-23 19:16+0000\n"
"Last-Translator: whytf <<EMAIL>>\n"
"Language: sk\n"
"Language-Team: Slovak "
"<https://translate.codeberg.org/projects/searxng/searxng/sk/>\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 "
"&& n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "bez ďalšieho zoskupenia"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "ostatné"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "súbory"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "všeobecné"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "hudba"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sociálne médiá"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "obrázky"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videá"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "rádio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "technológia"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "správy"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "veda"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikácie"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "slovníky"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "texty piesní"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "programové balíčky"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "otázky a odpovede"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repozitáre"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "Dokumentácie aplikácií"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "vedecké publikácie"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automaticky"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "svetlý"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tmavý"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "čierna"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Doba prevádzky"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "O nás"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Priemerná teplota"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Oblačnosť"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Podmienka"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Aktuálna podmienka"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Večer"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Pocitovo ako"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Vlhkosť"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Max teplota"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Min teplota"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Ráno"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noc"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Poludnie"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Tlak"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Východ slnka"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Západ slnka"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Teplota"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Index UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Viditeľnosť"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vietor"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "odberatelia"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "príspevky"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktívny používatelia"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "komentáre"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "používateľ"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "komunita"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "body"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "názov"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Otvoriť"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Zatvoriť"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "Odpovedané"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nič sa nenašlo"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Zdroj"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Chyba pri načítaní ďalšej stránky"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Nesprávne nastavenia, prosím upravte svoje predvoľby"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Nesprávne nastavenia"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "chyba vyhľadávania"

#: searx/webutils.py:35
msgid "timeout"
msgstr "časový limit"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "chyba parsovania"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "chyba HTTP protokolu"

#: searx/webutils.py:38
msgid "network error"
msgstr "chyba siete"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL error: overenie certifikátu zlyhalo"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "neočakávaná chyba"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP chyba"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "chyba pripojenia cez HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "chyba proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "priveľa žiadostí"

#: searx/webutils.py:58
msgid "access denied"
msgstr "prístup bol odmietnutý"

#: searx/webutils.py:59
msgid "server API error"
msgstr "API chyba servera"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Pozastavené"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "pred {minutes} minútami"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "pred {hours} hodinami, {minutes} minútami"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Vytvoriť rôzné náhodné hodnoty"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Vypočítať {func} z argumentov"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Zobraziť trasu na mape .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ZASTARANÉ)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Táto položka bola nahradená"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanál"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitrate"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "hlasy"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "kliknutia"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Jazyk"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citácií od roku {firstCitationVelocityYear} do roku "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Danú webovú adresu obrázka sa nepodarilo načítať. Môže to byť spôsobené "
"nepodporovaným formátom súboru. TinEye podporuje iba obrázky JPEG, PNG, "
"GIF, BMP, TIFF alebo WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Obrázok je príliš nízkej kvality na to aby sa našla zhoda. TinEye "
"vyžaduje vyššiu kvalitu detailov v obrázku na identifikáciu zhôd."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Obrázok nemohol byť stiahnutý."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Hodnotenie knižky"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Kvalita súboru"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Čierna listina Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Odfiltruj výsledky onion, ktoré sa zobrazujú na čiernej listine Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Základná Kalkulačka"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Vypočítaj matematické výrazy cez vyhľadávací panel"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash plugin"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Skonvertuje text pomocou rôznych hash funkcií."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hash hodnota"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Plugin názvov hostiteľov"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Prepísať názvy hostiteľov, odstrániť výsledky alebo ich uprednostniť na "
"základe názvu hostiteľa"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Otvoriť prístup k prepísaniu DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Vyhnúť sa plateným bránam presmerovaním na verejne prístupné verzie "
"publikácií ak sú k dispozícii"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Vlastné informácie"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Zobrazí vašu IP, ak je dopyt „ip“, a váš user agent, ak je dopyt „user-"
"agent“."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Vaša IP adresa je: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Váš používateľský agent je: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Kontrola Tor plugin"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Tento plugin kontroluje, či žiadaná adresa je výstupný bod TORu, a "
"informuje používateľa ak je, ako check.torproject.org ale od SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nepodarilo sa stiahnuť zoznam výstupných uzlov Tor z"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Používate Tor a vyzerá to, že máte externú IP adresu"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Nepoužívate Tor a máte externú IP adresu"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Odstraňovanie sledovacích argumentov"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Odstrániť sledovacie argumenty z vrátenej URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Modul konvertora jednotiek"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Previesť medzi jednotkami"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Stránka sa nenašla"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Choď na %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "stránka vyhľadávania"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Prispejte"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Nastavenia"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Používame"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "otvorený metavyhľadávač rešpektujúci súkromie"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Zdrojový kód"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Sledovanie problémov"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Štatistiky vyhľadávača"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Verejné inštancie"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Ochrana súkromia"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontaktujte správcu inštancie"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Kliknite na lupu pre vyhľadávanie"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Dĺžka"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Zobrazenia"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "z vyrovnávacej pamäte"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Začnite s pridaním nového problému na Githube"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Skontrolujte prosím existujúce chyby tohto vyhľadávaču na Githube"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Potvrdzujem, že neexistuje žiadna chyba týkajúca sa problému, s ktorým sa"
" stretávam"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Ak ide o verejnú inštanciu, uveďte v hlásení o chybe adresu URL"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Odošlite novú chybu na Github vrátane informácii nad"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Žiadne HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Zobraziť záznamy chýb a odoslať hlásenie o chybe"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang pre tento vyhľadávač"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang pre jeho kategórie"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Medián"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Neúspešný(é) kontrolný(é) test(y): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Chyby:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Všeobecné"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Predvolené kategórie"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "UI"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Súkromie"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Vyhľadávače"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "List práve používaných vyhľadávačov"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Špeciálne vyhľadávania"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Počet výsledkov"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informácie"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Späť na začiatok"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Predošlá strana"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Ďalšia strana"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Zobraz úvodnú stranu"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Hľadať..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "vyčistiť"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "hľadať"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Momentálne nie su dostupné žiadne dáta."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Názov vyhľadávača"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Hodnotenia"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Počet výsledkov"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Doba odozvy"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Spoľahlivosť"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Celkom"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Spracovávanie"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Varovania"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Chyby a výnimky"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Výnimka"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Správa"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Úroveň"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Názov súboru"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkcia"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kód"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Kontrolór"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Zlyhaný test"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komentár(e)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Príklady"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definície"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonymá"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Odpovede"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Výsledky na stiahnutie"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Skús hľadať:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Hlásenia z vyhľadávačov"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekundy"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Adresa URL vyhľadávania"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Skopírované"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopírovať"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Návrhy"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Jazyk vyhľadávania"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Predvolený jazyk"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Auto-detekcia"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Bezpečné vyhľadávanie"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Striktné"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Mierne"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Žiadne"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Časový rozsah"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Kedykoľvek"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Posledný deň"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Posledný týždeň"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Posledný mesiac"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Posledný rok"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informácia!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "momentálne nie su definované žiadne cookies."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Je nám ľúto!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Nenašli sa žiadne výsledky. Môžete skúsiť:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Nie sú k dispozícii žiadne ďalšie výsledky. Môžete skúsiť:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Obnovte stránku."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Vyhľadajte inú požiadavku alebo vyberte inú kategóriu (vyššie)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Zmeňte použitý vyhľadávač v preferenciách:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Prepnúť na inú inštanciu:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Vyhľadajte inú požiadavku alebo vyberte inú kategóriu."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""
"Vráťte sa na predchádzajúcu stránku pomocou tlačidla predchádzajúcej "
"stránky."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Povoliť"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Kľúčové slová (prvé slovo v dopyte)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Názov"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Popis"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Toto je zoznam modulov okamžitých odpovedí SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Toto je zoznam pluginov."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automatické dokončovanie"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Vyhľadávať počas písania"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Zarovnanie na stred"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Zobrazenie výsledkov v strede stránky (Oscar layout)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Toto je zoznam cookies a ich hodnôt, ktoré vo vašom počítači ukladá "
"SearXNG."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Týmto zoznamom môžete zhodnotiť priehľadnosť SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Názov cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Hodnota"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Vyhľadávacia adresa (URL) stávajúcich, uložených nastavení"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Poznámka: zadanie osobitých nastavení vo vyhľadávacej adrese (URL) môže "
"zredukovať úroveň súkromia tým že poskytne doplňujúce údaje kliknutým "
"adresám vo výsledkoch."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "Adresa (URL) pre obnovu nastavení v inom prehliadači"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Adresa URL obsahujúca vaše preferencie. Túto adresu URL môžete použiť na "
"obnovenie nastavení v inom zariadení."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopírovať hash predvolieb"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Vložte skopírovaný hash kód predvolieb (bez adresy URL) na obnovenie"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash kód predvolieb"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digitálny identifikátor objektu (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "DOI vyhodnocovač otvoreným prístupom"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Vyberte službu, ktorú používa DOI rewrite"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Táto stránka neexistuje v používateľskom rozhraní, ale môžete v nich "
"vyhľadávať pomocou jej !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Povoliť všetko"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Zakázať všetko"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Podporuje zvolený jazyk"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Váha/Hmotnosť"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maximálny čas"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Riešiteľ favikón"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Zobraziť favikóny pri výsledkoch vyhľadávania"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Tieto nastavenia sú uložené v cookies, čo nám umožňuje neukladať dáta o "
"vás na našej strane."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Tieto cookies slúžia výhradné pre vaše pohodlie a nie sú používané na "
"sledovanie."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Uložiť"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Obnoviť predvolené"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Späť"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Klávesové skratky"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Ako Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigujte vo výsledkoch vyhľadávania pomocou klávesových skratiek "
"(vyžaduje sa JavaScript). Stlačte klávesu \"h\" na hlavnej stránke alebo "
"na stránke s výsledkami vyhľadávania pre získanie pomoci."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy pre obrázky"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Sprostredkovanie výsledkov snímok cez SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Nekonečné posúvanie"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Automaticky načítať ďalšiu stránku pri posunutí na koniec aktuálnej "
"stránky"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Aký jazyk preferujete pre vyhľadávanie?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Ak chcete, aby SearXNG zistil jazyk vášho vyhľadávania, vyberte možnosť "
"Auto-detect (Automatické zisťovanie)."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Metóda HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Zmena spôsobu odosielania formulárov"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Dotaz v názve stránky"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Ak je táto možnosť povolená, názov stránky s výsledkami obsahuje vašu "
"požiadavku. Váš prehliadač môže tento názov zaznamenať"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Výsledky v novom tabe"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Otvoriť odkazy v novom tabe"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrovanie obsahu"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Vyhľadávanie pri výbere kategórie"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Okamžité vykonanie vyhľadávania, ak je vybraná kategória. Zakážte pre "
"výber viacerých kategórií"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Téma"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Zmena SearXNG vzhľadu"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Štýl témy"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Vyberte možnosť auto, aby sa riadila nastaveniami vášho prehliadača"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Engine tokeny"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Prístupové tokwny pre súkromné nástroje"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Jazyk rozhrania"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Zmena jazyku rozhrania"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formátovanie URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Pekné"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Plné"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Hostiteľ"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Zmeniť formátovanie URL výsledku"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repozitár"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "ukázať médiá"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "skryť médiá"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Táto stránka neposkytuje žiaden popis."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Veľkosť súboru"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dátum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Typ"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Rozlíšenie"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formát"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Vyhľadávač"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Zobraziť zdroj"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresa"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "ukázať mapu"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "skryť mapu"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Verzia"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Správca"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Aktualizované v"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Značky"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularita"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licencia"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Domovská stránka projektu"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Dátum publikácie"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Žurnál"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editor"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Vydavateľ"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "odkaz na magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent súbor"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Odosielateľ"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Príjemca"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Počet súborov"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "ukázať video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "skryť video"

#~ msgid "Engine time (sec)"
#~ msgstr "Načítanie vyhľadávača (sek)"

#~ msgid "Page loads (sec)"
#~ msgstr "Načítanie stránky (sek)"

#~ msgid "Errors"
#~ msgstr "Chyby"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Prepísať odkazy HTTP na HTTPS, ak je to možné"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Výsledky sú otvorené v rovnakom okne "
#~ "predvolene. Tento plugin prepíše predvolené"
#~ " správanie otvoriania odkazov na nových "
#~ "taboch a oknách. (Je potrebný "
#~ "JavaScript)"

#~ msgid "Color"
#~ msgstr "Farba"

#~ msgid "Blue (default)"
#~ msgstr "Modrá (predvolené)"

#~ msgid "Violet"
#~ msgstr "Fialová"

#~ msgid "Green"
#~ msgstr "Zelená"

#~ msgid "Cyan"
#~ msgstr "Azúrová"

#~ msgid "Orange"
#~ msgstr "Oranžová"

#~ msgid "Red"
#~ msgstr "Červená"

#~ msgid "Category"
#~ msgstr "Kategória"

#~ msgid "Block"
#~ msgstr "Blokovať"

#~ msgid "original context"
#~ msgstr "pôvodný kontext"

#~ msgid "Plugins"
#~ msgstr "Zásuvné moduly"

#~ msgid "Answerers"
#~ msgstr "Rýchle odpovede"

#~ msgid "Avg. time"
#~ msgstr "Priemerný čas"

#~ msgid "show details"
#~ msgstr "ukázať detaily"

#~ msgid "hide details"
#~ msgstr "skryť detaily"

#~ msgid "Load more..."
#~ msgstr "Načítať viac..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Zmena rozhrania searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Zobrazovanie výsledkov obrázkov cez searx proxy"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Toto je zoznam modulov rýchlej odpovede pre searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "Toto je zoznam cookies a ich hodnôt uložených searx na vašom počítači"

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Pomocou tohto zoznamu môžte vidieť transparentnosť searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Zdá sa, že používate searx prvýkrát."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""

#~ msgid "Themes"
#~ msgstr "Téma"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metóda"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Pokročilé nastavenia"

#~ msgid "Close"
#~ msgstr "Zatvoriť"

#~ msgid "Language"
#~ msgstr "Jazyk"

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "podporovaný"

#~ msgid "not supported"
#~ msgstr "nepodporovaný"

#~ msgid "about"
#~ msgstr "o nás"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr "Používateľské prostredie"

#~ msgid "Choose style for this theme"
#~ msgstr "Vyberte si štýl pre túto tému"

#~ msgid "Style"
#~ msgstr "Štýl"

#~ msgid "Show advanced settings"
#~ msgstr "Zobraziť pokročilé nastavenia"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr ""

#~ msgid "Query"
#~ msgstr "Dotaz"

#~ msgid "save"
#~ msgstr "uložiť"

#~ msgid "back"
#~ msgstr "späť"

#~ msgid "Links"
#~ msgstr "Odkazy"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Výsledky vyhľadávania"

#~ msgid "next page"
#~ msgstr "ďalšia strana"

#~ msgid "previous page"
#~ msgstr "predchádzajúca strana"

#~ msgid "Start search"
#~ msgstr "Začať vyhľadávanie"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "štatistiky"

#~ msgid "Heads up!"
#~ msgstr "Pozor!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Dobrá práca!"

#~ msgid "Settings saved successfully."
#~ msgstr "Nastavenia sa uložili."

#~ msgid "Oh snap!"
#~ msgstr "Ó nie!"

#~ msgid "Something went wrong."
#~ msgstr "Stalo sa niečo neočakávané."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Získať obrázok"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "nastavenia"

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "prispôsobitelný meta-vyhľadávač, ktorý rešpektuje vaše súkromie"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Pre túto publikáciu nie je dostupný žiadny abstrakt."

#~ msgid "Self Informations"
#~ msgstr "Informácie o sebe"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Zmeniť spôsob, akým sú odosielané "
#~ "formuláre, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">dozvedieť sa viac o "
#~ "týchto metódach</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Tento plugin kontroluje, či žiadaná "
#~ "adresa je výstupný bod TORu, a "
#~ "informuje používateľa ak je, ako "
#~ "check.torproject.org ale od searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Zoznam výstupných bodov TORu "
#~ "(https://check.torproject.org/exit-addresses) je "
#~ "nedostupný."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Používate TOR. Zdá sa, že vaša IP adresa je: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Nepoužívate TOR. Zdá sa, že vaša IP adresa je: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Autodetekcia jazyka vyhľadávania"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Automatická detekcia a prepnutie na jazyk dopytu."

#~ msgid "others"
#~ msgstr "iné"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Táto karta sa nezobrazuje vo výsledkoch"
#~ " vyhľadávania, ale môžete vyhľadávať v "
#~ "enginoch, ktoré sú tu uvedené, pomocou"
#~ " Bangs."

#~ msgid "Shortcut"
#~ msgstr "Skratka"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Vyhľadávače nemôžu získať výsledky."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Skúste znova neskôr, prosím, alebo použite inú inštanciu SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Presmerovanie na verzie publikácií s "
#~ "otvoreným prístupom, ak sú k dispozícii"
#~ " (vyžaduje sa plugin)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Zmena spôsobu odosielania formulárov, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">dozvedieť sa viac o "
#~ "metódach žiadosti</a>"

#~ msgid "On"
#~ msgstr "Zapnuté"

#~ msgid "Off"
#~ msgstr "Vypnuté"

#~ msgid "Enabled"
#~ msgstr "Povolené"

#~ msgid "Disabled"
#~ msgstr "Zakázané"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Vyhľadávať okamžite, ak je kategória "
#~ "vybraná. Vypnúť pre vyberanie viacerých "
#~ "kategórií. (Je potrebný JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Skratky ako vo VIM"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Prechádzať výsledky vyhľadávania klávesovými "
#~ "skratkami ako VIM (je potrebný "
#~ "JavaScript). Stlačte klávesy \"h\" na "
#~ "hlavnej stránke alebo na stránke s "
#~ "výsledkami pre zobrazenie pomoci."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "nepodarilo sa nájsť žiadne výsledky. "
#~ "Skúste použiť iné zadanie alebo "
#~ "vyhľadávajte vo viacerých kategóriach."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Informácie o sebe"

#~ msgid "Bytes"
#~ msgstr "bajtov"

#~ msgid "kiB"
#~ msgstr "kB"

#~ msgid "MiB"
#~ msgstr "MB"

#~ msgid "GiB"
#~ msgstr "GB"

#~ msgid "TiB"
#~ msgstr "TB"

#~ msgid "Hostname replace"
#~ msgstr "Nahradenie názvu servera"

#~ msgid "Error!"
#~ msgstr "Chyba!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Vyhľadávače nemôžu získať výsledky"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Začnite s pridaním nového problému na Githube"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generátor nahodných hodnôt"

#~ msgid "Statistics functions"
#~ msgstr "Štatistické funkcie"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Vypočítať {functions} argumentov"

#~ msgid "Get directions"
#~ msgstr "Požiadať o navigáciu"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Zobrazí vašu IP ak je dotaz \"ip\""
#~ " a user agenta ak dotaz obsahuje "
#~ "\"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Nepodarilo sa stiahnuť zoznam Tor "
#~ "exit-nodes z: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Používate Tor a vyzerá to, že máte túto externú IP adresu: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Nepoužívate Tor a máte túto externú IP adresu: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Kľúčové slová"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Zadaním osobitých nastavení v adrese "
#~ "(URL) nastavení je možné synchronizovať "
#~ "nastavenia do iných zariadení."

#~ msgid "proxied"
#~ msgstr "cez proxy"

