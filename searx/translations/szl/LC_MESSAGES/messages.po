# szl translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-03-16 13:04+0000\n"
"Last-Translator: gkkulik <<EMAIL>>\n"
"Language: szl\n"
"Language-Team: Silesian "
"<https://translate.codeberg.org/projects/searxng/searxng/szl/>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "bez dalszych podgrup"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "inksze"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "zbiory"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "ôgōlne"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muzyka"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "społeczności"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "ôbrazy"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "wideo"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "informatyka"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "wiadōmości"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "karta"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "cebule"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "nauka"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "apki"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "słowniki"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "teksty śpiywek"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pakety"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "pyt. i ôdp."

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repozytoryja"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wiki ôprogramowanio"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "nec"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "naukowe publikacyje"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "autōmatyczny"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "jasny"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "ciymny"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "czorny"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Czas fungowanio"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Informacyje"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Postrzednio tymperatura"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Pokrycie chmurami"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Stōn"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Teroźny stōn"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Wieczōr"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Czuć choćby"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Wilgłość"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maks. tymperatura"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Min. tymperatura"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Rano"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noc"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Połednie"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Ciśniynie"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Wschōd słōńca"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Zachōd słōńca"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Tymperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Indeks UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Widoczność"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Wiater"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "subskrybyńcio"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "wpisy"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktywni używocze"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kōmyntorze"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "używocz"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "społeczność"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "pōnkty"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "tytuł"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autōr"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "ôtwarty"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "zawarty"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "ôdpedziany"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Żodyn elymynt niy znojdziōny"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Zdrzōdło"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Feler ladowanio nastympnyj strōny"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Niynoleżne sztalōnki, zmiyń swoje preferyncyje"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Niynoleżne sztalōnki"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "błōnd wyszukowanio"

#: searx/webutils.py:35
msgid "timeout"
msgstr "kōniec czasu"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "feler przetworzanio"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Feler protokołu HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "feler necu"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Feler SSL: niy podarziło sie poświadczynie certyfikatu"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "niyspodziano awaryjo"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "Feler HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Feler połōnczynio HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "feler proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "za moc żōndań"

#: searx/webutils.py:58
msgid "access denied"
msgstr "dostymp ôdkozany"

#: searx/webutils.py:59
msgid "server API error"
msgstr "feler serwera API"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Strzimane"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minut(y) tymu"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} godzin(y), {minutes} minut(y) tymu"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Wygyneruj insze werty losowe"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Porachuj {func} ôd argumyntōw"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Pokoż trasa na karcie..."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ZASTARZAŁE)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Tyn wpis bōł zastōmpiōny ôd"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanał"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "cug bitōw"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "głosy"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "kliki"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Jynzyk"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} cytowań ôd roku {firstCitationVelocityYear} do "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Niy szło przeczytać adresy ôd tego ôbrozka. To może wynikać ze "
"niyspiyranego formatu zbioru. TinEye spiyro ino ôbrazy JPEG, PNG, GIF, "
"BMP, TIFF i WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Tyn ôbroz je za mały, żeby znojś coś, co pasuje. TinEye potrzebuje "
"podstawowego poziōmu wizualnyj akuratności, żeby akuratnie idyntyfikować "
"pasowne ôbrazy."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Tego ôbrazu niy szło ściōngnōńć."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Ôcyna ksiōnżki"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Jakość ôd zbioru"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Rachuj matymatyczne wyrazy we posku szukanio"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Przidowek hashōw"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Kōnwertuje frazy na rozmajte skrōty hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "skrōt hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Przidowek hostnames"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Nadpisowanie DOI z ôtwartym dostympym"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Unikej płacynio za dostymp bez przekerowowanie do ôtwartych wersyji "
"publikacyji, kej sōm dostympne"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Włosne informacyje"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr ""

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr ""

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Przidowek sprawdzanio necu Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Tyn przidowek sprawdzo, jeźli adresa ôd żōndanio to je wynzoł wyjścio TOR"
" i informuje używocza, jeźli tak je. To jak check.torproject.org ino ôd "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Wymazowanie trackrōw z URL-ōw"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Wymaż argumynta trackrōw ze swrōcōnyj adresy URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr ""

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Strōna niy znojdziōno"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Idź do %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "strōny wyszukowanio"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Spōmōż"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferyncyje"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Spiyrane ôd"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "ôtwarto metawyszukowarka, co szanuje prywatność"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Kod zdrzōdłowy"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Dziynnik problymōw"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statystyki wyszukowarki"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Publiczne instancyje"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Polityka prywatności"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Skōntaktuj sie ze administratorym instancyje"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Kliknij na lupa, coby wykōnać wyszukowanie"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Dugość"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr ""

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autōr"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "buforowane"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Zacznij ôtwiyrać nowy problym na GitHubie"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Sprawdź teroźne felery ôd tego motoru na GitHubie"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Potwiyrdzōm, że niy ma teroźnego feleru, co by sie tykoł mojigo problymu"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Jeźli to je publiczno instancyjo, to podej URL we reporcie ô felerze"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Wyślij nowe zgłoszynie problymu na Github ze informacyjōm wyżyj"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Brak HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Pokoż dziynniki felerōw i wyślij report ô felerze"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr ""

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr ""

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediana"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Niypodarzōne testy weryfikacyjne: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Felery:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Ôgōlne"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Wychodne kategoryje"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interfejs używocza"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Prywatność"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Wyszukowarki"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Teroźnie używane wyszukowarki"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Ekstra zapytania"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Liczba wynikōw"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Info"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Nazod do wiyrchu"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Piyrwyjszo strōna"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Dalszo strōna"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Pokoż przodnio strōna"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Szukej..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "wysnoż"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "szukanie"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Teroz niy ma dostympnych danych. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Miano ôd wyszukowarki"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Wyniki"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Wielość wynikōw"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Czas ôdpowiedzi"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Wiarogodność"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Społym"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Przetworzanie"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Ôstrzeżynia"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Felery i wyjōntki"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Wyjōntek"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Kōmunikat"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Procynt"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Miano zbioru"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkcyjo"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kod"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Weryfikacyjo"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Niypodarzōny test"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Kōmyntorz(e)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Przikłady"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr ""

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Ôdpowiedzi"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Ściōng wyniki"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Sprōbuj wyszukać:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr ""

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL wyszukowanio"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr ""

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr ""

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Dorady"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Jynzyk wyszukowanio"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Wychodny jynzyk"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Bezpieczne szukanie"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Ścisłe"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Postrzednie"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Brak"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Zakres czasu"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Z leda kedy"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Z ôstatnigo dnia"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Z ôstatnigo tydnia"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Z ôstatnigo miesiōnca"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Z ôstatnigo roku"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informacyjo!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "teroźnie niy ma zdefiniowanych żodnych cookies."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Niystety!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr ""

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr ""

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr ""

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Zwōl"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Miano"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Ôpis"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "To je wykoz modułōw wartkij ôdpowiedzi we SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "To je wykoz przidowkōw."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Autodopołnianie"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Szukej w czasie pisanio"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Wypostrzodkowanie"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Pokoż wyniki we postrzodku strōny (ukłod Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"To je wykoz cookies i jejich werty, co SearXNG zapisuje na twojim "
"kōmputrze."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Ze pōmocōm tego wykazu możesz ôcynić przejzdrzistość SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Miano ôd cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Wert"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Wyszukej adresy URL aktualnie spamiyntanych preferyncyji"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Pozōr: ôkryślanie sztalōnkōw niysztandardowych w adresie URL wyszukowanio"
" może zmyńszyć prywatność bez przenoszynie danych do klikniyntych strōn z"
" wynikōw."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL to prziwrōcynio twojich sztalōnkōw na inkszyj przeglōndarce"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Podsystym DOI z ôtwartym dostympym"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr ""

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr ""

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Spiyro ôbrany jynzyk"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr ""

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maks. czas"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Te sztalōnki sōm trzimane we zbiorach cookies, tōż mogymy niy trzimać "
"tych danych ô ciebie."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Te zbiory cookies sużōm ino twojimu kōmfortowi, niy używōmy ich do "
"śledzynio cie."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Spamiyntej"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Prziwrōć wychodne"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Nazod"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy ôbrazōw"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Przesyłanie wynikōw ôbrazōw bez proxy SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Niyskōńczōne przewijanie"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Autōmatycznie laduj nastympno strōna przi przewijaniu do spodka teroźnyj "
"strōny"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "W jakim jynzyku wolisz wyszukować?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Metoda HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr ""

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Zapytanie we tytule ôd strōny"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Jak włōnczōne, to twoje zapytanie je we tytule ôd strōny wynikōw. Twoja "
"przeglōndarka może spamiyntać tyn tytuł"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Wyniki na nowych kartach"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Ôtwōrz linki wynikōw we nowych kartach przeglōndarki"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtruj treści"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Szukej po ôbraniu kategoryje"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tymat"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Zmiyń ukłod ôd SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Styl ôd tymatu"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Wybier autōmatyczny, żeby sie szaltrowoł podug sztalōnkōw przeglōndarki"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokyny ôd motora"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tokyny dostympu do prywatnych motorōw"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Jynzyk interfejsu"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Zmiyń jynzyk układu"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repozytoryja"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "pokoż media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "skryj mydia"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ta strōna niy podała żodnego ôpisu."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Miara zbioru"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr ""

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Typ"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr ""

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motōr"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Pokoż zdrzōdło"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresa"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "pokoż karta"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "skryj karta"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etykety"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Data publikacyje"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Cajtōng"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redachtōr"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Wydowca"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "link magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "zbiōr torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Wysyłocz"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Ściōngocz"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Wielość zbiorōw"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "pokoż wideo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "skryj wideo"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferyncyje"

#~ msgid "Scores per result"
#~ msgstr "Wyniki na rezultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "hakowalno metawyszukowarka, co szanuje prywatność"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Skrōcynie niy ma dostympne dlo tyj publikacyje."

#~ msgid "Self Informations"
#~ msgstr "Informacyje ô siebie"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Zmiyń metoda przesyłanio formularōw, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">przewiydz sie wiyncyj ô "
#~ "metodach HTTP</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Tyn przidowek sprawdzo, jeźli adresa ôd"
#~ " żōndanio to je wynzoł wyjścio TOR"
#~ " i informuje używocza, jeźli tak je."
#~ " To jak check.torproject.org ino ôd "
#~ "searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Wykoz wynzłōw wyjścio TOR "
#~ "(https://check.torproject.org/exit-addresses) niy "
#~ "ôdpowiado."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Używosz TOR. Twoja adresa IP wyglōndo na: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Niy używosz TOR. Twoja adresa IP wyglōndo na: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Autowykrywanie jynzyka wyszukowanio"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Autōmatycznie wykrywo jynzyk zapytanio i szaltruje na niego."

#~ msgid "others"
#~ msgstr "inksze"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Wyszukowarki z tyj zokłodki niy "
#~ "pokazujōm sie we wynikach wyszukowanio, "
#~ "ale możesz ich używać bez bangs."

#~ msgid "Shortcut"
#~ msgstr "Skrōt"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Wyszukowarki niy mogōm pobrać wynikōw."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Sprōbuj zaś niyskorzij abo znojdź inkszo instancyjo SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Przekeruj do ôtwartych wersyji publikacyji,"
#~ " kej sōm dostympne (potrzebne rozszyrzynie)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Zmiyń to, jak sōm wysyłane formulary,"
#~ " <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">przewiydz sie wiyncyj ô "
#~ "metodach żōndań</a>"

#~ msgid "On"
#~ msgstr "Włōnczōny"

#~ msgid "Off"
#~ msgstr "Zastawiōne"

#~ msgid "Enabled"
#~ msgstr "Włōnczōne"

#~ msgid "Disabled"
#~ msgstr "Zastawiōne"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Wykōnej wyszukowanie zaroz po ôbraniu "
#~ "kategoryje. Zastow, coby ôbrać wiyncyj "
#~ "kategoryji. (Potrzebny Javascript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Skrōty jak we Vinie"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Ruszej sie po wynikach wyszukowanio ze"
#~ " skrōtami jak we Vimie (potrzebny "
#~ "Javascript). Naciś knefel „h” na strōnie"
#~ " głōwnyj abo wynikōw, coby dostać "
#~ "pōmoc."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "niy szło znojś wynikōw. Użyj inkszego"
#~ " zapytanio abo poszukej tyż we "
#~ "inkszych kategoryjach."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Przerōb miana ôd hostōw we wynikach "
#~ "abo ôdciep wyniki na podstawie miana "
#~ "ôd hosta"

#~ msgid "Bytes"
#~ msgstr "Bajty"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Zastōmpiynie miana ôd hosta"

#~ msgid "Error!"
#~ msgstr "Feler!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Wyszukowarki niy mogōm pobrać wynikōw"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Zacznij ôtwiyrać nowy problym na GitHubie"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Gyneratōr losowych wert"

#~ msgid "Statistics functions"
#~ msgstr "Funkcyje statystyczne"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Porachuj {functions} ôd argumyntōw"

#~ msgid "Get directions"
#~ msgstr "Znojdź skazōwki"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Pokazuje twoja adresa IP, jeźli "
#~ "zapytanie to „ip”, i twojigo agynta "
#~ "używocza, jeźli zapytanie zawiyro „user "
#~ "agent”."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""

#~ msgid "Keywords"
#~ msgstr "Słowa kluczowe"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Skazowanie włosnych parametrōw we adresie "
#~ "sztalōnkōw może być używane do "
#~ "synchrōnizowanio sztalōnkōw miyndzy maszinami."

#~ msgid "proxied"
#~ msgstr "ze proxy"

