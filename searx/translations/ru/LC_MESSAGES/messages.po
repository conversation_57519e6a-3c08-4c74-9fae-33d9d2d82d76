# Russian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON>, 2017-2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2015
# <AUTHOR> <EMAIL>, 2015,2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2018
# Дмитрий <PERSON>ирев, 2016-2017
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: yurtpage <<EMAIL>>\n"
"Language-Team: Russian <https://translate.codeberg.org/projects/searxng/"
"searxng/ru/>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || ("
"n%100>=11 && n%100<=14)? 2 : 3);\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "без дополнительной разбивки на подгруппы"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "другие"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "файлы"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "общие"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "музыка"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "социальные сети"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "изображения"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "видео"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "радио"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "ТВ"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "ИТ"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "новости"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "карты"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr ".onion"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "наука"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "приложения"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "словари"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "текст песни"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "пакеты"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "вопросы-ответы"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "репозитории"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "программные вики"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "веб"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "научные публикации"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "автоматически"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "светлая"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "тёмная"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "чёрная"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Вр. работы"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "О программе"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Средняя темп."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Облачность"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Условия"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Текущие условия"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Вечер"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Ощущается как"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Влажность"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Макс. темп."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Мин. темп."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Утро"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Ночь"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Полдень"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Давление"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Восход"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Закат"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Температура"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "УФ-индекс"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Видимость"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Ветер"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Ясное небо"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Облачно"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Ясно"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Туман"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Проливной дождь с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Ливень с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Сильные ливни"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Проливной дождь"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Сильный мокрый снег с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Сильные ливни с мокрым снегом и грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Сильные ливни с мокрым снегом"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Сильный мокрый снег"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Сильный снег с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Сильный снегопад с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Сильный снегопад"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Сильный снег"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Небольшой дождь с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Слабые ливни с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Слабые ливни"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Небольшой дождь"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Небольшой мокрый снег с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Краткосрочные ливни с мокрым снегом и грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Краткосрочные ливни с мокрым снегом"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Небольшой мокрый снег"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Небольшой снег с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Небольшой снегопад с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Небольшой снегопад"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Небольшой снег"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Переменная облачность"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Дождь с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Ливни с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Ливни"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Дождь"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Мокрый снег с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Ливни с мокрым снегом и грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Ливни с мокрым снегом"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Мокрый снег"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Снег с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Снегопад с грозой"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Снегодап"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Снег"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "подписчики"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "записи"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "активные пользователи"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "комментарии"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "пользователь"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "сообщество"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "пункты"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "название"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "автор"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "открыт"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "закрыт"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "ответил"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Ничего не найдено"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Источник"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Не удалось загрузить следующую страницу"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Неправильные параметры, пожалуйста измените ваши настройки"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Неверные настройки"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "ошибка поиска"

#: searx/webutils.py:35
msgid "timeout"
msgstr "истекло время ожидания"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "ошибка разбора"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Ошибка протокола HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "ошибка сети"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "ошибка SSL: проверка сертификата провалена"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "непредвиденная ошибка"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "ошибка HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "ошибка HTTP-соединения"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "ошибка прокси"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "КАПЧА"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "слишком много запросов"

#: searx/webutils.py:58
msgid "access denied"
msgstr "доступ запрещён"

#: searx/webutils.py:59
msgid "server API error"
msgstr "ошибка API сервера"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Приостановлено"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} минут(-у) назад"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} час(ов), {minutes} минут(а) назад"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Генерирует разные случайные значения"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Вычислить {func} от аргументов"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Показать маршрут в карте .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (УСТАРЕЛО)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Эта запись была заменена на"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Канал"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "битрейт"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "голоса"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "нажатия"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Язык"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} цитирований с {firstCitationVelocityYear} года по "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Не удалось прочитать изображение по ссылки. Возможно это вызвано "
"неподдерживаемым форматом файла. TinEye поддерживает только следующие "
"форматы: JPEG, PNG, GIF, BMP, TIFF or WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Изображение слишком простое для нахождения похожих. TinEye требует "
"базовый уровень визуальных деталей для успешного определения совпадений."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Не удалось загрузить изображение."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Рейтинг книги"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Качество файла"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Чёрный список Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Отфильтровать найденные onion-ссылки, входящие в чёрный список Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Простой калькулятор"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Считать математические выражения в строке поиска"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Хеш плагин"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Рассчитывает контрольные суммы от строки."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "контрольная сумма"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Плагин имён хостов"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Переписывать имена хостов, удалять и приоритизировать результаты в "
"зависимости от имён хостов"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Искать Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Пробовать избегать платного доступа путём перенаправления на открытые "
"версии публикаций"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Информация о себе"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Отображает ваш IP, если запрос \"ip\", и ваш user agent, если запрос "
"\"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Ваш IP-адрес: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Информация о вашем браузере: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Плагин проверки Tor'a"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Этот плагин проверяет, принадлежит ли адрес запроса выходному узлу Tor и "
"информирует пользователя если это так; как check.torproject.org, но от "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Не удалось загрузить список выходных узлов Tor из"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Вы используете Tor и кажется что у вас есть внешний айпи адрес"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Вы не используете Tor и у вас внешний IP-адрес"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Убрать отслеживание URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Удаление параметров для отслеживания пользователя из URL-адреса"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Плагин - конвертер единиц измерения"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Преобразовать единицы измерения"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Страница не найдена"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Перейти к %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "страница поиска"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Пожертвовать"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Настройки"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Работает на"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "открытая метапоисковая система, соблюдающая конфиденциальность"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Исходный код"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Сообщить о проблеме"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Статистика по поисковым системам"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Публичные зеркала"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Политика конфиденциальности"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Сопровождающий текущего зеркала"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Нажмите на лупу, чтобы выполнить поиск"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Длительность"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Просмотры"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Автор"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "веб-архив"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Создайте задачу на GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Пожалуйста проверьте ныне существующие ошибки этого движка на GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Я подтверждаю, что не существует ошибки, связанной со встретившейся мне "
"проблемой"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Если это публичное зеркало, пожалуйста, укажите ссылку в баг-репорте"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Отправить новое сообщение о проблеме на Github, включая вышеуказанную "
"информацию"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Без HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Просмотр журнала ошибок и отправка отчета об ошибках"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang для этого движка"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang для его категорий"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Медиана"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Проваленные проверки: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Ошибки:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Общие"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Категории по умолчанию"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Внешний вид"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Конфиденциальность"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Поисковые системы"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Используемые поисковые системы"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Особые запросы"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Количество результатов"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Информация"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Наверх"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Предыдущая страница"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Следующая страница"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Показать главную страницу"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Искать..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "очистить"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "поиск"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "На данный момент данные недоступны. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Поисковая система"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Попаданий"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Число результатов"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Время отклика"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Надёжность"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Всего"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Обработка"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Предупреждения"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Ошибки и исключения"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Исключение"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Сообщение"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Процент"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Параметр"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Имя файла"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Функция"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Код"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Проверщик"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Неудачный тест"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Комментарии"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Пример"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Определения"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Синонимы"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Ощущается как"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Ответы"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Скачать результаты"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Попробуйте поискать:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Сообщения от поисковых систем"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "сек."

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Ссылка поиска"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Скопировано"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Копировать"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Предложения"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Язык поиска"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Язык по умолчанию"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Авто-определение"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Безопасный поиск"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Строгий"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Умеренный"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Отключен"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Временной диапазон"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Когда угодно"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Последние сутки"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Последняя неделя"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Последний месяц"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Последний год"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Информация!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "в данный момент cookie-файлы не определены."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Извините!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Не было найдено никаких результатов. Вы можете попробовать:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Больше никаких результатов нет. Вы можете попробовать:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Обновите страницу."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Выполните поиск с другим запросом или выберите другую категорию (выше)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Измените поисковую систему, указанную в настройках:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Поменяйте инстанцию на другую:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Найдите другой запрос или выберите другую категорию."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Вернитесь на предыдущую страницу с помощью кнопки предыдущей страницы."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Использовать"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Ключевые слова (первое слово в запросе)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Название"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Описание"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Модули SearXNG с мгновенным ответом."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Список плагинов."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Автодополнение"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Показывать предложения по мере ввода запроса"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Выравнивание по центру"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Отображать результаты по центру страницы (макет Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "Список cookies и их значений, которые SearXNG хранит в вашем браузере."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "SearXNG ничего от вас не скрывает."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Значение"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL с сохраненными настройками"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Внимание: использование URL с параметрами может привести к утечке данных "
"на сайты, открытые из результатов поиска."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL-адрес для восстановления ваших настроек в другом браузере"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"URL-адрес, хранящий ваши настройки. Этот URL-адрес можно использовать для"
" восстановления настроек с другого устройства."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Скопировать хэш настроек"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Вставить скопированный хэш настроек (без URL-адреса) для их восстановления"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Хэш настроек"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Цифровой идентификатор объекта (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Источник Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr ""
"Выберите службу, используемую переписыванием «Цифрового идентификатора "
"объекта»"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Эта вкладка не существует в пользовательском интерфейсе, но вы можете "
"искать в этих системах по ее !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Включить всё"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Отключить всё"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Поддерживает выбранный язык"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Вес"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Максимальное время"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Получение значков сайтов"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Показывать значки сайтов около результатов поиска"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Все настройки сохраняются в cookie вашего браузера. Это позволяет нам не "
"хранить о вас никаких данных на серверах."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Cookie нужны исключительно для вашего удобства, мы не используем cookie "
"для слежки."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Сохранить"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Восстановить настройки по умолчанию"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Назад"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Горячие клавиши"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Наподобие Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Перемещаться по результатам поиска при помощи горячих клавиш (необходим "
"JavaScript). Нажмите клавишу \"h\" на главной странице или странице "
"результатов поиска для получения помощи."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Прокси для картинок"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Проксировать изображения в результатах методами SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Бесконечная прокрутка"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Автоматически загружать следующую страницу при прокрутке до конца страницы"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Какой язык предпочтителен для поиска?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Выберите Авто-определение, чтобы SearXNG сам определял язык вашего "
"запроса."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Метод запросов"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Изменить содержание форм"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Поисковый запрос в заголовке страницы"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Добавить поисковый запрос в заголовок страницы с результатами. Браузер "
"может сохранять этот заголовок"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Результаты в новых вкладках"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Открывать результаты поиска в новых вкладках"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Отбирает только пристойные результаты"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Поиск по выбранной категории"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Выполняйте мгновенный поиск при выборе категории. Выключите для выбора "
"нескольких категорий сразу"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Тема"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Изменить расположение элементов SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Стиль темы"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Выберите \"автоматически\" для использования настроек вашего браузера"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Токены движка"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Доступные токены для частных движков"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Язык интерфейса"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Изменить язык интерфейса"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Отображение URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Красивое"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Полное"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Имя хоста"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Формат отражения URL в результатах"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "Репозиторий"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "показать медиа"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "скрыть медиа"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Этот сайт не предоставил описания."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Размер файла"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Дата"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Тип"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Разрешение"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Формат"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Движок"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Перейти к источнику"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "адрес"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "показать карту"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "скрыть карту"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Версия"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Сопровождающий"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Обновлено"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Теги"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Популярность"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Лицензия"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Проект"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Страница проекта"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Дата публикации"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Журнал"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Редактор"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Издатель"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet-ссылка"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "торрент-файл"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Сиды"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Личи"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Количество файлов"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "показать видео"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "скрыть видео"

#~ msgid "Engine time (sec)"
#~ msgstr "Время поиска (сек)"

#~ msgid "Page loads (sec)"
#~ msgstr "Загрузка страниц (сек)"

#~ msgid "Errors"
#~ msgstr "Ошибки"

#~ msgid "CAPTCHA required"
#~ msgstr "Требуется капча"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Заменять в ссылках HTTP на HTTPS если это возможно"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "По умолчанию результаты открываются в "
#~ "том же окне. Этот плагин переопределяет"
#~ " поведение по умолчанию для открытия "
#~ "ссылок в новых вкладках/окнах. (Требуется "
#~ "JavaScript)"

#~ msgid "Color"
#~ msgstr "Цвет"

#~ msgid "Blue (default)"
#~ msgstr "Синий (по умолчанию)"

#~ msgid "Violet"
#~ msgstr "Фиолетовый"

#~ msgid "Green"
#~ msgstr "Зеленый"

#~ msgid "Cyan"
#~ msgstr "Бирюзовый"

#~ msgid "Orange"
#~ msgstr "Оранжевый"

#~ msgid "Red"
#~ msgstr "Красный"

#~ msgid "Category"
#~ msgstr "Категория"

#~ msgid "Block"
#~ msgstr "Блокировать"

#~ msgid "original context"
#~ msgstr "исходный контекст"

#~ msgid "Plugins"
#~ msgstr "Плагины"

#~ msgid "Answerers"
#~ msgstr "Ответчики"

#~ msgid "Avg. time"
#~ msgstr "Среднее время"

#~ msgid "show details"
#~ msgstr "показать подробности"

#~ msgid "hide details"
#~ msgstr "скрыть подробности"

#~ msgid "Load more..."
#~ msgstr "Загрузить еще…"

#~ msgid "Loading..."
#~ msgstr "Загрузка..."

#~ msgid "Change searx layout"
#~ msgstr "Изменить вид сайта"

#~ msgid "Proxying image results through searx"
#~ msgstr "Проксировать найденные изображения с помощью searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Это список модулей мгновенного ответа searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Это список cookie-файлов и их значения,"
#~ " которые searx хранит на Вашем "
#~ "компьютере."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "С помощью этого списка можно изменить прозрачность searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Похоже, вы используете searx впервые."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Пожалуйста, попробуйте позже или воспользуйтесь другим сервером searx."

#~ msgid "Themes"
#~ msgstr "Темы"

#~ msgid "Reliablity"
#~ msgstr "Надежность"

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Cпособ"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "На этой вкладке отсутсвуют результаты, "
#~ "но вы можете использовать поисковики "
#~ "перечисленные ниже."

#~ msgid "Advanced settings"
#~ msgstr "Дополнительные настройки"

#~ msgid "Close"
#~ msgstr "Закрыть"

#~ msgid "Language"
#~ msgstr "Язык"

#~ msgid "broken"
#~ msgstr "сломанный"

#~ msgid "supported"
#~ msgstr "поддерживается"

#~ msgid "not supported"
#~ msgstr "не поддерживается"

#~ msgid "about"
#~ msgstr "О сайте"

#~ msgid "Avg."
#~ msgstr "примерно"

#~ msgid "User Interface"
#~ msgstr "Внешний вид"

#~ msgid "Choose style for this theme"
#~ msgstr "Цветовое решение для выбранной темы"

#~ msgid "Style"
#~ msgstr "Стиль"

#~ msgid "Show advanced settings"
#~ msgstr "Дополнительные настройки"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Развернуть дополнительные настройки на главной странице"

#~ msgid "Allow all"
#~ msgstr "Выбрать все"

#~ msgid "Disable all"
#~ msgstr "Выключить все"

#~ msgid "Selected language"
#~ msgstr "Выбранный язык"

#~ msgid "Query"
#~ msgstr "Запрос"

#~ msgid "save"
#~ msgstr "Сохранить"

#~ msgid "back"
#~ msgstr "Назад"

#~ msgid "Links"
#~ msgstr "Ссылки"

#~ msgid "RSS subscription"
#~ msgstr "RSS-подписка"

#~ msgid "Search results"
#~ msgstr "Результаты поиска"

#~ msgid "next page"
#~ msgstr "следующая страница"

#~ msgid "previous page"
#~ msgstr "предыдущая страница"

#~ msgid "Start search"
#~ msgstr "Начать поиск"

#~ msgid "Clear search"
#~ msgstr "Очистить запрос"

#~ msgid "Clear"
#~ msgstr "Очистить"

#~ msgid "stats"
#~ msgstr "статистика"

#~ msgid "Heads up!"
#~ msgstr "Вот чёрт!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Похоже, вы используете SearXNG впервые."

#~ msgid "Well done!"
#~ msgstr "Отлично!"

#~ msgid "Settings saved successfully."
#~ msgstr "Настройки успешно сохранены."

#~ msgid "Oh snap!"
#~ msgstr "Вот черт!"

#~ msgid "Something went wrong."
#~ msgstr "Что-то пошло не так."

#~ msgid "Date"
#~ msgstr "Дата"

#~ msgid "Type"
#~ msgstr "Тип"

#~ msgid "Get image"
#~ msgstr "Скачать картинку"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "Настройки"

#~ msgid "Scores per result"
#~ msgstr "Попаданий за результат"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "открытая метапоисковая система, уважающая приватность"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Нет доступного примечания для этой публикации."

#~ msgid "Self Informations"
#~ msgstr "Информация"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Способ отправки запросов. <a "
#~ "href=\"http://ru.wikipedia.org/wiki/HTTP#Методы\" "
#~ "rel=\"external\">Подробнее о методах HTTP</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Этот плагин проверяет, не является ли"
#~ " запрошенный адрес выходным узлом Tor'a,"
#~ " и информирует пользователя, если это "
#~ "так, как check.torproject.org, но от "
#~ "searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Список выходных узлов Tor'a "
#~ "(https://check.torproject.org/exit-addresses) недоступен."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Вы используете Tor. Ваш IP адрес может быть: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Вы не используете Tor. Ваш IP адрес может быть: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Автоматически определять язык поиска"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Автоматически определять язык поиска запроса и переключаться на него."

#~ msgid "others"
#~ msgstr "Другие"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Результаты из этого раздела не "
#~ "отображаются в общих, но вы можете "
#~ "использовать эти поисковые движки через "
#~ "восклицательный знак."

#~ msgid "Shortcut"
#~ msgstr "Сокращение"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Эта вкладка не существует в "
#~ "пользовательском интерфейсе, но вы можете "
#~ "искать в этих системах по ее "
#~ "!bangs."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Поисковые системы не могут получить результат."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Пожалуйста, попробуйте ещё раз позднее, "
#~ "либо перейдите на другое зеркало "
#~ "SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Перенаправлять на открытые версии публикаций"
#~ " при их наличии (требуется плагин)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Способ отправки запросов. <a "
#~ "href=\"http://ru.wikipedia.org/wiki/HTTP#Методы\" "
#~ "rel=\"external\">Подробнее о методах HTTP</a>"

#~ msgid "On"
#~ msgstr "Включено"

#~ msgid "Off"
#~ msgstr "Выключено"

#~ msgid "Enabled"
#~ msgstr "Включено"

#~ msgid "Disabled"
#~ msgstr "Выключено"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Выполнять поиск немедленно, если выбрана "
#~ "категория. Отключите для выбора нескольких "
#~ "категорий. (требуется JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Горячие клавиши в стиле Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Навигация по результатам поиска с "
#~ "помощью горячих клавиш в стиле Vim "
#~ "(требуется JavaScript). Чтобы получить "
#~ "справку, нажмите клавишу \"h\" на "
#~ "главной странице или на страницах "
#~ "результатов."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "мы не нашли никаких результатов. "
#~ "Попробуйте изменить запрос или поищите в"
#~ " других категориях."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Заменить имя хоста или удалить результаты на основе имени хоста"

#~ msgid "Bytes"
#~ msgstr "Байт"

#~ msgid "kiB"
#~ msgstr "КиБ"

#~ msgid "MiB"
#~ msgstr "МиБ"

#~ msgid "GiB"
#~ msgstr "ГиБ"

#~ msgid "TiB"
#~ msgstr "ТиБ"

#~ msgid "Hostname replace"
#~ msgstr "Замена имени сайта"

#~ msgid "Error!"
#~ msgstr "Ошибка!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Поисковые системы не могут получить результат"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Откройте issue на GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Генератор случайных значений"

#~ msgid "Statistics functions"
#~ msgstr "Статистические функции"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Применяет функции {functions} к аргументам"

#~ msgid "Get directions"
#~ msgstr "Запрашивать маршруты"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Показывать ваш IP-адрес по запросу "
#~ "\"ip\" и информацию о браузере по "
#~ "запросу \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Не удалось загрузить список выходных "
#~ "узлов Tor с адреса "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Вы не используете Tor. Ваш публичный IP-адрес: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Вы не используете Tor, и у вас "
#~ "следующий публичный IP-адрес: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Ключевые слова"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "URL-адреса с пользовательскими настройками "
#~ "можно использовать для синхронизации настроек"
#~ " между устройствами."

#~ msgid "proxied"
#~ msgstr "через прокси"
