# Chinese (Traditional, Taiwan) translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2017,2019
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024.
# morenewsavailable
# <<EMAIL>>, 2024, 2025.
# <AUTHOR> <EMAIL>,
# 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: Shihfu Juan <<EMAIL>>\n"
"Language-Team: Chinese (Traditional Han script) <https://"
"translate.codeberg.org/projects/searxng/searxng/zh_Hant/>\n"
"Language: zh_Hant_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "沒有進一步分組"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "其他"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "檔案"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "一般"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "音樂"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "社群媒體"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "圖片"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "影片"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "廣播電臺"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "電視"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "資訊科技"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "新聞"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "地圖"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "洋蔥"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "科學"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "應用程式"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "字典"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "歌詞"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "套件"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "問答"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "版本庫"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "軟體維基"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "網站"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "學術文章"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "自動"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "明亮"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "黑暗"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "黑色"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "上線時間"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "關於"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "平均溫度"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "雲量"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "狀況"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "目前狀況"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "傍晚"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "體感溫度"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "溼度"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "最高溫度"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "最低溫度"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "早上"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "晚上"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "中午"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "氣壓"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "日出"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "日落"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "氣溫"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "紫外線指數"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "能見度"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "風"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "晴天"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "多雲"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "晴"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "霧"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "雷暴雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "強雷陣雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "強陣雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "暴雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "強雷雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "強雷陣雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "強陣雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "強雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "雷暴雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "強雷陣雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "強陣雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "暴雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "雷伴小雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "弱雷伴陣雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "弱陣雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "小雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "雷伴小雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "雷伴弱陣雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "弱陣雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "小雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "雷伴小雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "雷伴弱陣雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "弱陣雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "小雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "局部多雲"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "雷雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "雷陣雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "陣雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "雷雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "雷陣雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "陣雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "雨夾雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "雷雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "雷陣雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "陣雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "雪"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "訂閱數"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "貼文"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "活躍使用者"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "留言"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "使用者"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "社群"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "積分"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "標題"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "作者"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "開啟"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "已關閉"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "已解答"

#: searx/webapp.py:292
msgid "No item found"
msgstr "找不到項目"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "來源"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "載入下個頁面時發生錯誤"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "無效的設定，請編輯您的偏好設定"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "無效的設定"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "搜尋錯誤"

#: searx/webutils.py:35
msgid "timeout"
msgstr "逾時"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "解析錯誤"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP 協議錯誤"

#: searx/webutils.py:38
msgid "network error"
msgstr "網路錯誤"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL錯誤：憑證驗證失敗"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "意外崩潰"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP 錯誤"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP 連接錯誤"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "代理錯誤"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "驗證碼"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "請求過於頻繁"

#: searx/webutils.py:58
msgid "access denied"
msgstr "拒絕存取"

#: searx/webutils.py:59
msgid "server API error"
msgstr "伺服器 API 錯誤"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "暫停服務"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} 分鐘前"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} 小時 {minutes} 分鐘前"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "產生不同的隨機數值"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "計算參數的 {func}"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "在地圖上顯示路線 .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title}（已過時）"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "此條目已被以下內容取代"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "頻道"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "位元速率"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "投票數"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "點擊數"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "語言"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{firstCitationVelocityYear} 至 {lastCitationVelocityYear} 間被引用 "
"{numCitations} 次"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr "無法存取該影像網址。這可能是因為不支援的文件格式造成的。TinEye 只支援 JPEG、PNG、GIF、BMP、TIFF 或 WebP 的影像。"

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr "影像過於簡單以致無法找到相符的結果。TinEye 需要具備基本的視覺細節才能成功識別匹配項。"

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "圖片無法下載。"

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "書籍評分"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "文件品質"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia 黑名單"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "過濾掉 Ahmia 黑名單中出現的洋蔥結果。"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "基本計算機"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "藉由搜尋欄計算數學式"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "雜湊 (Hash) 外掛"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "將字串轉換為不同的雜湊摘要值。"

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "雜湊摘要值"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "主機名稱外掛"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "重寫主機名稱、移除結果或根據主機名稱決定其優先級別"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "開放存取 DOI 重寫"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "盡可能重新導向至出版品的開放存取版本，以避免收費牆"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "自身訊息"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr "如果查詢的是 \"ip\"，則顯示您的 IP； 如果查詢的是 \"user-agent\"，則顯示您的使用者代理。"

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "您的 IP 是： "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "您的使用者代理是： "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor 網路檢測外掛"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr "此外掛程式檢查請求的位址是否為 Tor 退出節點，並通知使用者；類似於 check.torproject.org，但來自 SearXNG。"

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "無法從以下位置下載 Tor 出口節點列表"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "您正在使用 Tor，並且您似乎有外部 IP 地址"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "您未使用 Tor，並且您有外部 IP 地址"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "追蹤器 URL 移除器"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "從傳回的 URL 中移除追蹤器參數"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "單位轉換外掛"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "在單位之間轉換"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "找不到頁面"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "前往 %(search_page)s。"

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "搜尋頁面"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "捐款"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "偏好設定"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "技術支援"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "尊重隱私的開源元搜尋引擎"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "原始碼"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "問題追蹤"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "引擎統計"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "公開站臺"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "隱私權條款"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "聯絡站點維護人員"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "點選放大鏡以執行搜尋"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "長度"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "檢視次數"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "作者"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "快照"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "在 GitHub 上提交問題"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "請在 GitHub 上檢查是否已有關於此引擎的已知錯誤"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "我確認提交的問題不在目前問題列表"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "如果這是公開站臺， 請在問題報告中附上 URL"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "在 GitHub 上提交包含上述資訊的新問題"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "無 HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "檢視錯誤日誌並提交錯誤報告"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "對於這個引擎的 !bang"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "對於其類別的 !bang"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "中位數"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "檢查程式測試失敗： "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "錯誤："

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "一般"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "預設分類"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "使用者介面"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "隱私"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "搜尋引擎"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "目前使用的搜尋引擎"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "特殊查詢"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "結果筆數"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "資訊"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "返回頂端"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "上一頁"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "下一頁"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "顯示前端頁面"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "搜尋..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "清除"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "搜尋"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "目前無可用資料。"

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "引擎名稱"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "分數"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "結果數量"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "反應時間"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "可靠性"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "總計"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "正在處理"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "警告"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "錯誤及異常"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "異常"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "訊息"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "百分比"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "參數"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "檔名"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "函式"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "程式碼"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "檢查程式"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "測試未透過"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "注釋"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "範例"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "定義"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "同義詞"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "體感溫度"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "答案"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "下載結果"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "嘗試搜尋："

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "來自搜尋引擎的訊息"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "秒"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "搜尋網址"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "已複製"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "複製"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "建議"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "搜尋語言"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "預設語言"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "自動偵測"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "安全搜尋"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "嚴格"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "適中"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "無"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "時間範圍"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "不限時間"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "一天內"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "一週內"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "一個月內"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "一年內"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "資訊！"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "目前沒有已儲存的 Cookie。"

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "抱歉！"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "未找到任何結果。您可以嘗試："

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "沒有更多的結果。您可以嘗試："

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "重新整理頁面。"

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "請以其他關鍵字搜尋或選擇其他類別（最上方）。"

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "在偏好設定裡更改使用的搜尋引擎："

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "切換至另一個站點："

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "請以其他關鍵字搜尋或選擇其他類別。"

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "使用上一頁按鈕返回上一頁。"

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "允許"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "關鍵字（查詢中的第一個詞）"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "名稱"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "描述"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "這是 SearXNG 的即時回應模組列表。"

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "這是外掛列表。"

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "自動完成"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "隨打即找"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "置中對齊"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "顯示搜尋結果於網頁中間（Oscar 版面配置）。"

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "這是 SearXNG 在您的裝置上儲存的 Cookie 列表及其對應的值。"

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "您可以根據此列表評估 SearXNG 的透明度。"

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Cookie 名稱"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "值"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "目前偏好設定的搜尋 URL"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr "注意：在搜尋 URL 中指定自訂設定可能會降低隱私，因為這可能會將資料洩漏至點擊的結果網站。"

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "用於在其他瀏覽器上還原您的偏好設定的網址"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr "包含您的偏好設定的 URL。此 URL 可用於在其他裝置上恢復您的設定。"

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "複製偏好設定雜湊值"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "插入複製（不帶 URL）的偏好設定雜湊來恢復"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "偏好設定雜湊"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "數位物件識別符（DOI）"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "開放存取 DOI 解析器"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "選擇 DOI 重寫所使用的服務"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr "這個分頁在使用者介面中不存在，但您可以用 !bang 在這些引擎中進行搜尋。"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "全部啟用"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "全部停用"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "支援選定的語言"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "權重"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "最大時間"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "網站圖標解析器"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "在搜尋結果旁顯示網站圖標"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr "這些設定只會儲存在您的 cookies 中，這樣我們無需也不會儲存關於您的資訊。"

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr "這些 cookies 只是為了提升您使用時的便利性，我們不會用來追蹤您的行為。"

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "儲存"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "重設為預設值"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "返回"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "快速鍵"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim 風格"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr "使用快捷鍵來瀏覽搜尋頁面（需要Javascript）。按 H 開啟說明頁面。"

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "圖片代理"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "透過 SearXNG 代理存取圖片結果"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "無限捲動"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "當捲動至目前頁面的底端時自動載入下一頁"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "您偏好用哪種語言搜尋？"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "選擇自動偵測讓 SearXNG 自動判斷您的搜尋語言。"

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP 方法"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "變更提交表單的方式"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "頁面標題顯示查詢關鍵字"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr "啟用時，結果頁的標題將包含您的查詢關鍵字。您的瀏覽器會記錄這個標題"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "在新分頁開啟結果"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "在新瀏覽器分頁中開啟結果連結"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "過濾內容"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "類別選取搜尋"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr "選擇一個類別時自動搜尋。關閉來選擇多項類別"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "主題"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "更改 SearXNG 版面配置"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "主題樣式"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "選擇自動以遵循您的瀏覽器設定"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "引擎權杖"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "私人引擎的存取權杖"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "介面語言"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "變更版面配置的語言"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL 格式"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "美觀"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "完整"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "主機"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "更改結果的 URL 格式"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "儲存庫"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "顯示媒體"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "隱藏媒體"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "此網站未提供任何描述。"

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "檔案大小"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "日期"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "分類"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "解析度"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "格式"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "引擎"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "檢視來源"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "地址"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "顯示地圖"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "隱藏地圖"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "版本"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "維護者"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "更新於"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "標籤"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "知名度"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "授權"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "專案"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "專案主頁"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "發布日期"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "期刊"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "編輯"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "出版者"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "國際標準期刊號（ISSN）"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "國際標準書號（ISBN）"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "磁力連結"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "種子檔案"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "做種用戶數"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "下載用戶數"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "檔案數量"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "顯示影片"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "隱藏影片"

#~ msgid "Engine time (sec)"
#~ msgstr "引擎時間（秒）"

#~ msgid "Page loads (sec)"
#~ msgstr "頁面載入（秒）"

#~ msgid "Errors"
#~ msgstr "錯誤"

#~ msgid "CAPTCHA required"
#~ msgstr "需要 CAPTCHA"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "可以的話將 HTTP 連結重寫為 HTTPS"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr "結果預設會在同一個視窗開啟。這個外掛程式會覆寫預設行為，會在新分頁／視窗中開啟連結。（需要 JavaScript）"

#~ msgid "Color"
#~ msgstr "顏色"

#~ msgid "Blue (default)"
#~ msgstr "藍色（預設值）"

#~ msgid "Violet"
#~ msgstr "紫色"

#~ msgid "Green"
#~ msgstr "綠色"

#~ msgid "Cyan"
#~ msgstr "青色"

#~ msgid "Orange"
#~ msgstr "橘色"

#~ msgid "Red"
#~ msgstr "紅色"

#~ msgid "Category"
#~ msgstr "分類"

#~ msgid "Block"
#~ msgstr "封鎖"

#~ msgid "original context"
#~ msgstr "原始內容"

#~ msgid "Plugins"
#~ msgstr "外掛程式"

#~ msgid "Answerers"
#~ msgstr "答案"

#~ msgid "Avg. time"
#~ msgstr "平均時間"

#~ msgid "show details"
#~ msgstr "顯示詳情"

#~ msgid "hide details"
#~ msgstr "隱藏詳情"

#~ msgid "Load more..."
#~ msgstr "載入更多……"

#~ msgid "Loading..."
#~ msgstr "正在載入……"

#~ msgid "Change searx layout"
#~ msgstr "變更 searx 佈局"

#~ msgid "Proxying image results through searx"
#~ msgstr "透過 searx 代理圖片結果"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "這是 searx 的即時回覆模組清單。"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "這是 searx 儲存在您電腦上的 cookies 與它們的值的清單。"

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "有了這份清單，您就可以評估 searx 的透明度。"

#~ msgid "It look like you are using searx first time."
#~ msgstr "看起來您是第一次使用 searx。"

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "請再試一次或是使用其他 searx 實體搜尋。"

#~ msgid "Themes"
#~ msgstr "佈景主題"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "方法"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "進階設定"

#~ msgid "Close"
#~ msgstr "關閉"

#~ msgid "Language"
#~ msgstr "語言"

#~ msgid "broken"
#~ msgstr "故障"

#~ msgid "supported"
#~ msgstr "支援"

#~ msgid "not supported"
#~ msgstr "不支援"

#~ msgid "about"
#~ msgstr "關於"

#~ msgid "Avg."
#~ msgstr "平均"

#~ msgid "User Interface"
#~ msgstr "用戶界面"

#~ msgid "Choose style for this theme"
#~ msgstr "選擇這個主題的樣式"

#~ msgid "Style"
#~ msgstr "樣式"

#~ msgid "Show advanced settings"
#~ msgstr "顯示高級設置"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "首頁默認顯示高級設置面板"

#~ msgid "Allow all"
#~ msgstr "允許全部"

#~ msgid "Disable all"
#~ msgstr "停用全部"

#~ msgid "Selected language"
#~ msgstr "已選取的語言"

#~ msgid "Query"
#~ msgstr "查詢"

#~ msgid "save"
#~ msgstr "儲存"

#~ msgid "back"
#~ msgstr "返回"

#~ msgid "Links"
#~ msgstr "連結"

#~ msgid "RSS subscription"
#~ msgstr "RSS 訂閱"

#~ msgid "Search results"
#~ msgstr "搜尋結果"

#~ msgid "next page"
#~ msgstr "下一頁"

#~ msgid "previous page"
#~ msgstr "上一頁"

#~ msgid "Start search"
#~ msgstr "開始搜尋"

#~ msgid "Clear search"
#~ msgstr "清除搜尋"

#~ msgid "Clear"
#~ msgstr "清除"

#~ msgid "stats"
#~ msgstr "統計"

#~ msgid "Heads up!"
#~ msgstr "注意！"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "這似乎是您首次使用 SearXNG。"

#~ msgid "Well done!"
#~ msgstr "很好！"

#~ msgid "Settings saved successfully."
#~ msgstr "設定成功儲存。"

#~ msgid "Oh snap!"
#~ msgstr "糟糕！"

#~ msgid "Something went wrong."
#~ msgstr "發生了一點問題。"

#~ msgid "Date"
#~ msgstr "日期"

#~ msgid "Type"
#~ msgstr "類型"

#~ msgid "Get image"
#~ msgstr "取得圖片"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "偏好設定"

#~ msgid "Scores per result"
#~ msgstr "每個結果的分數"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "一個尊重隱私，可再開發的集合式搜尋引擎"

#~ msgid "No abstract is available for this publication."
#~ msgstr "此出版品無可用摘要。"

#~ msgid "Self Informations"
#~ msgstr "自身訊息"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "變更遞交形式，<a href=\"https://zh.wikipedia.org/wiki/超文本传输协议#请求方法\""
#~ " rel=\"external\">看看更多關於請求方法的介紹</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr "這會檢查 IP 是否 Tor 出口節點，並通知用戶。類近於 check.torproject.org。"

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr "無法獲取 Tor 出口節點列表 (https://check.torproject.org/exit-addresses)。"

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "目前您正在使用 Tor，IP 地址： {ip_address}。"

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "目前您沒有使用 Tor，IP 地址：{ip_address}。"

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "自動偵測搜尋語言"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "由搜尋字串自動偵測語言，並作為搜尋語言來使用。"

#~ msgid "others"
#~ msgstr "其他"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr "這個標籤頁不會顯示在搜索結果中，但您可以通過 ! 搜索這裡列出的引擎。"

#~ msgid "Shortcut"
#~ msgstr "快捷鍵"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "引擎無法擷取結果。"

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "請稍後再試，或換用其他 SearXNG 站點。"

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr "盡可能重新導向至出版品的開放存取版本（需要外掛程式）"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "變更遞交形式，<a href=\"https://zh.wikipedia.org/wiki/超文本传输协议#请求方法\""
#~ " rel=\"external\">看看更多關於請求方法的介紹</a>"

#~ msgid "On"
#~ msgstr "開啟"

#~ msgid "Off"
#~ msgstr "關閉"

#~ msgid "Enabled"
#~ msgstr "已啟用"

#~ msgid "Disabled"
#~ msgstr "已停用"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr "若分類被選取時立刻執行搜尋。停用以選取多個分類。（需要 JavaScript）"

#~ msgid "Vim-like hotkeys"
#~ msgstr "類 Vim 快捷鍵"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr "以類 Vim 的快捷鍵導覽搜尋結果（需要 JavaScript）。在主頁面或結果頁面按「h」鍵以取得說明。"

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr "我們找不到任何結果。請使用其他搜尋方式或在更多分類中搜尋。"

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "重寫結果的主機名或在此主機名移除結果"

#~ msgid "Bytes"
#~ msgstr "位元組"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "主機名替換"

#~ msgid "Error!"
#~ msgstr "錯誤！"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "引擎無法擷取結果"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "在 GitHub 上提交問題"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "隨機數值產生器"

#~ msgid "Statistics functions"
#~ msgstr "統計功能"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "計算 {functions} 參數"

#~ msgid "Get directions"
#~ msgstr "取得路線"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr "若搜尋字串為「ip」則顯示您的 IP，而若是「user agent」則顯示您的使用者代理字串。"

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr "無法從如下地址下載 Tor 出口節點的名單: https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "您似乎在使用 Tor，您的外部 IP 地址為: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "您並未使用 Tor，您的外部 IP 地址為: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "關鍵字"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr "帶有偏好設定的 URL 可讓您將偏好設定同步至其他裝置。"

#~ msgid "proxied"
#~ msgstr "已代理"
