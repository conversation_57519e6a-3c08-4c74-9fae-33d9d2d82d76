# pap translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
# Angelo <PERSON> <<EMAIL>>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2022-10-14 14:11+0000\n"
"PO-Revision-Date: 2022-07-06 00:21+0000\n"
"Last-Translator: <PERSON> <aluid<PERSON>@stargue.com>\n"
"Language: pap\n"
"Language-Team: Papiamento "
"<https://weblate.bubu1.eu/projects/searxng/searxng/pap/>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#. CONSTANT_NAMES['DEFAULT_GROUP_NAME']
#: searx/searxng.msg
msgid "others"
msgstr "otronan"

#. CONSTANT_NAMES['OTHER_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "otro"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "filenan"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "general"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muzik"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "media sosial"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "imagenan"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videonan"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr ""

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "notisia"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "siboyonan"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "siensia"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "apps"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "dikshonario"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "letranan"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketenan"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "q&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repos"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "software wikinan"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr ""

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "outo"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "lus"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "skur"

#: searx/webapp.py:164
msgid "timeout"
msgstr "timeout"

#: searx/webapp.py:165
msgid "parsing error"
msgstr ""

#: searx/webapp.py:166
msgid "HTTP protocol error"
msgstr ""

#: searx/webapp.py:167
msgid "network error"
msgstr ""

#: searx/webapp.py:168
msgid "SSL error: certificate validation has failed"
msgstr ""

#: searx/webapp.py:170
msgid "unexpected crash"
msgstr ""

#: searx/webapp.py:177
msgid "HTTP error"
msgstr ""

#: searx/webapp.py:178
msgid "HTTP connection error"
msgstr ""

#: searx/webapp.py:184
msgid "proxy error"
msgstr ""

#: searx/webapp.py:185
msgid "CAPTCHA"
msgstr ""

#: searx/webapp.py:186
msgid "too many requests"
msgstr "demasiado petishon"

#: searx/webapp.py:187
msgid "access denied"
msgstr "akseso ninga"

#: searx/webapp.py:188
msgid "server API error"
msgstr ""

#: searx/webapp.py:365
msgid "No item found"
msgstr ""

#: searx/engines/qwant.py:217
#: searx/templates/simple/result_templates/images.html:20 searx/webapp.py:367
msgid "Source"
msgstr "Fuente"

#: searx/webapp.py:369
msgid "Error loading the next page"
msgstr ""
"Ora skroling ta aktivá, e mensahe aki ta aparesé abou riba e página ora e"
" siguiente página no por wòrdu di presentá."

#: searx/webapp.py:521 searx/webapp.py:953
msgid "Invalid settings, please edit your preferences"
msgstr "E settingnan ta inválido, por fabor kambia bo preferensianan"

#: searx/webapp.py:537
msgid "Invalid settings"
msgstr "Settingnan ta inválido"

#: searx/webapp.py:614 searx/webapp.py:690
msgid "search error"
msgstr ""

#: searx/webapp.py:852
msgid "Suspended"
msgstr "Suspendé"

#: searx/webutils.py:161
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minüt awor ei"

#: searx/webutils.py:162
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} ora, {minutes} minüt awor ei"

#: searx/answerers/random/answerer.py:67
msgid "Random value generator"
msgstr "Generado di sifra"

#: searx/answerers/random/answerer.py:68
msgid "Generate different random values"
msgstr "Generá un sifra diferente"

#: searx/answerers/statistics/answerer.py:47
msgid "Statistics functions"
msgstr "Funshon nan estadístiko"

#: searx/answerers/statistics/answerer.py:48
msgid "Compute {functions} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:160
msgid "Get directions"
msgstr ""

#: searx/engines/pdbe.py:96
msgid "{title} (OBSOLETE)"
msgstr ""

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr ""

#: searx/engines/qwant.py:219
msgid "Channel"
msgstr ""

#: searx/engines/semantic_scholar.py:81
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""

#: searx/engines/tineye.py:40
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""

#: searx/engines/tineye.py:46
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""

#: searx/engines/tineye.py:52
msgid "The image could not be downloaded."
msgstr ""

#: searx/engines/wttr.py:101
msgid "Morning"
msgstr ""

#: searx/engines/wttr.py:101
msgid "Noon"
msgstr ""

#: searx/engines/wttr.py:101
msgid "Evening"
msgstr ""

#: searx/engines/wttr.py:101
msgid "Night"
msgstr ""

#: searx/plugins/hash_plugin.py:24
msgid "Converts strings to different hash digests."
msgstr ""

#: searx/plugins/hash_plugin.py:52
msgid "hash digest"
msgstr ""

#: searx/plugins/hostname_replace.py:9
msgid "Hostname replace"
msgstr ""

#: searx/plugins/hostname_replace.py:10
msgid "Rewrite result hostnames or remove results based on the hostname"
msgstr ""

#: searx/plugins/oa_doi_rewrite.py:9
msgid "Open Access DOI rewrite"
msgstr ""

#: searx/plugins/oa_doi_rewrite.py:10
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""

#: searx/plugins/search_on_category_select.py:19
msgid "Search on category select"
msgstr ""

#: searx/plugins/search_on_category_select.py:20
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories. (JavaScript required)"
msgstr ""

#: searx/plugins/self_info.py:20
msgid "Self Information"
msgstr ""

#: searx/plugins/self_info.py:21
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"contains \"user agent\"."
msgstr ""

#: searx/plugins/tor_check.py:25
msgid "Tor check plugin"
msgstr ""

#: searx/plugins/tor_check.py:28
msgid ""
"This plugin checks if the address of the request is a TOR exit node, and "
"informs the user if it is, like check.torproject.org but from searxng."
msgstr ""

#: searx/plugins/tor_check.py:62
msgid ""
"The TOR exit node list (https://check.torproject.org/exit-addresses) is "
"unreachable."
msgstr ""

#: searx/plugins/tor_check.py:78
msgid "You are using TOR. Your IP address seems to be: {ip_address}."
msgstr ""

#: searx/plugins/tor_check.py:84
msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
msgstr ""

#: searx/plugins/tracker_url_remover.py:29
msgid "Tracker URL remover"
msgstr ""

#: searx/plugins/tracker_url_remover.py:30
msgid "Remove trackers arguments from the returned URL"
msgstr ""

#: searx/plugins/vim_hotkeys.py:3
msgid "Vim-like hotkeys"
msgstr ""

#: searx/plugins/vim_hotkeys.py:4
msgid ""
"Navigate search results with Vim-like hotkeys (JavaScript required). "
"Press \"h\" key on main or result page to get help."
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr ""

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr ""

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr ""

#: searx/templates/simple/base.html:46
msgid "About"
msgstr ""

#: searx/templates/simple/base.html:50
msgid "Donate"
msgstr ""

#: searx/templates/simple/base.html:54
#: searx/templates/simple/preferences.html:99
msgid "Preferences"
msgstr ""

#: searx/templates/simple/base.html:64
msgid "Powered by"
msgstr ""

#: searx/templates/simple/base.html:64
msgid "a privacy-respecting, open metasearch engine"
msgstr ""

#: searx/templates/simple/base.html:65
msgid "Source code"
msgstr ""

#: searx/templates/simple/base.html:66
msgid "Issue tracker"
msgstr ""

#: searx/templates/simple/base.html:67 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr ""

#: searx/templates/simple/base.html:69
#: searx/templates/simple/messages/no_results.html:15
msgid "Public instances"
msgstr ""

#: searx/templates/simple/base.html:72
msgid "Privacy policy"
msgstr ""

#: searx/templates/simple/base.html:75
msgid "Contact instance maintainer"
msgstr ""

#: searx/templates/simple/categories.html:24
msgid "Click on the magnifier to perform search"
msgstr ""

#: searx/templates/simple/macros.html:36
msgid "Length"
msgstr ""

#: searx/templates/simple/macros.html:37
#: searx/templates/simple/result_templates/images.html:18
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr ""

#: searx/templates/simple/macros.html:45
msgid "cached"
msgstr ""

#: searx/templates/simple/macros.html:45
msgid "proxied"
msgstr ""

#: searx/templates/simple/new_issue.html:64
msgid "Start submiting a new issue on GitHub"
msgstr ""

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""

#: searx/templates/simple/preferences.html:29
msgid "No HTTPS"
msgstr ""

#: searx/templates/simple/messages/no_results.html:10
#: searx/templates/simple/preferences.html:31
#: searx/templates/simple/preferences.html:32
#: searx/templates/simple/results.html:49
msgid "View error logs and submit a bug report"
msgstr ""

#: searx/templates/simple/preferences.html:53
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr ""

#: searx/templates/simple/preferences.html:54
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr ""

#: searx/templates/simple/preferences.html:55
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr ""

#: searx/templates/simple/preferences.html:83
msgid "Failed checker test(s): "
msgstr ""

#: searx/templates/simple/preferences.html:85
msgid "Errors:"
msgstr ""

#: searx/templates/simple/preferences.html:105
msgid "General"
msgstr ""

#: searx/templates/simple/preferences.html:108
msgid "Default categories"
msgstr ""

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences.html:115
msgid "Search language"
msgstr ""

#: searx/templates/simple/filters/languages.html:2
#: searx/templates/simple/preferences.html:118
msgid "Default language"
msgstr ""

#: searx/templates/simple/preferences.html:124
msgid "What language do you prefer for search?"
msgstr ""

#: searx/templates/simple/preferences.html:129
msgid "Autocomplete"
msgstr ""

#: searx/templates/simple/preferences.html:138
msgid "Find stuff as you type"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences.html:143
#: searx/templates/simple/preferences.html:311
msgid "SafeSearch"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences.html:146
msgid "Strict"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences.html:147
msgid "Moderate"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences.html:148
msgid "None"
msgstr ""

#: searx/templates/simple/preferences.html:151
msgid "Filter content"
msgstr ""

#: searx/templates/simple/preferences.html:157
msgid "Open Access DOI resolver"
msgstr ""

#: searx/templates/simple/preferences.html:167
msgid ""
"Redirect to open-access versions of publications when available (plugin "
"required)"
msgstr ""

#: searx/templates/simple/preferences.html:171
msgid "Engine tokens"
msgstr ""

#: searx/templates/simple/preferences.html:175
msgid "Access tokens for private engines"
msgstr ""

#: searx/templates/simple/preferences.html:179
msgid "User interface"
msgstr ""

#: searx/templates/simple/preferences.html:182
msgid "Interface language"
msgstr ""

#: searx/templates/simple/preferences.html:190
msgid "Change the language of the layout"
msgstr ""

#: searx/templates/simple/preferences.html:195
msgid "Theme"
msgstr ""

#: searx/templates/simple/preferences.html:203
msgid "Change SearXNG layout"
msgstr ""

#: searx/templates/simple/preferences.html:206
msgid "Theme style"
msgstr ""

#: searx/templates/simple/preferences.html:214
msgid "Choose auto to follow your browser settings"
msgstr ""

#: searx/templates/simple/preferences.html:217
msgid "Center Alignment"
msgstr ""

#: searx/templates/simple/preferences.html:220
#: searx/templates/simple/preferences.html:232
#: searx/templates/simple/preferences.html:244
msgid "On"
msgstr ""

#: searx/templates/simple/preferences.html:221
#: searx/templates/simple/preferences.html:233
#: searx/templates/simple/preferences.html:245
msgid "Off"
msgstr ""

#: searx/templates/simple/preferences.html:224
msgid "Displays results in the center of the page (Oscar layout)."
msgstr ""

#: searx/templates/simple/preferences.html:229
msgid "Results on new tabs"
msgstr ""

#: searx/templates/simple/preferences.html:236
msgid "Open result links on new browser tabs"
msgstr ""

#: searx/templates/simple/preferences.html:241
msgid "Infinite scroll"
msgstr ""

#: searx/templates/simple/preferences.html:248
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""

#: searx/templates/simple/preferences.html:254
msgid "Privacy"
msgstr ""

#: searx/templates/simple/preferences.html:257
msgid "HTTP Method"
msgstr ""

#: searx/templates/simple/preferences.html:264
msgid ""
"Change how forms are submitted, <a "
"href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
" rel=\"external\">learn more about request methods</a>"
msgstr ""

#: searx/templates/simple/preferences.html:269
msgid "Image proxy"
msgstr ""

#: searx/templates/simple/preferences.html:272
#: searx/templates/simple/preferences.html:284
msgid "Enabled"
msgstr ""

#: searx/templates/simple/preferences.html:273
#: searx/templates/simple/preferences.html:285
msgid "Disabled"
msgstr ""

#: searx/templates/simple/preferences.html:276
msgid "Proxying image results through SearXNG"
msgstr ""

#: searx/templates/simple/preferences.html:281
msgid "Query in the page's title"
msgstr ""

#: searx/templates/simple/preferences.html:288
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""

#: searx/templates/simple/preferences.html:294
msgid "Engines"
msgstr ""

#: searx/templates/simple/preferences.html:295
msgid "Currently used search engines"
msgstr ""

#: searx/templates/simple/preferences.html:302
msgid ""
"This tab does not show up for search results, but you can search the "
"engines listed here via bangs."
msgstr ""

#: searx/templates/simple/preferences.html:307
#: searx/templates/simple/preferences.html:358
msgid "Allow"
msgstr ""

#: searx/templates/simple/preferences.html:308
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr ""

#: searx/templates/simple/preferences.html:309
msgid "Shortcut"
msgstr ""

#: searx/templates/simple/preferences.html:310
msgid "Supports selected language"
msgstr ""

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences.html:312
msgid "Time range"
msgstr ""

#: searx/templates/simple/preferences.html:313
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr ""

#: searx/templates/simple/preferences.html:314
msgid "Max time"
msgstr ""

#: searx/templates/simple/preferences.html:315
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr ""

#: searx/templates/simple/preferences.html:353
msgid "Special Queries"
msgstr ""

#: searx/templates/simple/preferences.html:359
msgid "Keywords"
msgstr ""

#: searx/templates/simple/preferences.html:360
msgid "Name"
msgstr ""

#: searx/templates/simple/preferences.html:361
msgid "Description"
msgstr ""

#: searx/templates/simple/preferences.html:362
msgid "Examples"
msgstr ""

#: searx/templates/simple/preferences.html:365
msgid "This is the list of SearXNG's instant answering modules."
msgstr ""

#: searx/templates/simple/preferences.html:376
msgid "This is the list of plugins."
msgstr ""

#: searx/templates/simple/preferences.html:393
msgid "Cookies"
msgstr ""

#: searx/templates/simple/preferences.html:395
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""

#: searx/templates/simple/preferences.html:396
msgid "With that list, you can assess SearXNG transparency."
msgstr ""

#: searx/templates/simple/preferences.html:401
msgid "Cookie name"
msgstr ""

#: searx/templates/simple/preferences.html:402
msgid "Value"
msgstr ""

#: searx/templates/simple/preferences.html:414
msgid "Search URL of the currently saved preferences"
msgstr ""

#: searx/templates/simple/preferences.html:418
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""

#: searx/templates/simple/preferences.html:419
msgid "URL to restore your preferences in another browser"
msgstr ""

#: searx/templates/simple/preferences.html:423
msgid ""
"Specifying custom settings in the preferences URL can be used to sync "
"preferences across devices."
msgstr ""

#: searx/templates/simple/preferences.html:428
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""

#: searx/templates/simple/preferences.html:430
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""

#: searx/templates/simple/preferences.html:433
msgid "Save"
msgstr ""

#: searx/templates/simple/preferences.html:434
msgid "Reset defaults"
msgstr ""

#: searx/templates/simple/preferences.html:435
msgid "Back"
msgstr ""

#: searx/templates/simple/results.html:23
msgid "Answers"
msgstr ""

#: searx/templates/simple/results.html:39
msgid "Number of results"
msgstr ""

#: searx/templates/simple/messages/no_results.html:6
#: searx/templates/simple/results.html:46
msgid "Error!"
msgstr ""

#: searx/templates/simple/results.html:46
msgid "Engines cannot retrieve results"
msgstr ""

#: searx/templates/simple/results.html:68
msgid "Suggestions"
msgstr ""

#: searx/templates/simple/results.html:90
msgid "Search URL"
msgstr ""

#: searx/templates/simple/results.html:96
msgid "Download results"
msgstr ""

#: searx/templates/simple/results.html:120
msgid "Try searching for:"
msgstr ""

#: searx/templates/simple/results.html:152
msgid "Back to top"
msgstr ""

#: searx/templates/simple/results.html:170
msgid "Previous page"
msgstr ""

#: searx/templates/simple/results.html:187
msgid "Next page"
msgstr ""

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr ""

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr ""

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr ""

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr ""

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr ""

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr ""

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr ""

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr ""

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr ""

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr ""

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr ""

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr ""

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr ""

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr ""

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr ""

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr ""

#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr ""

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr ""

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr ""

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr ""

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr ""

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr ""

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr ""

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr ""

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr ""

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr ""

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr ""

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr ""

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr ""

#: searx/templates/simple/messages/no_results.html:6
msgid "Engines cannot retrieve results."
msgstr ""

#: searx/templates/simple/messages/no_results.html:15
msgid "Please, try again later or find another SearXNG instance."
msgstr ""

#: searx/templates/simple/messages/no_results.html:20
msgid "Sorry!"
msgstr ""

#: searx/templates/simple/messages/no_results.html:21
msgid ""
"we didn't find any results. Please use another query or search in more "
"categories."
msgstr ""

#: searx/templates/simple/result_templates/default.html:6
msgid "show media"
msgstr ""

#: searx/templates/simple/result_templates/default.html:6
msgid "hide media"
msgstr ""

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr ""

#: searx/templates/simple/result_templates/images.html:19
msgid "Format"
msgstr ""

#: searx/templates/simple/result_templates/images.html:21
msgid "Engine"
msgstr ""

#: searx/templates/simple/result_templates/images.html:22
msgid "View source"
msgstr ""

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr ""

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr ""

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:6
msgid "magnet link"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:7
msgid "torrent file"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:9
msgid "Seeder"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:9
msgid "Leecher"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:11
msgid "Filesize"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:12
msgid "Bytes"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:13
msgid "kiB"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:14
msgid "MiB"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:15
msgid "GiB"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:16
msgid "TiB"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:20
msgid "Number of Files"
msgstr ""

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr ""

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr ""

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr ""

#~ msgid "No abstract is available for this publication."
#~ msgstr ""

#~ msgid "Self Informations"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

