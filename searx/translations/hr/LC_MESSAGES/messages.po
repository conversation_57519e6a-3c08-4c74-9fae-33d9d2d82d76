# Croatian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# df3fdd29c9d33426452a2db187d128e3, 2017
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022, 2023.
# <AUTHOR> <EMAIL>, 2022.
# p<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# U<PERSON>mo <<EMAIL>>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-02-08 02:17+0000\n"
"Last-Translator: SecularSteve "
"<<EMAIL>>\n"
"Language: hr\n"
"Language-Team: Croatian "
"<https://translate.codeberg.org/projects/searxng/searxng/hr/>\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "bez daljnjeg podgrupiranja"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "drugo"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "datoteke"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "općenito"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "glazba"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "društvene mreže"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "slike"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "video zapisi"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "vijesti"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "karta"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "luk"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "znanost"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikacije"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "rječnici"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "tekstovi"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketi"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "pitanja i odgovori"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repozitoriji"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "programski wikiji"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "mreža"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "znanstvene publikacije"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatski"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "svijetlo"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tamno"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "crno"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Vrijeme rada"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "O nama"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "prosječna temperatura."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "naoblaka"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Cremenski uvjeti"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Trenutni vremenski uvjeti"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Večer"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "izgleda kao"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "vlažnost"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "maks. temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Min. temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Jutro"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noć"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Podne"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Pritisak"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "izlazak sunca"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "zalazak"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV index"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "vidljivost"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "vjetar"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "pretplatnici"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "objave"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktivni korisnici"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "komentari"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "korisnik"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "zajednica"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "bodovi"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "naslov"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "otvoren"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "zatvoren"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "odgovoren"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nije pronađena nijedna stavka"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Izvor"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Greška u učitavnju sljedeće stranice"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Nevažeće postavke, molimo uredite svoje postavke"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Nevažeće postavke"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "greška u pretraživanju"

#: searx/webutils.py:35
msgid "timeout"
msgstr "pauza"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "pogreška pri raščlanjivanju"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "greška HTTP protokola"

#: searx/webutils.py:38
msgid "network error"
msgstr "greška u mreži"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL pogreška: provjera valjanosti certifikata nije uspjela"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "neočekivani prekid"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP greška"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "greška HTTP veze"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxy greška"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "previše upita"

#: searx/webutils.py:58
msgid "access denied"
msgstr "pristup odbijen"

#: searx/webutils.py:59
msgid "server API error"
msgstr "server API greška"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspendirano"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "prije {minutes} minut(u,e,a)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "prije {hours} sat(i,a) i {minutes} minut(u,e,a)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generirajte različite nasumične vrijednosti"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Izračunajte {func} argumenata"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Pokaži rutu na karti .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ZASTARJELO)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Ovaj je unos zamijenio"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitrata"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "glasovi"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikovi"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Jezik"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citati iz godine {firstCitationVelocityYear} do "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Nije moguće učitati sliku sa URL-a. Moguće da je u pitanju neispravan "
"format dokumenta. TinEye samo podržava slike JPEG, PNG, GIF, BMP, TIFF i "
"WebP formata."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Slika je previše jednostavna da bi se pronašla sličnost. TinEye zahtjeva "
"osnovnu razinu detalja za pronalaženje sličnosti."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Sliku nije moguće preuzeti."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Ocjena knjige"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Kvaliteta datoteke"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Izračunaj matematički izraz putem tražilice"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Heš dodatak"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Pretvara niz u drukčije hash mješavine."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "Izlaz hash funkcije"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Dodatak (plugin) za Hostnames"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Prepiši hostanmes, ukloni rezultate ili ih prioritiziraj na temelju "
"hostname-a"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Otvoreni pristup DOI prijepisa"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "Izbjegnite plaćanje u slučaju dostupnosti besplatne objave"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informacije o sebi"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Prikazuje vaš IP ako je upit \"ip\" i vaš korisnički agent ako je upit "
"\"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Vaš IP je: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Vaš user-agent je: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor plugin za provjeru"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Ovaj plugin provjerava da li je adresa zahtjeva TOR izlazna adresa, i "
"šalje obavijest korisniku, kao check.torproject.org ali od strane "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nije moguće preuzeti popis Tor izlaznih čvorova iz"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Vi koristite Tor i izgleda da imate vanjsku IP adresu"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Ne koristite Tor i imate vanjsku IP adresu"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Ukloni praćenje URL-ova"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Ukloni elemente za označavanje rezultata vraćenih s URL-a"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konvertiraj između jedinica"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Stranica nije pronađena"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Idi na %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "pretraži stranicu"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Donirajte"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Postavke"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Pokreće"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "otvoreni metapretraživač koji poštuje privatnost"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Izvorni kod"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Tragač problema"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Podaci o tražilici"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Javne instance"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Politika privatnosti"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontaktirajte održavatelja instance"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Kliknite na povećalo za izvođenje pretraživanja"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Dužina"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Pogledi"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "spremljeno"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Počnite izlagati novi slučaj na GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Provjerite postoje li greške u vezi s ovim motorom na GitHub-u"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Potvrđujem da ne postoji greška u vezi s problemom na koji sam naišao"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Ako je ovo javna instanca, navedite URL u izvješću o pogrešci"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Postavi novi problem na Github uključujući podatke poviše"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Nema HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Pogledajte zapisnike grešaka i pošaljite izvješće o greškama"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang za ovaj motor"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang za svoje kategorije"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Medijan"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Neuspjeli test(ovi) za provjeru: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Greške:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Općenito"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Zadane kategorije"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Korisničko sučelje"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privatnost"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Tražilice"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Trenutno korištene tražilice"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Posebni upiti"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Kolačići"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Broj rezultata"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informacije"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Natrag na vrh"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Prethodna stranica"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Sljedeća stranica"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Prikaži naslovnu stranicu"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Traži..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "očisti"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "traži"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Trenutačno nema dostupnih podataka."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Naziv tražilice"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Pogodci"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Broj rezultata"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Vrijeme odziva"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Pouzdanost"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Ukupno"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Obrada"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Upozorenja"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Pogreške i iznimke"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Iznimka"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Poruka"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Postotak"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametar"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Naziv datoteke"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkcija"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Koda"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Provjernik"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Neuspjeli test"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komentar(i)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Primjeri"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definicije"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinonimi"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Odgovori"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Preuzmi rezultate"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Pokušajte tražiti sljedeće:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Poruke s tražilica"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekunde"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Pretraži URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopirano"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopiraj"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Prijedlozi"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Jezik pretraživanja"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Zadani jezik"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Automatski otkrij"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Sigurno pretraživanje"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Strogo"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Umjereno"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Ništa"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Vremenski raspon"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Bilokad"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Posljednji dan"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Prošli tjedan"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Prošli mjesec"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Prošle godine"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informacija!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "trenutačno nema definiranih kolačića."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Ispričavamo se!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Nema rezultata. Možete pokušati:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Nema više rezultata. Možete pokušati:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Osvježiti stranicu."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Potražiti druge upite ili da odaberete drugu kategoriju (iznad)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Promijenite tražilicu korištenu u postavkama:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Prijeđi na drugu instancu:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Potražite drugi upit ili odaberite drugu kategoriju."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Vratite se na prethodnu stranicu pomoću gumba prethodne stranice."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Dozvoli"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Ključne riječi (prva riječ u upitu)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Naziv"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Opis"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Ovo je popis SearXNG-ovih modula za trenutno javljanje."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Ovo je popis dodataka."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automatsko dovršavanje"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Pronađite stvari prilikom upisivanja"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Središnje poravnanje"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Prikazuje rezultate u sredini stranice (Oscar raspored)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Ovo je popis kolačića i njihovih vrijednosti koje SearXNG pohranjuje na "
"vašem kompjuteru."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "S tim popisom možete procijeniti prozirnost SearXNG-a."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Naziv kolačića"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Vrijednost"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Pretraži URL adresu trenutno spremljenih postavki"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Napomena: određivanje prilagođenih postavki u URL-u za pretraživanje može"
" smanjiti privatnost zbog propuštanja podataka na kliknute web lokacije "
"rezultata."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL da biste vratili vaše postavke u drugom pregledniku"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopirajte preferencu hash-a"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Umetnite kopiranu preferencu hash-a (bez URL-a) za rješenje"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Preference hash-a"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digitalno-objektatski indentifikator (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Otvoreni pristup DOI rješenja"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Odaberite uslugu koju koristi DOI iznovopis"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ova kartica ne postoji u korisničkom sučelju, ali u ovim tražilicama "
"možete pretraživati po !bangs-ima."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Omogući sve"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Onemogući sve"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Podržava odabrani jezik"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Težina"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maksimalno vrijeme"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Razrješivač favikona"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Prikažite favikone pored rezultata pretraživanja"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Ove postavke su pohranjene u Vašim kolačićima, što omogućuje da ne "
"spremamo podatke o Vama."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Ovi kolačići služe Vašoj pogodnosti, ne upotrebljavamo te kolačiće da bi "
"Vas pratili."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Sačuvati"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Vraćanje zadanih postavki"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Natrag"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Prečaci"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Slično Vimu"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Krećite se rezultatima pretraživanja pomoću prečaca (potreban je "
"JavaScript). Pritisnite tipku \"h\" na glavnoj stranici ili stranici s "
"rezultatima za pomoć."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy slike"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proxy slikovni rezultati putem SearXNG-a"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Beskonačno pomicanje"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Automatski učitajte sljedeću stranicu kada se pomaknete do dna trenutne "
"stranice"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Koji jezik želite za pretraživanje?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Odaberite Automatsko otkrivanje kako bi SearXNG otkrio jezik vašeg upita."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP metoda"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Promijenite način slanja obrazaca"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Upit u naslovu stranice"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Kada je omogućeno, naslov stranice s rezultatima sadrži vaš upit. Vaš "
"preglednik može zabilježiti ovaj naslov"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Rezultati u novim karticama"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Otvorite veze rezultata na novim karticama preglednika"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtriranje sadržaja"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Traži u odabranoj kategoriji"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Izvršite pretragu odmah ako je odabrana kategorija. Onemogući odabir više"
" kategorija"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Promijenite izgled SearXNG-a"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Izgled teme"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Odaberite automatski kako biste pratili postavke vašeg preglednika"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Motorni žetoni"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Pristupite žetone za privatne motore"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Jezik sučelja"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Promijenite jezik prikaza"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL formatiranje"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Lijepo"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Cijelo"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Izvorno"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Promjenite oblikovanje URL-a rezultata"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repozitoriji"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "prikaži medije"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "sakrij medije"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ova stranica nije dala nikakav opis."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Veličina datoteke"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Datum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tip"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Razlučivost"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Prikaži izvor"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresa"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "prikaži kartu"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "sakrij kartu"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Verzija"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Održavatelj"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Ažurirano u"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Oznake"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularnost"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licenca"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projekt početna stranica"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Datum objave"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Časopis"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Urednik"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Izdavač"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet link"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent datoteka"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Hranilac"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Broj datoteka"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "prikaži video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "sakrij video"

#~ msgid "Engine time (sec)"
#~ msgstr "Vrijeme pretraživanja (sek)"

#~ msgid "Page loads (sec)"
#~ msgstr "Učitavanje stranice (sek)"

#~ msgid "Errors"
#~ msgstr "Greške"

#~ msgid "CAPTCHA required"
#~ msgstr "Treba CAPTCHU ispuniti"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Zamijeni HTTP veze sa HTTPS ukoliko je moguće"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Po zadanom, rezultati se otvaraju u "
#~ "istom prozoru. Ovaj dodatak poništava "
#~ "zadano ponašanje za otvaranje veza na"
#~ " novim karticama/prozorima. (Potreban je "
#~ "JavaScript)"

#~ msgid "Color"
#~ msgstr "Boja"

#~ msgid "Blue (default)"
#~ msgstr "Plava (zadano)"

#~ msgid "Violet"
#~ msgstr "Ljubičasta"

#~ msgid "Green"
#~ msgstr "Zelena"

#~ msgid "Cyan"
#~ msgstr "Cijan"

#~ msgid "Orange"
#~ msgstr "Narančasta"

#~ msgid "Red"
#~ msgstr "Crvena"

#~ msgid "Category"
#~ msgstr "Kategorija"

#~ msgid "Block"
#~ msgstr "Blokiraj"

#~ msgid "original context"
#~ msgstr "izvorni sadržaj"

#~ msgid "Plugins"
#~ msgstr "Dodaci"

#~ msgid "Answerers"
#~ msgstr "Davatelji odgovora"

#~ msgid "Avg. time"
#~ msgstr "Prosječno vrijeme"

#~ msgid "show details"
#~ msgstr "prikaži detalje"

#~ msgid "hide details"
#~ msgstr "sakrij detalje"

#~ msgid "Load more..."
#~ msgstr "Učitaj više..."

#~ msgid "Loading..."
#~ msgstr "Učitavanje..."

#~ msgid "Change searx layout"
#~ msgstr "Promijenite izgled searxa"

#~ msgid "Proxying image results through searx"
#~ msgstr "Koristite proxy za slike dobivene pretraživanjem searxa"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Ovo je popis searx modula za odgovore"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Ovo je popis kolačića i njihovih "
#~ "vrijednosti koje pohranjuju na Vašem "
#~ "računalu."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "S tim popisom možete procijeniti transparentnost pretraživanja."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Izgleda kao da prvi puta koristite searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Pokušajte ponovo kasnije ili potražite drugu searx instancu."

#~ msgid "Themes"
#~ msgstr "Teme"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metoda"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Napredne postavke"

#~ msgid "Close"
#~ msgstr "Zatvori"

#~ msgid "Language"
#~ msgstr "Jezik"

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "podržano"

#~ msgid "not supported"
#~ msgstr "nije podržano"

#~ msgid "about"
#~ msgstr "o nama"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Odaberite stil za ovu temu"

#~ msgid "Style"
#~ msgstr "Stil"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "Dozvoli sve"

#~ msgid "Disable all"
#~ msgstr "Isključi sve"

#~ msgid "Selected language"
#~ msgstr "Odabrani jezik"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "spremi"

#~ msgid "back"
#~ msgstr "natrag"

#~ msgid "Links"
#~ msgstr "Poveznice"

#~ msgid "RSS subscription"
#~ msgstr "RSS pretplata"

#~ msgid "Search results"
#~ msgstr "Rezultati pretraživanja"

#~ msgid "next page"
#~ msgstr "Sljedeća stranica"

#~ msgid "previous page"
#~ msgstr "Prethodna stranica"

#~ msgid "Start search"
#~ msgstr "Pokreni pretraživanje"

#~ msgid "Clear search"
#~ msgstr "Očistite pretražnik"

#~ msgid "Clear"
#~ msgstr "Očisti"

#~ msgid "stats"
#~ msgstr "statistika"

#~ msgid "Heads up!"
#~ msgstr "Glavu gore!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Odlično!"

#~ msgid "Settings saved successfully."
#~ msgstr "Postavke uspješno spremljene."

#~ msgid "Oh snap!"
#~ msgstr "Ups!"

#~ msgid "Something went wrong."
#~ msgstr "Nešto je pošlo po zlu."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Dohvati sliku"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "postavke"

#~ msgid "Scores per result"
#~ msgstr "Pogodci po rezultatu"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "meta-tražilica koja poštuje privatnost"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Nijedan sažetak nije dostupan za ovu objavu."

#~ msgid "Self Informations"
#~ msgstr "Podatci o sebi"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Promijenite način slanja obrasca, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">saznajte više o metodama "
#~ "zahtjeva</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Ovaj plugin provjerava da li je "
#~ "adresa zahtjeva TOR izlazna adresa, i"
#~ " šalje obavijest korisniku, kao "
#~ "check.torproject.org ali od strane searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "TOR lista izlaznih adresa "
#~ "(https://check.torproject.org/exit-addresses) je "
#~ "nedostupna."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Vi koristite TOR. Vaša IP adresa se čini da je: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Vi koristite TOR. Izgleda da je vaša IP adresa: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "drugi"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Ova kartica nije prikazana za rezultate"
#~ " pretrage, ali možete pretraživati motore"
#~ " navedene ovdje putem šiških."

#~ msgid "Shortcut"
#~ msgstr "Prečac"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Tražilice ne mogu dohvatiti rezultate."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Molimo vas da pokušate ponovo kasnije"
#~ " ili da pronađete drugu SearXNG "
#~ "instancu."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Preusmjeri na verzije izdanja otvorenog "
#~ "pristupa kada je isto dostupno (potreban"
#~ " je dodatak)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Promijenite način slanja obrazaca, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">saznajte više o metodama "
#~ "zahtjeva</a>"

#~ msgid "On"
#~ msgstr "Uključeno"

#~ msgid "Off"
#~ msgstr "Isključeno"

#~ msgid "Enabled"
#~ msgstr "Omogućeno"

#~ msgid "Disabled"
#~ msgstr "Onemogućeno"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Izvrši pretraživanje odmah ako je "
#~ "odabrana kategorija. Onemogući odabir više "
#~ "kategorija. (Potreban je JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim tipkovni prečaci"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Kretanje rezultatima pretraživanja pomoću "
#~ "tipkovnih prečaca sličnih Vim-u (potreban "
#~ "je JavaScript). Pritisnite tipku \"h\" "
#~ "na glavnoj stranici ili stranici s "
#~ "rezultatima kako biste dobili pomoć."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "nema rezultata pretraživanja. Unesite novi "
#~ "upit ili pretražite u više kategorija."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Ispravite (prepišite) rezultat hostnameova ili"
#~ " maknite rezultate bazirane na hostname"

#~ msgid "Bytes"
#~ msgstr "Bajti"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Zamjena lokalnog imena"

#~ msgid "Error!"
#~ msgstr "Greška!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Tražilice ne mogu dohvatiti rezultate"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Počnite izlagati novi slučaj na GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Nasumični generator vrijednosti"

#~ msgid "Statistics functions"
#~ msgstr "Statistične funkcije"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Izračunajte {functions} argumenata"

#~ msgid "Get directions"
#~ msgstr "Dobij upute"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Prikazuje vašu IP adresu ako je "
#~ "upit \"ip\" i vaš korisnički agent "
#~ "ako upit sadrži \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Nije moguće preuzeti popis Tor izlaznih"
#~ " čvorova s: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Vi koristite Tor i izgleda da imate ovu vanjsku IP adresu: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Vi ne koristite Tor i imate ovu vanjsku IP adresu: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Ključne riječi"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Navođenje prilagođenih postavki u URL-u "
#~ "može se koristiti za sinkronizaciju "
#~ "postavki na svim uređajima."

#~ msgid "proxied"
#~ msgstr "preko proxyja"

