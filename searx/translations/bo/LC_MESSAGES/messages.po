# Tibetan translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-01-06 15:52+0000\n"
"Last-Translator: return42 <<EMAIL>>"
"\n"
"Language: bo\n"
"Language-Team: Tibetan "
"<https://translate.codeberg.org/projects/searxng/searxng/bo/>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr ""

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr ""

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ཡིག་ཚགས།"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "དྲ་སྦྲེལ།"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "རོལ་མོ།"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "སྤྱི་འབྲེལ།"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "པར་རིས།"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "བརྙན་ཟློས།"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr ""

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr ""

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "ཆ་འཕྲིན་ལག་རྩལ།"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "གསར་འགྱུར།"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "ས་བཀྲ།"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr ""

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "ཚན་རིག"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr ""

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr ""

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr ""

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr ""

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr ""

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr ""

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr ""

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr ""

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr ""

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr ""

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr ""

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr ""

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr ""

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr ""

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr ""

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr ""

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr ""

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr ""

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr ""

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr ""

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr ""

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr ""

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr ""

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr ""

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr ""

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr ""

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr ""

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr ""

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr ""

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr ""

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr ""

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr ""

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr ""

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr ""

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr ""

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr ""

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr ""

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr ""

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr ""

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr ""

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr ""

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr ""

#: searx/webapp.py:292
msgid "No item found"
msgstr "རྣམ་གྲངས་གང་ཡང་རྙེད་རྒྱུ་མ་བྱུང་།"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr ""

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr ""

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "ནུས་མེད་ཀྱི་སྒྲིག་འགོད།ཁྱེད་ཀྱིས་གདམ་ཀ་ལ་བཅོས་སྒྲིག་གཏོང་རོགས།"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "ནུས་མེད་ཀྱི་སྒྲིག་འགོད།"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "འཚོལ་བཤེར་ལ་ནོར་འཁྲུལ་བྱུང་།"

#: searx/webutils.py:35
msgid "timeout"
msgstr ""

#: searx/webutils.py:36
msgid "parsing error"
msgstr ""

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr ""

#: searx/webutils.py:38
msgid "network error"
msgstr ""

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr ""

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr ""

#: searx/webutils.py:48
msgid "HTTP error"
msgstr ""

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr ""

#: searx/webutils.py:55
msgid "proxy error"
msgstr ""

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr ""

#: searx/webutils.py:58
msgid "access denied"
msgstr ""

#: searx/webutils.py:59
msgid "server API error"
msgstr ""

#: searx/webutils.py:78
msgid "Suspended"
msgstr ""

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "སྐར་མ་ {minutes} སྔོན་ལ།"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "ཆུ་ཚོད་ {hours} དང་སྐར་མ {minutes} སྔོན་ལ།"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "ངེས་མེད་གྲངས་ཀ་ཁ་ཤས་ཐོབ་པར་བྱེད།"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr ""

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr ""

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "འཚོལ་བྱང་འདི་གཞན་གྱིས་ཚབ་བྱེད་འདུག"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr ""

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr ""

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr ""

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr ""

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr ""

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr ""

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr ""

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr ""

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr ""

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr ""

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr ""

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr ""

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr ""

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr ""

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr ""

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr ""

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr ""

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "དྲ་གནས་རྗེས་འདེད་སྤོ་འབུད།"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr ""

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr ""

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "དྲ་ངོས་རྙེད་རྒྱུ་མ་བྱུང་།"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "%(search_page)s ལ་བསྐྱོད།"

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "འཚོལ་བཤེར་དྲ་ངོས།"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr ""

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "སྒྲིག་བཀོད།"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "བཟོ་སྐུན་པ་ནི"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr ""

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr ""

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr ""

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "སྒུལ་བྱེད་ཀྱི་སྡོམ་རྩིས།"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr ""

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr ""

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr ""

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "ས་བོན་སྟེང་གི་སྦྲེལ་ཐག་ལ་རྡེབ་ནས་འཚོལ་བཤེར་གཏོང་།"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr ""

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr ""

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr ""

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "འདྲ་བཤུས་རྒྱབ་ཚར།"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr ""

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr ""

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr ""

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr ""

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr ""

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr ""

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr ""

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "སྤྱི་བཏང་།"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "གཞི་བཞག་གི་རིགས།"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "མདུན་ངོས།"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "མི་སྒེར་གསང་དོན།"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "སྒུལ་བྱེད།"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "ཉེ་ལམ་སྤྱད་ཟིན་པའི་འཚོལ་བྱེད་སྒུལ་བྱེད།"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr ""

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "རྐང་རྗེས།"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "འཚོལ་འབྲས་ཀྱི་ཁ་གྲངས།"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr ""

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr ""

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr ""

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr ""

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr ""

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "འཚོལ་བཤེར་ནང་དོན།"

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr ""

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr ""

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "ཉེ་བར་ཐོབ་རུང་བའི་ཡིག་ཆ་གང་ཡང་མེད།"

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "སྒུལ་བྱེད་ཀྱི་མིང་།"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "ཐོབ་སྐར།"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr ""

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr ""

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr ""

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr ""

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr ""

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr ""

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr ""

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr ""

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr ""

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr ""

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr ""

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr ""

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr ""

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr ""

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr ""

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr ""

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "དཔེ་བརྗོད།"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr ""

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "ལན།"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "འཚོལ་འབྲས་ཕབ་ལེན།"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "འཚོལ་བཤེར་ནང་དོན་ནི།"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr ""

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "འཚོལ་བཤེར་དྲ་གནས།"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr ""

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr ""

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "འོས་སྦྱོས།"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "འཚོལ་བཤེར་ནང་དོན་མཚོན་བྱེད་ཀྱི་སྐད་རིགས།"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "གཞི་བཞག་སྐད་རིགས།"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "བདེ་འཇགས་འཚོལ་བཤེར།"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "ནན་ཏན།"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "འབྲིང་ཙམ།"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "གང་ཡང་མེད།"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "དུས་ཀྱི་ཁྱབ་ཁོངས།"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "དུས་ངེས་མེད།"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "ཉིན་སྔོན་མ།"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "གཟའ་སྔོན་མ།"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "ཟླ་བ་སྔོན་མ།"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "ལོ་སྔོན་མ།"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "ཆ་འཕྲིན།"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "ཉེ་བར་དྲ་ངོས་རྗེས་འདེད་གང་ཡང་མེད།"

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "དགོངས་དག"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr ""

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr ""

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr ""

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "ཆོག་མཆན།"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "མིང་།"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "འབྲེལ་ཡོད་ངོ་སྤྲོད།"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr ""

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr ""

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "རང་ཤུགས་ཀྱིས་སྒྲུབ།"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "འཚོལ་བྱ་གཏགས་པ་ཇི་བཞིན་བཙལ།"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr ""

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "རྗེས་འདེད་ཀྱི་ཡིག་ཆའི་མིང་།"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "ཚད་གཞི།"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "ཉེ་ལམ་རང་མོས་སྒྲིག་འགོད་ཁྲོད་དུ་གསོག་འཇོག་བྱས་ཟིན་པའི་དྲ་གནས་འཚོལ་བཤེར།"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr ""

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr ""

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "རྒྱབ་སྐྱོར་ཐོབ་པའི་སྐད་རིགས་གདམ་གསེས།"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr ""

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "མང་མཐའི་དུས་ཚོད།"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr ""

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "གཞི་བཞག་གི་རྣམ་པར་སྒྲིག་བཀོད་བྱེད།"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "རི་མོ་མངག་བཅོལ་གྱི་ཞབས་ཞུ་སྒྲིག་ཆས།"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr ""

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "མཐའ་མེད་པའི་འཆར་ངོས།"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "དྲ་ངོས་མར་འདྲུད་ནས་ཞབས་ལ་སླེབས་དུས་རང་འགུལ་སྒོས་འཕྲོ་མའི་ནང་དོན་འཆར་པར་བྱེད།"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "ཁྱེད་ཀྱིས་ཆེས་སྤྱོད་བདེ་པའི་འཚོལ་བཤེར་སྐད་རིགས་གང་ཡིན་ནམ།"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr ""

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr ""

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr ""

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "ཤོག་ངོས་གསར་བ་ནས་འཚོལ་འབྲས་འཆར།"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "ཤོག་ངོས་གསར་བ་ཞིག་ནས་དྲ་ངོས་འཆར་པར་བྱེད།"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "ནང་དོན་བཙག་བྱེད།"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "རིགས་གདམ་གསེས་བཏང་ནས་འཚོལ་བཤེར་གཏོང་།"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr ""

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr ""

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr ""

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr ""

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr ""

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr ""

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "དྲ་ངོས་སྐད་རིགས།"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "སྐད་རིགས་གདམ་གསེས་ཀྱི་དྲ་ངོས་བརྗེ་བསྒྱུར།"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr ""

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "སྨྱན་གཟུགས་འཆར་པར་བྱེད།"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "སྨྱན་གཟུགས་སྦས་པར་བྱེད།"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr ""

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "ཡིག་ཆའི་ཆེ་ཆུང་།"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr ""

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr ""

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr ""

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr ""

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr ""

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "ཡོངས་ཁུངས་ལ་ལྟ།"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr ""

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "ས་བཀྲ་འཆར།"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ས་བཀྲ་སྦས།"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "ཐོན་ཁུངས་ཀྱི་དྲ་གནས།"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "ས་བོན་ཡིག་ཆ།"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "མཁོ་སྤྲོད་གཏོང་མཁན།"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "དང་ལེན་བྱེད་མཁན།"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "ཡིག་ཆའི་ཁ་གྲངས།"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "བརྙན་ཟློས་འཆར།"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "རྙན་ཟློས་སྦས།"

#~ msgid "Engine time (sec)"
#~ msgstr "འཚོལ་བཤེར་དུས་ཡུན། (སྐར་ཆ།)"

#~ msgid "Page loads (sec)"
#~ msgstr "འདྲེན་འཇུག་དུས་ཡུན། (སྐར་ཆ།)"

#~ msgid "Errors"
#~ msgstr "ནོར་འཁྲུལ།"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "HTTP དྲ་ངོས་སྦྲེལ་ཐག་རྣམས HTTPS ལ་བསྒྱུར།"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"

#~ msgid "Color"
#~ msgstr "ཁ་དོག"

#~ msgid "Blue (default)"
#~ msgstr "སྔོན་པོ། (གཞི་བཞག)"

#~ msgid "Violet"
#~ msgstr "དམར་སྨུག"

#~ msgid "Green"
#~ msgstr "ལྗང་ཁུ།"

#~ msgid "Cyan"
#~ msgstr "སྔོ་སྐྱ།"

#~ msgid "Orange"
#~ msgstr "ལི་ཁྲི།"

#~ msgid "Red"
#~ msgstr "དམར་པོ།"

#~ msgid "Category"
#~ msgstr "རིགས།"

#~ msgid "Block"
#~ msgstr "བཀག་སྡོམ།"

#~ msgid "original context"
#~ msgstr "གདོད་མའི་ནང་དོན།"

#~ msgid "Plugins"
#~ msgstr "ལྷུ་ལག"

#~ msgid "Answerers"
#~ msgstr "ལན།"

#~ msgid "Avg. time"
#~ msgstr "ས་སྙོམས་དུས་ཚོད།"

#~ msgid "show details"
#~ msgstr "ངོ་སྤྲོད་འཆར།"

#~ msgid "hide details"
#~ msgstr "ངོ་སྤྲོད་སྦས།"

#~ msgid "Load more..."
#~ msgstr "གང་བྱུང་དྲ་ཚིགས་འཆར་པར་བྱེད།"

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "དྲ་ངོས་ཀྱི་རྣམ་པ་བརྗེ་བསྒྱུར།"

#~ msgid "Proxying image results through searx"
#~ msgstr "རི་མོ་མ་ལག་བརྒྱུད་ནས་མངག་བཅོལ་བྱས་ཟིན།"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "འདི་ནི་མ་ལག་ནས་ཐོན་པའི་བྲིས་ལན་བསྟར་ཕྲེང་ཡིན།"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "འདི་ནི མ་ལག་གི་བརྡ་སྤྲོད་ལན་སློག་གི་གསལ་ཐོ་ཞིག་ཡིན།"

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr ""
#~ "གསལ་ཐོ་འདི་བརྒྱུད་ནས། ཁྱེད་ཀྱིས searx "
#~ "དྲ་ཚིགས་ཀྱི་བདེན་པ་རང་བཞིན་ལ་མཉམ་ཚོར་ལེན་ཐུབ།"

#~ msgid "It look like you are using searx first time."
#~ msgstr "བལྟས་སོང་ན་ཁྱེད་ཀྱིས་ཐེངས་དང་པོ་ངེད་དྲ་བ་སྤྱོད་བཞིན་པ་རེད།"

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "ཏོག་ཙམ་འགོར་རྗེས་ཡང་བསྐྱར་ཚར་ལྟ་བྱོས།"

#~ msgid "Themes"
#~ msgstr "རྣམ་པ།"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "ཐབས་ཤེས།"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "མཐོ་རིམ་སྒྲིག་འགོད།"

#~ msgid "Close"
#~ msgstr "ཁ་རྒྱག"

#~ msgid "Language"
#~ msgstr ""

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "རྒྱབ་སྐྱོར་ཐོབ་ཟིན་པ།"

#~ msgid "not supported"
#~ msgstr "རྒྱབ་སྐྱོར་མི་ཐོབ།"

#~ msgid "about"
#~ msgstr "ངེད་ཀྱི་སྐོར།"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "དྲ་ངོས་རྣམ་པ་འདི་ལ་སྒྲིག་འགོད་གཏོང་།"

#~ msgid "Style"
#~ msgstr "ཚུགས་ཀ"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "སྐད་རིགས་གདམ་གསེས།"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "གསོག་འཇོག"

#~ msgid "back"
#~ msgstr "ཕྱིར་ལོག"

#~ msgid "Links"
#~ msgstr "སྦྲེལ་ཐག"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "འཚོལ་འབྲས།"

#~ msgid "next page"
#~ msgstr "དྲ་ངོས་གཞུག་མ།"

#~ msgid "previous page"
#~ msgstr "དྲ་ངོས་སྔོན་མ།"

#~ msgid "Start search"
#~ msgstr "འཚོལ་བཤེར་མགོ་རྩོམ།"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "སྡོམ་རྩིས།"

#~ msgid "Heads up!"
#~ msgstr "མཉམ་འཇོག་བྱེད།"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "ཡག་བྱུང་།"

#~ msgid "Settings saved successfully."
#~ msgstr "སྒྲིག་འགོད་བདེ་ལེགས་ངང་གསོག་འཇོག་བྱས་ཟིན།"

#~ msgid "Oh snap!"
#~ msgstr "ཨ། བྱ་འདི།"

#~ msgid "Something went wrong."
#~ msgstr "ནོར་འཁྲུལ་ཆ་གེ་མོ་ཞིག་བྱུང་ཟིན།"

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "པར་རིས་ཕབ་ལེན།"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "སྒྲིག་བཀོད།"

#~ msgid "Scores per result"
#~ msgstr "འཚོལ་འབྲས་རེ་རེ་ཡི་ཐོབ་སྐར།"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "མི་སྒེར་ཆ་འཕྲིན་ལ་བརྩི་གསོག་ལྡན་ཞིང་འཚོལ་བྱེད་ནང་དོན་ཕུན་སུམ་པའི་འཚོལ་བཤེར་སྒུལ་བྱེད་མ་ལག"

#~ msgid "No abstract is available for this publication."
#~ msgstr ""

#~ msgid "Self Informations"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr ""

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "མགྱོགས་མྱུར་མཐེབ་གཞོང་།"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "འཙོལ་བཤེར་གཏོང་མི་ནུས། དགོངས་དག"

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "ཁ་ཕྱེས།"

#~ msgid "Off"
#~ msgstr "ཁ་རྒྱབ།"

#~ msgid "Enabled"
#~ msgstr "ཁ་འབྱེད་ཟིན།"

#~ msgid "Disabled"
#~ msgstr "ཁ་རྒྱབ་ཟིན།"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""

#~ msgid "Vim-like hotkeys"
#~ msgstr "མགྱོགས་མྱུར་མཐེབ་གཞོང་གི་སྤྱོད་སྟངས།"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "འཚོལ་འབྲས་གང་ཡང་མ་ཐོབ། "
#~ "ཁྱེད་ཀྱིས་འཚོལ་བཤེར་ཐ་སྙད་གཞན་པ་ནས་ཚོད་ལྟ་བྱེད་རོགས།"

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""

#~ msgid "Bytes"
#~ msgstr "གྲངས་གནས།"

#~ msgid "kiB"
#~ msgstr "kB"

#~ msgid "MiB"
#~ msgstr "MB"

#~ msgid "GiB"
#~ msgstr "GB"

#~ msgid "TiB"
#~ msgstr "TB"

#~ msgid "Hostname replace"
#~ msgstr ""

#~ msgid "Error!"
#~ msgstr "ནོར་འཁྲུལ་བྱུང་ཟིན།"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "འཚོལ་བཤེར་སྒུལ་བྱེད་ལ་ནོར་འཁྲུལ་ཅུང་ཟད་བྱུང་།"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr ""

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "ངེས་མེད་གྲངས་ཀ་མཁོ་སྤྲོད།"

#~ msgid "Statistics functions"
#~ msgstr "སྡོམ་བརྩིས་ཀྱི་བྱེད་ནུས།"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "{functions} གཞི་གྲངས་གྲངས་རྩིས།"

#~ msgid "Get directions"
#~ msgstr ""

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""

#~ msgid "Keywords"
#~ msgstr "ཐ་སྙད་གཙོ་བོ།"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""

#~ msgid "proxied"
#~ msgstr "མངག་བཅོལ་བྱེད་ཟིན།"

