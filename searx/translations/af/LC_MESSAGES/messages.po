# Afrikaans translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <PERSON> <sean<PERSON><PERSON>@gmail.com>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-01-28 06:11+0000\n"
"Last-Translator: return42 <<EMAIL>>"
"\n"
"Language: af\n"
"Language-Team: Afrikaans "
"<https://translate.codeberg.org/projects/searxng/searxng/af/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "sonder verdere subgroepering"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "ander"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "lêers"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "algemeen"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musiek"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sosiale media"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "prente"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "video's"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "draadloos"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "inligtingstegnologie"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "nuus"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "kaart"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "uie"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "wetenskap"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "toeps"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "woordeboeke"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "lirieke"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pakkette"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "v&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "bewaarplekke"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "sagteware wiki's"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "Wetenskaplike publikasies"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "outo"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "lig"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "donker"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "swart"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "optyd"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Aangaande"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "gemiddelde temperatuur"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "wolk dekking"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "geval"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Huidige toestand"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "aand"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Voel soos"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Humiditeit"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maksimum temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Minimum temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Oggend"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nag"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Middag"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Druk"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Sonopkoms"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Sonsondergang"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatuur"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV indeks"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Sigbaarheid"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Wind"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "intekenare"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "plasings"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktiewe gebruikers"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kommentaar"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "gebruiker"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "gemeenskap"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "punte"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "titel"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "outeur"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "oop"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "toe"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "geantwoord"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Geen item gevind"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Bron"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Fout met die laai van die volgende bladsy"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ongeldige opstellings, redigeer asb jou voorkeure"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ongeldige opstellings"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "soekfout"

#: searx/webutils.py:35
msgid "timeout"
msgstr "tydsverloop"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "ontledingsfout"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP protokol fout"

#: searx/webutils.py:38
msgid "network error"
msgstr "netwerk fout"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL vout: Kon nie sertifikaat verifieer nie"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "onverwagse breek"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP fout"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP koppelingsfout"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proksie fout"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "te veel versoeke"

#: searx/webutils.py:58
msgid "access denied"
msgstr "toegang geweier"

#: searx/webutils.py:59
msgid "server API error"
msgstr "bediener API fout"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Opgehef"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minute terug"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} ure, {minutes} minute terug"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Genereer verskillende ewekansige waardes"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr ""

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (VEROUDERD)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Hierdie inskrywing was vervang deur"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanaal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitsnelheid"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "stemme"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikke"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Taal"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} aanhalings vanaf die jaar {firstCitationVelocityYear} tot "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Kon nie daardie prent url lees nie. Dit mag weens 'n lêer formaat wees "
"wat nie ondersteun is nie. TinEye ondersteun slegs prente wat JPEG, PNG, "
"GIF, BMP, TIFF of WebP is."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Hierdie prent is te eenvoudig om ooreenkomste te vind. TinEye benodig 'n "
"basiese vlak van visuele detail om suksesvol ooreenkomste te "
"identifiseer."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Die prent kon nie afgelaai word nie."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "boekgradering"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Lêer kwaliteit"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Bereken wiskundige uitdrukkings via die soekbalk"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Skakel snare om na verskillende hash digests."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hash digest"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Gasheername-inprop"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Herskryf gasheername, verwyder resultate of prioritiseer dit op grond van"
" die gasheernaam"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "oop toegang DOI oorskryf"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Vermy betaalmure deur na ope-toegang weergawes van publikasies te herlei "
"wanneer beskikbaar"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Self-inligting"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Jou IP is: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Jou gebruiker-agent is: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor toets inprop"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Hierdie inprop toepassing kontroleer of die adres van die versoek 'n TOR "
"uitgang nodus is en stel die gebruiker in kennis indien wel, soos "
"check.torproject.org maar vanaf SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Spoorsnyer URL verwyderaar"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Verwyder spoorsnyersargumente van die teruggestuurde URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Skakel tussen eenhede om"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Bladsy nie gevind"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Gaan na %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "soekblad"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Skenk"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Voorkeure"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Aangedryf deur"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "'n oop metasoekenjin wat privaatheid respekteer"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Bronkode"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Uitgawe spoorsnyer"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Enjin statistieke"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Openbare instansies"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Privaatheidsbeleid"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontak instansie onderhouer"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Kliek op die vergrootglas om 'n soektog te doen"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Lengte"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "sienings"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Outeur"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "gekas"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Begin om 'n nuwe probleem op GitHub in te dien"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Kyk asseblief vir bestaande goggas vir hierdie enjin op GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Ek bevestig daar is geen bestaande gogga oor die probleem wat ek teekom"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"As dit 'n publieke geval is, spesifiseer asseblief die URL in die "
"foutverslag"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Dien 'n nuwe probleem in op GitHub insluitend die bogenoemde inligting"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Geen HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Bekyk foutlogboeke en dien 'n foutverslag in"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang vir hierdie enjin"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang vir sy kategorieë"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediaan"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Mislukte toetsertoets(e): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Foute:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Algemeen"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Verstek kategoriee"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Gebruikerskoppelvlak"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privaatheid"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Enjins"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Huidige gebruikte soekenjins"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Spesiale Navrae"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Koekies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Aantal resultate"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Info"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Terug na bo"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Vorige bladsy"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Volgende bladsy"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Vertoon die voorblad"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Soek vir..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "maak skoon"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "soek"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Daar is tans geen data beskikbaar nie."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Enjin naam"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Tellings"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Resultaattelling"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Reaksietyd"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Betroubaarheid"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Totaal"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Verwerking"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Waarskuwings"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Foute en uitsonderings"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Uitsondering"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Boodskap"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Persentasie"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Lêernaam"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funksie"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kode"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Inspekteur"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Mislukte toets"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Opmerking(s)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Voorbeelde"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "sinonieme"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Antwoord"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Laai resultate af"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Probeer soek na:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Boodskappe van die soek enjins"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekondes"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Soek URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Gekopieer"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "kopieer"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Voorstelle"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Soek taal"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Verstek taal"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Outo-bespeur"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "VeiligeSoek"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Streng"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Matig"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Geen"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Tydreeks"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Enige tyd"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Laaste dag"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Laas week"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Laas maand"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Laas jaar"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informasie!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "tans is daar geen koekies gedefinieer nie."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Jammer!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Geen resultate was gevind nie. Jy kan probeer om:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Daar is geen meer resultate nie. Jy kan probeer om:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Verfris die bladsy."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Soek vir 'n ander navraag of kies 'n ander kategorie (hierbo)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Verander die soekenjin wat in die voorkeure gebruik word:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Skakel oor na 'n ander geval:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Soek vir 'n ander navraag of kies 'n ander kategorie."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Gaan terug na die vorige bladsy deur die vorige bladsy-knoppie te gebruik."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Laat toe"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Naam"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Beskrywing"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Dit is die lys van SearXNG se kitsantwoordmodules."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Dit is die lys van plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Outovoltooi"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Vind goed soos jy tik"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Middelbelyning"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Vertoon resultate in die middel van die bladsy (Oscar uitleg)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Dit is die lys van koekies en hul waardes wat SearXNG op jou rekenaar "
"stoor."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Met daardie lys kan u SearXNG-deursigtigheid assesseer."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Koekie naam"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Waarde"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Soek URL van die tans gestoorde voorkeure"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Let wel: om gepasmaakte instellings in die soek-URL te spesifiseer, kan "
"privaatheid verminder deur data na die geklikte resultaatwebwerwe te lek."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL om jou voorkeure in 'n ander blaaier te herstel"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopieer voorkeur-hash"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Voeg gekopieerde voorkeur-hash (sonder URL) in om te herstel"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Voorkeure hash"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Ooptoegang DOI-oplosser"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Kies diens wat deur DOI herskryf gebruik word"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Hierdie oortjie bestaan nie in die gebruikerskoppelvlak nie, maar jy kan "
"in hierdie enjins soek volgens sy !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Aktiveer alles"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Deaktiveer alles"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Ondersteun gekose taal"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Gewig"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maks tyd"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon Resolver"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Wys favicons naar aan soek aantworde"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Hierdie instellings word in jou koekies gestoor, dit laat ons toe om nie "
"hierdie data oor jou te stoor nie."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Hierdie koekies dien jou enigste gerief, ons gebruik nie hierdie koekies "
"om jou op te spoor nie."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Stoor"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Stel verstekwaardes terug"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Terug"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "sleutelbord kortpaaie"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-agtig"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigeer soekresultate met sneltoetse (JavaScript vereis). Druk "
"\"h\"-sleutel op hoof- of resultaatbladsy om hulp te kry."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Beeld proksie"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proksie beeld resultate deur SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Oneindige blaai"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Laai die volgende bladsy outomaties wanneer blaai na die onderkant van "
"die huidige bladsy"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Watter taal verkies jy vir soek?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Kies Outo-detect om SearXNG die taal van jou navraag te laat opspoor."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Metode"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Verander hoe vorms ingedien word"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Navraag in die bladsy se titel"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Wanneer geaktiveer sal die resultaat se bladsy titel jou navraag bevat. "
"Jou blaaier kan hierdie titel opneem"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultate op nuwe oortjies"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Maak resultaat skakels oop in nuwe blaaier oortjies"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filter inhoud"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Soek op kategorie selekteer"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Voer onmiddellik soektog uit as 'n kategorie gekies is. Deaktiveer om "
"verskeie kategorieë te kies"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Verander SearXNG uitleg"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Tema styl"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Kies outo om jou blaaier verstellings te volg"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Enjin tekens"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Toegangstekens vir private enjins"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Koppelvlak taal"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Verander die uitleg taal"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL formatering"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Mooi"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Vol"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Bediener"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Verander aantword URL formatering"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "bewaarplek"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "media wys"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "versteek media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Hierdie webwerf het geen beskrywing verskaf nie."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Lêergrootte"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Datum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tik"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolusie"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formaat"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Enjin"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Sien Bron"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adres"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "wys kaart"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "versteek kaart"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Weergawe"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Onderhouer"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Opgedateer by"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Merkers"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Gewildheid"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lisensie"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projek"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projek tuisblad"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Gepubliseerde datum"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Joernaal"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redakteur"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Uitgewer"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magneet skakel"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent lêer"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Saaier"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Suier"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Aantal lêers"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "versteek video"

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr ""

#~ msgid "No abstract is available for this publication."
#~ msgstr "Geen uittreksel is beskikbaar vir hierdie publikasie nie."

#~ msgid "Self Informations"
#~ msgstr "Eie informasie"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Verander hoe vorms ingedien word, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\"> leer meer oor versoek"
#~ " metodes</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Hierdie inprop toepassing kontroleer of "
#~ "die adres van die versoek 'n TOR"
#~ " uitgang nodus is en stel die "
#~ "gebruiker in kennis indien wel, soos "
#~ "check.torproject.org maar vanaf searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Die TOR uitgang nodus lys "
#~ "(https://check.torproject.org/exit-addresses) is nie"
#~ " bereikbaar nie."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Jy gebruik tans TOR. Jou IP adres lyk na : {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Jy gebruik nie tans TOR nie. Jou IP adres lyk na: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Outo-bespeur soektog taal"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Spoor die navraagsoektaal outomaties op en skakel daaroor oor."

#~ msgid "others"
#~ msgstr "andere"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "Kortpad"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr ""

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Aan"

#~ msgid "Off"
#~ msgstr "Af"

#~ msgid "Enabled"
#~ msgstr "Geaktiveer"

#~ msgid "Disabled"
#~ msgstr "Gedeaktiveer"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Doen soektog onmiddelik indien 'n "
#~ "kategorie geselekteer is. Deaktiveer om "
#~ "veelvoudige kategoriee te selekteer. "
#~ "(JavaScript benodig)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Soortgelyke-VIM kortpad sleutels"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navigeer soekresultate met Vim-agtige "
#~ "sneltoetse (JavaScript vereis). Druk "
#~ "\"h\"-sleutel op hoof- of resultaatbladsy "
#~ "om hulp te kry."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Herskryf resultaatgasheername of verwyder "
#~ "resultate op grond van die gasheernaam"

#~ msgid "Bytes"
#~ msgstr "Grepe"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "vervang Gasheernaam"

#~ msgid "Error!"
#~ msgstr "Fout!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Enjins kan nie resultate ophaal nie"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Begin om 'n nuwe probleem op GitHub in te dien"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Ewekansige getal genereerder"

#~ msgid "Statistics functions"
#~ msgstr "Statistiese funksies"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Verwerk {functions} van die argumente"

#~ msgid "Get directions"
#~ msgstr "Kry aanwysings"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Vertoon jou IP indien die navraag "
#~ "\"ip\" is en jou gebruiker agent "
#~ "indien die navraag \"user agent\" bevat."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Kon nie die lys van Tor-"
#~ "uitgangsnodes aflaai vanaf: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Jy maak gebruik van Tor en dit "
#~ "lys as of jy hierdie eksterne "
#~ "IP-adres het :{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Jy maak gebruik van Tor en dit "
#~ "lys as of jy hierdie eksterne "
#~ "IP-adres het :{ip_address}"

#~ msgid "Keywords"
#~ msgstr "Sleutelwoorde"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Deur gepasmaakte instellings in die "
#~ "voorkeur-URL te spesifiseer, kan dit "
#~ "gebruik word om voorkeure oor toestelle"
#~ " heen te sinkroniseer."

#~ msgid "proxied"
#~ msgstr "gevolmagtig"

