# Arabic translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# ButterflyOfFire ButterflyOfFire, 2018
# ButterflyOfFire, 2018
# d506c013dc1b502e7a53f91ebcbf8f29_985b4b3, 2017-2018
# <PERSON> <<EMAIL>>, 2022, 2023.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <PERSON><PERSON>-<PERSON><PERSON> <Ya<PERSON>-<EMAIL>>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-15 10:37+0000\n"
"Last-Translator: DZDevelopers <<EMAIL>>\n"
"Language: ar\n"
"Language-Team: Arabic "
"<https://translate.codeberg.org/projects/searxng/searxng/ar/>\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : "
"n%100>=3 && n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "بدون تقسيم"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "آخر"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ملفات"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "عام"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "موسيقى"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "شبكات التواصل الإجتماعي"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "صور"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "ڤيديوهات"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "راديو"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "تلفاز"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "علوم التكنولوجيا"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "أخبار"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "خريطة"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "برمجيات البصلة"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "عِلم"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "تطبيقات"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "قواميس"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "كلمات الأغاني"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "حِزم"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "سؤال وجواب"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "مستودعات"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "الموسوعات التشاركية للبرنامج"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "الشبكة العالمية"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "المنشورات العلمية"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "تلقائي"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "فاتح"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "مظلم"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "اسود"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "فترة التشغيل"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "حَول"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "متوسط الحرارة"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "حالة الطقس"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "غائم"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "الحالة الحالية"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "مساء"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "كأنه"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "رطوبة"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "الحرارة العظمى"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "الحرارة الدنيا"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "صباحا"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "ليلا"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "ظهيرة"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "الضغط"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "الشروق"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "الغروب"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "درجة الحرارة"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "مؤشر الأشعة فوق البنفسجية"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "الرؤيا"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "الرياح"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "المشتركين"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "المنشور"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "المستخدمين النشطين"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "التعليقات"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "المستخدم"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "المجتمع"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "النقاط"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "العنوان"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "الكاتب"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "مفتوح"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "مغلق"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "أُجيبت"

#: searx/webapp.py:292
msgid "No item found"
msgstr "تعذر العثور على عناصر"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "المصدر"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "حدث خلل أثناء تحميل الصفحة التالية"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "إنّ الإعدادات خاطئة، يرجى تعديل خياراتك"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "إعدادات غير صالحة"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "خطأ في البحث"

#: searx/webutils.py:35
msgid "timeout"
msgstr "نفذ الوقت"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "خطأ تحليل"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "خطأ في بروتوكول HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "خطأ في الشبكة"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "خطأ SSL: فشل التحقق من صحة الشهادة"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "تعطل غير متوقع"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "خطأ HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "خطأ في اتصال HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "خطأ في وكيل البروكسي"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "أسئلة التحقق"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "الكثير من الطلبات"

#: searx/webutils.py:58
msgid "access denied"
msgstr "الدخول مرفوض"

#: searx/webutils.py:59
msgid "server API error"
msgstr "خطأ في API الخادم"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "معلق"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minute(s) ago"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "قبل {hours} ساعات، {minutes} دقائق"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "توليد قِيم عشوائية مختلفة"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "حساب {func} من الحجج"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "أظهِر الطريق على الخريطة .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (قديما)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "هذا الإدخال تم استبداله بـ"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "القناة"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "معدل البت"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "تصويتات"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "نقرات"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "اللغة"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations}استجلاب من العام {firstCitationVelocityYear} إلى "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"تعذر قراءة عنوان url للصورة. قد يكون هذا بسبب تنسيق ملف غير مدعوم. تدعم "
"TinEye فقط الصور بتنسيق JPEG أو PNG أو GIF أو BMP أو TIFF أو WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"الصورة أبسط من أن تجد مطابقات. يتطلب TinEye مستوى أساسيًا من التفاصيل "
"المرئية لتحديد التطابقات بنجاح."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "لا يمكن تنزيل الصورة."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "تقييم الكتاب"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "جودة الملف"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "{دالة}"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "قم بتصفية نتائج .onion التي تظهر في القائمة السوداء الخاصة بـ Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "آلة حاسبة بسيطة"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "حساب التعبيرات الرياضية عبر شريط البحث"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "إضافة تجزئة"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "يحول السلسلة إلى ملخص التجزئة."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "ملخص التجزئة"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "مُلحق لأسماء المضيفين (Hostnames)"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"أعِد كتابة أسماء المضيفين (hostnames) أو أزِل النتائج أو حدّد أولوياتها "
"بناءً على اسم المضيف (hostname)"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "فتح الوصول معرف الكائن الرقمي إعادة كتابة"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"تجنب جدران الدفع عن طريق إعادة التوجيه إلى إصدارات الوصول المفتوح من "
"المنشورات عند توفرها"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "نشرة المعلومات"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"يعرض عنوان IP الخاص بك إذا كان الاستعلام \"ip\" ووكيل المستخدم الخاص بك "
"إذا كان الاستعلام \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "عنوانك هو (Ip) "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "وكيل المستخدم الخاص بك هو "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "فحص المكون الإضافي ل Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"يتحقق هذا المكون الإضافي مما إذا كان عنوان الطلب هو عقدة خروج TOR ، ويبلغ"
" المستخدم إذا كان كذلك ، مثل check.torproject.org ولكن من SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "تعذر تنزيل قائمة عقد الخروج الخاصة بـ Tor من"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "أنت تستخدم Tor ويبدو أن لديك عنوان IP الخارجي"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "أنت لا تستخدم Tor ولديك عنوان IP الخارجي"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "مزيل روابط التعقّب"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr ""
"إزالة وسيطات التتبع من \"URL\" الذي تم إرجاعه , إزالة وسيطات التتبع من "
"محدد موقع الموارد الموحد الذي تم إرجاعه"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "إضافة محول الوحدات"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "التحويل بين الوحدات"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "تعذر العثور على الصفحة"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "إذهب إلى %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "صفحة البحث"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "تبرُّع"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "التفضيلات"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "مدعوم بواسطة"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "الخصوصية ذو الاعتبار, محرك البحث عميق عُموميا"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "شيفرة مصدرية"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "تعقب القضايا"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "إحصائيات المحرك"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "نماذج الخوادم العمومية"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "سياسة الخصوصية"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "اتصال بالمشرف المخدم النموذجي"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "انقر على رمز المكبر للقيام بالبحث"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "الطول"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "المشاهدات"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "الكاتب"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "النسخة المخبأة"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "ابدأ بتقديم قضية جديدة على GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "الرجاء التحقق من الأخطاء الموجودة حول هذا المحرك على GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "أؤكد عدم وجود أخطاء حول المشكلة التي أواجهها"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "إذا كان هذا مثيلًا عامًا ، فيرجى تحديد عنوان URL في تقرير الخطأ"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "قم بتقديم مشكلة جديدة على GitHub بالمعلومات الواردة أعلاه"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "دون HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "عرض سجلات الأخطاء وتقديم تقرير خطأ"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!بانج لهذا محرك"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!بانج لمجموعاته"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "وسطي"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "صفحة 80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "صفحة 95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "فشل اختبار المدقق: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "الأخطاء:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "الرئيسية"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "القوائم الإفتراضية"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "واجهة المستخدم"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "الخصوصية"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "المحركات"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "محركات البحث المُستخدَمة حاليًا"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "استفسارات خاصة"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "كعكات الكوكيز"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "حصيلة نتائج البحث"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "معلومات"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "العودة للأعلى"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "الصفحة السابقة"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "الصفحة التالية"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "اعرض الصفحة الامامية"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "البحث عن ..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "مسح"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "بحث"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "لم يتم العثور على أية بيانات في الوقت الحالي. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "إسم المحرك"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "نتائج"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "نتيجة العد"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "مدة الإستجابة"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "إمكانية الإشتغال"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "إجمالي"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "يتم المعالجة"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "تحذيرات"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "الأخطاء والاستثناءات"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "استثناء"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "الرسالة"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "نسبة"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "معامل"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "اسم الملف"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "وظيفة"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "شفرة"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "مدقّق"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "اختبار فاشل"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "تعليق/تعليقات"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "أمثلة"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "التعريفات"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "مرادفات"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "الإجابات"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "حصيلة التنزيل"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "حاول البحث عن :"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "رسائل من محركات البحث"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "ثواني"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "رابط البحث"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "نسخ"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "نسخة"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "الإقتراحات"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "لغة البحث"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "اللغة الإفتراضية"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "الاكتشاف التلقائي"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "البحث المؤمَّن"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "صارم"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "معتدل"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "لا شيء"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "الفترة"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "في أي وقت"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "آخر يوم"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "الأسبوع الماضي"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "الشهر الماضي"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "السنة الفارطة"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "معلومة !"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "حاليا لم تقم بتحديد أي مِن كعكات الكوكيز."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "عفوا !"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "لم يتم العثور على نتائج. يمكنك محاولة:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "لا يوجد أي نتائج آخرى. يمكنك أن تحاول:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "حدث الصفحة."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "ابحث عن استعلام آخر أو اختار فئة أخرى (أعلاه)"

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "قم بتغيير محرك البحث المستخدم في الإعدادات:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "قم بتبديل SearxNG إلى نسخة أخرى:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "ابحث عن استعلام آخر أو اختار فئة أخرى."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "إرجع إلى الصفحة السابقة باستخدام زر العودة."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "تمكين"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "الكلمات الرئيسية (أول كلمة في الاستعلام)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "التسمية"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "الوصف"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "هذه قائمة وحدات الرد الفوري في SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "هذه قائمة المكونات الإضافية."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "تكملة تلقائية"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "العثور على نتائج أثناء الكتابة"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "محاذاة المركز"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "اعرض النتائج في منتصف الصفحة (تخطيط Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"هذه قائمة ملفات تعريف الارتباط وقيمها التي يخزنها SearXNG على جهاز "
"الكمبيوتر الخاص بك."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "باستخدام هذه القائمة ، يمكنك تقييم شفافية SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "إسم ملف تعريف الارتباط‮"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "القيمة"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "ابحث عن عنوان URL للتفضيلات المحفوظة حاليًا"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"ملاحظة: يمكن أن يؤدي تحديد إعدادات مخصصة في عنوان URL للبحث إلى تقليل "
"الخصوصية عن طريق تسريب البيانات إلى مواقع النتائج التي تم النقر عليها."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL لاستعادة تفضيلاتك في متصفح آخر"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"رابط يحتوي على تفضيلاتك. يمكن استخدام هذا الرابط لاستعادة إعداداتك على "
"جهاز مختلف."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "نسخ تجزئة التفضيلات"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "أدخل تجزئة التفضيلات المنسوخة (بدون عنوان URL) لاستعادتها"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "تجزئة التفضيلات"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "معرف الكائن الرقمي (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "فتح الوصول إلى محلل DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "حدد الخدمة التي يستخدمه المعرف الرقمي. (DOI)"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"علامة التبويب هذه غير موجودة في واجهة المستخدم ، ولكن يمكنك البحث في هذه "
"المحركات من خلال !bangs"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "فعّل الكل"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "عطّل الكل"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "يدعم اللغة المختارة"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "وَزن"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "أقصى مدّة"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "محلل أيقونة المفضلة"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "عرض المفضلات قرب نتائج البحث"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"يتم تخزين هذه الإعدادات في ملفات تعريف الارتباط الخاصة بك ، وهذا يسمح لنا"
" بعدم تخزين هذه البيانات عنك."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"تخدم ملفات تعريف الارتباط هذه راحتك وحدك ، فنحن لا نستخدم ملفات تعريف "
"الارتباط هذه لتتبعك."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "حفظ"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "إعاد التعيين إلى الإعدادات الإفتراضية"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "الخلف"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "مفاتيح الاختصار"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "مشابه لبرنامج vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"تنقل في نتائج البحث باستخدام مفاتيح التشغيل السريع (يتطلب JavaScript). "
"اضغط على المفتاح \"h\" في الصفحة الرئيسية أو صفحة النتائج للحصول على "
"المساعدة."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "وكيل بروكسي الصور"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "تمرير نتائج البحث عن الصور عبر بروكسي SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "تمرير الصفحات بلا حدود"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "حمّل تلقائيا الصفحة التالية عن السحب إلى أسفل النتائج"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "ما هي لغتك المفضلة للبحث ؟"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "اختر الاكتشاف التلقائي للسماح لـ SearXNG باكتشاف لغة استعلامك."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "أسلوب HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "تغيير كيفية تقديم الاستمارات"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "طلب بحث في عنوان الصفحة"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"عند التمكين ، يحتوي عنوان صفحة النتائج على كلمات البحث الرئيسية الخاصة "
"بك. يمكن لمتصفحك تسجيل هذا العنوان"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "عرض نتائج البحث في ألسنة جديدة"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "عرض روابط نتائج البحث في ألسنة جديدة للمتصفح"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "فلترة المحتوى"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "البحث في الفئة المحددة"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr "قم بإجراء البحث على الفور إذا تم تحديد فئة. قم بتعطيل لتحديد فئات متعددة"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "السمة"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "تغيير مظهر سيركس"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "نمط"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "اختر تلقائي لاحترام إعدادات متصفحك"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "الرمز المميز للمحرك"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "رمز الوصول إلى المحرك الخاص"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "لغة الواجهة"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "تغيير لغة واجهة البحث"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "تنسيق العنوان"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "جميل"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "ممتلىء"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "مضيف"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "تغيير تنسيق العنوان للنتيجة"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "مستودعات"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "عرض الوسائط"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "إخفاء الوسائط"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "هذا الموقع لم يقدم أي وصف."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "حجم الملف"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "تاريخ"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "نوع"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "الدقة"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "صيغة"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "محرك"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "عرض المصدر"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "عنوان"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "عرض الخريطة"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "إخفاء الخريطة"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "الإصدار"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "المسئول عن صيانة"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "تم التحديث في"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "السمات"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "الشعبية"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "الترخيص"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "المشروع"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "صفحة المشروع الرئيسية"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "تاريخ النشر"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "السجل اليومي"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "المحرر"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "الناشر"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "رابط ماغنت"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "ملف تورنت"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "الزارع"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "الحاصد"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "عدد الملفات"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "عرض الفيديو"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "إخفاء الفيديو"

#~ msgid "Engine time (sec)"
#~ msgstr "المدة المستغرقة للمحرك (ثواني)"

#~ msgid "Page loads (sec)"
#~ msgstr "مدة تحميل الصفحة (ثواني)"

#~ msgid "Errors"
#~ msgstr "أخطاء"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "تحويل روابط HTTP إلى روابط آمنة HTTPS عندما يكون ذلك مُتاحًا"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""

#~ msgid "Color"
#~ msgstr "اللون"

#~ msgid "Blue (default)"
#~ msgstr "أزرق (إفتراضي)"

#~ msgid "Violet"
#~ msgstr "بنفسجي"

#~ msgid "Green"
#~ msgstr "أخضر"

#~ msgid "Cyan"
#~ msgstr "سماوي"

#~ msgid "Orange"
#~ msgstr "برتقالي"

#~ msgid "Red"
#~ msgstr "أحمر"

#~ msgid "Category"
#~ msgstr "الفئة"

#~ msgid "Block"
#~ msgstr "حظر"

#~ msgid "original context"
#~ msgstr "الوضع الأصلي"

#~ msgid "Plugins"
#~ msgstr "الإضافات"

#~ msgid "Answerers"
#~ msgstr "المجيبون"

#~ msgid "Avg. time"
#~ msgstr "متوسط الوقت"

#~ msgid "show details"
#~ msgstr "عرض المعلومات الإضافية"

#~ msgid "hide details"
#~ msgstr "إخفاء المعلومات الإضافية"

#~ msgid "Load more..."
#~ msgstr "تحميل المزيد ..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "تغيير مظهر سيركس"

#~ msgid "Proxying image results through searx"
#~ msgstr "تمرير نتائج البحث عن الصور عبر بروكسي Searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr ""

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr ""

#~ msgid "It look like you are using searx first time."
#~ msgstr "يظهر أنك تستخدم محرك سيركس لأول مرة."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""
#~ "يرجى إعادة المحاولة لاحقًا. إن كنت "
#~ "في عجلة من أمرك، ندعوك إلى البحث"
#~ " عن مثيل خادم آخر لمحرك سيركس."

#~ msgid "Themes"
#~ msgstr "السمات"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "الطريقة"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "الإعدادات المتقدمة"

#~ msgid "Close"
#~ msgstr "غلق"

#~ msgid "Language"
#~ msgstr "اللغة"

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "مدعوم"

#~ msgid "not supported"
#~ msgstr "غير مدعوم"

#~ msgid "about"
#~ msgstr "عن سيركس"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr "واجهة المستخدم"

#~ msgid "Choose style for this theme"
#~ msgstr "إختر الشكل الذي ستبدو عليه هذه السمة"

#~ msgid "Style"
#~ msgstr "الشكل"

#~ msgid "Show advanced settings"
#~ msgstr "إظهار الإعدادات المتقدمة"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "إظهار الكل"

#~ msgid "Disable all"
#~ msgstr "تعطيل الكل"

#~ msgid "Selected language"
#~ msgstr "اللغة المختارة"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "حفظ"

#~ msgid "back"
#~ msgstr "العودة"

#~ msgid "Links"
#~ msgstr "الروابط"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "حصيلة البحث"

#~ msgid "next page"
#~ msgstr "الصفحة التالية"

#~ msgid "previous page"
#~ msgstr "الصفحة السابقة"

#~ msgid "Start search"
#~ msgstr "إبدأ البحث"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "الإحصاءات"

#~ msgid "Heads up!"
#~ msgstr ""

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "يظهر أنك تستخدم محرك سيركس لأول مرة."

#~ msgid "Well done!"
#~ msgstr "أحسنت !"

#~ msgid "Settings saved successfully."
#~ msgstr "تمت عملية حفظ الإعدادات بنجاح."

#~ msgid "Oh snap!"
#~ msgstr "يا للهول !"

#~ msgid "Something went wrong."
#~ msgstr "لقد حدث هناك خلل ما."

#~ msgid "Date"
#~ msgstr "تاريخ"

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "تحصل على الصورة"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "التفضيلات"

#~ msgid "Scores per result"
#~ msgstr "درجات لكل نتيجة"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "محرك بحث يحمي الخصوصية و قابل للتهكير"

#~ msgid "No abstract is available for this publication."
#~ msgstr "لا يوجد ملخص متاح لهذا المنشور."

#~ msgid "Self Informations"
#~ msgstr "معلومات شخصية"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "تغيير طريقة إرسال النماذج ، <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\"> تعرف على المزيد حول "
#~ "طرق الطلب </a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "يتحقق هذا المكون الإضافي مما إذا "
#~ "كان عنوان الطلب هو عقدة خروج TOR"
#~ " ، ويبلغ المستخدم إذا كان كذلك "
#~ "، مثل check.torproject.org ولكن من "
#~ "searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "لا يمكن الوصول إلى قائمة عقدة "
#~ "الخروج TOR (https://check.torproject.org/exit-"
#~ "addresses)."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "انك تستخدم شبكة TOR. ان IP Adress الخاص بك يبدو انه: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "انك لا تستخدم شبكة TOR. ان IP Adress الخاص بك يبدو انه: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "كشف تلقائي عن لغة البحث"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "كشف تلقائي عن لغة البحث والتبديل إليها."

#~ msgid "others"
#~ msgstr "أخرى"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "لا تظهر علامة التبويب هذه في نتائج"
#~ " البحث ، ولكن يمكنك البحث في "
#~ "المحركات المدرجة هنا عبر bangs."

#~ msgid "Shortcut"
#~ msgstr "الإختصار"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "لم تتمكن المحركات من العثور على أية نتيجة."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "يرجى إعادة المحاولة لاحقًا. إن كنت "
#~ "في عجلة من أمرك، ندعوك إلى البحث"
#~ " عن مثيل خادم آخر لمحرك سيركس."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "إعادة التوجيه إلى إصدارات مفتوحة الوصول"
#~ " من المنشورات حيثما أمكن ذلك (يلزم"
#~ " وجود مكون إضافي)"

#~ msgid "Bang"
#~ msgstr "!انفجار"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "تغيير طريقة إرسال النماذج، <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">إعرف المزيد عن طرق "
#~ "الطلب</a>"

#~ msgid "On"
#~ msgstr "يشتغل"

#~ msgid "Off"
#~ msgstr "مُعطَّل"

#~ msgid "Enabled"
#~ msgstr "مُفَعَّل"

#~ msgid "Disabled"
#~ msgstr "غير مُفَعَّل"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "اقامت بحث فوري اذا تم اختيار "
#~ "مجموعة, توقيف تعدد الخيارات (java script"
#~ " ضروريه)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "إختصارات لوحة المفاتيح مثل التي في Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "تصفح نتائج البحث باستخدام مفاتيح "
#~ "الاختصار التي تشبه Vim (مطلوب "
#~ "JavaScript). اضغط على مفتاح \"h\" في "
#~ "الصفحة الرئيسية أو صفحة النتائج للحصول"
#~ " على المساعدة."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "لم نتوصل إلى العثور على أية نتيجة."
#~ " الرجاء إعادة صياغة طلب البحث أو "
#~ "إبحث مع تحديد أكثر من فئة."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "أعد كتابة أسماء مضيفي النتائج أو أزل النتائج بناءً على اسم المضيف"

#~ msgid "Bytes"
#~ msgstr "بايت"

#~ msgid "kiB"
#~ msgstr "ك.بايت"

#~ msgid "MiB"
#~ msgstr "ميغابايت"

#~ msgid "GiB"
#~ msgstr "جيجابيت"

#~ msgid "TiB"
#~ msgstr "تيرابيت"

#~ msgid "Hostname replace"
#~ msgstr "استبدال اسم المضيف"

#~ msgid "Error!"
#~ msgstr "خطأ !"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "لم تتمكن محركات البحث من العثور على أية نتيجة"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "ابدأ بتقديم قضية جديدة على GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "مولّد قيمة عشوائية"

#~ msgid "Statistics functions"
#~ msgstr "الدالات الإحصائية"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "حوسبة معطيات ال{functions}"

#~ msgid "Get directions"
#~ msgstr "احصل على الاتجاهات"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "يعرض IP إذا كان الاستعلام \"ip\" و"
#~ " وكيل المستخدم الخاص بك إذا كان "
#~ "الاستعلام يحتوي على\"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "لم يمكن تنزيل قائمة Tor exit-nodes"
#~ " من عناوين: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "انت تستعمل Tor ويبدو انه لديك هذا الIP: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "أنت لا تستعمل Tor حالياً وهذا هو عنوان الـIP الخاص بك: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "الكلمات الرمزية"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "يمكن استخدام تحديد الإعدادات المخصصة في"
#~ " تفضيلات URL لمزامنة التفضيلات عبر "
#~ "الأجهزة."

#~ msgid "proxied"
#~ msgstr "المخدم البروكسي"

