# Filipino translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# gr01d, 2018
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <PERSON>a <PERSON> <<EMAIL>>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
# <PERSON><PERSON> <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-09 02:43+0000\n"
"Last-Translator: Kita Ikuyo <<EMAIL>>\n"
"Language: fil\n"
"Language-Team: Filipino "
"<https://translate.codeberg.org/projects/searxng/searxng/fil/>\n"
"Plural-Forms: nplurals=2; plural=(n == 1 || n==2 || n==3) || (n % 10 != 4"
" || n % 10 != 6 || n % 10 != 9);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "nang walang karagdagang pagbubuklod ng data"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "iba pa"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "file"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "pangkalahatan"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musika"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "social media"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "larawan"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "mga bidyo"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radyo"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "it"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "mga balita"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "mga onion links"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "agham't siyénsiyá"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "mga aplikasyon"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "diksyonaryo"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "mga liriko"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "Packages"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "Katanungan at Sagot"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repositoryo"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "mga software wikis"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "mga lathalaing pang agham't siyénsiyá"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "Awto"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "maliwanag"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "madilim"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "itim"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "\"uptime\""

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Tungkol"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Karaniwang temp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "sakop ng mga ulap"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Kondisyon"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Kasalukuyang kondisyon"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Hapon"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "parang pakiramdam ng"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "halumimig"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "pinakamataas na temperatura"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "pinikamababang temperatura"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Umaga"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Gabi"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Tanghali"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "presyon"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "silang ng araw"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "sibsib ng araw"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "index ng UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "bisibílidád"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "hangin"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "mga suskritor"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "mga post"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "mga aktibong gumagamit"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "mga komento"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "tagagamit"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "pamayanan"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "mga punto"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "titulo"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "maykatha"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "bukas"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "sarado"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "sinagot"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Walang nakita na aytem"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Pinagmulan"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Error sa paglo-load ng susunod na pahina"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Di-wastong mga setting, pakibago ang iyong mga kagustuhan"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Di-wastong mga setting"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "nagkaproblema sa paghahanap ng mga resulta"

#: searx/webutils.py:35
msgid "timeout"
msgstr "panandaliang pagtigil"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "error sa pag-parse ng mga resulta"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Error sa HTTPS protokol"

#: searx/webutils.py:38
msgid "network error"
msgstr "Network Error"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL error: Nabigo ang pagpapatunay ng sertipiko"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "Hindi inaasahang pagbagsak"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP error"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Error sa koneksyong HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "Proxy Error"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "masyadong maraming mga kahilingan"

#: searx/webutils.py:58
msgid "access denied"
msgstr "tinanggihan ang access"

#: searx/webutils.py:59
msgid "server API error"
msgstr "pagkakamali sa server API"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspendido"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} na minuto ang nakalipas"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} oras at {minutes} na minto ang nakalipas"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Maglabas ng iba't ibang halaga"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Ikwenta ang {func} ng mga argumento"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Pakita sa mapa ang ruta .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (Luma)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Ang tala na ito ay ipinagpaliban ng"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Tyanel"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitrate"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "mga boto"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "mga click"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Wika"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} mga sipi mula sa taon {firstCitationVelocityYear} at "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Hindi mabasa ang url ng imahe. Baka ang format ay hindi suportado. JPEG, "
"PNG, GIF, BMP, TIFF o WebP lamang ang tinatanggap ng TinEye."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Masyadong payak ang imahe. Gusto ni TinEye ng higit pang detalye para "
"makahanap ng katugma."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Hindi ma-download ang imahe na ito."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "rating ng libro"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Kalidad ng file"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Blacklist ng Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Alisin ang mga onion na resulta na lumalabas sa blacklist ng Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Basic na Calculator"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "kalkulahin ang matematika gamit ang rehas ng pagsaliksik"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin na hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Isinasalin ang string sa iba't ibang hash digests."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "Hash digest"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Hostnames plugin"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Isulat muli ang mga hostname, alisin ang mga resulta o unahin ang mga ito"
" batay sa hostname"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Malayang akses sa muling pagsulat ng DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Iwasan ang paywall sa pag-redirect sa open-access na bersyon ng "
"pahahayagan kapagmakukuha"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Pansariling impormasyon"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Ipapakita ang iyong IP kung ang query ay \"ip\" at ang iyong user agent "
"kung ang query ay \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Ang iyong IP ay: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Ang iyong user-agent ay: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Plugin na pang-suri ng Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Ang plugin na ito ay tsini-check kung ang address ng request ay isang TOR"
" exit node, at i-iinform ang user kung oo, gaya ng check.torproject.org "
"ngunit SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Hindi madownload ang listahan ng Tor exit-nodes mula sa"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Ikaw ay gumagamit ng Tor at mukhang meron kang pang-labas na IP address"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Ikaw ay hindi gumagamit ng Tor at meron kang pang-labas na IP address"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Alisin ang URL tracker"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Alisin ang tracker sa ibabalik na URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Converter ng Yunit na plugin"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "ipalit sa pamamagitan ng mga yunit"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Hindi natagpuan ang pahina"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Pumunta sa %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "ang pahina ng paghahanap"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Magbigay"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Mga Kagustuhan"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Pinapatakbo ng"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "isang nagrerespeto sa privacy, at open na metasearch engine"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "sors kowd"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Tagasubaybay ng isyu"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Engine stats"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Pampublikong instances"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Polisiyang pampribado"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontakin ang iyong instance maintainer"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Pindutin ang magnifier para maghanap"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Haba"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "mga pananaw"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Awtor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "naka-cache"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Mag-simulang mag-abot ng mga bagong isyu sa GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Pakitingnan ang mga umiiral nang bug tungkol sa engine na ito sa GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Kinukumpirma kong walang umiiral na bug tungkol sa isyung nakatagpo ko"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Kung ito ay isang pampublikong instance, mangyaring tukuyin ang URL sa "
"ulat ng bug"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Mga-submit ng isang bagong issue sa GitHub kasama ng impormasyong nasa "
"itaas"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Walang HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Tignan ang error logs at magsumite ng bug report"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang para sa engine"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang para sa katerogidad"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Gitna"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Nabigo ang checker test(s): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Mga error:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Pangkalahatan"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Ang mga default na uri"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Ang User interface"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Pagiging Pribado"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Engines"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Ang ginagamit natin na search engines"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Mga Espesyal na Queries"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Bilang ng resulta"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Impormasyon"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Balik sa taas"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Kaninang Pahina"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Susunod na page"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Ipakita ang front page"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Maghanap ng..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "Malinaw"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "maghanap"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Wala pa sa ngayon na makitang datos."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Pangalan ng engine"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Iskor"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "bilang ng mga resulta"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Oras ng pagtugon"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "pagiging maaasahan"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Kabuuan"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "prumoproseso"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Mga babala"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Mga error at exceptions"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "pagkakabigo/eksepsyon"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mensahe"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "porsyento"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "pamantayan"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "pangalan ng file"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "gawain"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "code"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "tagasuri"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Nabigong Pagsusulit"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "(mga) komento"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Mga halimbawa"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Mga Kahulugan"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "síngkahulugán"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Mga sagot"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "I-download ang mga resulta"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Subukan maghanap ng:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Mga mensahe mula sa mga search engine"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "mga segundo"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL ng Search"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "nakopya"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "kopyahin"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Mga mungkahi"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Ang wika ng paghahanap"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Default na wika"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "awtomatikong pangdedetekta"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Ligtas na Paghahanap"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Mahigpit"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Banayad"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Wala"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Agwat ng oras"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Kahit anong oras"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Nakaraang araw"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Nakaraang linggo"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Nakaraang buwan"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Nakaraang taon"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Impormasyon!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "wala pang cookies na naka-define sa ngayon."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Paumanhin!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Walang mga resultang nahanap. Pwede mong subukang:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Walang karagdagang mga resulta. Maaari mong subukan ang:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "I-refresh ang pahina."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Hanapin ang iba pang query o pumili ng ibang kategorya (sa itaas)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Baguhin ang ginagamit na search engine sa mga nais:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Lumipat sa ibang \"instance\":"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Humanap ng ibang query o pumili ng ibang kategorya."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Bumalik sa nakaraang pahina gamit ang button ng nakaraang pahina."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Payagan"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Mga mahahalagang salita (unang salita sa query)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Pangalan"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Paglalarawan"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Ito ang listahan ng mga instant answering module ng SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Ito ang listahan ng mga plugin."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Kusang tinatapos"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Maghanap habang ikaw ang nag-tytype"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Pag-align sa Gitnang"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Tignan ang resulta sa gitnang bahagi ng pahina (Oscar layout)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Ito ang listahan ng mga cookies at values na ini-store ng SearXNG sa "
"i'yong computer."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Sa tulong ng listahan, maaari mong suriin ang transparansiya ng SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Pangalan ng cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Nilalaman"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Search URL ng kasalukuyan na naka-save sa preferences"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Tandaan: kapag gumawa ng sariling settings sa search URL mababawasan ang "
"pagiging pribado dahil magkakaroon ng butas sa datos dahil sa pag-pindot "
"sa resulta na sites."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL para ibalik ang iyong mga preference sa ibang browser"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"URL na naglalaman ng iyong mga kagustuhan. Maaaring gamitin ang URL na "
"ito para i-restore ang mga setting sa ibang device."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopyahin ang hash ng mga preference"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""
"Ilagay ang kinopyahang hash ng mga preference (walang URL) upang mag-"
"restore"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash ng mga preference"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital Object Identifier (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI resolver"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Pumili ng serbisyo na ginagamit sa pagsulat ng DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ang tab na ito ay hindi umiiral sa interface ng gumagamit, ngunit maaari "
"kang maghanap sa mga engine na ito sa pamamagitan ng kanilang !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "paganahin ang lahat"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "huwag paganahin ang lahat"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Suportado ang pinili na wika"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Timbang"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Ang max na oras"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "favicon solver"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Ipakita ang mga favicon malapit sa mga resulta ng paghahanap"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Ang settings ay nakalagay sa cookies upang hindi kami makakuha ng datos "
"mula sa iyo."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Hindi namin ginagamit ang cookies para i-track ka, ito ay para maging "
"maayos ang paggamit mo."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "I-save"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "I-reset ang defaults"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Bumalik"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Mga Hotkeys"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Katulad ng Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Mag-navigate sa mga resulta ng paghahanap gamit ang mga hotkey "
"(JavaScript ay kinakailangan). Pindutin ang \"h\" na key sa pangunahin o "
"resultang pahina para sa tulong."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy ng larawan"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Ang pag-proxy ng mga resulta ng imahe sa pamamagitan ng SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Walang hanggan na pag-scroll"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Awtomatiko na ikarga ang sumunod na pahina kapag nakarating na sa dulo ng"
" kasalukuyang pahina"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Ano ang gusto mong wika sa paghahanap?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Pumili ng Auto-detect upang payagan ang SearXNG na matukoy ang wika ng "
"iyong query."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Paraan ng HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Ipalit panong i-submit ang mga form"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Query sa pamagat ng page"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Kapag pinagana, ang pamagat ng pahina ng resulta ay naglalaman ng iyong "
"query. Maaaring i-record ng iyong browser ang pamagat na ito"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Ang resulta ay na sa bagong tab"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Buksan ang resulta sa panibagong browser tab"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Salain ang mga nilalaman"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Maghanap kapag nakapili ng uri"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Isagawa ang paghahanap kaagad kung may kategoryang pinili. I-disable "
"upang pumili ng maramihang mga kategorya"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Baguhin ang SearXNG layout"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Stilo ng theme"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Pumili ng auto para sundin ang mga setting ng iyong browser"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Mga token ng makina"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "I-access ang mga token para sa mga pribadong makina"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Ang wika ng Interface"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Ibahin ang layout pangwika"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Format ng URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Maganda"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Puno"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Host"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Palitan ang pag-format ng URL ng resulta"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repositoryo"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "ipakita ang media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "itago ang media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ang site na ito ay hindi nagbigay ng deskripsyon."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Laki ng file"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Petsa"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Uri"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolusyon"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Anyo"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Engine"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Tignan ang source"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "address"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "ipakita ang mapa"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "itago ang mapa"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Bersyon"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Tagapangasiwa"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Na-update sa"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Mga Tag/Tatak"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Kasikatan"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lisensya"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "proyekto"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "homepage ng proyekto"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Petsa ng Paglathala"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Talaan"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Patnugot"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "tagapaglathala"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "i-magnet ang link"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "file na torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeder"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Bilang ng mga files"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "ipakita ang video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "itago ang video"

#~ msgid "Engine time (sec)"
#~ msgstr "Oras ng engine (segundo)"

#~ msgid "Page loads (sec)"
#~ msgstr "Oras ng pagkarga ng pahina (segundo)"

#~ msgid "Errors"
#~ msgstr "Kamalian"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Palitan ang HTTP links patungo sa HTTPS hanggat maaari"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Ang mga resulta ang bubuksan sa "
#~ "kasalukuyan window, ito ay ang default."
#~ " Ang plugin na ito ay babaliwalain"
#~ " ang default na kilos sa pagbukas "
#~ "ng panibagong link sa bagong tab/window."

#~ msgid "Color"
#~ msgstr "Kulay"

#~ msgid "Blue (default)"
#~ msgstr "Asul (default)"

#~ msgid "Violet"
#~ msgstr "Byoleta"

#~ msgid "Green"
#~ msgstr "Berde"

#~ msgid "Cyan"
#~ msgstr "Siyano"

#~ msgid "Orange"
#~ msgstr "Kulay-dalandan"

#~ msgid "Red"
#~ msgstr "Pula"

#~ msgid "Category"
#~ msgstr "Uri"

#~ msgid "Block"
#~ msgstr "Harangan"

#~ msgid "original context"
#~ msgstr "orihinal na nilalaman"

#~ msgid "Plugins"
#~ msgstr "Plugins"

#~ msgid "Answerers"
#~ msgstr "Mga pangsagot"

#~ msgid "Avg. time"
#~ msgstr "Gitnang oras"

#~ msgid "show details"
#~ msgstr "ipakita ang nilalaman"

#~ msgid "hide details"
#~ msgstr "itago ang nilalaman"

#~ msgid "Load more..."
#~ msgstr "Maghanap pa..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Ibahin ang layout ng searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "I-Proxy ang resulta ng mga larawan papunta sa searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Ito ang listahan ng instant answering modules ni searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Ito ang listahan ng cookies at ang"
#~ " kanilang value na inilagay ni searx"
#~ " sa iyon kompyuter."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr ""
#~ "Dahil sa listahan na iyon, maaari "
#~ "mong makita ang pagiging transparent ni"
#~ " searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Mukhang ito ang unang paggamit mo ng searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Paki subukan muli o gumamit ng ibang instance ng searx."

#~ msgid "Themes"
#~ msgstr "Tema"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Paraan"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr ""

#~ msgid "Close"
#~ msgstr "Isara"

#~ msgid "Language"
#~ msgstr ""

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "suportado"

#~ msgid "not supported"
#~ msgstr "hindi suportado"

#~ msgid "about"
#~ msgstr "hinggil"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Pumili ng estilo para sa tema na ito"

#~ msgid "Style"
#~ msgstr "Estilo"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "Piniling wika"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "i-save"

#~ msgid "back"
#~ msgstr "bumalik"

#~ msgid "Links"
#~ msgstr "Links"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Mga resulta ng paghahanap"

#~ msgid "next page"
#~ msgstr "pumanhik sa sumunod na pahina"

#~ msgid "previous page"
#~ msgstr "bumalik sa nakaraang pahina"

#~ msgid "Start search"
#~ msgstr "Simulan ang paghahanap"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "stats"

#~ msgid "Heads up!"
#~ msgstr "Alerto!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Mahusay!"

#~ msgid "Settings saved successfully."
#~ msgstr "Tagumpay sa pag-save ng settings."

#~ msgid "Oh snap!"
#~ msgstr "Naku!"

#~ msgid "Something went wrong."
#~ msgstr "Nagkaproblema."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Kuhanin ang larawan"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr ""

#~ msgid "Scores per result"
#~ msgstr "Iskor ng bawat resulta"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "isang gumagalang sa pagiging pribado, hackable na metasearch engine"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Walang nakita na abstract para sa pahahayag na ito."

#~ msgid "Self Informations"
#~ msgstr "Pansariling impormasyon"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ibahin kung paano ang mga forms "
#~ "are pinapasa, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">alamin ang iba pang mga"
#~ " request methods</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Ang plugin na ito ay tsini-check"
#~ " kung ang address ng request ay "
#~ "isang TOR exit node, at i-iinform "
#~ "ang user kung oo, gaya ng "
#~ "check.torproject.org ngunit searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Ang TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) ay "
#~ "unreachable."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Ikaw ay gumagamit ng TOR. Ang i'yong IP address ay: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Ikaw ay hindi gumagamit ng TOR. Ang i'yong IP address ay: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "other pa"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Ang tab na ito ay hindi lumalabas"
#~ " para sa mga resulta ng paghahanap,"
#~ " ngunit maaari kang maghanap sa mga"
#~ " engine na nakalista dito sa "
#~ "pamamagitan ng bangs."

#~ msgid "Shortcut"
#~ msgstr "Pagikliin/Maikli"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Hindi makuha ng engines ang mga resulta."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Pakiusap, subukan muli mamaya o humanap ng ibang SearXNG instance."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Mag-redirect sa open-access na mga"
#~ " bersyon ng mga publikasyon kapag "
#~ "available (kailangan ang plugin)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Bukas"

#~ msgid "Off"
#~ msgstr "Sara"

#~ msgid "Enabled"
#~ msgstr "Paganahin"

#~ msgid "Disabled"
#~ msgstr "Hindi paganahin"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Gawin ang paghahanap hanggat maaari "
#~ "kapag nakapili ng uri. Alisin kapag "
#~ "nakapili ng maraming uri. (Nangagailangan "
#~ "ng JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Kasingtulad ng vim hotkeys"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Maglakbay sa resulta ng paghahanap gamit"
#~ " ang 'Vim-like hotkeys' (Nangagailangan "
#~ "ito ng JavaScript). Pindutin ang \"h\""
#~ " na pindutan sa panguna o ang "
#~ "pahina ng resulta para makahingi ng "
#~ "tulong."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "wala kaming nakita na resulta. Pakiusap"
#~ " na ibahin ang tanong o maghanap "
#~ "sa maraming uri."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Palitan ang resulta ng hostname o "
#~ "tanggalin ang resulta base sa hostname"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Palitan ang hostname"

#~ msgid "Error!"
#~ msgstr "Kamalian!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Hindi makuha ng engines ang mga resulta"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Mag-simulang mag-abot ng mga bagong isyu sa GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Random na generator ng halaga"

#~ msgid "Statistics functions"
#~ msgstr "Estatistika ng mga tungkulin"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Tuusin ang {functions} ng pangangatuwiran"

#~ msgid "Get directions"
#~ msgstr "Kumuha ng direksyon"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Ipapakita ang iyong IP kapag ang "
#~ "tanong ay \"ip\" at ang iyong user"
#~ " agent kapag ang sa tanong ay "
#~ "naglalaman ng \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Hindi ma-download ang listahan ng "
#~ "mga Tor exit-node mula sa: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Ginagamit mo ang Tor at mukang ito"
#~ " ang iyong external IP address: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Hindi mo ginagamit ang Tor at ito"
#~ " ang iyong external IP address: "
#~ "{ip_address}"

#~ msgid "Keywords"
#~ msgstr "Mga keyword"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Ang pagtukoy ng pasadyang mga setting"
#~ " sa URL ng mga preference ay "
#~ "maaaring gamitin upang i-sync ang mga"
#~ " preference sa iba't ibang mga "
#~ "aparato."

#~ msgid "proxied"
#~ msgstr "na-proxy"

