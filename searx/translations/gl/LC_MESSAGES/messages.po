# Galician translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020, 2022.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018-2019, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-03 03:33+0000\n"
"Last-Translator: ghose <<EMAIL>>\n"
"Language-Team: Galician <https://translate.codeberg.org/projects/searxng/"
"searxng/gl/>\n"
"Language: gl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "sen posterior subagrupamento"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "outro"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ficheiros"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "xeral"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "música"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "medios sociais"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "imaxes"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "vídeos"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "TIC"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "novas"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "cebolas"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "ciencia"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "apps"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "dicionario"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "letras"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paquetes"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "preguntas e respostas"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repos"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wikis de software"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "publicacións científicas"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automático"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "claro"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "escuro"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "negro"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Activo fai"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Sobre"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Temp. media"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Cuberto"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Situación"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Estado actual"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Tarde"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Sensación"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Humidade"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Temp. Máx."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Temp. Mín."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Mañán"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noite"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Mediodía"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Presión"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Abrente"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Solpor"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Índice UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Visibilidade"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vento"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Ceo despexado"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Nubrado"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Agradable"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Néboa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Chuvia forte e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Trebóns en tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Treboada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Chuvia forte"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Pedrazo forte e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Trebóns con pedrazo e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Trebóns con pedrazo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Pedrazo forte"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Nevarada forte e trono"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Nevaradas fortes e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Nevaradas fortes"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Neve mesta"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Chuvieras e tronadas"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Chuvieras febles e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Chuvieiras lixeiras"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Chuvia lixeira"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Pedrazo miúdo e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Chuvieiras con pedrazo miúdo e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Chuvieiras con pedrazo miúdo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Pedrazo miúdo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Nevarada lixeira e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Nevaradas lixeiras e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Nevaradas lixeiras"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Nevarada lixeira"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Parcialmente cuberto"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Chuvia e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Chuvieiras e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Chuvieiras"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Chuvia"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Pedrazo e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Chuvieiras con pedrazo e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Chuvieiras con pedrazo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Pedrazo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Nevarada e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Nevaradas e tronada"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Nevaradas"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Neve"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "subscritoras"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "publicacións"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "usuarias activas"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "comentarios"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "usuaria"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "comunidade"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "puntos"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "título"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autoría"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Abrir"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "fechado"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "respondido"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Non se atoparon elementos"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Fonte"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Erro ao cargar a páxina seguinte"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Axustes non válidos, por favor edita a configuración"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Axustes non válidos"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "fallo na busca"

#: searx/webutils.py:35
msgid "timeout"
msgstr "tempo máximo"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "erro sintáctico"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "erro de protocolo HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "erro de conexión"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Erro SSL: fallou a validación do certificado"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "erro non agardado"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "Erro HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Erro da conexión HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "erro do proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "demasiadas solicitudes"

#: searx/webutils.py:58
msgid "access denied"
msgstr "acceso denegado"

#: searx/webutils.py:59
msgid "server API error"
msgstr "erro na API do servidor"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspendido"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "fai {minutes} minuto(s)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "fai {hours} hora(s), {minutes} minuto(s)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Xerar diferentes valores aleatorios"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Cálculo {func} dos argumentos"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Mostrar ruta no mapa..."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLETO)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Esta entrada foi proporcionada por"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Canle"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "taxa de bits"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "votos"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "clicks"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Idioma"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citas desde o ano {firstCitationVelocityYear} ao "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Non se puido ler o url da imaxe. Podería ser debido a un formato do "
"ficheiro non soportado. TinEye só soporta imaxes tipo JPEG, PNG, GIF, "
"BMP, TIFF ou WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"A imaxe é demasiado simple para atopar coincidencias. TinEyes require un "
"nivel de detalle básico para poder atopar coincidencias."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Non se puido descargar a imaxe."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Valoración do libro"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Calidade do ficheiro"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Lista negra de Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrar os resultados de onion que aparecen na lista negra de Ahmia"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Calculadora básica"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Calcular expresións matemáticas usando a barra de busca"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Complemento de suma"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Converte o escrito usando diferentes funcións hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "función hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Complemento de nomes de servidor"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Reescribe nomes de servidor, elimina resultados ou prioriza en función do"
" servidor"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Reescritura Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Evitar valados de pago redirixindo a versións abertas das publicacións "
"cando estean dispoñibles"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Información propia"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Mostra o teu IP se a consulta é «ip» e o User Agent se a consulta é "
"«user-agent»."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "O teu IP: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "O teu user-agent: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Complemento para comprobar Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Este complemento comproba se o enderezo da solicitude é un nodo-saída de "
"Tor, e informate de se o é; como check.torproject.org, pero desde "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Non se descargou a lista de nodos de saída de Tor desde"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Estás a usar Tor e semella que tes o enderezo IP de saída"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Non estás a usar Tor e tes o enderezo IP de saída"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Eliminador de rastrexadores na URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Elimina os elementos de rastrexo da URL devolta"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Complemento conversor de unidades"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Converter unidades"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Páxina non atopada"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Ir a %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "páxina de busca"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Doar"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Axustes"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Proporcionado por"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "metabuscador aberto que respecta a privacidade"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Código fonte"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Seguimento de incidencias"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Estatísticas do buscador"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instancias públicas"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Política de privacidade"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Contactar coa administración"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Preme na lupa para realizar a busca"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Duración"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Visualizacións"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autoría"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "en memoria"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Abre unha incidencia en GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Comproba que non exista xa un informe sobre este motor en GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Confirmo que non existe un informe sobre este problema que atopei"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Se esta é unha instancia pública, indica o URL no informe do problema"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Abre unha incidencia en Github incluíndo a información superior"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Sen HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Ver rexistros do erro e enviar informe do problema"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang para este buscador"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang para as súas catergorías"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Median"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Test con fallo(s): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Erros:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Xeral"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Categorías por defecto"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interface"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privacidade"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Motores"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Motores de busca utilizados actualmente"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Consultas especiais"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Rastros"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Número de resultados"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Info"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Ir arriba"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Páxina anterior"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Páxina seguinte"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Mostrar páxina de inicio"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Buscar por..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "limpar"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "buscar"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Non hai datos dispoñibles. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nome do motor"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Puntuacións"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Número de resultados"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Tempo de resposta"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Fiabilidade"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Procesando"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Avisos"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Erros e excepcións"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Excepción"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mensaxe"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Porcentaxe"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parámetro"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nome de ficheiro"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Función"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Código"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Verificador"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Test con fallo"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Comentario(s)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Exemplos"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definicións"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinónimos"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Síntese como"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Respostas"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Descargar resultados"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Intenta buscar:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Mensaxes desde os motores de busca"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "segundos"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL da busca"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Copiado"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copiar"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Suxestións"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Idioma de busca"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Idioma por defecto"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Autodetectar"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Busca segura"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Estrita"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderada"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Ningunha"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Marco temporal"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Calquera momento"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Último día"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Última semana"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Último mes"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Último ano"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Información!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "actualmente non hai rastros establecidos."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Lamentámolo!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Non hai resultados. Podes probar:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Non hai máis resultados. Intenta:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Actualizar a páxina."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Facer outra consulta ou escoller outra categoría (das de arriba)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Cambiar o motor de busca establecido nos axustes:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Cambiar a outra instancia:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Facer unha nova consulta ou seleccionar outra categoría."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Volver á páxina anterior usando o botón de páxina anterior."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Permitir"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Palabras clave (primeira palabra na consulta)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nome"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Descrición"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Esta é a lista de módulos de respostas instantáneas de SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Esta é a lista de complementos."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Autocompletar"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Ir buscando metras escribes"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Situar no centro"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Mostra os resultados no centro da páxina (interface Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Esta é a lista de rastros que SearXNG garda na túa computadora xunto cos "
"seus valores."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Con esta lista podes dar conta da transparencia de SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nome do rastro"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valor"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL de Busca dos axustes gardados actualmente"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Nota: establecer axustes personalizados no URL de busca pode reducir a "
"túa privacidade ao filtrar datos aos sitios web dos resultados."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL para restablecer as túas preferencias noutro navegador"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Un URL que contén as túas preferencias. Este URL pode usarse para "
"restablecer os axustes noutro dispositivo."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copiar suma de comprobación dos axustes"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""
"Escribe a suma de comprobación copiada das preferencias (sen URL) para "
"restablecer"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Suma de comprobación das preferencias"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Identificador do Obxecto Dixital (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Resolutor Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Elixe o servizo utilizado para rescribir DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Esta lapela non existe na interface de usuaria, mais podes buscar nestes "
"buscadores grazas aos seus !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Activar todo"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Desactivar todo"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Soporta o idioma seleccionado"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Peso"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Tempo máx"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Orixe da icona da web"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Mostrar icona da web preto do resultado da busca"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Estes axustes gárdanse en rastros, así non temos que almacenar ningún "
"dato sobre ti."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Estes rastros son para a túa conveniencia, non utilizamos os rastros para"
" rastrexarte."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Gardar"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Restablecer"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Volver"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Atallos"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Estilo-Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navega entre os resultdos cos atallos (require JavaScript). Preme tecla "
"\"h\" na páxina de resultados para obter axuda."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy de imaxes"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Usar o proxy de SearXNG para resultados das imaxes"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Desprazamento infinito"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Cargar automáticamente a seguinte páxina ó desprazarse ó fondo da páxina "
"actual"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Que idioma prefires para buscar?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Elixe Autodetectar para deixar que SearXNG detecte o idioma da túa "
"consulta."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Método HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Cambiar o xeito en que se envían formularios"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Consulta no título da páxina"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Se está activado, o título da páxina de resultados contén a túa consulta."
" O navegador pode rexistrar este título"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultados en novas lapelas"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Abrir ligazóns de resultados en novas lapelas do navegador"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtro de contido"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Busca en categoría seleccionada"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Buscar inmediatamente se hai unha categoría seleccionada. Desactiva para "
"elixir varias categorías"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Decorado"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Cambiar a interface de SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Estilo do decorado"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Elixe auto para que siga os axustes do navegador"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokens do buscador"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tokens de acceso para buscadores privados"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Idioma da interface"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Cambiar o idioma da interface"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Dar formato ao URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Embelecer"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Completo"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Servidor"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Cambiar o formato do URL do resultado"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repo"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "mostrar medios"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "agochar medios"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "A web non proporcionou unha descrición."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Tamaño do ficheiro"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Data"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tipo"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolución"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formato"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Ver fonte"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "enderezo"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "mostrar mapa"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "agochar mapa"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versión"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Mantemento"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Actualizado o"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etiquetas"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularidade"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licenza"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Proxecto"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Páxina web do proxecto"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Data de publicación"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Xornal"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Edición"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Editorial"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "ligazón magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "ficheiro torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Sementadora"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Cliente"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Número de ficheiros"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "mostrar vídeo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "agochar vídeo"

#~ msgid "Engine time (sec)"
#~ msgstr "Tempo de busca (seg)"

#~ msgid "Page loads (sec)"
#~ msgstr "Cargou en (seg)"

#~ msgid "Errors"
#~ msgstr "Fallos"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA requerido"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Reescribir ligazóns HTTP a HTTPS se fose posible"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Por omisión, os resultados ábrense na"
#~ " mesma lapela. Este engadido sobreescribe"
#~ " o comportamento por omisión para "
#~ "abrir as ligazóns en novas "
#~ "lapelas/ventás. (Require JavaScript)"

#~ msgid "Color"
#~ msgstr "Cor"

#~ msgid "Blue (default)"
#~ msgstr "Azul (por omisión)"

#~ msgid "Violet"
#~ msgstr "Violeta"

#~ msgid "Green"
#~ msgstr "Verde"

#~ msgid "Cyan"
#~ msgstr "Cian"

#~ msgid "Orange"
#~ msgstr "Laranxa"

#~ msgid "Red"
#~ msgstr "Vermello"

#~ msgid "Category"
#~ msgstr "Categoría"

#~ msgid "Block"
#~ msgstr "Bloquear"

#~ msgid "original context"
#~ msgstr "contexto orixinal"

#~ msgid "Plugins"
#~ msgstr "Engadidos"

#~ msgid "Answerers"
#~ msgstr "Respostas"

#~ msgid "Avg. time"
#~ msgstr "Tempo medio"

#~ msgid "show details"
#~ msgstr "mostrar detalles"

#~ msgid "hide details"
#~ msgstr "agochar detalles"

#~ msgid "Load more..."
#~ msgstr "Cargar máis..."

#~ msgid "Loading..."
#~ msgstr "Cargando..."

#~ msgid "Change searx layout"
#~ msgstr "Cambiar a disposición de searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Utilizar o proxy de searx para as imaxes dos resultados"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Este é o listado dos módulos de respostas instantáneas de searx"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Este é o listados dos testemuños e"
#~ " os seus valores que searx almacena"
#~ " na túa computadora."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Con esta lista podes comprobar a transparencia de searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Semella que é a primeira vez que utilizas searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Por favor, inténtao máis tarde ou busca outra instancia de searx."

#~ msgid "Themes"
#~ msgstr "Decorados"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Método"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Axustes avanzados"

#~ msgid "Close"
#~ msgstr "Pechar"

#~ msgid "Language"
#~ msgstr "Idioma"

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "soportado"

#~ msgid "not supported"
#~ msgstr "non soportado"

#~ msgid "about"
#~ msgstr "Acerca de"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Escolle o estilo para este decorado"

#~ msgid "Style"
#~ msgstr "Estilo"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "Permitir todo"

#~ msgid "Disable all"
#~ msgstr "Desactivar todo"

#~ msgid "Selected language"
#~ msgstr "Idioma seleccionado"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "gardar"

#~ msgid "back"
#~ msgstr "atrás"

#~ msgid "Links"
#~ msgstr "Ligazóns"

#~ msgid "RSS subscription"
#~ msgstr "Subscrición RSS"

#~ msgid "Search results"
#~ msgstr "Resultados da busca"

#~ msgid "next page"
#~ msgstr "páxina seguinte"

#~ msgid "previous page"
#~ msgstr "páxina anterior"

#~ msgid "Start search"
#~ msgstr "Iniciar busca"

#~ msgid "Clear search"
#~ msgstr "Baleirar busca"

#~ msgid "Clear"
#~ msgstr "Baleirar"

#~ msgid "stats"
#~ msgstr "estatísticas"

#~ msgid "Heads up!"
#~ msgstr "Heads up!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Ben feito!"

#~ msgid "Settings saved successfully."
#~ msgstr "Gardáronse correctamente os Axustes."

#~ msgid "Oh snap!"
#~ msgstr "Vaia!"

#~ msgid "Something went wrong."
#~ msgstr "Algo fallou."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Obter imaxe"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "axustes"

#~ msgid "Scores per result"
#~ msgstr "Puntuacións por resultado"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "un metabuscador configurable que respecta a túa privacidade"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Non hai dispoñible un extracto para esta publicación."

#~ msgid "Self Informations"
#~ msgstr "Información propia"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Cambiar cómo se envían os formularios,"
#~ " <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">aprende máis sobre os "
#~ "métodos de consulta</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Este complemento comproba se o enderezo"
#~ " da solicitude é un nodo de "
#~ "saída TOR, e informa ás usuarias "
#~ "se o é, como check.torproject.org pero"
#~ " desde searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "A lista dos nodos de saída TOR "
#~ "(https://check.torproject.org/exit-addresses) non é"
#~ " accesible."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Estás a usar TOR. O teu enderezo ip semella ser: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Non estás a usar TOR. O teu enderezo IP semella ser: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""
#~ "Non se puido descargar a lista de"
#~ " nodos de saída a Tor desde "
#~ "https://check.torproject.org/exit-addresses."

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""
#~ "Estás a usar Tor. Este semella ser"
#~ " o teu enderezo IP externo: "
#~ "{ip_address}."

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr "Non estás a usar Tor. Tes este enderezo IP externo: {ip_address}."

#~ msgid "Autodetect search language"
#~ msgstr "Detección automática do idioma"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Detectar automáticamente o idioma usado na busca e cambiar a el."

#~ msgid "others"
#~ msgstr "outros"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Esta lapela non é para os "
#~ "resultados, pero podesbuscar nos buscadores"
#~ " aquí mostrados a través de bangs."

#~ msgid "Shortcut"
#~ msgstr "Atallo"

#~ msgid "!bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Esta lapela non existe na interface "
#~ "da usuaria, pero podes buscar nestes "
#~ "buscadores grazas aos !bangs."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Os buscadores non poden obter resultados."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Inténtao máis tarde ou busca noutra instancia de SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Redireccionar a versións abertas das "
#~ "publicacións cando estén dispoñibles (require"
#~ " o engadido)"

#~ msgid "Bang"
#~ msgstr "Bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Cambiar o xeito de enviar formularios,"
#~ " <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">coñece máis sobre os "
#~ "métodos de solicitude</a>"

#~ msgid "On"
#~ msgstr "On"

#~ msgid "Off"
#~ msgstr "Off"

#~ msgid "Enabled"
#~ msgstr "Activado"

#~ msgid "Disabled"
#~ msgstr "Desactivado"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Busca ó momento se hai unha "
#~ "categoría seleccionada. Desactivar para "
#~ "seleccionar múltiples categorías. (Require "
#~ "JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Atallos como os de Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navegar nos resultados da busca con "
#~ "atallos como os de Vim (require "
#~ "JavaScript). Preme \"h\" na pantalla "
#~ "principal ou de resultados para obter"
#~ " axuda."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "non atopamos ningún resultado. Por "
#~ "favor, realiza outra consulta ou busca"
#~ " en máis categorías."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Reescribir o nome do servidor dos "
#~ "resultados ou eliminar resultados en "
#~ "función do nome do servidor"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Substituír servidor"

#~ msgid "Error!"
#~ msgstr "Fallo!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Os buscadores non obtiveron resultados"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Crea un novo informe en GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Xerador de valor aleatorio"

#~ msgid "Statistics functions"
#~ msgstr "Funcións de estatística"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Calcula {functions} dos argumentos"

#~ msgid "Get directions"
#~ msgstr "Obter direccións"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Mostra o teu IP se a consulta "
#~ "é \"ip\", e o teu User Agent "
#~ "se a consulta contén \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Non se puido descargar a lista de"
#~ " nodos de saída a Tor desde: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Estás usando Tor e semella que tes"
#~ " este enderezo IP externo: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Non estás usando Tor e tes este endero IP externo: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Palabras chave"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Cos axustes personalizados gardados nun "
#~ "URL coas preferencias podes utilizalo "
#~ "para sincronizalas entre dispositivos."

#~ msgid "proxied"
#~ msgstr "a través de proxy"
