# Basque translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# beriain, 2018
# beriain, 2018-2019
# beriain, 2020-2021
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-02-12 15:39+0000\n"
"Last-Translator: alexgabi <<EMAIL>>"
"\n"
"Language: eu\n"
"Language-Team: Basque "
"<https://translate.codeberg.org/projects/searxng/searxng/eu/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "Itzuli"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "beste bat"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "fitxategiak"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "orokorra"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musika"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sare sozialak"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "irudiak"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "bideoak"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "irratia"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tb"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "informatika"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "berriak"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "tipulak"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "zientzia"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikazioak"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "hiztegiak"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "letrak"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketeak"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "Galdera eta erantzunak"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "biltegiak"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "software wikiak"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "argitalpen zientifikoak"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatikoa"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "argia"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "iluna"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "beltza"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Epea"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Honi buruz"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Batez besteko tenp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Lainotua"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Baldintza"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Uneko baldintza"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Arratsaldean"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Gustura sentitzen da"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Hezetasuna"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Gehienezko tenp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Gutxienezko tenp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Goizean"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Gauean"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Eguerdian"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Presioa"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Egunsentia"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Ilunabarra"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Tenperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV indizea"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Ikusgarritasuna"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Haizea"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "harpidedunak"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "mezuak"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "erabiltzaile aktiboak"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "iruzkinak"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "erabiltzailea"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "komunitatea"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "puntuak"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "izenburua"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "egilea"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "ireki"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "itxita"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "erantzunda"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Ez da elementurik aurkitu"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Iturria"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Errorea hurrengo orrialdea kargatzean"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ezarpen baliogabeak, editatu zure hobespenak"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ezarpen baliogabeak"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "bilaketa akatsa"

#: searx/webutils.py:35
msgid "timeout"
msgstr "itxarote-denbora"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "analizatze errorea"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP protokoloaren errorea"

#: searx/webutils.py:38
msgid "network error"
msgstr "sareko errorea"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL errorea: ziurtagiria baliozkotzeak huts egin du"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "ustekabeko kraskatzea"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP errorea"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP konexioaren errorea"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxy-aren errorea"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "eskaera gehiegi"

#: searx/webutils.py:58
msgid "access denied"
msgstr "sarbidea ukatua"

#: searx/webutils.py:59
msgid "server API error"
msgstr "API zerbitzariaren errorea"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Etenda"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "duela {minutes} minutu"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "duela {hours} ordu eta {minutes} minutu"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Ausazko balio ezberdinak sortu"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Kalkulatu argumentuen {func}"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Erakutsi ibilbidea mapan..."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ZAHARKITUA)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Sarrera hau hurrengoarekin ordezkatu da"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanala"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bit emaria"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "botoak"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikak"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Hizkuntza"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} aipamen {firstCitationVelocityYear} urtetik "
"{lastCitationVelocityYear} bitartekoak"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Ezin izan da irudiaren URLa irakurri. Baliteke hori onartzen ez den "
"fitxategi-formatu baten ondorioz izatea. TinEye-k JPEG, PNG, GIF, BMP, "
"TIFF edo WebP diren irudiak soilik onartzen ditu."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Irudia sinpleegia da antzekoak aurkitzeko. TinEye-k oinarrizko xehetasun "
"bisual bat behar du antzekoak ongi identifikatzeko."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Ezin izan da deskargatu irudia."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Liburuaren balorazioa"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Fitxategiaren kalitatea"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Kalkulatu adierazpen matematikoak bilaketa-barraren bidez"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash plugina"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Kateak traola laburpen desberdinetara bihurtzen ditu."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "traola laburpena"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Hostnames plugina"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Berridatzi ostalari-izenak, kendu emaitzak edo eman lehentasuna ostalari-"
"izenaren arabera"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Berridatzi Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Saihestu ordain-hormak argitalpenen sarbide irekiko bertsioetara "
"birbideratuz, ahal denean"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Norberaren informazioa"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Zure IPa bistaratzen du kontsulta \"ip\" bada eta zure erabiltzaile-"
"agentea baldin eta \"erabiltzaile-agentea\" bada."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "zure IPa hau da: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Zure erabiltzaile-agentea hau da: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor check plugina"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Plugin honek eskaeraren helbidea Tor-eko irteera-nodo bat den egiaztatzen"
" du eta hala ote den jakinarazten dio erabiltzaileari; "
"check.torproject.org bezala, baina SearXNG-tik."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Ezin izan da Tor-en irteera-nodoen zerrenda deskargatu"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Tor erabiltzen ari zara eta badirudi kanpoko IP helbidea duzula"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Tor erabiltzen ari zara eta kanpoko IP helbidea duzula dirudi"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "URL aztarnariak kendu"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Aztarnarien argumentuak kendu itzulitako URLtik"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Bihurtu unitateak"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Orria ez da aurkitu"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "%(search_page)s(e)ra joan."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "bilaketa orria"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Lagundu diruz"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Hobespenak"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Garatzailea"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "pribatutasuna errespetatzen duen metabilatzaile irekia"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Iturburu-kodea"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Arazoen jarraipena"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Bilatzaileen estatistikak"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instantzia publikoak"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Pribatutasun politika"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Instantziaren mantentzailearekin harremanetan jarri"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Lupan sakatu bilaketa egiteko"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Luzera"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Ikuspegiak"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Egilea"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "cachean gordeta"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Hasi gai -issue- berri bat bidaltzen GitHub-en"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Egiaztatu motor honi buruzko akatsik dagoen GitHub-en"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Berresten dut aurkitzen dudan arazoari buruzko akatsik ez dagoela"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Hau instantzia publikoa bada, mesedez zehaztu URLa akatsen txostenean"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Bidali gai -issue- berri bat Github-en goiko informazioa barne"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "HTTPS-rik ez"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Ikusi erroreen erregistroak eta bidali akatsen txostena"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang motor honetarako"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang bere kategorietarako"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Tartekoa"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Egiaztatzailearen probak huts egin du: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Erroreak:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Orokorra"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Lehenetsitako kategoriak"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Erabiltzailearen interfazea"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Pribatutasuna"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Bilatzaileak"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Erabiltzen ari diren bilatzaileak"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Kontsulta bereziak"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookieak"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Emaitza kopurua"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informazioa"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Gora bueltatu"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Aurreko orria"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Hurrengo orria"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Erakutsi hasierako orria"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Bilatu..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "garbitu"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "bilatu"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Une honetan ez dago daturik eskuragarri."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Bilatzailearen izena"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Balorazioak"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Emaitzen zenbaketa"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Erantzuteko denbora"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Fidagarritasuna"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Guztira"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Prozesatzen"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Abisuak"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Errore eta salbuespenak"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Salbuespena"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mezua"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Ehunekoa"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametroa"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Fitxategiaren izena"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funtzioa"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kodea"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Zuzentzailea"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Probak huts egin du"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Iruzkina(k)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Adibideak"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definizioak"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinonimoak"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Erantzunak"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Deskargatu emaitzak"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Saiatu hau bilatzen:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Bilatzaileen mezuak"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "segundo"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Bilaketaren URLa"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopiatuta"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopiatu"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Iradokizunak"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Bilaketaren hizkuntza"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Lehenetsitako hizkuntza"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Auto-detektatu"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Bilaketa segurua"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Zorrotza"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderatua"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Bat ere ez"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Denbora tartea"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Edonoiz"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Azken eguna"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Azken astea"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Azken hilabetea"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Azken urtea"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informazioa!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "une honetan, ez dago cookierik definituta."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Barkatu!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Ez da emaitzarik aurkitu. Saia zaitezke:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Ez dago emaitza gehiago. Saia zaitezke:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Eguneratu orrialdea."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Bilatu beste kontsulta bat edo hautatu beste kategoria bat (goian)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Aldatu hobespenetan erabilitako bilatzailea:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Aldatu beste instantzia batera:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Bilatu beste kontsulta bat edo hautatu beste kategoria bat."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Itzuli aurreko orrialdera aurreko orrialdeko botoia erabiliz."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Baimendu"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Gako-hitzak (lehenengo hitza kontsultan)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Izena"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Deskribapena"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Hau da SearXNG-ren berehalako erantzuteko moduluen zerrenda."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Hau da pluginen zerrenda."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Osatze automatikoa"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Aurkitu idatzi bitartean"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Erdiko Lerrokadura"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Emaitzak orriaren erdialdean bistaratzen ditu (Oscar diseinua)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Hau da SearXNG zure ordenagailuan gordetzen ari den cookieen zerrenda eta"
" haien balioak."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Zerrenda horrekin, SearXNGren gardentasuna ebaluatu dezakezu."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Cookiearen izena"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Balioa"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Bilatu une honetan gordetako hobespenen URLa"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Oharra: bilaketa URLan ezarpen pertsonalizatuak zehazteak pribatutasuna "
"txikiagotu dezake klikatutako erantzun guneetara datuak emanez."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "Zure hobespenak beste arakatzaile batean leheneratzeko URLa"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopiatu hobespenen hash"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Txertatu kopiatutako hobespenen hash (URLrik gabe) leheneratzeko"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hobespenen hash"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Objektu digitaleko identifikatzailea (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI ebatzi"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Hautatu DOI berridazketa-k erabilitako zerbitzua"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Fitxa hau ez dago erabiltzailearen interfazean, baina motor hauetan "
"bilatu dezakezu bere !bang-en arabera."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Gaitu dena"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Desgaitu dena"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Hautatutako hizkuntza onartzen du"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Pisua"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Gehienezko denbora"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon Resolver"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Erakutsi fabikonoak bilaketa-emaitzetatik gertu"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Ezarpen hauek zure cookietan gordetzen dira, honek zuri buruzko "
"informaziorik ez gordetzea baimentzen digu."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Cookie hauek zure onurarako besterik ez dira, ez ditugu zure jarraipenik "
"egiteko erabiltzen."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Gorde"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Berrezarri lehenetsiak"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Atzera"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "teklatuko lasterbideak"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim bezala"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Arakatu bilaketa-emaitzak teklatuko lasterbideekin (JavaScript behar da)."
" Sakatu \"h\" tekla orri nagusian edo emaitzen orrian laguntza lortzeko."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Irudien proxya"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proxy irudien emaitzak SearXNG bidez"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Korritze amaigabea"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Kargatu automatikoki hurrengo orria uneko orrialdearen behealdera "
"mugitzean"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Zein hizkuntzan egin nahi duzu bilaketa?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Aukeratu Auto-detektatu SearXNG-k zure kontsultaren hizkuntza "
"detektatzeko."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP metodoa"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Aldatu inprimakiak nola bidaltzen diren"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Kontsulta orriaren izenburuan"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Gaituta dagoenean, emaitza orriaren izenburuak zure kontsulta dauka. Zure"
" arakatzaileak izenburu hau graba dezake"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Emaitzak fitxa berrietan"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Emaitzen estekak nabigatzailearen fitxa berrietan ireki"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Edukia iragazi"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Bilatu kategoria hautatzean"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Egin bilaketa berehala kategoria bat hautatuz gero. Desgaitu hainbat "
"kategoria hautatzeko"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Itxura"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Aldatu SearXNGren diseinua"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Estiloa"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Aukeratu automatikoa nabigatzailearen ezarpenak jarraitzeko"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Bilatzaileen token-ak"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Sarbide token-ak bilatzaile pribatuetarako"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Interfazearen hizkuntza"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Aldatu interfazearen hizkuntza"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL formatua"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Polita"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Osoa"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Ostalaria"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Aldatu emaitzaren URL formatua"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "biltegiak"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "erakutsi multimediak"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "ezkutatu multimediak"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Gune honek ez du deskribapenik eman."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Fitxategiaren tamaina"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Data"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Mota"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Ebazpena"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formatua"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Bilatzailea"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Ikusi iturria"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "helbidea"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "erakutsi mapa"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ezkutatu mapa"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Bertsioa"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Mantentzailea"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Une honetan eguneratua"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etiketak"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Ospea"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lizentzia"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Proiektua"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Proiektuaren hasiera orria"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Argitaratutako data"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Egunkaria"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editorea"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Argitaletxea"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet lotura"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent fitxategia"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Ereilea"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Fitxategi kopurua"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "erakutsi bideoa"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ezkutatu bideoa"

#~ msgid "Engine time (sec)"
#~ msgstr "Bilatzailearen denbora (seg)"

#~ msgid "Page loads (sec)"
#~ msgstr "Orri kargak (seg)"

#~ msgid "Errors"
#~ msgstr "Erroreak"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA beharrezkoa da"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "HTTP loturak HTTPS bihurtu ahal denean"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Emaitzak leiho berdinean irekitzen dira "
#~ "lehenetsi bezala. Plugin honek lehenetsitako"
#~ " jokabidea aldatzen du estekak fitxa/leiho"
#~ " berrietan irekitzeko. (JavaScript behar "
#~ "du)"

#~ msgid "Color"
#~ msgstr "Kolorea"

#~ msgid "Blue (default)"
#~ msgstr "Urdina (lehenetsia)"

#~ msgid "Violet"
#~ msgstr "Bioleta"

#~ msgid "Green"
#~ msgstr "Berdea"

#~ msgid "Cyan"
#~ msgstr "Zian"

#~ msgid "Orange"
#~ msgstr "Laranja"

#~ msgid "Red"
#~ msgstr "Gorria"

#~ msgid "Category"
#~ msgstr "Kategoria"

#~ msgid "Block"
#~ msgstr "Blokeatu"

#~ msgid "original context"
#~ msgstr "jatorrizko testuingurua"

#~ msgid "Plugins"
#~ msgstr "Pluginak"

#~ msgid "Answerers"
#~ msgstr "Erantzun emaileak"

#~ msgid "Avg. time"
#~ msgstr " Batezbesteko denbora"

#~ msgid "show details"
#~ msgstr "xehetasunak erakutsi"

#~ msgid "hide details"
#~ msgstr "xehetasunak ezkutatu"

#~ msgid "Load more..."
#~ msgstr "Kargatu gehiago..."

#~ msgid "Loading..."
#~ msgstr "Kargatzen..."

#~ msgid "Change searx layout"
#~ msgstr "Searxen diseinua aldatu"

#~ msgid "Proxying image results through searx"
#~ msgstr "Irudien emaitzak searx proxyaren bidez pasatu"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Hau da searxen berehalako erantzunen moduluen zerrenda."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Hau searxek zure ordenagailuan gordetzen "
#~ "ari den cookien eta haien balioen "
#~ "zerrenda bat da."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Zerrenda horrekin, searxen gardentasuna balioztatu dezakezu."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Searx lehen aldiz erabiltzen ari zarela ematen du."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""
#~ "Mesedez, saiatu berriz beranduago edo "
#~ "bila ezazu beste searx instantzia bat."

#~ msgid "Themes"
#~ msgstr "Itxurak"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metodoa"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Ezarpen aurreratuak"

#~ msgid "Close"
#~ msgstr "Itxi"

#~ msgid "Language"
#~ msgstr "Hizkuntza"

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "onartua"

#~ msgid "not supported"
#~ msgstr "ez onartua"

#~ msgid "about"
#~ msgstr "honi buruz"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Gai honetarako estiloa hautatu"

#~ msgid "Style"
#~ msgstr "Estiloa"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "Guztiak baimendu"

#~ msgid "Disable all"
#~ msgstr "Guztiak ezgaitu"

#~ msgid "Selected language"
#~ msgstr "Hautatutako hizkuntza"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "gorde"

#~ msgid "back"
#~ msgstr "atzera"

#~ msgid "Links"
#~ msgstr "Estekak"

#~ msgid "RSS subscription"
#~ msgstr "RSS harpidetza"

#~ msgid "Search results"
#~ msgstr "Bilaketaren emaitzak"

#~ msgid "next page"
#~ msgstr "hurrengo orrialdea"

#~ msgid "previous page"
#~ msgstr "aurreko orrialdea"

#~ msgid "Start search"
#~ msgstr "Bilaketa hasi"

#~ msgid "Clear search"
#~ msgstr "Bilaketa garbitu"

#~ msgid "Clear"
#~ msgstr "Garbitu"

#~ msgid "stats"
#~ msgstr "estatistikak"

#~ msgid "Heads up!"
#~ msgstr "Kasu!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Ondo egina!"

#~ msgid "Settings saved successfully."
#~ msgstr "Ezarpenak ongi gorde dira."

#~ msgid "Oh snap!"
#~ msgstr "Hara!"

#~ msgid "Something went wrong."
#~ msgstr "Zerbait gaizki joan da."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Irudia eskuratu"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "hobespenak"

#~ msgid "Scores per result"
#~ msgstr "Balorazioak emaitza bakoitzeko"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "pribatutasun-errespetatzaile, metabilaketa motor hackeagarri bat"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Ez dago abstrakturik eskuragarri argitalpen honetarako."

#~ msgid "Self Informations"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Aldatu inprimakiak nola bidaltzen diren, "
#~ "<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">ikasi gehiago eskaera metodoen"
#~ " inguruan</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "besteak"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "Lasterbidea"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Bilatzaileek ezin dute emaitzarik lortu."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Argitalpenen sartze-askeko bertsioetara "
#~ "berbidali ahal denean (plugina behar du)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Piztuta"

#~ msgid "Off"
#~ msgstr "Itzalita"

#~ msgid "Enabled"
#~ msgstr "Gaituta"

#~ msgid "Disabled"
#~ msgstr "Desgaituta"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Bilaketa egin kategoria hautatu bezain "
#~ "laster. Ezgaitu ezazu hainbat kategoria "
#~ "hautatu ahal izateko. (JavaScript behar "
#~ "du)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim antzeko laster-teklak"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Emaitzetan zehar Vim-en antzeko "
#~ "laster-teklekin nabigatu (JavaScript behar "
#~ "du). Sakatu \"h\" tekla orri nagusian"
#~ " edo emaitzen orrian laguntza ikusteko."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "ez dugu emaitzarik aurkitu. Mesedez "
#~ "beste kontsulta bat egin edo bilatu "
#~ "kategoria gehiagotan."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Berridatzi emaitzen ostalari-izenak edo "
#~ "kendu emaitzak ostalari-izenaren arabera"

#~ msgid "Bytes"
#~ msgstr "Byteak"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Ostalariaren izena ordezkatu"

#~ msgid "Error!"
#~ msgstr "Errorea!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Bilatzaileek ezin dute emaitzarik lortu"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Hasi gai -issue- berri bat bidaltzen GitHub-en"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Ausazko balio sortzailea"

#~ msgid "Statistics functions"
#~ msgstr "Funtzio estatistikoak"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Kalkulatu argumentuen {funtzioak}"

#~ msgid "Get directions"
#~ msgstr "Lortu jarraibideak"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Zure IPa bistaratzen du kontsulta \"ip\""
#~ " bada eta zure erabiltzaile-agentea "
#~ "kontsultak \"erabiltzaile-agentea\" badu."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Ezin izan da Tor irteera-nodoen "
#~ "zerrenda deskargatu: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Tor erabiltzen ari zara eta kanpoko "
#~ "IP helbide hau duzula dirudi: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Ez zara Tor erabiltzen ari eta "
#~ "kanpoko IP helbide hau duzu: "
#~ "{ip_address}"

#~ msgid "Keywords"
#~ msgstr "Gako-hitzak"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Hobespenen URLan ezarpen pertsonalizatuak "
#~ "zehaztea erabil daiteke gailuen hobespenak "
#~ "sinkronizatzeko."

#~ msgid "proxied"
#~ msgstr "proxyan gordeta"

