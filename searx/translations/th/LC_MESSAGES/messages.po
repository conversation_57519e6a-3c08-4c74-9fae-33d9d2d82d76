# Thai translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-05-17 15:04+0000\n"
"Last-Translator: wetinee <<EMAIL>>\n"
"Language: th\n"
"Language-Team: Thai "
"<https://translate.codeberg.org/projects/searxng/searxng/th/>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "โดยไม่ต้องแบ่งกลุ่มย่อยเพิ่มเติม"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "หมวดหมู่อื่น ๆ"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ไฟล์"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "ทั่วไป"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "ดนตรี"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "สื่อสังคม"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "รูปภาพ"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "วิดีโอ"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "วิทยุ"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "ทีวี"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "ไอที"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "ข่าว"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "แผนที่"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "หัวหอม"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "วิทยาศาสตร์"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "แอป"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "พจนานุกรม"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "เนื้อเพลง"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "แพ็กเกจ"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "ถาม-ตอบ"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "ที่เก็บข้อมูล"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "ซอฟต์แวร์วิกิ"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "เว็บ"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "งานตีพิมพ์ทางวิทยาศาสตร์"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "อัตโนมัติ"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "สว่าง"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "มืด"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "สีดำ"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "ช่วงเวลาทำงาน"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "เกี่ยวกับ"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "อุณหภูมิเฉลี่ย"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "เมฆปกคลุม"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "สภาพ"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "สภาพปัจจุบัน"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "เย็น"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "รู้สึกเหมือน"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "ความชื้น"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "อุณหภูมิสูงสุด"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "อุณหภูมิต่ำสุด"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "เช้า"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "ค่ำ"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "เที่ยงวัน"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "ความดัน"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "อาทิตย์ขึ้น"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "อาทิตย์ตก"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "อุณหภูมิ"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "ดัชนีรังสียูวี"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "ทัศนวิสัย"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "ลม"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "ผู้ติดตาม"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "โพสต์"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "ผู้ใช้งานขณะนี้"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "คอมเมนต์"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "ผู้ใช้งาน"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "ประชาคม"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "คะแนน"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "ชื่อเรื่อง"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "ผู้เขียน"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "สร้าง"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "ลบแล้ว"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "ตอบแล้ว"

#: searx/webapp.py:292
msgid "No item found"
msgstr "ไม่พบรายการ"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "แหล่งที่มา"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "เกิดข้อผิดพลาดขณะโหลดหน้าถัดไป"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "การตั้งค่าไม่ถูกต้อง โปรดแก้ไขการตั้งค่าของคุณ"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "การตั้งค่าไม่ถูกต้อง"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "ข้อผิดพลาดจากการค้นหา"

#: searx/webutils.py:35
msgid "timeout"
msgstr "หมดเวลา"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "ข้อผิดพลาดระหว่างแจงโครงสร้างไวยากรณ์"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "เกิดข้อผิดพลาดของโปรโตคอล HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "ข้อผิดพลาดทางเครือข่าย"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "ข้อผิดพลาดทาง SSL: การตรวจสอบใบรับรองล้มเหลว"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "ข้อผิดพลาดที่ไม่คาดคิด"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "ข้อผิดพลาดจาก HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "ข้อผิดพลาดจากการเชื่อมต่อ HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "ข้อผิดพลาดจากพร็อกซี"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "แคปต์ชา"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "คำขอมากเกินไป"

#: searx/webutils.py:58
msgid "access denied"
msgstr "การเข้าถึงถูกปฏิเสธ"

#: searx/webutils.py:59
msgid "server API error"
msgstr "ข้อผิดพลาดจาก API ของเซิร์ฟเวอร์"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "ถูกระงับ"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} นาทีที่แล้ว"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} ชั่วโมง {minutes} นาทีที่แล้ว"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "ทำการสุ่มค่าที่แตกต่างกัน"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "คำนวณ {func} ของอาร์กิวเมนต์"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "แสดงเส้นทางบนแผนที่ .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ล้าสมัย)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "รายการนี้ถูกแทนที่โดย"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "ช่องทาง"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "บิตเรต"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "โหวต"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "คลิก"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "ภาษา"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"การอ้างอิง {numCitations} รายการตั้งแต่ปี {firstCitationVelocityYear} ถึง"
" {lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"ไม่สามารถอ่านภาพจากลิงก์ได้ เนื่องจากอาจเป็นไฟล์ประเภทที่ไม่รองรับ ระบบ "
"TinEye รองรับเฉพาะไฟล์ประเภท JPEG, PNG, GIF, BMP, TIFF หรือ WebP เท่านั้น"

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"รูปภาพนี้มีจุดสังเกตที่น้อยเกินไป ระบบของ TinEye "
"นั้นต้องใช้ภาพที่มีลายละเอียดจุดเด่นที่ชัดเจนเล็กน้อย "
"ถึงจะสามารถหาภาพที่คล้ายกันได้"

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "ไม่สามารถดาวน์โหลดภาพนี้ได้"

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "บันทึกการให้คะแนน"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "คุณภาพไฟล์"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "แบล็กลิสต์ Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "กรองผลการค้นหา onion ที่อยู่ในแบล็กลิสต์ Ahmia"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "เครื่องคิดเลขพื้นฐาน"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "คำนวณนิพจน์คณิตศาสตร์ผ่านช่องค้นหา"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "ปลั๊กอินแฮช"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "แปลงสตริงเป็นแฮชย่อยที่ต่างกัน"

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "แฮชย่อย"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "ชื่อโฮส ปลั๊กอิน"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "ขียนชื่อโฮสต์ใหม่ ลบผลลัพธ์ หรือจัดลำดับความสำคัญตามชื่อโฮสต์"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "เปิดการเข้าถึง DOI ที่เขียนใหม่"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "หลีกเลี่ยงข้อจำกัดการชำระเงินโดยเปลี่ยนเส้นทางไปรุ่นเอกสารที่เปิดให้ใช้งาน"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "ข้อมูลตนเอง"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"จะแสดง IP ของคุณหากคำค้นคือ 'ip' และแสดงข้อมูล User Agent "
"ของคุณหากคำค้นคือ 'user-agent'"

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "ไอพีของคุณคือ "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "user-agent ของคุณคือ "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "ทอร์ตรวจสอบปลั๊กอิน"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"ปลั๊กอินนี้จะตรวจสอบว่าที่อยู่ของคำขอเป็นโหนดทางออกของ Tor หรือไม่ "
"และแจ้งให้ผู้ใช้ทราบว่าเป็นหรือไม่ เช่น check.torproject.org แต่มาจาก "
"SearXNG"

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "ไม่สามารถดาวน์โหลดรายการของโหนดทางออกของ Tor จาก"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "คุณกำลังใช้ Tor และดูเหมือนว่าคุณมีที่อยู่ IP ภายนอก"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "คุณไม่ได้ใช้ Tor และคุณมีที่อยู่ IP ภายนอก"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "ลบตัวติดตาม URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "ลบอาร์กิวเมนต์ตัวติดตามออกจากการส่งค่าคืนของ URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "ปลั๊กอินแปลงหน่วย"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "แปลงหน่วย"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "ไม่พบหน้านี้"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "ไปยัง %(search_page)s"

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "หน้าค้นหา"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "บริจาค"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "การตั้งค่า"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "ขับเคลื่อนโดย"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "เครื่องมือค้นหาเมตาแบบเปิดที่เคารพความเป็นส่วนตัว"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "แหล่งที่เก็บโค้ด"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "ตัวติดตามปัญหา"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "เครื่องมือสถิติ"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "อินสแตนซ์สาธารณะ"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "นโยบายความเป็นส่วนตัว"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "ติดต่อผู้ดูแลอินสแตนซ์"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "คลิกที่แว่นขยายเพื่อทำการค้นหา"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "ความยาว"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "มุมมอง"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "ผู้เขียน"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "แคช"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "เริ่มส่งฉบับใหม่บน GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "โปรดตรวจสอบข้อบกพร่องที่มีอยู่เกี่ยวกับกลไกนี้บน GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "ฉันยืนยันว่าไม่มีข้อบกพร่องเกี่ยวกับปัญหาที่ฉันพบ"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "หากนี่เป็นกรณีสาธารณะ โปรดระบุ URL ในรายงานข้อบกพร่อง"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "ส่งปัญหาใหม่บน Github รวมทั้งข้อมูลข้างต้นด้วย"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "ไม่มี HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "ดูบันทึกของข้อผิดพลาดและส่งรายงานข้อผิดพลาด"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!ปังสำหรับเครื่องยนต์นี้"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang สำหรับหมวดหมู่ของมัน"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "ค่าเฉลี่ย"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "ตัวตรวจสอบการทดสอบล้มเหลว: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "ข้อผิดพลาด:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "ทั่วไป"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "หมวดหมู่เริ่มต้น"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "ส่วนต่อประสานกับผู้ใช้"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "ความเป็นส่วนตัว"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "เครื่องมือ"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "เครื่องมือค้นหาที่ใช้อยู่ในปัจจุบัน"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "การคิวรีพิเศษ"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "คุกกี้"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "จำนวนผลลัพธ์"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "ข้อมูล"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "กลับไปด้านบน"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "หน้าก่อนหน้านี้"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "หน้าต่อไป"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "แสดงหน้าแรก"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "ค้นหา..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "ล้าง"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "ค้นหา"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "ขณะนี้ยังไม่มีข้อมูลที่มีอยู่"

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "ชื่อเครื่องมือ"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "คะแนน"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "จำนวนผลลัพธ์"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "เวลาตอบสนอง"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "ความน่าเชื่อถือ"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "ทั้งหมด"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "กำลังประมวลผล"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "คำเตือน"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "ข้อผิดพลาดและข้อยกเว้น"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "ข้อยกเว้น"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "ข้อความ"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "เปอร์เซ็นต์"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "พารามิเตอร์"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "ชื่อไฟล์"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "ฟังก์ชั่น"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "โค้ด"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "ตัวตรวจสอบ"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "ทดสอบไม่ผ่าน"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "ความคิดเห็น"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "ตัวอย่าง"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "คำนิยาม"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "คำเหมือน"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "คำตอบ"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "ดาวน์โหลดผลลัพธ์"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "ลองค้นหา:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "ข้อความจากเครื่องมือค้นหา"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "วินาที"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "ค้นหา URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "คัดลอกแล้ว"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "สำเนา"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "ข้อเสนอแนะ"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "ค้นหาภาษา"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "ภาษาเริ่มต้น"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "ตรวจจับอัตโนมัติ"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "ค้นหาแบบปลอดภัย"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "เข้มงวด"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "ปานกลาง"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "ไม่มี"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "ช่วงเวลา"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "ทุกเวลา"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "วันล่าสุด"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "สัปดาห์ล่าสุด"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "เดือนล่าสุด"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "ปีล่าสุด"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "สารสนเทศ!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "ขณะนี้ไม่มีการกำหนดคุกกี้ใดๆ"

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "เสียใจด้วย!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "ไม่พบผลลัพธ์ คุณสามารถลอง:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "ไม่มีผลลัพธ์อีกต่อไป คุณสามารถลอง:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "รีเฟรชหน้า"

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "ค้นหาคำค้นหาอื่นหรือเลือกหมวดหมู่อื่น (ด้านบน)"

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "เปลี่ยนเครื่องมือค้นหาที่ใช้ในการตั้งค่า:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "สลับไปยังอินสแตนซ์อื่น:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "ค้นหาคำค้นหาอื่นหรือเลือกหมวดหมู่อื่น"

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "กลับไปที่หน้าก่อนหน้าโดยใช้ปุ่มหน้าก่อนหน้า"

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "อนุญาต"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "คำหลัก (คำแรกของคำค้นหา)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "ชื่อ"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "คำอธิบาย"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "นี่คือรายการโมดูลที่ตอบรับทันทีของเซียร์เอ็กซ์เอ็นจี"

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "นี่คือรายการปลั๊กอิน"

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "เติมข้อความอัตโนมัติ"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "ค้นหาสิ่งต่างๆในขณะพิมพ์"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "จัดตำแหน่งกึ่งกลาง"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "แสดงผลตรงกลางหน้า (เค้าโครงออสการ์)"

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "นี่คือรายการคุกกี้และค่าของคุกกี้ที่เซียร์เอ็กซ์เอ็นจีจัดเก็บไว้ในคอมพิวเตอร์ของคุณ"

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "ด้วยรายการดังกล่าว คุณสามารถประเมินความโปร่งใสของเซียร์เอ็กซ์เอ็นจีได้"

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "ชื่อคุกกี้"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "ค่า"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "ค้นหา URL จากการตั้งค่าที่บันทึกไว้ในปัจจุบัน"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"หมายเหตุ: การระบุการตั้งค่าแบบกำหนดเองใน URL "
"การค้นหาสามารถลดความเป็นส่วนตัวได้โดยการทำให้ข้อมูลรั่วไหลไปยังไซต์ผลลัพธ์ที่คลิก"

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL เพื่อกู้คืนการตั้งค่าของคุณในเบราว์เซอร์อื่น"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"ลิงก์ URL ที่บรรจุค่าการตั้งค่าของคุณ "
"ซึ่งสามารถนำไปใช้กู้คืนการตั้งค่าเหล่านั้นบนอุปกรณ์อื่นได้"

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "คัดลอกแฮชการตั้งค่า"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "แทรกแฮชการตั้งค่าที่คัดลอกไว้ (ไม่มี URL) เพื่อกู้คืน"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "การตั้งค่าแฮช"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "รหัส DOI"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "เปิดการเข้าถึงตัวแก้ไข DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "เลือกบริการที่ใช้โดย DOI เขียนใหม่"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"แท็บนี้ไม่มีอยู่ในอินเทอร์เฟซผู้ใช้ "
"แต่คุณสามารถค้นหาในเอ็นจิ้นเหล่านี้ได้ด้วย !bangs"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "เปิดใช้งานทั้งหมด"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "ปิดใช้งานทั้งหมด"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "รองรับภาษาที่เลือกแล้ว"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "น้ำหนัก"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "เวลาสูงสุด"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "แก้ไข Favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "แสดงไอคอน Fav ใกล้ผลการค้นหา"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"การตั้งค่าพวกนี้ถูกเก็บไว้ในคุกกี้ของคุณแล้ว "
"ซึ่งช่วยให้เราไม่สามารถจัดเก็บข้อมูลนี้เกี่ยวกับคุณได้"

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"คุกกี้พวกนี้ให้บริการเพื่อความสะดวกของคุณ "
"เราไม่ใช้คุกกี้เหล่านี้เพื่อติดตามคุณ"

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "บันทึก"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "รีเซ็ตค่าเริ่มต้น"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "กลับ"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "ปุ่มลัด"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "คล้าย Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"นำทางผลการค้นหาด้วยปุ่มลัด (ต้องใช้ JavaScript) กดปุ่ม \"h\" "
"บนหน้าหลักหรือหน้าผลลัพธ์เพื่อรับความช่วยเหลือ"

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "พร็อกซีรูปภาพ"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "การแสดงพร็อกซี่ของภาพผ่าน เซียร์เอ็กซ์เอ็นจี"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "เลื่อนเมาส์แบบไม่มีที่สิ้นสุด"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "โหลดหน้าถัดไปโดยอัตโนมัติเมื่อเลื่อนเมาส์ลงไปที่ด้านล่างของหน้าปัจจุบัน"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "คุณต้องการค้นหาภาษาใด?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "เลือก ตรวจหาอัตโนมัติ เพื่อให้ SearXNG ตรวจจับภาษาของข้อความค้นหาของคุณ"

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP เมธอด"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "เปลี่ยนวิธีการส่งแบบฟอร์ม"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "คิวรีในชื่อหน้า"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"เมื่อเปิดใช้งานแล้ว ชื่อของหน้าผลลัพธ์จะมีข้อความคิวรีของคุณ "
"เบราว์เซอร์ของคุณสามารถบันทึกชื่อของหน้านี้ได้"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "ผลลัพธ์ในแท็บใหม่"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "เปิดลิงก์ผลลัพธ์ด้วยแท็บเบราว์เซอร์ใหม่"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "ตัวกรอกเนื้อหา"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "ค้นหาในตัวเลือกหมวดหมู่"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr "ทำการค้นหาทันทีหากเลือกหมวดหมู่ไว้ ปิดการใช้งานเพื่อเลือกหลายประเภท"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "ธีม"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "เปลี่ยนเค้าโครง เซียร์เอ็กซ์เอ็นจี"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "รูปแบบธีม"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "เลือกอัตโนมัติเพื่อติดตามการตั้งค่าของเบราว์เซอร์ของคุณ"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "โทเค็นของเครื่องมือ"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "โทเคนการเข้าถึงของเครื่องมือส่วนตัว"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "ภาษาส่วนต่อประสาน"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "เปลี่ยนภาษาของเค้าโครง"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "การจัดรูปแบบ URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "แบบสวยงาม"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "แบบเต็ม"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "โฮสต์"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "เปลี่ยนรูปแบบ URL ผลการค้นหา"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "พื้นที่เก็บข้อมูล"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "แสดงสื่อ"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "ซ่อนสื่อ"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "ไซต์นี้ไม่ได้ให้คำอธิบายใดๆไว้"

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "ขนาดไฟล์"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "วันที่"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "พิมพ์"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "ความละเอียดจอ"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "จัดรูปแบบ"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "เครื่องมือ"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "ดูแหล่งที่มา"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "ที่อยู่"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "แสดงแผนที่"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ซ่อนแผนที่"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "เวอร์ชัน"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "ผู้ดูแล"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "อัปเดตล่าสุดเมื่อ"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "แท็ก"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "ความนิยม"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "สัญญาอนุญาต"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "โปรเจกต์"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "หน้าหลักโปรเจกต์"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "วันที่เผยแพร่"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "วารสาร"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "บรรณาธิการ"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "สำนักพิมพ์"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "ลิงก์แม่เหล็ก"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "ไฟล์ทอร์เรนต์"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "ผู้ที่แบ่งปันไฟล์"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "ผู้ที่ดาวน์โหลดไฟล์"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "จำนวนไฟล์"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "แสดงวิดีโอ"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ซ่อนวิดีโอ"

#~ msgid "Scores per result"
#~ msgstr "คะแนนต่อผลลัพธ์"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "เคารพความเป็นส่วนตัว เครื่องมือค้นหา meta ที่แฮ็กได้"

#~ msgid "No abstract is available for this publication."
#~ msgstr "ไม่มีเรื่องย่อของเอกสารนี้"

#~ msgid "Self Informations"
#~ msgstr "ข้อมูลตนเอง"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "เปลี่ยนวิธีการส่งแบบฟอร์ม <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">เรียนรู้เพิ่มเติมเกี่ยวกับวิธีการส่งคำขอ</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "ตรวจพบคำในการค้นหาอัตโนมัติ"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "ระบบจะตรวจจับภาษาที่ใช้ในการค้นหา และเปลี่ยนไปค้นหาในภาษานั้นอัตโนมัติ"

#~ msgid "others"
#~ msgstr "ชื่ออื่นๆ"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "แท็บนี้ไม่แสดงผลการค้นหา "
#~ "แต่คุณสามารถค้นหาเครื่องมือที่แสดงไว้ที่นี่ที่เรียบง่าย"

#~ msgid "Shortcut"
#~ msgstr "ทางลัด"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "เครื่องมือไม่สามารถดึงผลลัพธ์ได้"

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "โปรดลองอีกครั้งในภายหลังหรือค้นหาอินสแตนซ์อื่นของเซียร์เอ็กซ์เอ็นจี"

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr "เปลี่ยนเส้นทางไปยังรุ่นเอกสารที่เปิดให้เข้าถึงได้ (ต้องใช้ปลั๊กอิน)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "เปิด"

#~ msgid "Off"
#~ msgstr "ปิด"

#~ msgid "Enabled"
#~ msgstr "เปิดใช้งาน"

#~ msgid "Disabled"
#~ msgstr "ปิดใช้งาน"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "ทำการค้นหาทันทีหากเลือกหมวดหมู่แล้ว "
#~ "ปิดการใช้งานเพื่อเลือกหมวดหมู่ได้หลายหมวดหมู่ "
#~ "(ต้องใช้จาวาสคริปต์)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "วิมเหมือนปุ่มลัด"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "นำทางผลลัพธ์จากการค้นหาด้วยปุ่มลัดแบบ Vim (ต้องใช้ "
#~ "JavaScript) กดปุ่ม \"h\" "
#~ "บนหน้าหลักหรือหน้าผลลัพธ์เพื่อรับความช่วยเหลือ"

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr "เราไม่พบผลลัพธ์ใดๆ โปรดใช้คิวรีอื่นหรือค้นหาในหมวดหมู่เพิ่มเติม"

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "เขียนผลลัพธ์ของชื่อโฮสต์ใหม่หรือลบผลลัพธ์ตามชื่อโฮสต์"

#~ msgid "Bytes"
#~ msgstr "ไบต์"

#~ msgid "kiB"
#~ msgstr "กิบิไบต์"

#~ msgid "MiB"
#~ msgstr "เมบิไบต์"

#~ msgid "GiB"
#~ msgstr "จิบิไบต์"

#~ msgid "TiB"
#~ msgstr "เทบิไบต์"

#~ msgid "Hostname replace"
#~ msgstr "ชื่อโฮสต์ที่แทนที่"

#~ msgid "Error!"
#~ msgstr "เกิดข้อผิดพลาด!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "เครื่องมือไม่สามารถดึงผลลัพธ์ได้"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "เริ่มส่งฉบับใหม่บน GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "ตัวสุ่มค่า"

#~ msgid "Statistics functions"
#~ msgstr "ฟังก์ชันเชิงสถิติ"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "คำนวณ {functions} จากอาร์กิวเมนต์"

#~ msgid "Get directions"
#~ msgstr "ขอเส้นทาง"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "แสดง IP ของคุณหากคิวรีเป็นไอพี "
#~ "และตัวแทนจากผู้ใช้ของคุณหากคิวรีเป็นตัวแทนผู้ใช้"

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "ไม่สามารถดาวน์โหลดรายการ Tor exit-nodes จาก:"
#~ " https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "คุณกำลังใช้ Tor และดูเหมือนว่าคุณมีที่อยู่ IP ภายนอกนี้: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "คุณไม่ได้ใช้ Tor และคุณมีที่อยู่ IP ภายนอกนี้: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "คำสำคัญ"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "การระบุการตั้งค่าแบบกำหนดเองใน URL "
#~ "ค่ากำหนดสามารถใช้เพื่อซิงค์กับค่ากำหนดในอุปกรณ์ต่างๆได้"

#~ msgid "proxied"
#~ msgstr "พร็อกซี่"

