# Ukrainian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# zubr139, 2016-2017
# <PERSON><PERSON><PERSON> <and<PERSON><PERSON><PERSON>@gmail.com>, 2022.
# <PERSON> <<EMAIL>>, 2022, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-01 06:39+0000\n"
"Last-Translator: SomeTr <<EMAIL>>\n"
"Language-Team: Ukrainian <https://translate.codeberg.org/projects/searxng/"
"searxng/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 "
"? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > "
"14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % "
"100 >=11 && n % 100 <=14 )) ? 2: 3);\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "без подальшого підгрупування"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "інше"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "файли"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "загальні"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "музика"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "соцмережі"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "зображення"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "відео"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "радіо"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "ТБ"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "новини"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "карти"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "наука"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "програми"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "словники"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "тексти пісень"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "пакети"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "запитання і відповіді"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "репозиторії"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "вікі програм"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "веб"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "наукові публікації"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "автоматично"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "світла"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "темна"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "чорна"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Час роботи"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Про"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Середня темп."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Хмарність"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Умови"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Поточні умови"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Вечір"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Відчувається як"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Вологість"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Макс. температура"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Мін. температура"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Ранок"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Ніч"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "День"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Тиск"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Схід"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Захід"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Температура"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Індекс УФ"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Видимість"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Вітер"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Ясне небо"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Хмарно"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Ясно"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Туман"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Сильний дощ із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Сильні зливи з грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Сильні зливи"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Сильний дощ"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Сильний мокрий сніг із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Сильні зливи з мокрим снігом і грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Сильні зливи з мокрим снігом"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Сильний мокрий сніг"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Сильний сніг із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Сильний снігопад із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Сильний снігопад"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Сильний сніг"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Невеликий дощ із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Короткочасні зливи з грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Короткочасні зливи"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Невеликий дощ"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Невеликий мокрий сніг із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Короткочасні зливи з мокрим снігом і грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Короткочасні зливи з мокрим снігом"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Невеликий мокрий сніг"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Невеликий сніг із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Невеликий снігопад із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Невеликий снігопад"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Невеликий сніг"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Мінлива хмарність"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Дощ із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Зливи з грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Зливи"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Дощ"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Mокрий сніг із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Зливи з мокрим снігом і грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Зливи з мокрим снігом"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Mокрий сніг"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Сніг із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Снігопад із грозою"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Снігопад"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Сніг"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "підписників"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "дописів"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "активних користувачів"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "коментарів"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "користувач"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "спільнота"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "балів"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "назва"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "автор"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "відкрито"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "закрито"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "є відповідь"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Нічого не знайдено"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Джерело"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Не вдалося завантажити наступну сторінку"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Неправильні налаштування, будь ласка, зробіть зміни в налаштуваннях"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Неправильні налаштування"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "помилка пошуку"

#: searx/webutils.py:35
msgid "timeout"
msgstr "тайм-аут"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "помилка парсингу"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "помилка протоколу HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "помилка мережі"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Помилка SSL: не вдалося перевірити сертифікат"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "непередбачена помилка"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "помилка HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "помилка з'єднання HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "помилка проксі"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "забагато запитів"

#: searx/webutils.py:58
msgid "access denied"
msgstr "доступ заборонено"

#: searx/webutils.py:59
msgid "server API error"
msgstr "помилка API сервера"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Призупинено"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} хвилин тому"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} годин, {minutes} хвилин тому"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Створити різні випадкові значення"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Обчислити {func} від аргументів"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Показати маршрут на карті ..."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ЗАСТАРІЛО)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Цей запис було замінено на"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Канал"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "бітрейт"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "голоси"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "кліки"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Мова"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} цитувань з {firstCitationVelocityYear} по "
"{lastCitationVelocityYear} рік"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Не вдалося зчитати зображення за вказаним URL. Можливо, тому що формат "
"цього зображення не підтримується. TinEye підтримує зображення у форматах"
" JPEG, PNG, GIF, BMP, TIFF та WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Зображення занадто просте, щоб знайти збіги. TinEye вимагає базового "
"рівня візуальної деталізації для успішного визначення збігів."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Зображення неможливо завантажити."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Рейтинг книги"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Якість файлу"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Чорний список Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Відфільтрувати onion-адреси, внесені до чорного списку Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Простий калькулятор"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Обчислювати математичні вирази в рядку пошуку"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Плагін Hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Конвертує рядки в різні геш-послідовності."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "геш-послідовність"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Плагін Hostnames"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Переписування імен хостів, видалення результатів або визначення їх "
"пріоритетності на основі імені хоста"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Переадресація на Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Уникайте платіжних каналів шляхом переадресації на версії публікацій з "
"відкритим доступом, коли це можливо"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Інформація про себе"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Показує вашу IP-адресу, якщо запит «ip», і ваш User-Agent, якщо запит "
"«user-agent»."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Ваша IP-адреса: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Ваш User-Agent: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Плагін перевірки Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Цей плагін перевіряє, чи належить адреса запиту вихідному вузлу Tor і, "
"якщо так, інформує користувача; як check.torproject.org, але від SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Не вдалося завантажити список вихідних вузлів Tor із"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Ви використовуєте Tor, і виглядає так, що у вас зовнішня IP-адреса"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Ви не використовуєте Tor, і у вас зовнішня IP-адреса"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Видалення URL-адреси трекера"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Вилучіть аргументи трекера з поверненої URL-адреси"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Плагін-конвертер одиниць вимірювання"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Конвертувати одиниці"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Сторінку не знайдено"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Перейти до %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "сторінки пошуку"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Пожертви"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Опції"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Використовується"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "відкрита пошукова система, що поважає конфіденційність"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Вихідний код"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Трекер помилок"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Статистика пошукової системи"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Публічні інстанси"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Політика приватності"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Зв'язатися з власником інстансу"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Натисніть лупу, щоб виконати пошук"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Довжина"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Перегляди"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Автор"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "кеш"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Створення нового повідомлення про проблему на GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Будь ласка, перевірте наявність недоліку цього рушія на GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Я підтверджую, що не існує помилки, пов'язаної з проблемою, що мені "
"зустрілася"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Якщо це публічний інстанс, укажіть URL-адресу у звіті про помилку"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Надіслати нове повідомлення про проблему на GitHub, включаючи вказану "
"вище інформацію"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Без HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Перегляньте журнали помилок і надішліть звіт про недолік"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang для цієї пошукової системи"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang для її категорій"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Медіана"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Невдалі тести: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Помилки:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Загальні"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Типові категорії"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Інтерфейс користувача"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Конфіденційність"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Пошукові системи"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Пошукові системи, які використовуються"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Особливі запити"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookie-файли"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Число результатів"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Інфо"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Доверху"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Попередня сторінка"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Наступна сторінка"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Показати головну сторінку"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Шукати..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "очистити"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "шукати"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "В даний час немає доступних даних. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Назва пошукової системи"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Влучань"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Кількість результатів"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Час відгуку"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Надійність"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Всього"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Обробляється"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Попередження"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Помилки та виключення"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Виключення"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Повідомлення"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Відсоток"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Параметр"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Назва файлу"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Функція"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Код"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Перевірник"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Провалений тест"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Коментар(і)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Приклади"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Визначення"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Синоніми"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Відчувається як"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Відповіді"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Завантажити результати"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Спробуйте шукати:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Повідомлення від пошукових систем"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "секунд"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Посилання на пошук"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Скопійовано"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Копіювати"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Пропозиції"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Мова пошуку"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Стандартна мова"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Автовизначення"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Безпечний пошук"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Строгий"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Помірний"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Вимкнений"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Часовий діапазон"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "За весь час"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "За останній день"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "За останній тиждень"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "За останній місяць"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "За останній рік"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Інформація!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "в даний час cookie-файли не встановлені."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Вибачте!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Результатів не знайдено. Ви можете спробувати:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Результатів більше немає. Ви можете спробувати:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Оновити сторінку."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Шукати за іншим запитом або вибрати іншу категорію (вгорі)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Змінити пошукову систему, вказану в налаштуваннях:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Перемкнути інстанс SearXNG:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Шукати за іншим запитом або вибрати іншу категорію."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Повернутися до попередньої сторінки, натиснувши «Попередня сторінка»."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Дозволити"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Ключові слова (перше слово в запиті)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Назва"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Опис"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Модулі SearXNG з миттєвою відповіддю."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Це список плагінів."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Автозаповнення"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Шукати під час набору"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Центрування"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Відображає результати в центрі сторінки (макет Оскар)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Список файлів cookie та їх значень, які SearXNG зберігає на вашому "
"комп’ютері."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "За допомогою цього списку ви можете оцінити прозорість SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Ім'я cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Значення"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL-адреса зі збереженими налаштуваннями"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Увага: використання URL з параметрами може призвести до витоку даних на "
"сайти, відкриті з результатів пошуку."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL для відновлення Ваших налаштувань в іншому оглядачі"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"URL-адреса, що містить Ваші налаштування. Цю URL-адресу можна використати"
" для відновлення налаштувань на іншому пристрої."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Копіювати хеш налаштувань"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Вставте скопійований хеш налаштувань (без URL-адреси), щоб відновити"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Хеш налаштувань"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Цифровий ідентифікатор об'єкта (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Джерело Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Виберіть послугу, яку використовує DOI rewrite"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ця вкладка не існує в інтерфейсі користувача, але ви можете шукати в цих "
"системах за допомогою їх !bang."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Увімкнути все"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Вимкнути все"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Підтримка обраної мови"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Вага"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Максимальний час"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Обробник значків"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Показувати значки біля результатів пошуку"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Налаштування зберігаються в ваших cookie-файлах, що дає нам змогу не "
"зберігати ці відомості про вас."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Ці cookie-файли необхідні винятково для вашої зручності, ми не "
"використовуємо ці cookie-файли, щоб відслідковувати вас."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Зберегти"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Відновити стандартні налаштування"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Назад"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Гарячі клавіші"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Вигляд як Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Переміщуйтеся в результатах пошуку за допомогою гарячих клавіш (потрібен "
"JavaScript). Натисніть клавішу «h» на головній сторінці або сторінці "
"результатів, щоб прочитати довідку."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Проксі для зображень"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Проксувати зображення в результатах методами SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Нескінченна прокрутка"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Автоматично завантажувати наступну сторінку при прокрутці поточної до "
"кінця"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Якій мові ви віддаєте перевагу для пошуку?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Виберіть автовизначення, щоб SearXNG сам визначав мову вашого запиту."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Метод HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Змінити спосіб надсилання форм"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Пошуковий запит у заголовку сторінки"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Якщо ввімкнено, заголовок сторінки результатів містить ваш пошуковий "
"запит. Ваш браузер може зберігати цю назву"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Результати в нових вкладках"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Відкривати посилання результатів у нових вкладках"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Фільтр контенту"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Пошук при виборі категорії"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Шукати негайно при виборі категорії. Вимкніть, якщо хочете вибрати кілька"
" категорій"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Тема"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Змінити макет SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Стиль теми"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Виберіть «автоматично» для використання налаштувань вашого браузера"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Токени пошукової системи"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Токени доступу для приватних пошукових систем"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Мова інтерфейсу"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Змінити мову сайту"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Формат URL-адрес"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Компактний"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Повний"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Хост"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Змінити форматування URL-адрес результатів"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "репозиторій"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "показати медіа"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "приховати медіа"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Цей сайт не надає опису."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Розмір файлу"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Дата"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Тип"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Роздільна здатність"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Формат"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Рушій"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Переглянути джерело"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "адреса"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "показати карту"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "приховати карту"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Версія"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Підтримка"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Оновлено о"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Теги"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Популярність"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Ліцензія"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Проєкт"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Сторінка проєкту"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Дата публікації"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Журнал"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Редактор"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Видавець"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "магнет-посилання"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "торрент-файл"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Сідер"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Лічер"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Кількість файлів"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "показати відео"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "приховати відео"

#~ msgid "Engine time (sec)"
#~ msgstr "Час пошуку (сек)"

#~ msgid "Page loads (sec)"
#~ msgstr "Час завантадення (сек)"

#~ msgid "Errors"
#~ msgstr "Помилок"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "За можливістю замінити в посиланнях HTTP на HTTPS"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Типово результати відкриваються в тому ж"
#~ " вікні. Цей плагін змінює поведінку, "
#~ "щоб посилання відкривались типово в "
#~ "нових вкладках/вікнах. (Необхідний JavaScript)"

#~ msgid "Color"
#~ msgstr "Колір"

#~ msgid "Blue (default)"
#~ msgstr "Синій (типово)"

#~ msgid "Violet"
#~ msgstr "Фіолетовий"

#~ msgid "Green"
#~ msgstr "Зелений"

#~ msgid "Cyan"
#~ msgstr "Блакитний"

#~ msgid "Orange"
#~ msgstr "Помаранчевий"

#~ msgid "Red"
#~ msgstr "Червоний"

#~ msgid "Category"
#~ msgstr "Категорія"

#~ msgid "Block"
#~ msgstr "Заблокувати"

#~ msgid "original context"
#~ msgstr "в контексті"

#~ msgid "Plugins"
#~ msgstr "Плагіни"

#~ msgid "Answerers"
#~ msgstr "Відповідачі"

#~ msgid "Avg. time"
#~ msgstr "Середній час"

#~ msgid "show details"
#~ msgstr "показати деталі"

#~ msgid "hide details"
#~ msgstr "приховати деталі"

#~ msgid "Load more..."
#~ msgstr "Завантажити більше..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Змінити вигляд сайту"

#~ msgid "Proxying image results through searx"
#~ msgstr "Проксувати знайдені зображення за допомогою searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Список модулів миттєвих відповідей searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Це список cookie-файлів та їх значень,"
#~ " які searx зберігає на вашому "
#~ "комп'ютері."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "По цьому списку ви можете оцінити відкритість searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Схоже, що ви використовуєте searx вперше."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""

#~ msgid "Themes"
#~ msgstr "Теми"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Метод"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Додаткові налаштування"

#~ msgid "Close"
#~ msgstr "Закрити"

#~ msgid "Language"
#~ msgstr ""

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr ""

#~ msgid "not supported"
#~ msgstr ""

#~ msgid "about"
#~ msgstr "про сайт"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Обрати стиль для цієї теми"

#~ msgid "Style"
#~ msgstr "Стиль"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr ""

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "зберегти"

#~ msgid "back"
#~ msgstr "назад"

#~ msgid "Links"
#~ msgstr "Посилання"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Результати пошуку"

#~ msgid "next page"
#~ msgstr "наступна сторінка"

#~ msgid "previous page"
#~ msgstr "попередня сторінка"

#~ msgid "Start search"
#~ msgstr "Розпочати пошук"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "статистика"

#~ msgid "Heads up!"
#~ msgstr "Отакої!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Чудово!"

#~ msgid "Settings saved successfully."
#~ msgstr "Налаштування успішно збережені."

#~ msgid "Oh snap!"
#~ msgstr "От халепа!"

#~ msgid "Something went wrong."
#~ msgstr "Щось пішло не так."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Завантажити зображення"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "опції"

#~ msgid "Scores per result"
#~ msgstr "Влучань за результат"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "вільна система метапошуку, яка поважає вашу приватність"

#~ msgid "No abstract is available for this publication."
#~ msgstr ""

#~ msgid "Self Informations"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Змінити спосіб відправки запитів, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">детальніше про методи "
#~ "запитів</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Ви використовуєте TOR. Здається, Ваша IP-адреса: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Ви не використовуєте TOR. Здається, Ваша IP-адреса: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "інші"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "Гарячі клавіші"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr ""

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Будь ласка, спробуйте пізніше або пошукайте інший екземпляр SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Ввімк."

#~ msgid "Off"
#~ msgstr "Вимк."

#~ msgid "Enabled"
#~ msgstr "Ввімкнено"

#~ msgid "Disabled"
#~ msgstr "Вимкнено"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Виконувати пошук зразу при обранні "
#~ "категорії. Вимкнути вибір декількох категорій."
#~ " (Необхідний JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Гарячі клавіші Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Переміщення результатів пошуку за допомогою"
#~ " віртуальних клавіш (потрібно JavaScript). "
#~ "Натисніть клавішу \"h\" на головній "
#~ "сторінці або на сторінці результатів, "
#~ "щоб отримати допомогу."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "ми не знайшли жодних результатів. Будь"
#~ " ласка, використайте інший запит або "
#~ "виконайте пошук в декількох категоріях."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Замінити ім'я хоста або видалити результати на основі імені хоста"

#~ msgid "Bytes"
#~ msgstr "Байтів"

#~ msgid "kiB"
#~ msgstr "КіБ"

#~ msgid "MiB"
#~ msgstr "МіБ"

#~ msgid "GiB"
#~ msgstr "ГіБ"

#~ msgid "TiB"
#~ msgstr "ТіБ"

#~ msgid "Hostname replace"
#~ msgstr "Зміна імені сайту"

#~ msgid "Error!"
#~ msgstr "Помилка!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Пошукові системи не можуть отримати результати"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Надсилайте нові проблеми на GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Генератор випадкових значень"

#~ msgid "Statistics functions"
#~ msgstr "Функції статистики"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Розрахувати {functions} аргументів"

#~ msgid "Get directions"
#~ msgstr "Отримати директорії"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Відображає IP-адресу при запиті \"ip\" "
#~ "та ваш user-agent при запиті "
#~ "\"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Не вдалося завантажити список вихідних "
#~ "вузлів Tor з: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Ви використовуєте Tor і, здається, Ваша IP-адреса така: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Ви не використовуєте Tor та маєте зовнішню IP-адресу: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Ключові слова"

#~ msgid "/"
#~ msgstr "/"

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Зазначення користувацьких налаштувань в "
#~ "URL-адресі можна використовувати для "
#~ "синхронізації налаштувань на різних пристроях."

#~ msgid "proxied"
#~ msgstr "проксовано"
