# Romanian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2015
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-30 15:18+0000\n"
"Last-Translator: return42 <<EMAIL>>\n"
"Language: ro\n"
"Language-Team: Romanian "
"<https://translate.codeberg.org/projects/searxng/searxng/ro/>\n"
"Plural-Forms: nplurals=3; plural=n==1 ? 0 : (n==0 || (n%100 > 0 && n%100 "
"< 20)) ? 1 : 2;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "fără subgrupări suplimentare"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "alta"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "fișiere"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "general"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muzică"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "rețele sociale"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "imagini"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videouri"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "it"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "știri"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "harta"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "cepe"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "știință"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplicații"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "dicționare"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "versuri"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pachete"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "î&r"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repozitorii"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "enciclopedii de programe"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "internet"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "lucrări științifice"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automat"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "luminos"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "întunecat"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "negru"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Timpul de funcționare"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Despre"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Temperatură medie."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Nebulozitate"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Condiție"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Condiție curentă"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Seara"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Se simte ca"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Umiditate"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Temperatură maximă."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Temperatură minimă."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Dimineata"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noapte"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Pranz"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Presiune"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Răsărit"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Apus"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatură"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Index UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Vizibilitate"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vânt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Abonați"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "Postări"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "Utilizatori activi"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "Comentarii"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "utilizator"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "comunitate"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "Puncte"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "Titlu"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "deschis"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "închis"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "răspuns"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Niciun element găsit"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Sursă"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Eroare la încărcarea paginii următoare"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Configurări nevalide, modificați preferințele"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Configurări nevalide"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "eroare de căutare"

#: searx/webutils.py:35
msgid "timeout"
msgstr "pauza"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "eroare de transpunere"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "eroare protocol HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "eroare rețea"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Eroare SSL: validarea certificatului a esuat"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "inchidere fortata neasteptata"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "eroare HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "eroare conexiune HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "eroare proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "Prea multe solicitări"

#: searx/webutils.py:58
msgid "access denied"
msgstr "Acces interzis"

#: searx/webutils.py:59
msgid "server API error"
msgstr "eroare la API pe Server"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Întrerupt"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minut(e) în urmă"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} oră(e), {minutes} minut(e) în urmă"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generează valori aleatoare diferite"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Calculați {func} argumentelor"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Arată ruta în hartă .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLETE)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Această intrare a fost inlocuită de"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Canal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "rata de biți"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "voturi"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "click-uri"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Limba"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} Citații din acest an {firstCitationVelocityYear} pâna la "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"URL-ul imaginii nu a putut fi citit. O posibilă cauză ar putea fi un "
"format de fișier nesuportat. TinEye suportă doar imagini care sunt JPEG, "
"PNG,GIF, BMP, TIFF sau WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Imaginea este prea simplă pentru a găsi potriviri. TinEye necesită cel "
"putin un nivel minimal al detaliilor pentru a găsi cu succes potriviri."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Imaginea nu a putut fi descărcată."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Recenzia cărții"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Calitatea fișierului"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Calculator de bază"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Calculați expresii matematice prin bara de căutare"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin Hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Convertește șirurile în diferite rezumate hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "rezumat hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Pluginul Hostnames"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Rescrieți hostnames, eliminați rezultatele sau prioritizați-le pe baza "
"numelui hostname"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Rescriere DOI cu acces deschis"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Evită „zidurile de plată” redirecționând către versiuni cu acces deschis "
"ale publicațiilor când sunt disponibile"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informații despre sine"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Afișează IP-ul dvs. dacă interogarea este „ip” și agentul de utilizator "
"dacă interogarea este „user-agent”."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "IP-ul dumneavoastră este: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "User-agent-ul dumneavoastră este: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Activeaza plugin Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Acest plugin verifică dacă adresa solicitării este un nod de ieșire Tor "
"și informează utilizatorul dacă este; la fel ca check.torproject.org, dar"
" de la SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nu s-a putut descărca lista de noduri de ieșire Tor din"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Folosiți Tor și se pare că aveți adresa IP externă"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Nu utilizați Tor și aveți adresa IP externă"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Eliminator de URL pentru urmăritor"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Elimină argumentele urmăritorului din URL-ul returnat"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Convertiți între unități"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Pagină negăsită"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Navighează la %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "pagină de căutare"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Donează"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferințe"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Motorizat de"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "motor de cautare gratuit ce respecta intimitatea"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Cod sursă"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Urmăritor de probleme"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statisticile motorului"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instanțe publice"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Politica de Confidențialitate"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Contactați întreținătorul instanței"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Apăsați pe lupă pentru a executa căutarea"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Lungime"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Afișări"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "stocat temporar"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Începe prin a trimite o nouă problemă la GiHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Vă rog să verificați existența erorilor în legătură cu acest motor de "
"căutare pe GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Confirm ca nu exista nici un bug in legatura cu situatia pe care o "
"intampin"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Dacă aceasta este o instanța publică, vă rog să specificați URL-ul în "
"raportul erorii"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Deschide un nou caz pe Github, cu toate informațiile din partea de "
"deasupra incluse"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Fara HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Vizualizați jurnalele de erori și trimiteți un raport de eroare"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang pentru acest motor de căutare"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang pentru categoriile sale"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Median"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Testele verificatoare au eșuat "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Erori:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Generale"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Categorii implicite"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interfața pentru utilizator"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Confidențialitate"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Motoare de căutare"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Motoarele de căutare folosite curent"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Întrebări speciale"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookie-uri"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Numărul de rezultate"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informații"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Înapoi sus"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Pagina precedentă"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Pagina următoare"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Afișați prima pagină"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Caută..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "Ștergeți"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "căutați"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Deocamdată nu există date disponibile."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Numele motorului"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Scoruri"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Număr de rezultate"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Timp de răspuns"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Fiabilitate"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Prelucrare"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Avertismente"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Erori și excepții"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Excepție"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mesaj"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Procentaj"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametru"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Numele fișierului"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funcție"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Cod"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Verificator"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Test eșuat"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Comentariu(ii)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Exemple"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definiții"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinonime"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Răspunsuri"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Descarcă rezultate"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Încercați să căutați după:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Mesaje de la motoarele de căutare"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "secunde"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL de căutare"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Copiat"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copiați"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Sugestii"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Limba de căutare"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Limbă implicită"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Auto-detectare"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "CăutareSigură"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "strictă"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderat"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Nimic"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Interval de timp"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Oricând"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Ultima zi"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Ultima săptămână"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Ultima lună"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Ultimul an"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informație!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "momentan, nu există cookie-uri definite."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Ne pare rău!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Niciun rezultat nu a fost găsit. Puteți încerca să:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Nu mai sunt rezultate. Puteți să:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Reîncărcați pagina."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Căutați pentru un alt termen sau selectați altă categorie (deasupra)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Schimbați motorul de căutare folosit în preferințe:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Treceți la o altă instanță:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Căutați folosind o altă solicitare sau selectați altă categorie."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Reveniți la pagina anterioară folosind butonul pentru pagina anterioară."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Permite"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Cuvinte cheie (primul cuvânt din interogare)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nume"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Descriere"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Aceasta este lista modulelor de răspuns instantaneu ale SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Aceasta este lista pluginurilor."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Completare automată"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Găsește lucruri în timp ce tastezi"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Aliniere centrală"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Afișează rezultatele pe centrul paginii (amplasare Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Aceasta este lista de cookie-uri și valorile lor pe care SearXNG le "
"stochează pe computerul dvs."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Cu această listă, puteți evalua transparența SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nume cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valuare"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL-ul de căutare al preferințelor salvate curent"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Notă: specificând configurări personalizate în URL-ul de căutare poate "
"reduce nivelul de confidențialitate prin scurgerea datelor către siturile"
" accesate la căutare."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL pentru revindecarea preferințelor dintr-un alt browser"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copiaza hash-ul preferintelor"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Introduceți hash-ul preferințelor copiate (fără URL) pentru a restaura"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash-ul preferințelor"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Identificator digital de obiect (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Rezolvator de acces deschis DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Selectionarea unui serviciu folosit pentru rescrierea DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Acest tab nu există în interfața de utilizator, dar o puteți căuta în "
"aceste motoare de căutare după !bangs specifice lor."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Activați toate"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Dezactivați toate"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Suportă limba selectată"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Greutate"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Timp maxim"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Rezolvator Favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Afișați favicons lângă rezultatele căutării"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Aceste configurări sunt stocate în cookie-uri, ceea ce ne permite să nu "
"stocăm aceste date despre dumeavoastră."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Aceste cookie-uri servesc doar pentru conveniența dumneavoastră, noi nu "
"stocăm aceste cookie-uri pentru a vă urmări."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Salvați"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Restabilește la valorile implicite"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Înapoi"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Taste rapide"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Similare cu Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigați în rezultatele căutării cu ajutorul tastelor rapide (JavaScript "
"este necesar). Apăsați tasta \"h\" pe pagina principală sau pe pagina de "
"rezultate pentru a obține ajutor."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy de imagini"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Trimitere prin proxy a rezultatelor imagini prin SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Derulare infinită"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Încarcă automat pagina următoare când se derulează la baza paginii curente"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Ce limbă preferați pentru căutare?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Selectați Auto-detect pentru a lăsa SearXNG să detecteze limba căutării "
"dumneavoastră."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Metodă HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Schimba cum forumurile sunt trimise"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Afișați căutarea în titlul paginii"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Când este activat, titlul paginii de rezultate conține căutarea dvs. "
"Browserul dumneavoastră poate înregistra acest titlu"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Rezultate în taburi noi"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Deschide legăturile rezultate în taburi noi"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrează conținutul"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Caută la selectarea categoriei"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Efectuează căutarea imediat dacă este selectată o categorie. Dezactivat "
"pentru a selecta mai multe categorii"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Temă"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Schimbă aspectul la SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Stilul temei"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Selectați auto pentru a urma setările browserului dvs"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokenurile motorului"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tokenuri de acces pentru motoare de căutare private"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Limba interfeței"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Schimbă limba aspectului"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formatare URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "frumos"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Complet"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Gazdă"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Schimbă formatarea URL a rezultatului"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "arhive digitale"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "arată media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "ascunde media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Acest site nu a oferit nici o descriere."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Dimensiune fișier"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dată"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tip"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Rezoluție"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Vizualizare sursă"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresă"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "arată harta"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ascunde harta"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versiune"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Responsabil"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Actualizat la"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etichete"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularitate"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licența"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Proiect"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Pagina proiectului"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Data publicării"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Jurnal"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editor"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Editura"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "legătură magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "fișier torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Sursă completă"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Sursă incompletă"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Numărul fișierelor"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "arată video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ascunde video"

#~ msgid "Engine time (sec)"
#~ msgstr "Timpul motorului (sec)"

#~ msgid "Page loads (sec)"
#~ msgstr "Încărcarea paginii (sec)"

#~ msgid "Errors"
#~ msgstr "Erori"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Rescrie legăturile HTTP cu HTTPS dacă e posibil"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Rezultatele sunt deschise în aceeași "
#~ "fereastră în mod implicit. Acest modul"
#~ " suprascrie acțiunea implicită de a "
#~ "deschide legături în ferestre/taburi noi. "
#~ "(Necesită JavaScript)"

#~ msgid "Color"
#~ msgstr "Culoare"

#~ msgid "Blue (default)"
#~ msgstr "Albastru (implicit)"

#~ msgid "Violet"
#~ msgstr "Violet"

#~ msgid "Green"
#~ msgstr "Verde"

#~ msgid "Cyan"
#~ msgstr "Azuriu"

#~ msgid "Orange"
#~ msgstr "Portocaliu"

#~ msgid "Red"
#~ msgstr "Roșu"

#~ msgid "Category"
#~ msgstr "Categorie"

#~ msgid "Block"
#~ msgstr "Blochează"

#~ msgid "original context"
#~ msgstr "contextul original"

#~ msgid "Plugins"
#~ msgstr "Module"

#~ msgid "Answerers"
#~ msgstr "Răspunzători"

#~ msgid "Avg. time"
#~ msgstr "Timp mediu"

#~ msgid "show details"
#~ msgstr "arată detalii"

#~ msgid "hide details"
#~ msgstr "ascunde detalii"

#~ msgid "Load more..."
#~ msgstr "Încarcă mai multe..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Schimbă aspectul lui searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Transferă rezultatele cu imagini prin searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Aceasta este lista de module de răspundere instantă a lui searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Aceasta este lista de cookie-uri "
#~ "și valorile lor pe care searx le"
#~ " stochează pe calculatorul dumneavoastră."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Cu acea listă puteți evalua nivelul de transparență al lui searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Se pare că folosiți searx pentru prima dată."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Încercați din nou mai târziu sau folosiți o altă instanță searx-"

#~ msgid "Themes"
#~ msgstr "Teme"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metodă"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Configurări avansate"

#~ msgid "Close"
#~ msgstr "Închide"

#~ msgid "Language"
#~ msgstr "Limbă"

#~ msgid "broken"
#~ msgstr "stricat"

#~ msgid "supported"
#~ msgstr "suportat"

#~ msgid "not supported"
#~ msgstr "nesuportat"

#~ msgid "about"
#~ msgstr "despre"

#~ msgid "Avg."
#~ msgstr "Medie"

#~ msgid "User Interface"
#~ msgstr "Interfața cu utilizatorul"

#~ msgid "Choose style for this theme"
#~ msgstr "Alegeți stilul pentru această temă"

#~ msgid "Style"
#~ msgstr "Stil"

#~ msgid "Show advanced settings"
#~ msgstr "Arată setări avansate"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Afișați panoul de setări avansate în pagina de pornire în mod implicit"

#~ msgid "Allow all"
#~ msgstr "Permite toate"

#~ msgid "Disable all"
#~ msgstr "Dezactivați toate"

#~ msgid "Selected language"
#~ msgstr "Limba selectată"

#~ msgid "Query"
#~ msgstr "Termen de căutare"

#~ msgid "save"
#~ msgstr "salvează"

#~ msgid "back"
#~ msgstr "înapoi"

#~ msgid "Links"
#~ msgstr "Legături"

#~ msgid "RSS subscription"
#~ msgstr "Abonament RSS"

#~ msgid "Search results"
#~ msgstr "Rezultatele căutării"

#~ msgid "next page"
#~ msgstr "pagina următoare"

#~ msgid "previous page"
#~ msgstr "pagina anterioară"

#~ msgid "Start search"
#~ msgstr "Pornește căutarea"

#~ msgid "Clear search"
#~ msgstr "Ștergeți căutarea"

#~ msgid "Clear"
#~ msgstr "Golește"

#~ msgid "stats"
#~ msgstr "statistici"

#~ msgid "Heads up!"
#~ msgstr "Atenție!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Se pare că utilizați SearXNG pentru prima dată."

#~ msgid "Well done!"
#~ msgstr "Bravo!"

#~ msgid "Settings saved successfully."
#~ msgstr "Configurările au fost salvate cu succes."

#~ msgid "Oh snap!"
#~ msgstr "Vai!"

#~ msgid "Something went wrong."
#~ msgstr "Ceva n-a funcționat corect."

#~ msgid "Date"
#~ msgstr "Dată"

#~ msgid "Type"
#~ msgstr "Tip"

#~ msgid "Get image"
#~ msgstr "Obține imaginea"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferințe"

#~ msgid "Scores per result"
#~ msgstr "Scoruri per rezultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "un meta-motor de căutare care respectă confidențialitatea"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Niciun abstract disponibil pentru această publicație."

#~ msgid "Self Informations"
#~ msgstr "Informații despre sine"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Modificați cum sunt trimise formularele, "
#~ "<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">învățați mai multe despre "
#~ "metodele de transfer</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Acest plugin verifica daca adresa "
#~ "cererii este un nod de iesire TOR"
#~ " si informeaza utilizatorul in caz "
#~ "afirmativ, ex. check.torproject.org dar prin"
#~ " searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Lista  cu nodul de iesire TOR "
#~ "(https://check.torproject.org/exit-addresses) nu "
#~ "poate fi gasita."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Folosesti TOR. Adresa ta IP pare a fi aceasta: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Nu folosesti TOR. Adresa ta IP pare a fi aceasta: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Detectează automat limba căutării"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Detectează automat limba de căutare a interogărilor și comută la ea."

#~ msgid "others"
#~ msgstr "altele"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Această filă nu apare pentru rezultatele"
#~ " căutării, dar puteți căuta în "
#~ "motoarele enumerate aici prin banguri."

#~ msgid "Shortcut"
#~ msgstr "Scurtătură"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Acest tab nu există în interfața "
#~ "de utilizator, dar o puteți căuta "
#~ "în aceste motoare de căutare după "
#~ "!bangs specifice lor."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Motoarele nu pot obține rezultate."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Vă rugăm să încercați din nou mai"
#~ " târziu sau să găsiți o altă "
#~ "instanță SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Redirecționează către versiuni cu acces "
#~ "deschis ale publicațiilor când sunt "
#~ "disponibile (modul necesar)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Schimbați cum sunt înregistrate cererile, "
#~ "<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">învățați mai multe despre "
#~ "metode de cerere</a>"

#~ msgid "On"
#~ msgstr "Pornit"

#~ msgid "Off"
#~ msgstr "Oprit"

#~ msgid "Enabled"
#~ msgstr "Activat"

#~ msgid "Disabled"
#~ msgstr "Dezactivat"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Execută căutarea imediat dacă o "
#~ "categorie este selectată. Dezactivează pentru"
#~ " a selecta categorii multiple. (Necesită"
#~ " JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Scurtături de tastatură în stilul Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navighează rezultatele căutării cu scurtături"
#~ " de tastatură în stilul Vim (necesită"
#~ " JavaScript). Apăsați tasta „h” în "
#~ "pagina principală sau în pagina cu "
#~ "rezultate pentru a obține ajutor."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "n-am găsit nici un rezultat. Folosiți"
#~ " o altă interogare sau căutați în "
#~ "mai multe categorii."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Rescrie hostname-urile rezultate sau "
#~ "șterge rezultatele bazate pe hostname"

#~ msgid "Bytes"
#~ msgstr "Octeți"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Schimbă hostname-ul"

#~ msgid "Error!"
#~ msgstr "Eroare!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Motoarele nu pot obține rezultate"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Începe prin a trimite o nouă problemă la GiHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generator de numere aleatorii"

#~ msgid "Statistics functions"
#~ msgstr "Funcții statistice"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Calculează {functions} din argumente"

#~ msgid "Get directions"
#~ msgstr "Gaseste directia"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Afișează IP-ul dacă interogarea este "
#~ "„ip” și agentul de utilizator dacă "
#~ "interogarea conține „user agent”."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Nu a putut fi descărcată lista de"
#~ " noduri de ieșire Tor de la: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Folosiți Tor și pare că aveți "
#~ "această adresă de IP externă: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Nu folosiți Tor și aveți această adresă de IP externă: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Cuvinte cheie"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Specificând setări personalizate în URLul "
#~ "de preferințe poate fi folosit pentru"
#~ " sincronizarea preferințelor pe toate "
#~ "aparatele."

#~ msgid "proxied"
#~ msgstr "delegat"

