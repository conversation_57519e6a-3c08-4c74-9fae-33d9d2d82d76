# Danish translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# lolm<PERSON><PERSON><PERSON> <<EMAIL>>, 2024.
# <PERSON>Nordh <<EMAIL>>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# lolm<PERSON><PERSON><PERSON> <lolm<PERSON><PERSON>@users.noreply.translate.codeberg.org>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: Anders<PERSON>ordh <<EMAIL>>\n"
"Language-Team: Danish <https://translate.codeberg.org/projects/searxng/"
"searxng/da/>\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "uden yderligere undergruppering"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "andre"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "filer"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "generelt"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musik"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sociale medier"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "billeder"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videoer"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "Radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "it"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "nyheder"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "kort"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onion-links"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "videnskab"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "applikationer"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "ordbøger"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "sangtekster"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pakker"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "spørgsmål og svar"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "depot"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "software-wikier"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "videnskabelige publikationer"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatisk"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "lys"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "mørk"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "sort"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Oppetid"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Om"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Gennemsnitlig temp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Skydække"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Forhold"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Nuværende forhold"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Aften"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Føles som"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Luftfugtighed"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maks. temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Min. temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Morgen"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nat"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Middag"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Lufttryk"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Solopgang"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Solnedgang"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatur"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV index"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Sigtbarhed"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vind"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "abonnenter"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "opslag"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktive brugere"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kommentare"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "bruger"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "fællesskab"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "point"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "titel"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "forfatter"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Åbn"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "lukket"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "svaret"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Intet fundet"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Kilde"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Fejl ved indlæsning af den næste side"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ugyldige indstillinger, redigér venligst dine valg"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ugyldig indstilling"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "søgefejl"

#: searx/webutils.py:35
msgid "timeout"
msgstr "udløbstid"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "fortolkningsfejl"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-protokolfejl"

#: searx/webutils.py:38
msgid "network error"
msgstr "netværksfejl"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL-fejl: certifikatvalidering mislykkedes"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "uventet nedbrud"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-fejl"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-tilkoblingsfejl"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxyfejl"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "for mange forespørgsler"

#: searx/webutils.py:58
msgid "access denied"
msgstr "adgang nægtet"

#: searx/webutils.py:59
msgid "server API error"
msgstr "server-API-fejl"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspenderet"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "for {minutes} minut(ter) siden"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "for {hours} time(r) og {minutes} minut(ter) siden"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generér forskellige tilfældige værdier"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Beregn {func} af argumenterne"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Vis rute på kort .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (FORÆLDET)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Denne værdi er blevet overskrevet af"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "Bitrate"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "Stemmer"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "Klik"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Sprog"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citater fra år {firstCitationVelocityYear} til "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Kunne ikke læse den specificerede billed-url. Dette kan skyldes et ikke-"
"understøttet filformat. TinEye understøtter kun billeder, der er i JPEG, "
"PNG, GIF, BMP, TIFF eller WebP format."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Billedet er for simpel til at finde matchene billeder. TinEye kræver et "
"grundlæggende niveau af visuelle detaljer for at kunne identificere "
"matchene billeder."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Dette billede kunne ikke downloades."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Bogbedømmelse"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Filkvalitet"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia sortliste"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrer onionresultater fra, der vises på Ahmias sortliste."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Grundlæggende lommeregner"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Udregn matematiske udtryk via søgefeltet"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash plugin"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Konverterer strenge til forskellige hash-digests."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hash-digest"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Værtsnavne plugin"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Omskriv værtsnavne, fjern resultater eller prioriter dem baseret på "
"værtsnavnet"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Open Access DOI-omskrivning"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Undgå betalingsmure ved at viderestille til en åbent tilgængelig version,"
" hvis en sådan findes"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Selv information"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Viser din IP, hvis forespørgslen er \"ip\", og din brugeragent, hvis "
"forespørgslen er \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Din IP er: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Din brugeragent er: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor undersøg plugin"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Dette plugin tjekker, om adressen på anmodningen er en TOR-exit-node, og "
"informerer brugeren, hvis den er, som check.torproject.org, men fra "
"SearXNG i stedet."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Kunne ikke downloade listen over Tor-udgangsnoder fra"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Du bruger Tor, og det ser ud til, at du har den eksterne IP-adresse"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Du bruger ikke Tor, og du har den eksterne IP-adresse"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Fjernelse af tracker URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Fjern trackeres parametre fra den returnerede URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Enhed konverter plugin"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konverter mellem enheder"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Side ikke fundet"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Gå til 1%(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "søgeside"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Donere"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Indstillinger"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Leveret af"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "en åben metasøgemaskine, der respekterer privatlivet"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Kildekode"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Problemsporer"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Søgemaskine-statistik"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Offentlige instanser"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Privatlivspolitik"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontakt tilbyderen af instansen"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Klik på forstørrelsesglasset for at udføre søgning"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Længde"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Visninger"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Forfatter"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "cachet"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Begynd at indsende et nyt problem på GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Venligst tjek for eksisterende, relateret til denne søgemaskine, på GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Jeg bekræfter, at der ikke er nogen eksisterende sag relateret til det "
"problem, jeg støder på"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Hvis det er en offentligt tilgængelige udgave, venligst tilføj URL'en i "
"fejl rapporten"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Udgiv en ny version på Github, med det overstående information inkluderet"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Ingen HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Vis fejllogger og send en fejlrapport ind"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang for denne søgemaskine"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang for dens kategorier"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Median"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Fejlet checkertest(s): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Fejl:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Generelt"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Standardkategorier"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Brugerinterface"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privatliv"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Søgemaskiner"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Pt. anvendte søgemaskiner"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Specielle Søgetermer"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Antal resultater"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Info"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Tilbage til toppen"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Forrige side"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Næste side"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Vis forsiden"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Søg efter..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "ryd"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "søg"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Der er pt. ingen tilgængelige data. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Søgemaskinenavn"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Vægtninger"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Antal resultater"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Svartid"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Driftsikkerhed"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Behandler"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Advarsler"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Fejl og undtagelser"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Undtagelser"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Besked"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Procentdel"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Filnavn"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funktion"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kode"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Kontrollør"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Fejlede tekst"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Kommentar(er)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Eksempler"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definitioner"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonymer"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Svar"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Hent resultater"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Prøv at søge efter:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Beskeder fra søgemaskinerne"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Søge-URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopieret"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopiér"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Forslag"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Søgesprog"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Standardsprog"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Automatisk registrering"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Sikker Søgning"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Stringent"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderat"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Ingen"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Tidsinterval"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Når som helst"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Det seneste døgn"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Den seneste uge"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Den seneste måned"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Det sidste år"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Information!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "der er pt. ingen cookies defineret."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Beklager!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Ingen resultater fundet. Du kan prøve at:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Der er ikke flere resultater. Prøv:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Genindlæs siden."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Søg efter en noget andet aller vælg en kategori (ovenover)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Vælg din ønskede søgemaskine i preferencer:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Skift til en anden instans:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Brug en anden søgestreng eller vælg en anden kategori."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Gå til den forrige side med Forrige-side-knappen."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Tillad"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Nøgleord (første ord i forespørgslen)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Navn"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Beskrivelse"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Dette er en liste over SearXNG's hurtig-svar moduler."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Dette er listen over plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automatisk fuldførelse"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Find under indtastning"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centeret"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Viser resultater på midten af siden."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Dette er listen over de cookies og deres værdier, som SearXNG gemmer på "
"din computer."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Med denne liste kan du vurdere SearXNG's åbenhed."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Cookie-navn"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Værdi"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Søge-URL for den nuværende gemte indstilling"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Bemærk: brugertilpassede indstillinger i søge-URL kan reducere niveauet "
"af beskyttelse ved at lække data til de sider der klikkes på i "
"resultatet."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL til at restaurere dine præferencer i en anden browser"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"En URL, der indeholder dine præferencer. Denne URL kan bruges til at "
"gendanne dine indstillinger på en anden enhed."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopier indstillinger-hash"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Indsæt kopieret indstillinger-hash (uden URL) for at gendanne"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Præference hash"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital Object Identifier (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI-forløser"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Vælg service brugt af DOI-omskrivning"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Denne fane eksisterer ikke i brugergrænsefladen, men du kan søge i disse "
"søgemaskiner via dens !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Aktiver alle"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Deaktiver alle"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Understøtter valgte sprog"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Vægt"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maks-tid"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon resolver"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Vis favicons i nærheden af søgeresultater"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Disse indstillnger gemmes cookies på din enhed. Dette gør, at vi ikke "
"behøver at gemme data om dig."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Disse cookies er kun til dine data. Vi benytter ikke disse til at spore "
"dig."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Gem"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Nustil til standard"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Tilbage"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Genvejstaster"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-lignende"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Naviger søgeresultater med genvejstaster (kræver Javascript). Tryk \"h\" "
"tasten på hoved- eller resultatsiden for hjælp."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Billede-proxy"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proxyer billedresulter gennem SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Uendelig rulning"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Indlæs automatisk næste side, når der rulles til bunden af den nuværende "
"side"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Hvilket sprog foretrækker du til søgninger?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Vælg automatisk registrering for at lade SearXNG registrere sproget af "
"din forespørgsel."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP-metode"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Skift hvordan formularer bliver sendt"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Søgeterm i sidens titel"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Når denne er aktiveret, indeholder titlen på resultatsiden dit søgeterm. "
"Din browser kan registrere denne titel"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultater på nye faner"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Åben resultat-link i nye browser-faner"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrér indhold"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Søg på kategori i stedet"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Udfør søgning med det samme hvis en kategori er valgt. Slå fra for at "
"vælge flere kategorier"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Ændr SearXNG layout"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Tema stil"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Vælg auto for at følge dine browserindstillinger"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Maskinmærker"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Adgangstokens til private søgemaskiner"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Sprog i brugergrænsefladen"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Ændring af layout-sproget"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL-formatering"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Fin"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Fuld"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Vært"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Skift resultat af URL-formatering"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "depot"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "vis media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "skjul media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Denne side gav ikke nogen beskrivelse."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Filstørrelse"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dato"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Type"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Opløsning"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Maskine"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Vis kilde"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresse"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "vis kort"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "skjul kort"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Version"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Vedligeholder"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Opdateret ved"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tags"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularitet"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licens"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projekt hjemmeside"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Dato publiceret"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Tidsskrift"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redaktør"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Forlægger"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet-link"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent-fil"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Afsender"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Henter"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Antal filer"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "vis video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "skjul video"

#~ msgid "Engine time (sec)"
#~ msgstr "Søgemaskine-tid (sek)"

#~ msgid "Page loads (sec)"
#~ msgstr "Sideindlæsninger (sek)"

#~ msgid "Errors"
#~ msgstr "Fejl"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Omskriv HTTP links til HTTPS hvis muligt"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Resultater åbnes som standard i det "
#~ "samme vindue. Dette plugin overskriver "
#~ "dette, således at link åbnes i nye"
#~ " tabs eller vinduer. (JavaScript påkrævet)"

#~ msgid "Color"
#~ msgstr "Farve"

#~ msgid "Blue (default)"
#~ msgstr "Blå (standard)"

#~ msgid "Violet"
#~ msgstr "Violet"

#~ msgid "Green"
#~ msgstr "Grøn"

#~ msgid "Cyan"
#~ msgstr "Cyan"

#~ msgid "Orange"
#~ msgstr "Orange"

#~ msgid "Red"
#~ msgstr "Rød"

#~ msgid "Category"
#~ msgstr "Kategori"

#~ msgid "Block"
#~ msgstr "Blokér"

#~ msgid "original context"
#~ msgstr "oprindelig sammenhæng"

#~ msgid "Plugins"
#~ msgstr "Plugins"

#~ msgid "Answerers"
#~ msgstr "Svarere"

#~ msgid "Avg. time"
#~ msgstr "Gns. tid"

#~ msgid "show details"
#~ msgstr "vis detaljer"

#~ msgid "hide details"
#~ msgstr "skjul detaljer"

#~ msgid "Load more..."
#~ msgstr "Indlæs mere..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Ændring af searx layout"

#~ msgid "Proxying image results through searx"
#~ msgstr "Send billeder via searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Dette er listen over searx's installationens svar-moduler"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "Dette er listen over de cookies og værdier searx gemmer på din computer"

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Med denne liste, kan du bekræfte gennemsigtigheden af searx"

#~ msgid "It look like you are using searx first time."
#~ msgstr "Det ser ud til at benytter searx for første gang."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Vær venlig at prøve igen senere, eller find en anden searx-instans."

#~ msgid "Themes"
#~ msgstr "Temaer"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metode"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Avancerede indstillinger"

#~ msgid "Close"
#~ msgstr "Luk"

#~ msgid "Language"
#~ msgstr "Sprog"

#~ msgid "broken"
#~ msgstr "defekt"

#~ msgid "supported"
#~ msgstr "understøttet"

#~ msgid "not supported"
#~ msgstr "ikke-understøttet"

#~ msgid "about"
#~ msgstr "om"

#~ msgid "Avg."
#~ msgstr "Gns."

#~ msgid "User Interface"
#~ msgstr "Bruger Interface"

#~ msgid "Choose style for this theme"
#~ msgstr "Vælg stil for dette tema"

#~ msgid "Style"
#~ msgstr "Stil"

#~ msgid "Show advanced settings"
#~ msgstr "Vis avancerede indstillinger"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""
#~ "Vis avancerede indstillinger panelet på "
#~ "forsiden som standardindstilling"

#~ msgid "Allow all"
#~ msgstr "Tillad alle"

#~ msgid "Disable all"
#~ msgstr "Deaktiver alt"

#~ msgid "Selected language"
#~ msgstr "Valgt sprog"

#~ msgid "Query"
#~ msgstr "Søgning"

#~ msgid "save"
#~ msgstr "gem"

#~ msgid "back"
#~ msgstr "tilbage"

#~ msgid "Links"
#~ msgstr "Links"

#~ msgid "RSS subscription"
#~ msgstr "RSS-abonnement"

#~ msgid "Search results"
#~ msgstr "Søgereresultater"

#~ msgid "next page"
#~ msgstr "næste side"

#~ msgid "previous page"
#~ msgstr "forrige side"

#~ msgid "Start search"
#~ msgstr "Start søgning"

#~ msgid "Clear search"
#~ msgstr "Ryd søgning"

#~ msgid "Clear"
#~ msgstr "Ryd"

#~ msgid "stats"
#~ msgstr "statistik"

#~ msgid "Heads up!"
#~ msgstr "OBS!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Det ser ud til, at det er første gang, du bruger SearXNG."

#~ msgid "Well done!"
#~ msgstr "Godt klaret!"

#~ msgid "Settings saved successfully."
#~ msgstr "Indstillinger gemt."

#~ msgid "Oh snap!"
#~ msgstr "Åh, pokkers!"

#~ msgid "Something went wrong."
#~ msgstr "Noget gik galt."

#~ msgid "Date"
#~ msgstr "Dato"

#~ msgid "Type"
#~ msgstr "Type"

#~ msgid "Get image"
#~ msgstr "Hent billede"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "indstillinger"

#~ msgid "Scores per result"
#~ msgstr "Vægtninger pr. resultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "en privatlivs--respekterende, hackbar meta-søgemaskine"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Intet sammendrag er tilgængelig for denne publikation."

#~ msgid "Self Informations"
#~ msgstr "Selvinformation"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ændring af hvordan webforms indsendes, "
#~ "<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">lær mere om request-"
#~ "metoder</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Dette plugin tjekker, om adressen på "
#~ "anmodningen er en TOR-exit-node, "
#~ "og informerer brugeren, hvis den er, "
#~ "som check.torproject.org, men fra searxng "
#~ "i stedet."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "TOR exit node listen "
#~ "(https://check.torproject.org/exit-addresses) er "
#~ "ikke tilgængelig."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Du bruger TOR. Din IP-adresse ser ud til at være: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Du bruger ikke TOR. Din IP-adresse ser ud til at være: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Autodetekter søgesprog"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Registrer automatisk søgesproget og skift til det."

#~ msgid "others"
#~ msgstr "andre"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Denne fane vises ikke i "
#~ "søgeresultaterne, men du kan søge i "
#~ "de søgemaskiner, der er anført her, "
#~ "via bangs."

#~ msgid "Shortcut"
#~ msgstr "Genvej"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Denne fane eksisterer ikke i "
#~ "brugergrænsefladen, men du kan søge i"
#~ " disse søgemaskiner via dens !bangs."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Søgemotorer kan ikke hente resultater."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Prøv igen senere, eller find en anden SearXNG-instans."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Omdiriger til open-access-udgaver af "
#~ "publikationer hvis tilgængelig (plugin "
#~ "påkrævet)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ændre hvordan formularer indsendes, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">lær mere om "
#~ "anmodningsmetoder</a>"

#~ msgid "On"
#~ msgstr "Til"

#~ msgid "Off"
#~ msgstr "Fra"

#~ msgid "Enabled"
#~ msgstr "Slået til"

#~ msgid "Disabled"
#~ msgstr "Slået fra"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Udfør søgning straks, hvis en kategori"
#~ " vælges. Slå dette fra for at "
#~ "kunne vælge flere kategorier (JavaScript "
#~ "påkrævet)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Genvejstaster i Vim-stil"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navigér søgeresultater med Vim-lignende "
#~ "genvejstaster (JavaScript påkrævet). Tryk på"
#~ " \"h\" på hoved- eller resultatsiden "
#~ "for at få hjælp."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Vi fandt ingen resultater. Benyt "
#~ "venligst en anden søge-streng eller "
#~ "søg i flere kategorier."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Omskriv resultatets værtsnavne eller fjerne"
#~ " resultater baseret på værtsnavnet"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Værtsnavn erstat"

#~ msgid "Error!"
#~ msgstr "Fejl!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Søgemotorer kan ikke hente resultater"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Opret ny sag på GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generator af tilfældig værdi"

#~ msgid "Statistics functions"
#~ msgstr "Statistiske funktioner"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Beregn {functions} af parametrene"

#~ msgid "Get directions"
#~ msgstr "Få rutevejledning"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Viser din IP adresse hvis søgningen "
#~ "er \"ip\" og din user-agent i "
#~ "søgningen indeholder \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Kunne ikke downloade liste af Tor "
#~ "exit-nodes fra: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Du bruger Tor og du har denne eksterne IP adresse: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Du bruger ikke Tor og du har denne eksterne IP adresse: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Nøgleord"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Specificere brugertilpassede indstillinger i "
#~ "præference-URL'en kan bruges til at "
#~ "synkronisere præference over flere enheder."

#~ msgid "proxied"
#~ msgstr "viderestillet"
