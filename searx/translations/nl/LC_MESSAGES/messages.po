# Dutch translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2014-2018,2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON>, 2015-2018
# <PERSON><PERSON> <<EMAIL>>, 2016-2017
# <PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022, 2023.
# <PERSON>weder doc <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024.
# yannickma<PERSON> <<EMAIL>>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024,
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# marcelStangenberger
# <<EMAIL>>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: artens <<EMAIL>>\n"
"Language-Team: Dutch <https://translate.codeberg.org/projects/searxng/"
"searxng/nl/>\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "zonder verdere onderverdeling"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "overig"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "bestanden"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "algemeen"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muziek"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sociale media"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "afbeeldingen"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "video’s"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "televisie"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "nieuws"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "kaart"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "wetenschap"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "apps"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "woordenboeken"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "liedteksten"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketten"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "vraag en antwoord"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "opslag (code)"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "software wiki's"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "wetenschapelijke publicaties"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatisch"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "licht"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "donker"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "zwart"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "bedrijfstijd"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Over"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Gemiddelde temp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Bewolking"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Omstandigheden"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Huidige weersomstandigheden"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Avond"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Voelt als"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Luchtvochtigheid"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maximum temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Minimum temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Ochtend"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nacht"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Middaguur"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Luchtdruk"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Zonsopkomst"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Zonsondergang"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatuur"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV-index"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Zichtbaarheid"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Wind"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Heldere hemel"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Bewolkt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Mooi"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Mist"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Hevige regen en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Hevige regen- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Hevige regenbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Hevige regen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Zware natte sneeuw en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Zware natte sneeuw- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Zware natte sneeuwbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Zware natte sneeuw"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Zware sneeuwval en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Zware sneeuw- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Zware sneeuwbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Zware sneeuwval"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Lichte regen met onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Lichte regen- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Lichte regenbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Lichte regenval"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Lichte natte sneeuw en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Lichte natte sneeuw- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Lichte natte sneeuwbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Lichte natte sneeuw"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Lichte sneeuwval en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Lichte sneeuw- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Lichte sneeuwbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Lichte sneeuwval"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Gedeeltelijk bewolkt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Regen en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Regen- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Regenbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Regen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Natte sneeuw en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Natte sneeuw- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Natte sneeuwbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Natte sneeuw"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Sneeuw en onweer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Sneeuw- en onweersbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Sneeuwbuien"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Sneeuw"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "abonnees"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "berichten"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "actieve gebruikers"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "reacties"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "gebruiker"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "gemeenschap"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "punten"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "titel"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "auteur"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "open"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "gesloten"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "beantwoord"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Geen resultaat gevonden"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Bron"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Fout bij het laden volgende pagina"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ongeldige instellingswaarde, controleer invoer"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ongeldige instellingen"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "zoekfout"

#: searx/webutils.py:35
msgid "timeout"
msgstr "verlopen"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "verwerkingsfout"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-protocolfout"

#: searx/webutils.py:38
msgid "network error"
msgstr "netwerkfout"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL-fout: de certificaatvalidatie is mislukt"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "onverwachte crash"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-fout"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-verbindingsfout"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxyfout"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "teveel verzoeken"

#: searx/webutils.py:58
msgid "access denied"
msgstr "toegang geweigerd"

#: searx/webutils.py:59
msgid "server API error"
msgstr "server-api-fout"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Geschorst"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minu(u)t(en) geleden"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} u(u)r(en), {minutes} minu(u)t(en) geleden"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Genereer verschillende willekeurige waarden"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Bereken {func} van de variabelen"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Toon route op kaart..."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (VEROUDERD)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Dit object is vervangen door"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanaal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitrate"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "stemmen"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikken"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Taal"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citaties sinds jaar {firstCitationVelocityYear} tot "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Kan die afbeeldings-URL niet lezen. Dit kan komen door een niet "
"ondersteunde bestandsindeling. TinEye ondersteunt alleen afbeeldingtypes "
"JPEG, PNG, GIF, BMP, TIFF of WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"De afbeelding bevat te weinig details om overeenkomsten te vinden. TinEye"
" vereist een basisniveau van visuele details om overeenkomsten succesvol "
"te identificeren."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "De afbeelding kon niet worden gedownload."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Boekbeoordelingswaarde"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Bestandskwaliteit"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia's zwarte lijst"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Toon resultaten zonder onion links die op de Ahmia's zwarte lijst staan"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Simpele Rekenmachine"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Bereken wiskundige formules via de zoekbalk"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Validatie waarde (hash) voor plugin"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Zet tekstwaarden om naar verschillende soorten validatiewaarden (hashes)."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "validatiewaarde"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Hostnamen plug-in"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Hernoem hostnamen, verwijder resultaten of geef prioriteit aan op basis "
"van de hostnaam"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Open Access DOI bewerken"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Omzeil betaalde bronsites met een doorverwijzing naar vrij toegankelijke "
"versies van publicaties indien beschikbaar"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informatie over jezelf"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Toont je IP wanneer de query \"ip\" is en je browser user agent wanneer "
"de query \"user-agent\" is."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Jouw IP is: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Jouw user-agent is: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor controle plug-in"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Deze plug-in controleert of het adres van de verzochte URL een Tor exit-"
"node is en informeert de gebruiker als dit zo is; net als bij "
"check.torproject.org, maar dan van SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Het downloaden van de lijst met Tor exit-nodes is mislukt vanaf de bron"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Je gebruikt Tor en het lijkt er op dat het externe IP adres is"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Je gebruikt geen Tor en hebt het externe IP adres"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Tracker-URL-verwijderaar"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Verwijdert trackerargumenten van de gekregen URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Eenheden conversie plugin"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Converteren tussen eenheden"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Pagina niet gevonden"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Ga naar %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "zoekpagina"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Doneren"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Voorkeuren"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Verzorgd door"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "een privacy respecterende meta zoek machine"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Broncode"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Probleem-tracker"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Zoekmachinestatistieken"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Openbare instanties"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Privacybeleid"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Neem contact op met beheerder instantie"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Klik op het vergrootglas om te zoeken"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Lengte"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Aantal keer bekeken"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Auteur"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "gecachet"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Maak een nieuwe issue aan op Github"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Controleer op bestaande bugs over deze engine op GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Ik bevestig dat er geen eerder gemelde bug is ingediend over het probleem"
" dat ik tegenkom"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Indien dit een openbaar benaderbare installatie is, specificeer de URL in"
" het probleemrapport"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Maak een nieuwe issue aan op Github met de bovenstaande informatie"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Geen HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Bekijk foutenlogboek en verstuur een probleemrapport"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang voor deze zoekmachine"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang voor diens categorieën"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediaan"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Gefaalde controletest(s): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Fouten:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Algemeen"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Standaardcategorieën"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Gebruikersinterface"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privacy"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Zoekmachines"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Momenteel gebruikte zoekmachines"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Speciale Zoekopdrachten"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Aantal zoekresultaten"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informatie"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Terug naar boven"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Vorige pagina"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Volgende pagina"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Toon homepagina"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Zoeken naar..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "wissen"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "zoeken"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Er zijn momenteel geen gegevens beschikbaar. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Naam zoekmachine"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Scores"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Aantal resultaten"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Responstijd"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Betrouwbaarheid"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Totaal"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Verwerken"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Waarschuwingen"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Foutmeldingen en uitzonderingen"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Uitzondering"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Bericht"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Percentage"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Bestandsnaam"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Functie"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Code"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Validatie"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Gefaalde test"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Opmerking(en)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Voorbeelden"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definities"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synoniemen"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Voelt als"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Antwoorden"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Zoekresultaten downloaden"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Probeer te zoeken naar:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Berichten van de zoekmachines"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "secondes"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Zoek-URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Gekopieerd"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopieer"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Suggesties"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Zoektaal"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Standaardtaal"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Automatisch herkennen"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "VeiligZoeken"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Strikt"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Gemiddeld"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Geen"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Tijdspanne"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Enig moment"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Gisteren"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Afgelopen week"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Afgelopen maand"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Afgelopen jaar"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informatie!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "er zijn momenteel geen cookies gedefinieerd."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Sorry!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Geen zoekresultaten. Probeer:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Er zijn geen resultaten meer. U kunt proberen om:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Ververs de pagina."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Zoek op iets anders of selecteer een andere categorie (zie boven)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Verander de zoekmachine gebruikt in de voorkeuren:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Verbind met een ander bronsysteem:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Zoek naar een andere zoekopdracht of selecteer een andere categorie."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Ga terug naar de vorige pagina met de knop Vorige pagina."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Toestaan"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Sleutelwoorden (eerste woord in zoekopdracht)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Naam"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Beschrijving"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Dit is de lijst met SearXNG's \"direct antwoord\"-modules."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Dit is de lijst met plug-ins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Auto-aanvullen"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Zoeken tijdens het typen"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centreren"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Laat de resultaten gecentreerd op de pagina zien (Oscar layout)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Dit is de lijst met cookies en hun waarden die SearXNG op je computer "
"opslaat."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Met die lijst kan je de transparantie van SearXNG beoordelen."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Cookienaam"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Waarde"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Zoek-URL van de huidig opgeslagen voorkeuren"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Let op: aangepaste instellingen opgeven in de zoek-URL kan privacy "
"verminderen, omdat het gegevens door kan geven aan de aangeklikte "
"resultaatwebsites."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL om uw instellingen te herstellen in een andere browser"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Een URL met jouw instellings voorkeuren. Deze URL kan gebruikt worden om "
"jouw instellingen op een ander apparaat te herstellen."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopieer sleutelwaarde (hash) instellingen"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""
"Voeg gekopieerde sleutelwaarde instellingen (zonder de URL) in om te "
"herstellen"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Instellingen sleutelwaarde (hash)"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digitaal Object Identificatienummer"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI bronzoeker"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Selecteer gebruikte dienst door DOI bronzoeker"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Deze tab bestaat niet in de gebruikers interface, maar u kunt in deze "
"machines zoeken via hun !bang."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Alles inschakelen"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Alles uitschakelen"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Ondersteunt geselecteerde taal"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Gewicht"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Max. duur"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon bronzoeker"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Toon favicons naast zoekresultaten"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Deze instellingen worden bewaard in je cookies. Hierdoor hoeven wij niets"
" over jou te bewaren."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Deze cookies zijn alleen voor je eigen gemak, we gebruiken deze cookies "
"niet om je te volgen."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Opslaan"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Herstel standaardinstellingen"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Terug"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Sneltoetsen"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "zoals Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigeer resultaten met sneltoetsen (vereist JavaScript). Gebruik de “h” "
"toets op de start- of resultatenpagina voor hulp."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Afbeeldingenproxy"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Gebruik SearXNG als Afbeeldingsresultaten proxy"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Oneindige lijst"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Volgende pagina automatisch laden bij bereiken van onderkant huidige "
"pagina"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Met welke taalinstelling wil je zoeken?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Kies 'Automatisch herkennen' om SearXNG de taal van uw zoekopdracht te "
"laten herkennen."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Methode"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Wijzig hoe formulieren worden ingediend"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Toon de zoekopdracht in paginatitel"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Indien aangevinkt, toont de paginatitel je zoekopdracht. Je browser kan "
"deze titel mogelijk opslaan"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Open resultaten op nieuw tabblad"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Open koppelingen in nieuwe tabbladen"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filteren op inhoud"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Zoeken bij selecteren van categorie"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Voer zoekopdracht direct uit wanneer categorie wordt geselecteerd. "
"Schakel functie uit om meerdere categorieën te selecteren"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Thema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Verander lay-out van SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Themastijl"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Kies auto om je browserinstellingen te gebruiken"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Engine tokens"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Toegangstokens voor privé-engines"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Interfacetaal"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Wijzig de taal van de lay-out"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL opmaak"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Mooi"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Volledig(e)"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Host"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Pas de resultaat URL opmaak aan"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repo's"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "toon media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "verberg media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Deze site is niet voorzien van een beschrijving."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Bestandsgrootte"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Datum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "type"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolutie"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formaat"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Zoekmachine"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Bekijk bron"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "Adres"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "toon kaart"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "verberg kaart"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versie"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Handhaver"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Bijgewerkt op"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "labels"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Populariteit"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licentie"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Project"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projectpagina"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "publicatie datum"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "dagboek"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redacteur"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "uitgever"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magneetlink"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrentbestand"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeders"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leechers"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Aantal bestanden"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "toon video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "verberg video"

#~ msgid "Engine time (sec)"
#~ msgstr "Snelheid zoekmachine (sec)"

#~ msgid "Page loads (sec)"
#~ msgstr "Laden van pagina’s (sec)"

#~ msgid "Errors"
#~ msgstr "Fouten"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA vereist"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Herschrijf HTTP-koppelingen naar HTTPS, indien mogelijk"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Resultaten worden standaard in hetzelfde "
#~ "venster geopend. Deze plug-in "
#~ "overschrijft het standaardgedrag zodat "
#~ "koppelingen in nieuwe tabbladen/vensters "
#~ "geopend worden. (JavaScript vereist)"

#~ msgid "Color"
#~ msgstr "Kleur"

#~ msgid "Blue (default)"
#~ msgstr "Blauw (standaard)"

#~ msgid "Violet"
#~ msgstr "Violet"

#~ msgid "Green"
#~ msgstr "Groen"

#~ msgid "Cyan"
#~ msgstr "Cyaan"

#~ msgid "Orange"
#~ msgstr "Oranje"

#~ msgid "Red"
#~ msgstr "Rood"

#~ msgid "Category"
#~ msgstr "Categorie"

#~ msgid "Block"
#~ msgstr "Blokkeren"

#~ msgid "original context"
#~ msgstr "oorspronkelijke context"

#~ msgid "Plugins"
#~ msgstr "Plug-ins"

#~ msgid "Answerers"
#~ msgstr "Beantwoorders"

#~ msgid "Avg. time"
#~ msgstr "Gem. duur"

#~ msgid "show details"
#~ msgstr "toon details"

#~ msgid "hide details"
#~ msgstr "verberg details"

#~ msgid "Load more..."
#~ msgstr "Meer laden..."

#~ msgid "Loading..."
#~ msgstr "Laden..."

#~ msgid "Change searx layout"
#~ msgstr "Opmaak van searx aanpassen"

#~ msgid "Proxying image results through searx"
#~ msgstr "Afbeeldingsresultaten via searx laden"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Dit is het overzicht van de instantantwoordmodules van searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Dit is de lijst van cookies en "
#~ "hun waarden die searx op je "
#~ "computer opslaat."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Met deze lijst kan je de openheid van searx beoordelen."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Het lijkt erop dat je searx voor de eerste keer gebruikt."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Probeer het later opnieuw, of gebruik een andere searx server."

#~ msgid "Themes"
#~ msgstr "Thema’s"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Methode"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Geavanceerde instellingen"

#~ msgid "Close"
#~ msgstr "Sluiten"

#~ msgid "Language"
#~ msgstr "Taal"

#~ msgid "broken"
#~ msgstr "stuk"

#~ msgid "supported"
#~ msgstr "ondersteund"

#~ msgid "not supported"
#~ msgstr "niet ondersteund"

#~ msgid "about"
#~ msgstr "over"

#~ msgid "Avg."
#~ msgstr "Gem."

#~ msgid "User Interface"
#~ msgstr "Gebruikersinterface"

#~ msgid "Choose style for this theme"
#~ msgstr "Kies een stijl voor dit thema"

#~ msgid "Style"
#~ msgstr "Stijl"

#~ msgid "Show advanced settings"
#~ msgstr "Geavanceerde instellingen tonen"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Paneel met geavanceerde instellingen standaard tonen op homepagina"

#~ msgid "Allow all"
#~ msgstr "Alles inschakelen"

#~ msgid "Disable all"
#~ msgstr "Alles uitschakelen"

#~ msgid "Selected language"
#~ msgstr "Geselecteerde taal"

#~ msgid "Query"
#~ msgstr "Zoekopdracht"

#~ msgid "save"
#~ msgstr "bewaren"

#~ msgid "back"
#~ msgstr "terug"

#~ msgid "Links"
#~ msgstr "Koppelingen"

#~ msgid "RSS subscription"
#~ msgstr "RSS-abonnement"

#~ msgid "Search results"
#~ msgstr "Zoekresultaten"

#~ msgid "next page"
#~ msgstr "volgende pagina"

#~ msgid "previous page"
#~ msgstr "vorige pagina"

#~ msgid "Start search"
#~ msgstr "Start zoeken"

#~ msgid "Clear search"
#~ msgstr "Zoekopdracht wissen"

#~ msgid "Clear"
#~ msgstr "Wissen"

#~ msgid "stats"
#~ msgstr "stats"

#~ msgid "Heads up!"
#~ msgstr "Opgelet!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Het lijkt erop dat je SearXNG voor de eerste keer gebruikt."

#~ msgid "Well done!"
#~ msgstr "Goed gedaan!"

#~ msgid "Settings saved successfully."
#~ msgstr "Instellingen opgeslagen."

#~ msgid "Oh snap!"
#~ msgstr "Oeps!"

#~ msgid "Something went wrong."
#~ msgstr "Er ging iets fout."

#~ msgid "Date"
#~ msgstr "Datum"

#~ msgid "Type"
#~ msgstr "Type"

#~ msgid "Get image"
#~ msgstr "Toon afbeelding"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "voorkeuren"

#~ msgid "Scores per result"
#~ msgstr "Scores per zoekresultaat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "een privacy-respecterende, aanpasbare meta-zoekmachine"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Voor deze publicatie is geen abstract beschikbaar."

#~ msgid "Self Informations"
#~ msgstr "Informatie Over Jezelf"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Bepaal hoe de formulieren worden "
#~ "ingestuurd, <a "
#~ "href=\"http://nl.wikipedia.org/wiki/Hypertext_Transfer_Protocol"
#~ "#HTTP-requests\" rel=\"external\">lees meer over"
#~ " opvraagmethodes</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Deze plugin controleert of het adres "
#~ "van de aanvraag een TOR exit node"
#~ " is, en informeert de gebruiker als"
#~ " dat zo is, net zoals "
#~ "check.torproject.org maar dan van searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "De TOR exit node lijst "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "onbereikbaar."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Je gebruikt TOR. Het lijkt erop dat uw IP adres {ip_adress} is."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Je gebruikt geen TOR. Het lijkt erop dat je IP adres {ip_address} is."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "overigen"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Dit tabblad wordt niet weergegeven voor"
#~ " zoekresultaten, maar u kunt de hier"
#~ " genoemde zoekmachines doorzoeken via "
#~ "bangs."

#~ msgid "Shortcut"
#~ msgstr "Snelkoppeling"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Deze tab bestaat niet in de "
#~ "gebruikers omgeving, maar u kunt in "
#~ "deze machines zoeken via hun !bang."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Zoekmachines konden geen resultaten ophalen."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Gelieve later opnieuw te proberen of "
#~ "een andere SearXNG-instantie te "
#~ "proberen."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Doorverwijzen naar vrij toegankelijke versies"
#~ " van publicaties, indien beschikbaar "
#~ "(plug-in vereist)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Bepaal hoe de formulieren worden "
#~ "ingestuurd, <a "
#~ "href=\"http://nl.wikipedia.org/wiki/Hypertext_Transfer_Protocol"
#~ "#HTTP-requests\" rel=\"external\">lees meer over"
#~ " opvraagmethodes</a>"

#~ msgid "On"
#~ msgstr "Aan"

#~ msgid "Off"
#~ msgstr "Uit"

#~ msgid "Enabled"
#~ msgstr "Ingeschakeld"

#~ msgid "Disabled"
#~ msgstr "Uitgeschakeld"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Zoekopdracht onmiddellijk uitvoeren wanneer "
#~ "een categorie geselecteerd wordt. Zet "
#~ "dit uit om meerdere categorieën te "
#~ "selecteren. (JavaScript vereist)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Sneltoetsen als in Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Blader door zoekresultaten met sneltoetsen "
#~ "zoals die in Vim (JavaScript vereist)."
#~ " Druk op ‘h’ op de hoofdpagina "
#~ "of de pagina met resultaten voor "
#~ "hulp."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "We konden geen resultaten vinden. "
#~ "Probeer een andere zoekopdracht, of zoek"
#~ " in meer categorieën."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Pas resulterende servernamen aan of "
#~ "verwijder resultaten op basis van de "
#~ "servernaam"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Servernaam vervangen"

#~ msgid "Error!"
#~ msgstr "Fout!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Zoekmachines konden geen resultaten ophalen"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Maak een nieuwe issue op Github"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Willekeurigewaardegenerator"

#~ msgid "Statistics functions"
#~ msgstr "Statistische functies"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Bereken {functions} van de opties"

#~ msgid "Get directions"
#~ msgstr "Routebeschrijving"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Geeft je IP-adres weer als de "
#~ "zoekopdracht ‘ip’ is en je "
#~ "browseridentificatie als de zoekopdracht ‘user"
#~ " agent’ bevat."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Kan de lijst met Tor exit-nodes"
#~ " niet downloaden van: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Je gebruikt Tor en het lijkt er"
#~ " op dat dit je externe IP adres"
#~ " is: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Je maakt geen gebruik van Tor en"
#~ " dit is je externe IP adres: "
#~ "{ip_address}"

#~ msgid "Keywords"
#~ msgstr "Kernwoorden"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Aangepaste instellingen in de instellingen "
#~ "URL kunnen worden gebruikt om "
#~ "instellingen te synchroniseren op "
#~ "verschillende apparaten."

#~ msgid "proxied"
#~ msgstr "geproxyt"
