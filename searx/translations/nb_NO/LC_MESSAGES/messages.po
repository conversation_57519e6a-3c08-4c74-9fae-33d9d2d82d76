# Norwegian Bokmål (Norway) translations for PROJECT.
# Copyright (C) 2021 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2021.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <PERSON><PERSON><PERSON>z <<EMAIL>>, 2024.
# A<PERSON><PERSON>z <<EMAIL>>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: Haraldher <<EMAIL>>\n"
"Language-Team: Norwegian Bokmål <https://translate.codeberg.org/projects/"
"searxng/searxng/nb_NO/>\n"
"Language: nb_NO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "uten ytterligere undergruppering"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "annet"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "filer"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "generelt"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musikk"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sosiale medier"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "bilder"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videoer"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "it"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "nyheter"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "kart"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "vitenskap"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "apper"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "ordbøker"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "sangtekster"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pakker"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "spørsmål og svar"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "pakkebrønner"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "programvare-wikier"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "vitenskaplige publikasjoner"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatisk"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "lys"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "mørk"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "svart"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Oppetid"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Om"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Gjennomsnittstemp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Skydekke"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Værforhold"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Nåværende værforhold"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Kveld"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Føles som"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Luftfuktighet"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maks temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Laveste temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Morgen"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Natt"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Middag"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Lufttrykk"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Soloppgang"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Solnedgang"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatur"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV-indeks"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Sikt"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vind"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Skyfri himmel"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Overskyet"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Fint"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Tåke"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Kraftig regn og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Kraftige regnbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Kraftige regnbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Kraftig regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Kraftig sludd og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Kraftige sluddbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Kraftige sluddbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Kraftig sludd"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Kraftig snø og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Kraftige snøbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Kraftige snøbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Kraftig snø"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Lett regn og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Lette regnbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Lette regnbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Lett regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Lett sludd og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Lette sluddbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Lette sluddbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Lett sludd"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Lett snø og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Lette snøbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Lette snøbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Lett snø"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Delvis skyet"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Regn og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Regnbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Regnbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Sludd og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Sluddbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Sluddbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Sludd"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Snø og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Snøbyger og torden"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Snøbyger"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Snø"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "abonnenter"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "innlegg"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktive brukere"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kommentarer"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "bruker"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "Fellesskap"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "poeng"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "tittel"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "forfatter"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "åpen"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "lukket"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "besvart"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Fant ingen elementer"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Kilde"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Feil ved lasting av neste side"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ugyldige innstillinger, rediger dine preferanser"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ugyldige innstillinger"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "søkefeil"

#: searx/webutils.py:35
msgid "timeout"
msgstr "tidsavbrudd"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "tolkningsfeil"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-protokollfeil"

#: searx/webutils.py:38
msgid "network error"
msgstr "nettverksfeil"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL-feil: sertifikat validering mislyktes"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "uventet krasj"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-feil"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-tilkoblingsfeil"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "mellomtjenerfeil"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "for mange forespørsler"

#: searx/webutils.py:58
msgid "access denied"
msgstr "tilgang nektet"

#: searx/webutils.py:59
msgid "server API error"
msgstr "server API feil"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspendert"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minutt(er) siden"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} time(r), {minutes} minutt(er) siden"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generer forskjellige tilfeldige verdier"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Beregn {func} av argumentene"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Vis rute på kartet .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (FORELDET)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Denne oppføringen har blitt erstattet av"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "overføringshastighet"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "stemmer"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikk"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Språk"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} sitater fra år {firstCitationVelocityYear} til "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Kunne ikke lese bilde-lenken. Dette kan være fordi bildet er i et format "
"som ikke er støttet. TinEye støtter bare JPEG, PNG, GIF, BMP, TIFF eller "
"WebP formater."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Bildet er for enkelt til å finne treff. TinEye krever et visst nivå av "
"visuell detalj for å identifisere like eller lignende bilder."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Bildet kunne ikke lastes ned."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Bokvurdering"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Filkvalitet"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia svarteliste"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrer ut onion-resultater som finnes i Ahmias svarteliste."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Enkel kalkulator"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Kalkuler matematiske uttrykk via søkebaren"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash-plugin"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Konverterer strenger til andre sjekksum-verdier."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "sjekksumverdi"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Vertnavn-plugin"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Skriv om vertnavn, fjern resultater eller prioriter dem basert på "
"vertnavnet"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Open Access DOI-omskriving"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Unngå betalingsmurer ved å omdirigere til versjoner med åpen tilgang når "
"tilgjengelig"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Egen informasjon"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Viser din IP hvis spørringen er «ip» og brukeragenten din hvis spørringen"
" er «user-agent»."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Din IP er: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Brukeragenten din er: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor sjekking plugin"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Denne plugin-en sjekker om adressen til forespørselen er en Tor "
"utgangsnode, og informerer brukeren om den er det; slik som "
"check.torproject.org gjør, men fra SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Kunne ikke laste ned listen over Tor-utgangsnoder fra"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Du bruker Tor, og det ser ut som du har den eksterne IP-adressen"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Du bruker ikke Tor, og du har den eksterne IP-adressen"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Sporings-nettadressefjerner"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Fjern sporingsargumenter fra den returnerte nettadressen"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Tillegg for enhetskonvertering"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konverter mellom enheter"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Fant ikke siden"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Gå til %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "søkeside"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Doner"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Innstillinger"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Drevet av"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "en åpen metasøkemotor som respekterer personvernet"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Kildekode"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Problemsporer"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Søkemotorstatistikk"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Offentlige instanser"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Personvernerklæring"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontakt tilbyderen av instansen"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Klikk på forstørrelsesglasset for å søke"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Lengde"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Visninger"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Forfatter"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "hurtiglagret"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Begynn opprettelse av en ny sak på GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Vennligst sjekk for eksisterende feil for denne motoren på GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Jeg bekrefter at det ikke finnes noen registrerte feil knyttet til "
"problemet jeg opplever"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Hvis dette er en offentlig instans, vennligst spesifiser URL-en i "
"feilrapporten"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Opprett en ny sak på Github med informasjonen ovenfor"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Ingen HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Vis feillogger og send inn en feilrapport"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang for denne motoren"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang for dens kategorier"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Median"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Mislykket/ede sjekkingstest(er): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Feil:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Generelt"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Forvalgte kategorier"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Brukergrensesnitt"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Personvern"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Søkemotorer"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Søkemotorer i bruk"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Spesialspørringer"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Informasjonskapsler"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Antall resultater"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informasjon"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Til toppen"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Forrige side"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Neste side"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Vis forsiden"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Søk etter …"

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "tøm"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "søk"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Ingen data tilgjengelig for øyeblikket. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Søkemotornavn"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Poengsummer"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Antall resultater"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Svartid"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Pålitelighet"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Totalt"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Behandler"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Advarsler"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Feil og unntak"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Unntak"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Melding"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Prosentandel"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Filnavn"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funksjon"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kode"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Sjekker"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Mislykket test"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Kommentar(er)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Eksempler"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definisjoner"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonymer"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Føles som"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Svar"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Last ned resultater"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Prøv å søke etter:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Meldinger fra søkemotorene"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekunder"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Søkenettadresse"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "kopiert"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopier"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Forslag"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Søkespråk"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Forvalgt språk"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Oppdag automatisk"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "TrygtSøk"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Strengt"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderat"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Ingen"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Tidsområde"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Når som helst"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Siste dag"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Siste uke"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Siste måned"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Siste år"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informasjon!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "det er ingen informasjonskapsler definert per nå."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Beklager!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Fant ingen resultater. Du kan prøve å:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Det er ingen flere resultater. Du kan prøve å:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Oppdater siden."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Bruk et annet søkeord eller velg en annen kategori (over)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Endre søkemotoren brukt i innstillingene:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Bytt til en annen instans:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Oppgi et annet søkeord eller velg en annen kategori."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Gå til den forrige siden med tilbake-knappen."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Tillat"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Nøkkelord (første ord i spørringen)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Navn"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Beskrivelse"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Dette er listen over SearXNG sine moduler for umiddelbare svar."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Dette er en liste over programtillegg."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Auto-fullføring"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Finn ting mens du skriver"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Senterjustering"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Viser resultater midt på siden (Oscar layout)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Dette er listen over informasjonskapsler og deres verdier SearXNG lagrer "
"på datamaskinen din."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Med denne listen kan du vurdere SearXNGs åpenhet."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Informasjonskapselnavn"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Verdi"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Nettadresse for søk med de nåværende lagrede innstillingene"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Merk: å angi egendefinerte innstillinger i søkenettadressen kan redusere "
"personvernet ved at data lekker til de sidene du klikker på."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "Nettadresse for å gjenopprette innstillingene dine i en annen nettleser"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"En nettadresse som inneholder innstillingene dine. Denne nettadressen kan "
"brukes for å gjenopprette innstillingene dine på en annen enhet."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopier innstillinger-hash"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Sett inn kopiert innstillinger-hash (uten URL) for å gjenopprette"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Innstillinger-hash"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital objektidentifikator (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI-utleder"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Velg tjenesten som brukes ved DOI-omskrivning"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Denne fanen eksisterer ikke i brukergrensesnittet, men du kan søke i "
"disse motorene ved hjelp av deres !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Aktiver alle"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Deaktiver alle"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Støtter valgt språk"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Vekt"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maks tid"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon-løser"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Vis favikoner nær søkeresultatene"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Disse innstillingene lagres i informasjonskapslene dine, noe som gjør at "
"vi slipper å lagre disse dataene om deg."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Disse informasjonskapslene er kun til din nytte, de brukes ikke til å "
"spore deg."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Lagre"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Tilbakestill forvalg"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Tilbake"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Hurtigtaster"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-lignende"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Naviger søkeresultatene med hurtigtastene (krever JavaScript). Trykk "
"«h»-tasten på hoved- eller resultatsiden for hjelp."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Bildemellomtjener"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Mellomtjener bilderesultater gjennom SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Uendelig rulling"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Last inn neste side automatisk ved rulling til bunnen av nåværende side"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Hvilket språk foretrekker du for søk?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Velg Auto-oppdag for å la SearXNG oppdage språket til søket ditt."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Metode"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Endre hvordan skjemaer blir sendt inn"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Spørring i sidens tittel"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Legger til spørringen din i tittelfeltet for opprettet side. Nettleseren "
"din kan registrere denne tittelen"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultater i nye faner"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Åpne resultatlenker i nye nettleserfaner"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrer innhold"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Søk ved kategorivalg"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Utfør søk umiddelbart hvis en kategori er valgt. Deaktiver for å velge "
"flere kategorier"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Drakt"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Endre SearXNG-sideoppsett"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Draktstil"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Velg «Automatisk» for å følge nettleserinnstillingene"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Søkemotorsymboler"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tilgangssymboler for private motorer"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Grensesnitts-språk"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Endre språket for oppsettet"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL-formatering"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Fin"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Fullstendig"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Vert"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Endre formatering av resultat-URL"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "pakkebrønner"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "vis media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "skjul media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Siden angav ingen beskrivelse."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Filstørrelse"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dato"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Type"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Oppløsning"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Søkemotor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Vis kilde"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresse"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "vis kart"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "skjul kart"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versjon"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Vedlikeholder"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Oppdatert"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tagger"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularitet"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lisens"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Prosjekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Prosjektets hjemmeside"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Publisert dato"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Tidsskrift"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redaktør"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Utgiver"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnetlenke"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrentfil"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Deler"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Henter"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Antall filer"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "vis video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "skjul video"

#~ msgid "Change searx layout"
#~ msgstr "Endre searx-oppsett"

#~ msgid "Proxying image results through searx"
#~ msgstr "Mellomtjener bilderesultater gjennom searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Dette er en liste over moduler for umiddelbare svar i searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Dette er en liste over kaker og"
#~ " verdiene i dem som searx lagrer "
#~ "på datamaskinen din."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Med denne listen kan du bedømme searx-åpenhet."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Det ser ut til at du bruker searx for første gang."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Prøv senere eller finn en annen searx-instans."

#~ msgid "Themes"
#~ msgstr "Drakter"

#~ msgid "Reliablity"
#~ msgstr "Pålitelighet"

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metode"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Denne fanen vises ikke for "
#~ "søkeresultater, men du kan søke i "
#~ "motorene som er opplistet her med "
#~ "utropstegn."

#~ msgid "Advanced settings"
#~ msgstr "Avanserte innstillinger"

#~ msgid "Close"
#~ msgstr "Lukk"

#~ msgid "Language"
#~ msgstr "Språk"

#~ msgid "broken"
#~ msgstr "knekt"

#~ msgid "supported"
#~ msgstr "støttet"

#~ msgid "not supported"
#~ msgstr "ikke støttet"

#~ msgid "about"
#~ msgstr "om"

#~ msgid "Avg."
#~ msgstr "Gjen."

#~ msgid "User Interface"
#~ msgstr "Brukergrensesnitt"

#~ msgid "Choose style for this theme"
#~ msgstr "Velg stil for denne drakten"

#~ msgid "Style"
#~ msgstr "Stil"

#~ msgid "Show advanced settings"
#~ msgstr "Vis avanserte innstillinger"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Vis panel for avanserte innstillinger på hjemmesiden som forvalg"

#~ msgid "Allow all"
#~ msgstr "Tillat alle"

#~ msgid "Disable all"
#~ msgstr "Nekt alle"

#~ msgid "Selected language"
#~ msgstr "Valgt språk"

#~ msgid "Query"
#~ msgstr "Forespørsel"

#~ msgid "save"
#~ msgstr "lagre"

#~ msgid "back"
#~ msgstr "tilbake"

#~ msgid "Links"
#~ msgstr "Lenker"

#~ msgid "RSS subscription"
#~ msgstr "RSS-abonnement"

#~ msgid "Search results"
#~ msgstr "Søkeresultater"

#~ msgid "next page"
#~ msgstr "neste side"

#~ msgid "previous page"
#~ msgstr "forrige side"

#~ msgid "Start search"
#~ msgstr "Start søk"

#~ msgid "Clear search"
#~ msgstr "Tøm søk"

#~ msgid "Clear"
#~ msgstr "Tøm"

#~ msgid "stats"
#~ msgstr "statistikk"

#~ msgid "Heads up!"
#~ msgstr "Obs!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Det ser ut til at du bruker SearXNG for første gang."

#~ msgid "Well done!"
#~ msgstr "Bra gjort."

#~ msgid "Settings saved successfully."
#~ msgstr "Innstillinger lagret."

#~ msgid "Oh snap!"
#~ msgstr "Oida."

#~ msgid "Something went wrong."
#~ msgstr "Noe gikk galt."

#~ msgid "Date"
#~ msgstr "Dato"

#~ msgid "Type"
#~ msgstr "Type"

#~ msgid "Get image"
#~ msgstr "Hent bilde"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "innstillinger"

#~ msgid "Scores per result"
#~ msgstr "Vektninger per resultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "en personvernsrespekterende, hackbar metasøkemotor"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Sammendrag er ikke tilgjengelig for denne publikasjonen."

#~ msgid "Self Informations"
#~ msgstr "Selv-informasjon"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Endre hvordan skjemaer innsendes, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">lær mer om "
#~ "forespørselsmetoder</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Denne plugin sjekker om adressen til "
#~ "forespørselen er en TOR-utgangsnode, og"
#~ " informerer brukeren om den er det,"
#~ " som check.torproject.org, men fra searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Du bruker TOR. Din ip-adresse er: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Du bruker ikke TOR. Din ip adresse er: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Automatisk oppdaging av søke språk"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Automatisk oppdag spørringens søke språk og bytt til dette."

#~ msgid "others"
#~ msgstr "andre"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Denne fanen vises ikke i søke "
#~ "resultatene, men du kan søke i "
#~ "søkemotorene listed her via bangs."

#~ msgid "Shortcut"
#~ msgstr "Snarvei"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Søkemotorene kan ikke hente inn resultater."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Prøv igjen senere eller finn en annen SearXNG-instans."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Videresend til åpen tilgang-versjoner av"
#~ " publikasjoner når de finnes "
#~ "(programtillegg kreves)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "På"

#~ msgid "Off"
#~ msgstr "Av"

#~ msgid "Enabled"
#~ msgstr "Påskrudd"

#~ msgid "Disabled"
#~ msgstr "Avskrudd"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Utfør søk umiddelbart når en kategori"
#~ " velges. Skru av for å velge "
#~ "flere kategorier. (JavaScript kreves)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim-lignende hurtigtaster"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Naviger søkeresultater med Vim-lignende "
#~ "hurtigtaster (JavaScript kreves). Trykk "
#~ "\"h\"-tasten på hoved- eller resultatsiden "
#~ "for å få hjelp."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr "fant ingen resultater. Søk etter noe annet, eller i flere kategorier."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Skriv om vertsnavn eller fjern resultater basert på vertsnavn"

#~ msgid "Bytes"
#~ msgstr "Byte"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Vertsnavnserstatning"

#~ msgid "Error!"
#~ msgstr "Feil!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Søkemotorer kan ikke motta resultater"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Begynn opprettelse av en ny sak på GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generator for tilfeldige tall"

#~ msgid "Statistics functions"
#~ msgstr "Statistikkfunksjoner"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Regn ut {functions} av parameterne"

#~ msgid "Get directions"
#~ msgstr "Få veibeskrivelser"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Viser din IP hvis spørringen er "
#~ "\"ip\" og din brukeragent hvis "
#~ "spørringen inneholder \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Kunne ikke laste ned listen over "
#~ "Tor-utgangsnoder fra: https://check.torproject.org"
#~ "/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Du bruker Tor og det ser ut "
#~ "som om du har denne eksterne IP"
#~ " adressen: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Du bruker ikke Tor og du har denne IP adressen: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Nøkkelord"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Å spesifisere egendefinerte innstillinger i"
#~ " preferanse-URLen kan brukes til å"
#~ " synkronisere preferanser på tvers av "
#~ "enheter."

#~ msgid "proxied"
#~ msgstr "mellomtjent"
