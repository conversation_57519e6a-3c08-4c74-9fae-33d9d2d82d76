# Welsh translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON><PERSON>or <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-03-07 00:07+0000\n"
"Last-Translator: DanielBoone <<EMAIL>>\n"
"Language: cy\n"
"Language-Team: Welsh "
"<https://translate.codeberg.org/projects/searxng/searxng/cy/>\n"
"Plural-Forms: nplurals=4; plural=(n==1) ? 0 : (n==2) ? 1 : (n != 8 && n "
"!= 11) ? 2 : 3;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "heb is-grwpio pellach"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "arall"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ffeiliau"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "cyffredinol"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "cerddoriaeth"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "cyfryngau cymdeithasol"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "delweddau"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "fideos"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "teledu"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "technoleg"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "newyddion"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "map"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "winwns"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "gwyddoniaeth"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "apiau"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "geiriaduron"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "geiriau"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pecynnau"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "q&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "storfeydd"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wicis meddalwedd"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "gwe"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "cyhoeddiadau gwyddonol"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "auto"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "golau"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tywyll"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "du"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Amser Llafur"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Ynghylch"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Tymheredd cyfartalog"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Gorchuddiad cwmwl"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Cyflwr"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Cyflwr presennol"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Noswaith"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Yn teimlo fel"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Lleithder"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Tymheredd uchaf"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Tymheredd isaf"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Bore"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nos"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Canol dydd"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Pwysedd"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Codiad haul"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Machlud"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Tymheredd"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Mynegai UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Gwelededd"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Gwynt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "tanysgrifwyr"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "postiau"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "defnyddwyr gweithredol"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "sylwadau"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "defnyddiwr"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "cymuned"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "pwyntiau"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "teitl"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "awdur"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "ar agor"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "ar gau"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "wedi'i ateb"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Ni chanfuwyd eitem"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Ffynhonnell"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Gwall wrth lwytho'r dudalen nesaf"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Gosodiadau annilys, golygwch eich dewisiadau"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Gosodiadau annilys"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "gwall chwilio"

#: searx/webutils.py:35
msgid "timeout"
msgstr "terfyn amser"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "gwall dosrannu"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "gwall protocol HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "gwall rhwydwaith"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Gwall SSL: dilysu tystysgrif wedi methu"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "damwain annisgwyl"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "gwall HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "gwall cysylltiad HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "gwall dirprwy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "gormod o geisiadau"

#: searx/webutils.py:58
msgid "access denied"
msgstr "mynediad wedi ei wrthod"

#: searx/webutils.py:59
msgid "server API error"
msgstr "gwall API gweinydd"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Atal"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} munud yn ôl"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} awr, {minutes} munud yn ôl"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Cynhyrchu gwahanol werthoedd ar hap"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr ""

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLETE)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Mae'r cofnod hwn wedi ei ddisodli gan"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Sianel"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "cyfradd didau"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "pleidleisiau"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "cliciau"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Iaith"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} o ddyfyniadau o'r flwyddyn {firstCitationVelocityYear} i "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Methu darllen url y ddelwedd honno. Gall hyn fod oherwydd fformat nad "
"yw'n cael ei gefnogi. Mae TinEye ond yn cefnogi delweddau JPEG, PNG, GIF,"
" BMP, TIFF neu WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Mae'r ddelwedd yn rhy syml i ganfod canlyniadau. Mae angen lefel "
"sylfaenol o fanylion gweledol i TinEye allu canfod canlyniadau yn "
"llwyddiannus."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Doedd dim modd islwytho'r ddelwedd."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Gradd llyfr"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "ansawdd ffeil"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Cyfrifo mynegiad mathemategol o'r bar chwilio"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Trosi llinynnau i wahanol dreuliadau hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "Digon o hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Ategyn enwau gwesteiwyr"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Newid, tynnu neu flaenoriaethu canlyniadau yn seiliedig ar yr enw "
"gwesteiwr"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Disodli DOI Open Access"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Osgoi wal dâl drwy arallgyfeirio i fersiynau mynediad agored o "
"gyhoeddiadau os ydynt ar gael"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Hunan-wybodaeth"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Eich cyfeiriad IP: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Eich asiant defnyddiwr: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Ategyn gwirio Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Mae'r ategyn hwn yn gwirio a ydy cyfeiriad y cais yn nod ymadael Tor, ac "
"yn rhoi gwybod i'r defnyddiwr os felly. Mae'n debyg i "
"check.torproject.org, ond gan SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Tynnu tracwyr URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Tynnu tracwyr sy'n ymddangos mewn URLs"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Trosi rhwng unedau"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Heb ganfod y dudalen"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Mynd i %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "tudalen chwilio"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Rhoddi"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Dewisiadau"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Pwerir gan"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "peiriant metachwilio sy'n parchu preifatrwydd"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Cod ffynhonnell"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Traciwr problemau"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Ystadegau'r peiriannau"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Gweinyddion cyhoeddus"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Polisi preifatrwydd"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Cysylltu â chynhaliwr y gweinydd"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Cliciwch ar y chwyddwydr i chwilio"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Hyd"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr ""

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Awdur"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "wedi'i storio"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Dechrau cyflwyno problem newydd ar GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Gwiriwch am namau sy'n bodoli eisoes ynglŷn â'r peiriant hwn ar GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Rwy'n cadarnhau nad oes nam yn bodoli eisoes ynghylch y broblem rwy'n ei "
"phrofi"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Os ydy hwn yn weinydd cyhoeddus, rhowch yr URL yn yr adroddiad"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Cyflwyno problem newydd ar GitHub gan gynnwys yr wybodaeth uchod"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Dim HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Gweld logiau gwallau a chyflwyno adroddiad nam"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang y peiriant hwn"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr ""

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Canolrif"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Wedi methu profion gwirio: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Gwallau:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Cyffredinol"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Categorïau rhagosodedig"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Rhyngwyneb defnyddiwr"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Preifatrwydd"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Peiriannau"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Peiriannau a ddefnyddir ar hyn o bryd"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Ymholiadau arbennig"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Briwsion"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Nifer o ganlyniadau"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Gwybodaeth"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Yn ôl i'r brig"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Tudalen flaenorol"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Tudalen nesaf"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Dangos y dudalen flaen"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Chwilio am..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "clirio"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "chwilio"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Does dim data ar gael ar hyn o bryd."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Enw'r peiriant"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Sgôr"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Canlyniadau"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Amser ymateb"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Dibynadwyedd"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Cyfanswm"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Prosesu"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Rhybuddion"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Gwallau ac eithriadau"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Eithriad"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Neges"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Canran"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Paramedr"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Enw ffeil"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Ffwythiant"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Cod"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Gwiriwr"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Wedi methu prawf"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Sylwadau"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Enghreifftiau"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr ""

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Atebion"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Islwytho'r canlyniadau"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Rhowch gynnig ar chwilio am:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr ""

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL y chwiliad"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Wedi'i gopïo"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copïo"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Awgrymiadau"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Iaith chwilio"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Iaith ragosodedig"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Canfod yn awtomatig"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Chwilio'n ddiogel"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Llym"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Cymedrol"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Dim"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Cyfnod amser"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Unrhyw bryd"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Y diwrnod diwethaf"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Yr wythnos diwethaf"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Y mis diwethaf"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Y flwyddyn ddiwethaf"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Gwybodaeth!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "ar hyn o bryd, does dim briwsion wedi'u diffinio."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Mae'n ddrwg gennym!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Chafodd dim canlyniadau eu canfod. Gallwch geisio:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Does dim rhagor o ganlyniadau. Gallwch geisio:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Ail-lwytho'r dudalen."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Chwilio am rywbeth arall neu ddewis categori arall (uchod)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Newid y peiriant chwilio a ddefnyddir yn eich dewisiadau:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Defnyddio gweinydd arall:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Chwilio am rywbeth arall neu ddewis categori arall."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Mynd yn ôl i'r dudalen flaenorol gan ddefnyddio'r botwm tudalen flaenorol."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Caniatáu"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Enw"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Disgrifiad"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Dyma'r rhestr o fodylau SearXNG sy'n ateb ar unwaith."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Dyma'r rhestr o ategion."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Cwblhau'n awtomatig"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Darganfod pethau wrth i chi deipio"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Alinio i'r canol"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Dangos canlyniadau yng nghanol y dudalen (cynllun Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Dyma'r rhestr o friwsion a'u gwerthoedd y mae SearXNG yn eu storio ar "
"eich cyfrifiadur."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Gyda'r rhestr hon, gallwch asesu tryloywder SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Enw'r briwsionyn"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Gwerth"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL chwilio sy'n cynnwys eich gosodiadau presennol"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Noder: gall rhoi gosodiadau addasedig o fewn URL eich chwiliadau wanhau "
"eich preifatrwydd drwy ddatgelu data i wefannau yr ymwelir â nhw o'r "
"dudalen ganlyniadau."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL i adfer eich dewisiadau mewn porwr arall"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copïo'r hash dewisiadau"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Rhowch hash dewisiadau yma (heb URL) i'w adfer"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash dewisiadau"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Atebydd DOI Open Access"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Dewis y gwasanaeth i'r disodlydd DOI ddefnyddio"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Galluogi pob un"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Analluogi pob un"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Cefnogir yr iaith a ddewiswyd"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Pwysau"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Amser hiraf"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Cedwir y gosodiadau hyn yn eich briwsion. Golyga hyn nad oes rhaid i "
"ninnau gadw'r data hyn amdanoch."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Er hwylustod i chi yn unig y defnyddir y briwsion hyn. Nid ydym eu "
"defnyddio i'ch tracio."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Cadw"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Ailosod y rhagosodiadau"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Yn ôl"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Fel Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Dirprwy delweddau"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Dirprwyo canlyniadau delweddau drwy SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Sgrolio diddiwedd"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Llwytho'r dudalen nesaf yn awtomatig wrth gyrraedd gwaelod y dudalen "
"bresennol"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Ym mha iaith ydych chi'n ffafrio chwilio?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Dewiswch canfod yn awtomatig i adael i SearXNG ganfod iaith eich chwiliad."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Dull HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Newid sut caiff ffurflenni eu hanfon"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Dangos y chwiliad yn nheitl y dudalen"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Os galluogir, caiff eich chwiliad ei gynnwys yn nheitl y dudalen "
"ganlyniadau. Gall eich porwr recordio'r teitl hwn."

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Canlyniadau mewn tabiau newydd"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Agor dolenni canlyniadau mewn tabiau newydd yn y porwr"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Hidlo cynnwys"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Chwilio ar unwaith wrth ddewis categori"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Chwilio ar unwaith os dewisir categori. Analluogwch hyn i ddewis sawl "
"categori"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Thema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Newid cynllun SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Arddull y thema"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Dewiswch yr opsiwn awtomatig i ddilyn gosodiadau eich porwr"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tocynnau peiriannau"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tocynnau mynediad ar gyfer peiriannau preifat"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Iaith y rhyngwyneb"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Newid iaith y rhyngwyneb"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "storfa"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "dangos cyfryngau"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "cuddio cyfryngau"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ni wnaeth y wefan ddarparu disgrifiad."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Maint ffeil"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dyddiad"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Math"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Cydraniad"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Fformat"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Peiriant"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Gweld y ffynhonnell"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "cyfeiriad"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "dangos map"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "cuddio'r map"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Fersiwn"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Cynhaliwr"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Diweddarwyd am"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tagiau"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Poblogrwydd"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Trwydded"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Prosiect"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Hafan y prosiect"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Dyddiad cyhoeddi"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Cylchgrawn"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Golygydd"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Cyhoeddwr"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "dolen magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "ffeil torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Hadwr"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Lawrlwythwyr"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Nifer o ffeiliau"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "dangos fideo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "cuddio'r fideo"

#~ msgid "Engine time (sec)"
#~ msgstr ""

#~ msgid "Page loads (sec)"
#~ msgstr ""

#~ msgid "Errors"
#~ msgstr "Gwallau"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr ""

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Mae canlyniadau fel arfer yn cael "
#~ "eu hagor yn yr un ffenestr. Mae'r"
#~ " ategolyn hwn yn newid hyn fel "
#~ "bod dolenni yn cael eu hagor mewn"
#~ " tabiau/ffenestri newydd. (Angen JavaScript)"

#~ msgid "Color"
#~ msgstr "Lliw"

#~ msgid "Blue (default)"
#~ msgstr "Glas (arferol)"

#~ msgid "Violet"
#~ msgstr "Fioled"

#~ msgid "Green"
#~ msgstr "Gwyrdd"

#~ msgid "Cyan"
#~ msgstr "Gwyrddlas"

#~ msgid "Orange"
#~ msgstr "Oren"

#~ msgid "Red"
#~ msgstr "Coch"

#~ msgid "Category"
#~ msgstr "Categori"

#~ msgid "Block"
#~ msgstr "Rhwystro"

#~ msgid "original context"
#~ msgstr "cyd-destun gwreiddiol"

#~ msgid "Plugins"
#~ msgstr "Ategolion"

#~ msgid "Answerers"
#~ msgstr "Atebwyr"

#~ msgid "Avg. time"
#~ msgstr ""

#~ msgid "show details"
#~ msgstr "dangos manylion"

#~ msgid "hide details"
#~ msgstr "cuddio manylion"

#~ msgid "Load more..."
#~ msgstr "Dysgu mwy..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Newid cynllun searX"

#~ msgid "Proxying image results through searx"
#~ msgstr ""

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr ""

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Dyma restr y cwcis, a'u gwerthoedd, "
#~ "mae searX yn eu cadw ar eich "
#~ "dyfais."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr ""

#~ msgid "It look like you are using searx first time."
#~ msgstr "Mae'n ymddangos eich bod yn defnyddio searx am y tro cyntaf."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""

#~ msgid "Themes"
#~ msgstr "Themâu"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Dull"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Gosodiadau uwch"

#~ msgid "Close"
#~ msgstr "Cau"

#~ msgid "Language"
#~ msgstr ""

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "cefnogir"

#~ msgid "not supported"
#~ msgstr "ni chefnogir"

#~ msgid "about"
#~ msgstr "ynghylch"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Dewis arddull ar gyfer y thema hon"

#~ msgid "Style"
#~ msgstr "Arddull"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "Iaith a ddewiswyd"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "cadw"

#~ msgid "back"
#~ msgstr "nôl"

#~ msgid "Links"
#~ msgstr "Dolenni"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Canlyniadau chwilio"

#~ msgid "next page"
#~ msgstr "tudalen nesaf"

#~ msgid "previous page"
#~ msgstr "tudalen ddiwethaf"

#~ msgid "Start search"
#~ msgstr "Dechrau chwilio"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "ystadegau"

#~ msgid "Heads up!"
#~ msgstr ""

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Da iawn!"

#~ msgid "Settings saved successfully."
#~ msgstr "Cadwyd y gosodiadau yn iawn."

#~ msgid "Oh snap!"
#~ msgstr ""

#~ msgid "Something went wrong."
#~ msgstr "Aeth rhywbeth o'i le."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Cael y ddelwedd"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "dewisiadau"

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr ""

#~ msgid "No abstract is available for this publication."
#~ msgstr ""

#~ msgid "Self Informations"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "eraill"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "Llwybr Byr"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Ni all y peiriannau cael canlyniadau."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Ymlaen"

#~ msgid "Off"
#~ msgstr "I ffwrdd"

#~ msgid "Enabled"
#~ msgstr "Galluogwyd"

#~ msgid "Disabled"
#~ msgstr "Analluogwyd"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""

#~ msgid "Vim-like hotkeys"
#~ msgstr ""

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Ni ddaethpwyd o hyd i unrhyw "
#~ "ganlyniadau. Defnyddiwch derm(au) chwilio "
#~ "gwahanol neu ehangu'r chwilio i ragor"
#~ " o gategorïau."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""

#~ msgid "Bytes"
#~ msgstr "Beitiau"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Disodli enwau gwesteiwyr"

#~ msgid "Error!"
#~ msgstr "Gwall!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Ni all y peiriannau gyrchu canlyniadau"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Dechrau cyflwyno problem newydd ar GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Cynhyrchydd hapwerthoedd"

#~ msgid "Statistics functions"
#~ msgstr "Swyddogaethau ystadegau"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Compute {functions} o'r dadleuon"

#~ msgid "Get directions"
#~ msgstr "Cael cyfarwyddiadau"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Dangos eich cyfeiriad IP os defnyddir"
#~ " yr ymholiad \"ip\" a'ch asiant "
#~ "defnyddiwr os ydy'ch ymholiad yn cynnwys"
#~ " \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Wedi methu islwytho'r rhestr o nodau "
#~ "ymadael Tor o: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Rydych chi'n defnyddio Tor ac mae'n "
#~ "ymddangos bod gennych y cyfeiriad IP "
#~ "allanol canlynol: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Dydych chi ddim yn defnyddio Tor "
#~ "ac mae gennych chi'r cyfeiriad IP "
#~ "allanol canlynol: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Allweddeiriau"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Gallwch ddefnyddio'r URL dewisiadau i "
#~ "gysoni eich dewisiadau ar draws "
#~ "dyfeisiau."

#~ msgid "proxied"
#~ msgstr "wedi'i ddirprwyo"

