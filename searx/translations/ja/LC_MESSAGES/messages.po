# Japanese translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016-2018
# <PERSON>, 2014-2015
# <AUTHOR> <EMAIL>, 2014,2016
# KAWASAKI ICHIRO, 2020
# <PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# pointhi, 2014
# <PERSON>, 2015-2016
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-11 15:12+0000\n"
"Last-Translator: ayame30 <<EMAIL>>\n"
"Language: ja\n"
"Language-Team: Japanese "
"<https://translate.codeberg.org/projects/searxng/searxng/ja/>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "未グループ"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "その他"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ファイル"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "一般"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "音楽"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "ソーシャルメディア"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "画像"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "動画"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "ラジオ"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "テレビ"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "情報技術"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "ニュース"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "地図"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "Tor"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "科学"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "アプリ"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "辞書"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "歌詞"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "パッケージ"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "Q&A"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "リポジトリ"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "ソフトウェアWiki"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "ウェブ"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "科学的な出版物"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "自動"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "ライト"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "ダーク"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "ブラック"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "稼働時間"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "関連情報"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "平均気温."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "曇り"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "天気"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "現在の天気"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "夕方"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "体感"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "湿度"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "最高気温."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "最低気温."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "朝"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "夜間"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "昼"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "気圧"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "日の出"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "日の入り"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "気温"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV指数"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "視界"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "風速"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "サブスクライバー"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "投稿"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "アクティブユーザー"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "コメント"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "ユーザー"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "コミュニティ"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "ポイント"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "タイトル"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "作"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "オープン"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "クローズ"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "回答"

#: searx/webapp.py:292
msgid "No item found"
msgstr "アイテムが見つかりません"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "ソース"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "次のページの読み込み中にエラーが発生しました"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "設定が無効です、設定を変更してください"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "無効な設定です"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "検索エラー"

#: searx/webutils.py:35
msgid "timeout"
msgstr "タイムアウト"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "解析エラー"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP プロトコルエラー"

#: searx/webutils.py:38
msgid "network error"
msgstr "ネットワークエラー"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL エラー: 証明書の検証に失敗しました"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "予期しないクラッシュ"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP エラー"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP 接続エラー"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "プロキシエラー"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "リクエストが多すぎます"

#: searx/webutils.py:58
msgid "access denied"
msgstr "アクセスが拒否されました"

#: searx/webutils.py:59
msgid "server API error"
msgstr "サーバー API エラー"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "一時停止"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} 分前"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} 時間と{minutes} 分前"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "異なるランダムな値を生成する"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "引数の {func} を計算する"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "地図にルートを表示.."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (廃止)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "このエントリは、置き換えられました:"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "チャンネル"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "ビットレート"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "票数"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "クリック"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "言語"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{firstCitationVelocityYear} 年から "
"{lastCitationVelocityYear}年まで{numCitations} が引用文献として"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr "この画像URLは読み取ることができません。サポートされていないフォーマットだと考えられます。TinEyeはJPEG、PNG、GIF、BMP、TIFF、WebPの画像のみサポートしています。"

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr "画像が単純すぎます。TinEyeが正しく照合を行うにはある程度詳細な視覚情報が必要です。"

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "この画像はダウンロードはできません。"

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "書籍評価点数"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "ファイル品質"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmiaのブラックリスト"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Ahmiaのブラックリストに表示されるオニオン結果を除外します。"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "基本的な計算機"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "検索バーで数式を計算"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "ハッシュプラグイン"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "文字列を異なるハッシュダイジェストに変換。"

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "ハッシュダイジェスト"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "ホスト名プラグイン"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "検索結果からこのホスト名を基に削除もしくは優先的に書き換えを行う"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "オープンアクセス DOI の書き換え"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "可能ならば オープンアクセス版の出版物へリダイレクトし、有料出版物を回避する"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "自分の情報"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr "クエリが\"ip\"の場合はあなたのIPを表示し、クエリが\"user-agent\"の場合はあなたのユーザーエージェントを表示します。"

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "あなたのIPは: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "あなたのユーザーエージェントは: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor 確認プラグイン"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr "このプラグインではcheck.torprogject.orgのようにTor 出口ノードのIPアドレスをSearXNGからチェックする。"

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Tor出口ノードのリストをダウンロードできませんでした"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "あなたはTorを使用しており、外部IPアドレスが確認されたようです"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "あなたはTorを使用しておらず、外部IPアドレスが確認されました"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "トラッカー URL リムーバー"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "返された URL からトラッカー引数を消去する"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "単位変換プラグイン"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "単位を変換"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "ページが見つかりません"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "%(search_page)s へ行く。"

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "検索ページ"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "寄付"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "設定"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Powered by"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "プライバシーを尊重する、オープンメタ検索エンジン"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "ソースコード"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "課題報告"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "検索エンジンの状態"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "パブリック インスタンス"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "プライバシーポリシー"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "インスタンスメンテナと連絡を取る"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "虫めがねをクリックして検索します"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "長さ"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "閲覧数"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "作者"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "キャッシュ"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Start submiting a new issue on GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Githubで、すでにこの件が出ていないか確認をしてください"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "発生している問題がすでに提出済みのバグ出ないことを確認してください"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "パブリックインスタンスなら、このURLにバグの報告をしてください"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Githubで情報を含めて新たな問題を提起"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "No HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "エラーログを表示し、バグレポートを送信します"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "当検索エンジンの!bang"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "そのカテゴリの!bang"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "中央値"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "失敗したチェッカーテスト: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "エラー:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "一般"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "デフォルトのカテゴリ"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "ユーザーインターフェース"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "プライバシー"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "検索エンジン"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "現在使用中の検索エンジン"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "特殊クエリー"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "クッキー"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "通知の数"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "インフォ"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "トップに戻る"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "前のページ"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "次のページ"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "フロントページを表示する"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "検索する..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "消す"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "検索"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "現在データがありません。 "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "検索エンジン名"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "スコア"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "結果カウント"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "応答時間"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "信頼性"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "合計"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "処理"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "注意"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "エラーと例外"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "例外"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "メッセージ"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "確率"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "パラメータ"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "ファイル名"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "ファンクション"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "コード"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "チェッカー"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "テストに失敗しました"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "コメント"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "例"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "定義"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "類義語"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "回答"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "ダウンロードするファイル形式"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "検索のオススメ:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "検索エンジンからのメッセージ"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "秒"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "この検索結果の URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "コピーしました"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "コピー"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "提案"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "検索の言語"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "デフォルトの言語"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "自動検出"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "セーフサーチ"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "厳重"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "標準"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "オフ"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "時間範囲"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "期間指定なし"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "24 時間以内"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "1 週間以内"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "1 か月以内"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "1 年以内"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "お知らせ!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "現在、クッキーは定義されていません。"

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "すみません!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "結果見つかりませんでした。再度行うなら :"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "これ以上の検索結果はありません。試すことができるのは :"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "ページを更新します。"

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "違うクエリか違う(上記の)カテゴリを選んで検索。"

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "検索エンジンに使う設定を変更する:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "別のインスタンスに切り替える:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "違うのクエリか違うカテゴリで検索を選択できます。"

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "前ページボタンを使うと、前のページに戻ることができます。"

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "許可する"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "キーワード(クエリの最初の単語)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "名前"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "説明"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "これは SearXNG の即席回答モジュールのリストです。"

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "これはプラグインのリストです。"

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "自動補完"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "自動補完に使う検索エンジン"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "中央揃え"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "中央揃えでページに結果表示（Oscar レイアウト)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "これはクッキーのリストで、これらの値はあなたのコンピュータに保存されています。"

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "このリストによって、あなたは SearXNG の透明性を評価できます。"

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "クッキー名"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "値"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "現在保存されている設定の検索 URL"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr "注意: 検索 URL にカスタム設定を指定すると、クリックした結果サイトにデータが漏洩し、プライバシーが低下する恐れがあります。"

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "このURLで違うブラウザに設定を復活"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr "設定内容が保存されたURLです。このURLを使用すると、別のデバイスで設定を復元できます。"

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "設定のハッシュをコピーする"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "設定を復元するために（URLなしでの）ハッシュをコピーして挿入する"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "設定ハッシュ"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "デジタルオブジェクト識別子(DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "オープンアクセス DOI リゾルバー"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "DOI書き換えにて使用するサービスを選択"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr "インタフェースやタブの中にはないが、!bangを使うことでこれらのエンジンで検索できる。"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "すべて有効"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "すべて無効"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "選択された言語のサポート"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "比重"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "最大時間"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "ファビコンリゾルバー"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "検索結果でfaviconに合いそうなものを表示する"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr "これらの設定はあなたのクッキーに保存されますが、これはサーバーがあなたの情報の保存するわけではありません。"

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr "クッキーはあなたが便利に使えるようにするために使うのであって、サーバーはあなたを追跡するためにクッキーを使うことはありません。"

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "保存"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "デフォルト設定に戻す"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "戻る"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "ショートカットキー"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim風"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr "(Javascriptが必要)ショートカットキーで検索の結果を得ることができます。「h」キーを押して、主な使い方や検索結果の方法を知ることができます。"

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "画像プロキシ"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "画像の結果をSearXNG経由でプロキシする"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "無限スクロール"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "現在のページの下端でスクロールすると自動的に次のページを読み込む"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "どの言語で検索しますか？"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "自動検出を選択すると、あなたのクエリの言語をSearXNGに検出させるようになります。"

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTPメソッド"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "フォームの送信の仕方を変更する"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "ページのタイトルでクエリを実行する"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr "有効にすると、検索結果ページのタイトルにクエリが含まれます。お使いのブラウザはこのタイトルを記録できます"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "新しいタブに結果を表示"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "検索結果のリンクを新しいタブで開く"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "コンテンツをフィルタリングする"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "カテゴリ選択したら検索を実行"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr "カテゴリが選択されている場合はすぐに検索を行います。複数のカテゴリが選択されている場合は無効です"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "テーマ"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "SearXNGレイアウトの変更"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "テーマスタイル"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "「自動」を選択すると、ブラウザの設定に従います"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "エンジントークン"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "ブライベートエンジンのアクセストークン"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "インターフェースの言語"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "表示する言語を変更"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL 書式"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "相当"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "一杯"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "ホスト"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "検索結果のURL書式を変更"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "リポジトリ"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "メディアを表示する"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "メディアを隠す"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "このサイトは説明を提供しませんでした。"

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "ファイルサイズ"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "日"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "分類"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "解像度"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "フォーマット"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "エンジン"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "ソースを閲覧する"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "アドレス"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "地図を表示する"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "地図を隠す"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "バージョン"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "メインテイナー"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "更新される"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "タグ"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "人気度"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "ライセンス"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "プロジェクト"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "プロジェクトホームページ"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "公開日"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "刊行物"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "編集者"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "発行者"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "マグネットリンク"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "トレントファイル"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "シーダー"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "リーチャー"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "ファイル数"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "動画を表示する"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "動画を隠す"

#~ msgid "Engine time (sec)"
#~ msgstr "検索時間 (秒)"

#~ msgid "Page loads (sec)"
#~ msgstr "ページ読み込み時間 (秒)"

#~ msgid "Errors"
#~ msgstr "エラー"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "可能ならば HTTP リンクを HTTPS リンクに書き換える"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "デフォルトでは結果は同じウィンドウで開きます。このプラグインはデフォルトの動作を書き換えて新しいタブ/ウィンドウで開くようにします。(JavaScript"
#~ " が必要です)"

#~ msgid "Color"
#~ msgstr "色"

#~ msgid "Blue (default)"
#~ msgstr "青 (初期設定)"

#~ msgid "Violet"
#~ msgstr "紫"

#~ msgid "Green"
#~ msgstr "緑"

#~ msgid "Cyan"
#~ msgstr "シアン"

#~ msgid "Orange"
#~ msgstr "オレンジ"

#~ msgid "Red"
#~ msgstr "赤"

#~ msgid "Category"
#~ msgstr "カテゴリー"

#~ msgid "Block"
#~ msgstr "禁止する"

#~ msgid "original context"
#~ msgstr "元の文脈"

#~ msgid "Plugins"
#~ msgstr "プラグイン"

#~ msgid "Answerers"
#~ msgstr "回答者"

#~ msgid "Avg. time"
#~ msgstr "平均時間"

#~ msgid "show details"
#~ msgstr "詳細を表示する"

#~ msgid "hide details"
#~ msgstr "詳細を隠す"

#~ msgid "Load more..."
#~ msgstr "もっと見る…"

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Searx のレイアウトの変更"

#~ msgid "Proxying image results through searx"
#~ msgstr "画像検索結果を searx でプロキシする"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "これは searx の即席回答モジュールのリストです。"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "これはクッキーのリストで、これらの値はあなたのコンピュータに保存されています。"

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "このリストによって、あなたは searx の透明性を評価できます。"

#~ msgid "It look like you are using searx first time."
#~ msgstr "Searxを使うのは初めてようですね。"

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "後でやり直すか、別の searx インスタンスを探して下さい。"

#~ msgid "Themes"
#~ msgstr "テーマ"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "方法"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "詳細設定"

#~ msgid "Close"
#~ msgstr "閉じる"

#~ msgid "Language"
#~ msgstr "言語"

#~ msgid "broken"
#~ msgstr "起動不可"

#~ msgid "supported"
#~ msgstr "サポート"

#~ msgid "not supported"
#~ msgstr "未サポート"

#~ msgid "about"
#~ msgstr "このサイトについて"

#~ msgid "Avg."
#~ msgstr "平均"

#~ msgid "User Interface"
#~ msgstr "ユーザーインタフェース"

#~ msgid "Choose style for this theme"
#~ msgstr "このテーマのスタイルを選択"

#~ msgid "Style"
#~ msgstr "スタイル"

#~ msgid "Show advanced settings"
#~ msgstr "詳細設定を表示"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "デフォルトでホームページで詳細設定を表示する"

#~ msgid "Allow all"
#~ msgstr "すべて許可"

#~ msgid "Disable all"
#~ msgstr "すべて無効"

#~ msgid "Selected language"
#~ msgstr "選択された言語"

#~ msgid "Query"
#~ msgstr "クエリ"

#~ msgid "save"
#~ msgstr "保存"

#~ msgid "back"
#~ msgstr "戻る"

#~ msgid "Links"
#~ msgstr "リンク"

#~ msgid "RSS subscription"
#~ msgstr "RSS登録"

#~ msgid "Search results"
#~ msgstr "検索結果"

#~ msgid "next page"
#~ msgstr "次のページ"

#~ msgid "previous page"
#~ msgstr "前のページ"

#~ msgid "Start search"
#~ msgstr "検索を開始"

#~ msgid "Clear search"
#~ msgstr "探索を消す"

#~ msgid "Clear"
#~ msgstr "消す"

#~ msgid "stats"
#~ msgstr "統計"

#~ msgid "Heads up!"
#~ msgstr "気をつけて!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "SearXNG を初めてお使いになるようですね。"

#~ msgid "Well done!"
#~ msgstr "あっぱれ!"

#~ msgid "Settings saved successfully."
#~ msgstr "設定の保存に成功しました。"

#~ msgid "Oh snap!"
#~ msgstr "おっと！"

#~ msgid "Something went wrong."
#~ msgstr "なにか問題が起こっているようです。"

#~ msgid "Date"
#~ msgstr "日付"

#~ msgid "Type"
#~ msgstr "タイプ"

#~ msgid "Get image"
#~ msgstr "画像を取得する"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "設定"

#~ msgid "Scores per result"
#~ msgstr "検索結果当たりスコア"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "プライバシー保護を重視した、ハッカブルなメタ検索エンジン"

#~ msgid "No abstract is available for this publication."
#~ msgstr "この出版物には要約がありません。"

#~ msgid "Self Informations"
#~ msgstr "自分の情報"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "フォームの送信方法を変更します。<a "
#~ "href=\"https://ja.wikipedia.org/wiki/Hypertext_Transfer_Protocol#リクエストメソッド\""
#~ " rel=\"external\">リクエストメソッドってなに？</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "このプラグインはsearxngからTOR exit node "
#~ "にアドレスを要求したとき、check.torproject.orgサイトのように、ユーザーに通知します。"

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "TOR exit node のリスト(https://check.torproject.org"
#~ "/exit-addresses)に到達できません。"

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "TORを利用しています。あなたのIPアドレスはここから来ていると思われます : {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "TORを利用していません。あなたのIPアドレスはここから来ていると思われます: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "検索言語自動検出"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "検索言語の自動検出と切り替えを実施。"

#~ msgid "others"
#~ msgstr "その他"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr "このタブは検索結果には表示されませんが、ここにリストされているエンジンをbangで検索できます。"

#~ msgid "Shortcut"
#~ msgstr "ショートカット"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr "インタフェースやタブの中にはないが、!bangを使うことで検索エンジンとして利用できる。"

#~ msgid "Engines cannot retrieve results."
#~ msgstr "エンジンは結果を取得できません。"

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "後でやり直すか、別の SearXNG インスタンスを試してみて下さい。"

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr "利用可能な場合(プラグインが必要)、オープンアクセス版の出版物にリダイレクトする"

#~ msgid "Bang"
#~ msgstr "Bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "フォームの送信方法を変更します。<a "
#~ "href=\"https://ja.wikipedia.org/wiki/Hypertext_Transfer_Protocol#リクエストメソッド\""
#~ " rel=\"external\">リクエストメソッドってなに？</a>"

#~ msgid "On"
#~ msgstr "有効"

#~ msgid "Off"
#~ msgstr "無効"

#~ msgid "Enabled"
#~ msgstr "有効"

#~ msgid "Disabled"
#~ msgstr "無効"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr "カテゴリが選択されたときに検索を実行します。複数のカテゴリを選択する場合は無効にします。(JavaScript が必要です)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim 風のホットキー"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "検索結果をVim 風のホットキーで操作します(JavaScript が必要)。メインページまたは検索結果ページで"
#~ " \"h\" キーを押してヘルプを表示します。"

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr "検索結果はありませんでした。別のカテゴリ、または他のクエリで検索してください。"

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "結果のホスト名を書き換えるか、ホスト名に基づいて結果を削除します"

#~ msgid "Bytes"
#~ msgstr "バイト"

#~ msgid "kiB"
#~ msgstr "キロバイト"

#~ msgid "MiB"
#~ msgstr "メガバイト"

#~ msgid "GiB"
#~ msgstr "ギガバイト"

#~ msgid "TiB"
#~ msgstr "テラバイト"

#~ msgid "Hostname replace"
#~ msgstr "ホストネーム入れ替え"

#~ msgid "Error!"
#~ msgstr "エラー!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "エンジンは結果を取得できません"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Githubへ新しい課題の提出をする"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "ランダムな値を生成"

#~ msgid "Statistics functions"
#~ msgstr "統計機能"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "変数の {functions} を計算する"

#~ msgid "Get directions"
#~ msgstr "経路を取得する"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "クエリが \"ip\" の場合にあなたのIPを、クエリに \"user agent\""
#~ " が含まれる場合にあなたのユーザーエージェントを表示します。"

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr "「https://check.torproject.org/exit-addresses」からTor 出口ノードの一覧をダウンロードできません"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "あなたの利用しているTorの外部IPアドレスは次のようになっている : {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "あなたはTorを利用しておらず外部IPアドレスは次のようになっている : {ip_address}"

#~ msgid "Keywords"
#~ msgstr "キーワード"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr "初期設定URLを使うことで、特別な設定をデバイスをまたいで同期できる。"

#~ msgid "proxied"
#~ msgstr "プロキシ"

