# Lithuanian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# Moo, 2019-2020
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-20 12:41+0000\n"
"Last-Translator: return42 <<EMAIL>>\n"
"Language: lt\n"
"Language-Team: Lithuanian "
"<https://translate.codeberg.org/projects/searxng/searxng/lt/>\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100"
" < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < "
"11) ? 1 : n % 1 != 0 ? 2: 3);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "be tolesnio pogrupio"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "kitas"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "failai"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "bendra"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muzika"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "socialiniai tinklai"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "nuotraukos"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "vaizdo įrašai"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radijas"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "televizorius"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "naujienos"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "žemėlapis"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "TOR puslapiai"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "mokslas"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "programos"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "žodynai"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "dainų žodžiai"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketai"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "Dažnai užduodami klausymai"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "saugyklos"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "programų žinynai"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "internetas"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "mokslinės publikacijos"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatinis"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "šviesi"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tamsi"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "juoda"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Veikimo laikas"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Apie"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Vidutinė temperatura"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Debesio serveris"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Sąlyga"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Esamos sąlygos"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Vakaras"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Jaučiasi kaip"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Dregmė"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Aukščiausia temperatura"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Mažiausia temperatura"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Rytas"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Naktis"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Vidurdienis"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Slėgis"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Saulėtekis"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Saulėlydis"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV indeksas"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Matomumas"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vėjas"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Prenumeratoriai"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "Įrašai"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "Aktyvus naudotojai"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "Komentarai"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "Naudotojai"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "Bendruomene"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "Taškai"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "Pavadinimas"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "Autorius"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Atidaryta"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Uždaryta"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "atsakyta"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Elementų nerasta"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Šaltinis"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Klaida keliant kitą puslapį"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Neteisingi nustatymai, pakeiskite savo nuostatas"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Neteisingi nustatymai"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "paieškos klaida"

#: searx/webutils.py:35
msgid "timeout"
msgstr "laikas baigėsi"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "parsavymo klaida"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP protokolo klaida"

#: searx/webutils.py:38
msgid "network error"
msgstr "tinklo klaida"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL klaida: liudijimo tikrinimas patyrė nesėkmę"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "netikėta klaida"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP klaida"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP ryšio klaida"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "persiuntimų serverio klaida"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "per daug užklausų"

#: searx/webutils.py:58
msgid "access denied"
msgstr "prieiga uždrausta"

#: searx/webutils.py:59
msgid "server API error"
msgstr "serverio API klaida"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Sustabdytas"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "prieš {minutes} min"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "prieš {hours} val., {minutes} min"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generuoja įvairias atsitiktinius skaičius"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Apskaičiuoti {func} iš argumentų"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Rodyti maršrutą žemėlapyje"

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (PASENĘS)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Šį įrašą pakeitė"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanalas"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "pralaidumas"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "balsai"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "paspaudimai"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Kalba"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citatos iš metų{firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Nepavyko perskaityti šio vaizdo URL. Taip gali būti dėl nepalaikomo failo"
" formato. TinEye palaiko tik JPEG, PNG, GIF, BMP, TIFF arba WebP vaizdus."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Vaizdas per paprastas, kad būtų galima rasti atitikmenų. Norint sėkmingai"
" nustatyti atitikmenis, „TinEye“ reikalingas pagrindinis vizualinių "
"detalių lygis."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Nepavyko atsisiųsti vaizdo."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Knygos įvertinimas"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Failo kokybė"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia juodasis sąrašas"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Išfiltruoti onion rezultatus esančius Ahmia juodajame sąraše."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Bazinis skaičiuotuvas"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Apskaičiuoti matematines lygtis paieškos laukelyje"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Maišos įskiepis"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Konvertuoja eilutes į skirtingas maišos santraukas."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "maišos santrauka"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Serverių pavadinimų įskiepis"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Svetainės vardų perrašymas, rezultatų pašalinimas ir prioriteto "
"suteikimas pagal svetainių vardus"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Atvirosios prieigos DOI perrašymas"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Vengti apmokamas sienas, peradresuojant į atviros prieigos publikacijų "
"versijas"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Savęs informacija"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Rodo tavo IP jei užklausa yra „ip“ ir tavo naudotojo agentą jei užklausa "
"yra „user-agent“."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Jūsų IP adresas: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Tavo naudotojo agentas (user-agent) yra: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "„Tor check“ papildinys"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Šis papildinys patikrina, ar užklausos adresas yra Tor išėjimo mazgas, ir"
" informuoja vartotoją, jei taip yra; kaip check.torproject.org, bet iš "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nepavyko atsisiųsti Tor išėjimo mazgų iš"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Jūs naudojate Tor ir atrodo, kad turite išorinį IP adresą"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Jūs nenaudojate Tor, tačiau atrodo, kad turite išorinį IP adresą"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Seklių URL šalintojas"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Šalinti seklių argumentus iš grąžinamų URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Matavimo vienetų konvertavimo papildinys"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konvertuoti tarp matavimo vienetų"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Puslapis nerastas"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Pereiti į %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "paieškos puslapį"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Paaukoti"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Nuostatos"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Veikia su"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "privatumą gerbiantis atviras metapaieškos variklis"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Šaltinio kodas"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Klaidų sekiklis"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statistika statistika"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Viešos instancijos"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Privatumo politika"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Susisiekite su instancijos prižiūrėtoju"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Norėdami atlikti paiešką, spustelėkite ant didinamojo stiklo"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Trukmė"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Peržiūros"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autorius"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "patalpinta"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Pateikite naują klaidą Github'e"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Prasome paziurėti esamas klaidas apie šią sistemą Github'e"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Aš patvirtinu, kad nera jokių esamų"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Jei tai yra vieša, prašome nurodyti URL bugų pranešime"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Pateikite nauja klaidą Github, įvedant informaciją viršuje"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Nėra HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Peržiūrėkite klaidų žurnalus ir pateikite klaidų ataskaitą"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang šiam varikliui"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang šiom kategorijom"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediana"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Nepavykęs tikrintojo testas (-ai): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Klaidos:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Bendra"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Numatytosios kategorijos"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Naudotojo sąsaja"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privatumas"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Sistemos"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Šiuo metu naudojamos paieškos sistemos"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Specialios Užklausos"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Slapukai"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Rezultatų skaičius"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informacija"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Atgal į viršų"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Praitas puslapis"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Kitas puslapis"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Rodyti pagrindinį puslapį"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Ko ieškoti..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "išvalyti"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "ieškoti"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Šiuo metu nėra jokių prieinamų duomenų. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Sistemos pavadinimas"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Įvertinimas"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Rezultatų skaičius"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Atsakymo greitis"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Patikimumas"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Visas"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Apdorojama"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Ispėjimai"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Klaidos ir išimtys"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Išimtis"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Žinutė"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Procentinė dalis"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametras"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Failo pavadinimas"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkcija"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kodas"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Tikrintojas"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Nepavykęs testas"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komentaras(-ai)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Pavyzdžiai"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Apibrėžimai"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "sinonimai"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Atsakymai"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Atsisiųsti rezultatus"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Bandykite ieškoti:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Pranešimai iš paieškos sistemų"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekundės"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Paieškos URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Nukopijuota"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopijuoti"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Pasiūlymai"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Paieškos kalba"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Numatytoji kalba"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Automatiškai aptikti"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Saugi paieška"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Griežta"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Nuosaiki"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Nėra"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Laiko diapazonas"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Bet kada"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Praeitą dieną"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Praeitą savaitę"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Praeitą mėnesį"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Praeitais metais"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informacija!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "šiuo metu nėra jokių apibrėžtų slapukų."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Atleiskite!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Nieko nebuvo rasta. Galite bandyti:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Daugiau rezultatų nėra. Galite pabandyti:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Atnaujinti puslapį"

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Ieškokite kitos užklausos arba pasirinkite kitą kategoriją (aukščiau)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Pakeiskite nuostatose naudojamą paieškos variklį:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Pakeisti instanciją:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Ieškoti kitos užklausos arba pasirinkti kitą kategoriją."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Grišti į praeita puslapi naudojant praeito puslapio mygutką."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Leisti"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Raktažodžiai (pirmasis užklausos žodis)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Pavadinimas"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Aprašas"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Šis sąrašas yra SearXNG"

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Čia yra papildinių sąrašas."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automatinis užbaigimas"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Rasti medžiagą berašant"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centro Išdėstymas"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Rodo rezultatus puslapio viduryje (Oskaro išdėstymas)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Čia yra slapukų sąrašas ir jų informacija, kurią SearXNG saugo jūsų "
"kompiuterije."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Su šiuo sąrašu, jūs gaunate prieiga prie SearXNG permatomumą."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Slapuko pavadinimas"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Reikšmė"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Šiuo metu įrašytų nuostatų paieškos URL"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Pastaba: paieškos URL adrese nurodant tinkintus nustatymus, gali būti "
"sumažintas jūsų privatumas, atskleidžiant duomenis toms rezultatų "
"svetainėms, ant kurių spustelėjate."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL, kad galėtumėte atstatyti savo nuostatas kitoje naršyklėje"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"URL adresas su jūsų nustatymais. Šis URL adresas gali būti panaudotas "
"atstatyti jūsų nustatymus kitame įrenginyje."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Skaitmeninis objekto identifikatorius (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Atvirosios prieigos DOI sprendimų įtaisas"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr ""

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Aktyvuoti viska"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Išjungti viska"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!pokšt"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Palaiko pasirinktą kalbą"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Svoris"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maksimalus laikas"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Šie nustatymai yra laikomi jūsų slapukuose, tai leidžia mums nesaugoti "
"šių duomenų apie jus."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Šie slapukai yra naudojami išskirtinai jūsų patogumui, mes nenaudojame "
"jų, kad jus sektume."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Išsaugoti"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Atstatyti numatytasias nuostatas"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Atgal"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Karštieji mygtukai"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Paveikslų persiuntimas"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Persiunčiami paveikslai per SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Begalinis slinkimas"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Automatiškai įkelti kitą puslapį, kai nuslenkama į esamo puslapio apačią"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Kokią kalbą pageidaujate paieškai?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Metodas"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr ""

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Užklausa puslapio pavadinime"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Įjungus, prie rezultatų puslapio pavadinimo pridedama jūsų užklausa. Jūsų"
" naršykle gali šį pavadinimą įrašyti"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Rezultatai naujose skirtukuose"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Atverti rezultatų nuorodas naujose naršyklės skirtukuose"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtruoti turinį"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Pasirinkus kategoriją, atlikti paiešką"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Pakeiskite SearXNG išdėstymą"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Temos stilius"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Pasirinkite automatini, kad sektumėte jūsų naršyklės nustatymus"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Sistemos prieigos raktai"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Privačiųjų sistemų prieigos raktai"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Sąsajos kalba"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Keisti išdėstymo kalbą"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "saugyklos"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "rodyti mediją"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "slėpti mediją"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Šis tiklalapis nepridėjo jokio aprašymo."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Failo dydis"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Data"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tipas"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Raiška"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formatas"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Sistema"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Rodyti šaltinį"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresas"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "rodyti žemėlapį"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "slėpti žemėlapį"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versija"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Atnaujinta"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Pupuliarumas"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licenzija"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projektas"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Paskelbimo data"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Žurnalas"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redaktorius"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Leidėjas"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet nuoroda"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent failas"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Skleidėjai"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Siuntėjai"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Failų skaičius"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "rodyti vaizdo įrašą"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "slėpti vaizdo įrašą"

#~ msgid "Engine time (sec)"
#~ msgstr "Sistemos laikas (sek.)"

#~ msgid "Page loads (sec)"
#~ msgstr "Puslapių įkėlimai (sek.)"

#~ msgid "Errors"
#~ msgstr "Klaidos"

#~ msgid "CAPTCHA required"
#~ msgstr "Reikalingas saugos kodas"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Jei įmanoma, perrašyti HTTP nuorodas į HTTPS"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Pagal numatymą, rezultatai yra atveriami "
#~ "tame pačiame lange. Šis įskiepis perrašo"
#~ " numatytąją elgseną taip, kad nuorodos "
#~ "būtų atveriamos naujose kortelėse/languose. "
#~ "(reikalinga JavaScript)"

#~ msgid "Color"
#~ msgstr "Spalva"

#~ msgid "Blue (default)"
#~ msgstr "Mėlyna (numatytoji)"

#~ msgid "Violet"
#~ msgstr "Violetinė"

#~ msgid "Green"
#~ msgstr "Žalia"

#~ msgid "Cyan"
#~ msgstr "Žydra"

#~ msgid "Orange"
#~ msgstr "Oranžinė"

#~ msgid "Red"
#~ msgstr "Raudona"

#~ msgid "Category"
#~ msgstr "Kategorija"

#~ msgid "Block"
#~ msgstr "Blokuoti"

#~ msgid "original context"
#~ msgstr "pradinis kontekstas"

#~ msgid "Plugins"
#~ msgstr "Įskiepiai"

#~ msgid "Answerers"
#~ msgstr "Atsakikliai"

#~ msgid "Avg. time"
#~ msgstr "Vid. laikas"

#~ msgid "show details"
#~ msgstr "rodyti informaciją"

#~ msgid "hide details"
#~ msgstr "slėpti informaciją"

#~ msgid "Load more..."
#~ msgstr "Įkelti daugiau..."

#~ msgid "Loading..."
#~ msgstr "Įkeliama..."

#~ msgid "Change searx layout"
#~ msgstr "Keisti searx išdėstymą"

#~ msgid "Proxying image results through searx"
#~ msgstr "Paveikslų persiuntimas įgaliotuoju serveriu per searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Tai yra searx greitų atsakiklių modulių sąrašas."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Tai yra slapukų ir jų reikšmių, "
#~ "kuriuos searx laiko jūsų kompiuteryje, "
#~ "sąrašas."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Naudodami sąrašą, galite įvertinti searx skaidrumą."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Atrodo, kad pirmą kartą naudojate searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Vėliau bandykite dar kartą arba raskite kitą searx egzempliorių."

#~ msgid "Themes"
#~ msgstr "Apipavidalinimai"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metodas"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Išplėstiniai nustatymai"

#~ msgid "Close"
#~ msgstr "Užverti"

#~ msgid "Language"
#~ msgstr "Kalba"

#~ msgid "broken"
#~ msgstr "nutrūkęs"

#~ msgid "supported"
#~ msgstr "palaikoma"

#~ msgid "not supported"
#~ msgstr "nepalaikoma"

#~ msgid "about"
#~ msgstr "apie"

#~ msgid "Avg."
#~ msgstr "Vid."

#~ msgid "User Interface"
#~ msgstr "Vartotojo sąsaja"

#~ msgid "Choose style for this theme"
#~ msgstr "Pasirinkti šio apipavidalinimo stilių"

#~ msgid "Style"
#~ msgstr "Stilius"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "Leisti visus"

#~ msgid "Disable all"
#~ msgstr "Išjungti visus"

#~ msgid "Selected language"
#~ msgstr "Pasirinkta kalba"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "Įrašyti"

#~ msgid "back"
#~ msgstr "Atgal"

#~ msgid "Links"
#~ msgstr "Nuorodos"

#~ msgid "RSS subscription"
#~ msgstr "RSS prenumerata"

#~ msgid "Search results"
#~ msgstr "Paieškos rezultatai"

#~ msgid "next page"
#~ msgstr "kitas puslapis"

#~ msgid "previous page"
#~ msgstr "ankstesnis puslapis"

#~ msgid "Start search"
#~ msgstr "Pradėti paiešką"

#~ msgid "Clear search"
#~ msgstr "Išvalyti paiešką"

#~ msgid "Clear"
#~ msgstr "Išvalyti"

#~ msgid "stats"
#~ msgstr "statistika"

#~ msgid "Heads up!"
#~ msgstr "Dėmesio!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Gerai padirbėta!"

#~ msgid "Settings saved successfully."
#~ msgstr "Nustatymai sėkmingai įrašyti."

#~ msgid "Oh snap!"
#~ msgstr "O, ne!"

#~ msgid "Something went wrong."
#~ msgstr "Kažkas nutiko."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Gauti paveikslą"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "nuostatos"

#~ msgid "Scores per result"
#~ msgstr "Įvertinimai pagal rezultatą"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "gerbianti privatumą, programuojama metapaieškos sistema"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Šiai publikacijai nėra prieinama jokia santrauka."

#~ msgid "Self Informations"
#~ msgstr "Jūsų informacija"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Keisti kaip yra pateikiamos formos, <a"
#~ " "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">sužinokite daugiau apie "
#~ "užklausos metodus</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Automatiškai aptikti paieškos kalbą"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Automatiškai aptikti paieškos užklausos kalbą ir perjungti į ją."

#~ msgid "others"
#~ msgstr "kiti"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Šis skirtukas nepasirodo ieškojimo "
#~ "rezultastuose, bet jūs galite ieškoti "
#~ "sistemas saraše naudojant trumpinius."

#~ msgid "Shortcut"
#~ msgstr "Trumpinys"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Sistemos negali gauti rezultatų."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Prašau, pabandykite vėliau arba suraskite kita SearXNG instanciją."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Kai įmanoma, peradresuoti į atvirojoje "
#~ "prieigoje esančias publikacijų versijas "
#~ "(reikalingas papildinys)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Įjungta"

#~ msgid "Off"
#~ msgstr "Išjungta"

#~ msgid "Enabled"
#~ msgstr "Įjungtas"

#~ msgid "Disabled"
#~ msgstr "Išjungtas"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Jei pasirenkama kategorija, nedelsiant atlikti"
#~ " paiešką. Išjunkite norėdami pasirinkti "
#~ "kelias kategorijas. (reikalingas JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim pavidalo spartieji klavišai"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Naršyti po paieškos rezultatus naudojant "
#~ "Vim pavidalo sparčiuosius klavišus "
#~ "(reikalingas JavaScript). Paspauskite pagrindiniame"
#~ " ar rezultatų puslapyje \"h\" klavišą "
#~ "norėdami gauti pagalbos."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "mes neradome jokių rezultatų. Naudokite "
#~ "kitokią užklausą arba ieškokite kitose "
#~ "kategorijose."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Perašyti kompiuterio pavadinimo rezultatus "
#~ "arba ištrinti rezultatus pagal kompiuterio "
#~ "pavadinimą"

#~ msgid "Bytes"
#~ msgstr "Baitai"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Kompiuterio pavadinimo pakeitimas"

#~ msgid "Error!"
#~ msgstr "Klaida!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Sistemos negali gauti rezultatų"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Pateikite naują klaidą Github'e"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Atsitiktinių skaičiu generatorius"

#~ msgid "Statistics functions"
#~ msgstr "Statistikos funkcijos"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Skaičiuoti argumentų {functions} funkcijas"

#~ msgid "Get directions"
#~ msgstr "Gauti nurodymus"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Rodo jūsų IP adresą, jei užklausa "
#~ "yra \"ip\" ir jūsų naudotojo agentą, "
#~ "jei užklausoje yra \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Nepavyko atsisiųsti „Tor“ išėjimo mazgų "
#~ "sąrašo iš: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Naudojate Tor ir atrodo, kad turite šį išorinį IP adresą: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Jūs nenaudojate Tor ir turite šį išorinį IP adresą: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Raktažodžiai"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Nurodant tinkintus nustatymus nuostatų URL,"
#~ " jūs galite susinchronizuoti nuostatas tarp"
#~ " prietaisų."

#~ msgid "proxied"
#~ msgstr "persiustas"

