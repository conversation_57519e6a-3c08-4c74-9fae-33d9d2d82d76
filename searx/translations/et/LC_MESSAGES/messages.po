# Estonian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON>, 2020
# <PERSON><PERSON>, 2019
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# Priit <PERSON> <<EMAIL>>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# Priit <PERSON> <<EMAIL>>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: Priit Jõerüüt <<EMAIL>>\n"
"Language-Team: Estonian <https://translate.codeberg.org/projects/searxng/"
"searxng/et/>\n"
"Language: et\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "ilma edasise alagrupeerimiseta"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "muu"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "failid"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "üldine"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muusika"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sotsiaalmeedia"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "pildid"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videod"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "raadio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "tehnoloogia"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "uudised"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "kaardid"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onion-võrgu lingid"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "teadus"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "rakendused"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "sõnastikud"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "laulusõnad"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketid"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "k&v"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "hoidlad"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "tarkvara vikid"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "veeb"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "teadusväljaanded"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automaatne"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "hele"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tume"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "must"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Töövõimeaeg"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "SearXNG teave"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Keskmine temperatuur"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Pilvekate"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Olud"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Praegused olud"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Õhtu"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Tundub nagu"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Niiskus"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maksimaalne temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Minimaalne temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Hommik"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Öö"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Keskpäev"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Õhurõhk"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Päikesetõus"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Päikeseloojang"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatuur"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV indeks"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Nähtavus"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Tuul"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Selge taevas"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Pilvine"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Ilus ilm"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Udu"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Tugev vihmasadu koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Tugev hoogvihm koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Tugev hoogvihm"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Tugev vihm"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Tugev lauslörts koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Hoogne lauslörts koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Hoogne lörts"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Tugev lauslörts"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Tugev lumesadu koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Tugev hooglumi koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Tugev hooglumi"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Tugev lumesadu"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Kerge vihmasadu koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Kerge hoogvihm koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Kerge hoogvihm"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Kerge vihm"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Kerge lörts koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Kerge lauslörts koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Kerge lauslörts"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Kerge lörts"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Kerge lumesadu koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Kerge hooglumi koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Kerge hooglumi"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Kerge lumesadu"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Osaline pilvisus"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Vihm koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Hoogvihm koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Hoogvihm"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Vihmasadu"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Lörts koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Hooglörts koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Hooglörts"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Lörts"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Lumesadu koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Hooglumi koos äiksega"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Hooglumi"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Lumesadu"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "tellijaid"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "postitusi"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktiivseid kasutajaid"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kommentaare"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "kasutaja"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "kogukond"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "punkte"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "pealkiri"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "Autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "ava"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "suletud"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "vastatud"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Üksust ei leitud"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Allikas"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Viga järgmise lehekülje laadimisel"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Sobimatud seaded, palun muuda oma eelistusi"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Sobimatud seaded"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "otsingu viga"

#: searx/webutils.py:35
msgid "timeout"
msgstr "päring aegus"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "parsimise viga"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-protokolli viga"

#: searx/webutils.py:38
msgid "network error"
msgstr "võrguviga"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL viga: sertifikaadi valideerimine ei õnnestunud"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "ootamatu krahh"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-viga"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-ühenduse viga"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proksiserveri viga"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "ROBOTILÕKS"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "liiga palju päringuid"

#: searx/webutils.py:58
msgid "access denied"
msgstr "ligipääs keelatud"

#: searx/webutils.py:59
msgid "server API error"
msgstr "serveri API viga"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Peatatud"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minut(it) tagasi"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} tund(i), {minutes} minut(it) tagasi"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Genereeri erinevaid juhuslikke väärtusi"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Arvuta argumentidest {func}"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Näita teekonda kaardil..."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (VANANENUD)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "See üksus on asendatud"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitikiirus"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "hääled"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikid"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Keel"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} aasta tsitaadid {firstCitationVelocityYear} kuni "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Ei saanud lugeda selle pildi linki. Võib-olla pole failivorming toetatud."
" TinEye ainult lubab kasutada ainult järgmisi vorminguid: JPEG, PNG, GIF,"
" BMP, TIFF või WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Pilt on liiga lihtne, et leida vasteid. TinEye nõuab vastete edukaks "
"tuvastamiseks elementaarseid visuaalseid üksikasju."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Pilti ei saanud alla laadida."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Raamatu hinnang"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Faili kvaliteet"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia keeluloend"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""
"Jäta onioni võrgust tehtud otsingute puhul välja vastused, mis leiduvad "
"Ahmia keeluloendis."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Lihtne taskuarvuti"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Arvuta otsinguribal matemaatilisi avaldisi"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Räsiarvutuse lisamoodul"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Teisendab sõned erinevateks räsitud sõnumilühenditeks."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "räsitud sõnumilühend"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Hostide lisamoodul"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Väärtusta hostide nimesid, eemalda tulemusi või muuda nende järjekorda "
"hosti nime alusel"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Avatud juurdepääsu DOI ümberkirjutamine"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Väldi maksumüüre, suunates võimalusel väljaannete avatud ligipääsuga "
"versioonidele"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Eneseteave"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Päring „ip“ kuvab vastuseks sinu arvuti või seadme ip-aadressi ning "
"„user-agent“ brauseri tunnuse."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Sinu arvuti või seadme IP-aadress on: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Sinu kasutatava brauseri tunnus on: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor kontrollplugin"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"See plugin kontrollib, kas päringu aadress on Tor'i väljumissõlm ja "
"teavitab kasutajat, kui see on nii: nagu check.torproject.org, aga alates"
" SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Tori võrgu väljundsõlmede loendi allalaadimine ei õnnestunud allikast"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Sa kasutad Tori võrku ja tundub, et olemas on väline ip-aadress"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Sa ei kasuta Tori võrku ja sinu arvutil/nutiseadmel on väline ip-aadress"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Jälitajate eemaldus URList"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Eemaldab jälitavad argumendid tagastatud URList"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Ühikute konverteerimise lisamoodul"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konverteeri eri ühikute vahel"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Lehte ei leidu"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Mine lehele %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "otsinguleht"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Anneta"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Eelistused"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Põhineb tarkvaral"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "üks privaatsust austav, vaba metaotsingumootor"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Lähtekood"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Veahaldus"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Mootori statistika"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Avalikud serverid"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Andmekaitsepõhimõtted"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Võta ühendust serveri haldajaga"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Otsingu teostamiseks klõpsa luubile"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Pikkus"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Vaateid"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "vahemälus"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Alusta veateate või ettepaneku koostamist GitHubis"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Eelnevalt palun uuri GitHubist olemasolevate selle otsingumootori "
"sarnasete vigade kohta"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Ma kinnitan et mul ei ole olemasolevat viga probleemi kohta millega ma "
"kokku puutun"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Palun täpsusta URL veateates, kui tegemist on avaliku serveriga"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Esita Githubis uus viga või probleem, mis sisaldab ülaltoodud teavet"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "HTTPS puudub"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Vaata vealogisid ja esita veateade"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang selle mootori jaoks"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang selle kategooriate jaoks"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediaan"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Ebaõnnestunud kontrolleri test(id): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Vead:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Üldine"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Vaikimisi kategooriad"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Kasutajaliides"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privaatsus"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Otsingumootorid"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Hetkel kasutatud otsingumootorid"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Spetsiaalsed päringud"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Küpsised"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Tulemuste arv"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Teave"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Tagasi üles"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Eelmine lehekülg"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Järgmine lehekülg"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Esilehe kuvamine"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Otsi..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "selge"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "otsing"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Hetkel andmed puuduvad."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Otsingumootori nimi"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Skoorid"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Tulemuste arv"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Vastamise aeg"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Usaldusväärsus"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Kokku"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Töötleme"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Hoiatused"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Vead ja erandid"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Erand"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Sõnum"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Protsentuaalne osakaal"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameeter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Failinimi"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funktsioon"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kood"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Kontrollija"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Ebaõnnestunud test"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Kommentaar(id)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Näited"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Määratlused"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sünonüümid"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Tundub nagu"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Vastused"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Laadi tulemused alla"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Proovi otsida:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Sõnumid otsingumootorist"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekundit"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Otsingu URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopeeritud"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopeeri"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Soovitused"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Otsingukeel"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Vaikimisi keel"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Tuvasta automaatselt"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Ohutu otsing"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Range"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Mõõdukas"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Puudub"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Ajavahemik"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Igal ajal"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Viimane päev"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Viimane nädal"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Viimane kuu"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Viimane aasta"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Tähelepanu!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "hetkel pole ühtegi küpsist määratud."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Vabandust!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Tulemusi ei leitud. Võid proovida:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Rohkem tulemusi ei ole. Võid proovida:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Värskenda lehekülge."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Tee muu päringu või vali muu kategooria (üleval)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Muuda eelistustes kasutatud otsingumootorit:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Vaheta teisele SearxNG serverile:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Tee uus otsing või vali muu kategooria."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Mine tagasi eelmisele lehele, kasutades nuppu eelmine lehekülg."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Luba"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Märksõnad (esimene sõna päringus)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nimi"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Kirjeldus"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "See on SearXNGi kohese kiirvastuste moodulite loend."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "See on pluginate nimekiri."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automaattäide"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Otsi asju kirjutamise ajal"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Keskele joondamine"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Kuvab tulemused lehekülje keskel (Oscari paigutus)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"See on nimekiri küpsistest ja nende väärtustest, mida SearXNG sinu "
"arvutisse salvestab."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Selle loetelu abil saad hinnata SearXNG läbipaistvust."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Küpsise nimi"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Väärtus"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Otsingu URL hetkel salvestatud eelistuste kohta"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Märkus: lekitades andmed klõpsatud tulemuste saitidele võib täpsemate "
"seadete määramine otsingu URLis vähendada privaatsust."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL et taastada oma eelistused teises brauseris"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Võrguaadress, kus leiduvad sinu eelistused. Saad seda kasutada oma "
"seadistuste tõstmisel teise nutiseadmesse või arvutisse."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopeeri eelistuste räsi"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Taastamiseks sisesta kopeeritud eelistuste räsi (ilma URL-aadressita)"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Eelistuste räsi"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Objekti digitunnus (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI resolver"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Vali teenus mida kasutab DOI ümberkirjutamine"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Seda vahekaarti ei ole kasutajaliideses olemas, kuid sa saad otsida neis "
"mootorites selle !bang järgi."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Luba kõik"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Keela kõik"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Toetab valitud keelt"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Kaal"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maksimaalne aeg"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Saidiikoonide kuvamine"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Kuva otsingutulemuste kõrval saidiikoone"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Need seaded salvestatame sinu brauseri küpsistes ja see annab meile "
"võimaluse sinu kohta andmeid meie serveris mitte salvestada."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Need küpsised on vaid mugavuse tarbeks, me ei kasuta neid sinu "
"jälitamiseks."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Salvesta"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Lähtesta vaikeseaded"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Tagasi"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Kiirklahvid"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-taoline"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Otsingutulemustes navigeerimine kiirklahvide abil (vajalik JavaScript). "
"Abi saamiseks vajuta põhi või tulemuslehel klahvi \"h\"."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Pildiproksi"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Pildiotsingu tulemuste edastamine SearXNG kaudu"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Lõpmatu kerimine"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Laadi lehe lõppu kerimisel järgmine leht automaatselt"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Mis keelt sa otsinguks eelistad?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Rt SearXNG tuvastaks sinu päringu keele vali \"Automaatne tuvastamine\"."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP-meetod"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Muuda vormide esitamise viisi"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Päring lehekülje pealkirjas"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Kui see on lubatud, sisaldab tulemuslehe pealkiri sinu päringut. Sinu "
"brauser võib selle pealkirja salvestada"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Tulemused uutel kaartidel"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Ava tulemuste lingid uutel brauserikaartidel"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtreeri sisu"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Otsi kategooria valimisel"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Teosta otsing kohe kui kategooria on valitud. Mitme kategooria valimiseks"
" keela see eelistus"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Teema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Muuda SearXNG paigutust"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Teema stiil"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Oma brauseri seadistuste järgimiseks vali \"automaatne\""

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Otsingumootori tunnusload"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Ligipääsu tunnusload privaatsetele otsingumootoritele"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Kasutajaliidese keel"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Muuda paigutuse keelt"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Võrguaadressi vorming"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Ilus"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Terviklik"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Hostikohane"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Muuda võrguaadressi vormingut otsinguvastustes"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "hoidla"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "kuva meedia"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "peida meedia"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "See sait ei andnud mingit kirjeldust."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Failisuurus"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Kuupäev"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tüüp"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolutsioon"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Vorming"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Otsingumootor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Vaata lähtekoodi"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "aadress"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "näita kaarti"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "peida kaart"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versioon"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Haldaja"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Uuendatud"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Sildid"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Populaarsus"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Litsents"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projekti koduleht"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Avaldamise kuupäev"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Ajakiri"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Toimetaja"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Väljaandja"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet-link"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrentifail"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seemendaja"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Kaanija"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Failide arv"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "näita videot"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "peida video"

#~ msgid "Engine time (sec)"
#~ msgstr "Mootori aeg (s)"

#~ msgid "Page loads (sec)"
#~ msgstr "Lehe laadimisi (s)"

#~ msgid "Errors"
#~ msgstr "Vead"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA nõutud"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Kirjuta võimalusel HTTP lingid HTTPSiks"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Tulemused avatakse vaikimisi samas aknas. "
#~ "See plugin kirjutab vaikimisi käitumise "
#~ "üle, et avada lingid uutel "
#~ "kaartidel/akendel. (Nõuab JavaScripti)"

#~ msgid "Color"
#~ msgstr "Värv"

#~ msgid "Blue (default)"
#~ msgstr "Sinine (vaikimisi)"

#~ msgid "Violet"
#~ msgstr "Violetne"

#~ msgid "Green"
#~ msgstr "Roheline"

#~ msgid "Cyan"
#~ msgstr "Erksinine"

#~ msgid "Orange"
#~ msgstr "Oranž"

#~ msgid "Red"
#~ msgstr "Punane"

#~ msgid "Category"
#~ msgstr "Kategooria"

#~ msgid "Block"
#~ msgstr "Keela"

#~ msgid "original context"
#~ msgstr "originaalne kontekst"

#~ msgid "Plugins"
#~ msgstr "Pluginad"

#~ msgid "Answerers"
#~ msgstr "Vastajad"

#~ msgid "Avg. time"
#~ msgstr "Keskmine aeg"

#~ msgid "show details"
#~ msgstr "kuva andmeid"

#~ msgid "hide details"
#~ msgstr "peida andmed"

#~ msgid "Load more..."
#~ msgstr "Laadi juurde..."

#~ msgid "Loading..."
#~ msgstr "Laadimine..."

#~ msgid "Change searx layout"
#~ msgstr "Muuda searxi paigutust"

#~ msgid "Proxying image results through searx"
#~ msgstr "Proksin pilditulemusi läbi searxi"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "See on searxi koheste vastajate moodulite nimekiri."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "See on küpsiste ja nende väärtuste "
#~ "nimekiri, mida searx hoiab sinu arvutis."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Selle nimekirjaga saad sa hinnata searxi läbipaistvust."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Tundub, et kasutad searxi esimest korda."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Palun proovi hiljem uuesti või leia teine searxi eksemplar."

#~ msgid "Themes"
#~ msgstr "Teemad"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Meetod"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Täpsemad seaded"

#~ msgid "Close"
#~ msgstr "Sulge"

#~ msgid "Language"
#~ msgstr "Keel"

#~ msgid "broken"
#~ msgstr "katki"

#~ msgid "supported"
#~ msgstr "toetatud"

#~ msgid "not supported"
#~ msgstr "mittetoetatud"

#~ msgid "about"
#~ msgstr "teave"

#~ msgid "Avg."
#~ msgstr "Keskmine."

#~ msgid "User Interface"
#~ msgstr "Kasutajaliides"

#~ msgid "Choose style for this theme"
#~ msgstr "Vali sellele teemale stiil"

#~ msgid "Style"
#~ msgstr "Stiil"

#~ msgid "Show advanced settings"
#~ msgstr "Näita täiustatud seadeid"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Näita täiustatud seadete paneeli vaikimisi avalehel"

#~ msgid "Allow all"
#~ msgstr "Luba kõik"

#~ msgid "Disable all"
#~ msgstr "Keela kõik"

#~ msgid "Selected language"
#~ msgstr "Valitud keel"

#~ msgid "Query"
#~ msgstr "Päring"

#~ msgid "save"
#~ msgstr "salvesta"

#~ msgid "back"
#~ msgstr "tagasi"

#~ msgid "Links"
#~ msgstr "Lingid"

#~ msgid "RSS subscription"
#~ msgstr "RSS jälgimus"

#~ msgid "Search results"
#~ msgstr "Otsingutulemused"

#~ msgid "next page"
#~ msgstr "järgmine leht"

#~ msgid "previous page"
#~ msgstr "eelmine leht"

#~ msgid "Start search"
#~ msgstr "Alusta otsingut"

#~ msgid "Clear search"
#~ msgstr "Tühjenda otsing"

#~ msgid "Clear"
#~ msgstr "Tühjenda"

#~ msgid "stats"
#~ msgstr "statistika"

#~ msgid "Heads up!"
#~ msgstr "Tähelepanu!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Tundub, et kasutate SearXNG'i esimest korda."

#~ msgid "Well done!"
#~ msgstr "Hästi tehtud!"

#~ msgid "Settings saved successfully."
#~ msgstr "Seaded edukalt salvestatud."

#~ msgid "Oh snap!"
#~ msgstr "Oh kurja!"

#~ msgid "Something went wrong."
#~ msgstr "Midagi läks valesti."

#~ msgid "Date"
#~ msgstr "Kuupäev"

#~ msgid "Type"
#~ msgstr "Tüüp"

#~ msgid "Get image"
#~ msgstr "Hangi pilt"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "eelistused"

#~ msgid "Scores per result"
#~ msgstr "Skoorid tulemuste kohta"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "privaatsust austaval, häkitaval metaotsingu mootoril"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Selle väljaande jaoks pole abstraktset."

#~ msgid "Self Informations"
#~ msgstr "Self Informatsioon"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Muuda viisi, kuidas väljad edastatakse, "
#~ "<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">loe taotlusmeetodite kohta "
#~ "lisaks</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Te kasutate TORi. Teie IP aadress paistab olevat : {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Te ei kasuta TORi. Teie IP aadress paistab olevat: {ip_adress}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "muud"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "See vahekaart ei näita otsingutulemusi, "
#~ "kuid siin loetletud mootoreid saab "
#~ "otsida \"bang\" kaudu."

#~ msgid "Shortcut"
#~ msgstr "Otsetee"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Mootorid ei saa tulemusi tagastada."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Palun proovige hiljem uuesti või leidke teine SearXNG instants."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Suuna võimalusel väljaannete avatud "
#~ "ligipääsuga versioonidele (nõuab pluginat)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Sees"

#~ msgid "Off"
#~ msgstr "Väljas"

#~ msgid "Enabled"
#~ msgstr "Lubatud"

#~ msgid "Disabled"
#~ msgstr "Keelatud"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Teosta otsing koheselt, kui kategooria "
#~ "on valitud. Keela mitme kategooria "
#~ "valimiseks. (Nõuab JavaScripti)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim'i-sarnased kiirklahvid"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navigeeri otsingutulemusi Vim'i-sarnaste "
#~ "kiirklahvidega (nõuab JavaScripti). Abi "
#~ "saamiseks vajuta avalehel või tulemuste "
#~ "lehel klahvi \"h\"."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "me ei leidnud ühtegi tulemust. Palun "
#~ "kasuta teist päringut või otsi "
#~ "rohkematest kategooriatest."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Tulemuste hostinimede ümberkirjutamine või "
#~ "tulemuste eemaldamine hostinime alusel"

#~ msgid "Bytes"
#~ msgstr "Baite"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Hostnime asendamine"

#~ msgid "Error!"
#~ msgstr "Viga!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Otsingumootorid ei anna päringutele vastuseid"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Alusta uue vea või probleemi esitamist GitHubis"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Juhusliku väärtuse generaator"

#~ msgid "Statistics functions"
#~ msgstr "Statistikafunktsioonid"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Arvuta argumentide {functions}"

#~ msgid "Get directions"
#~ msgstr "Hangi juhised"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Kuvab sinu arvuti või seadme IP-"
#~ "aadressi, kui päringuks on \"ip\" ning"
#~ " veebibrauseri tunnust, kui päringuks on"
#~ " \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Ei saanud alla laadida Tori "
#~ "väljumissõlmede nimekirja aadressilt: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Sa kasutad Tor'i ja tundub, et "
#~ "sinu arvutil on see väline IP-"
#~ "aadress: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Sa ei kasuta Tor'i ja sinu arvutil"
#~ " on see väline IP-aadress: "
#~ "{ip_address}"

#~ msgid "Keywords"
#~ msgstr "Märksõnad"

#~ msgid "/"
#~ msgstr "/"

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Kohandatud seadete määramine eelistuste URL-i"
#~ " saad kasutada eelistuste sünkroniseerimiseks "
#~ "eri seadmete vahel."

#~ msgid "proxied"
#~ msgstr "proksiserveris"
