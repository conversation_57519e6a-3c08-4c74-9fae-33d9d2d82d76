# Czech translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2018
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: Fjuro <************>\n"
"Language-Team: Czech <https://translate.codeberg.org/projects/searxng/"
"searxng/cs/>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "bez dalších podskupin"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "ostatní"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "soubory"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "obecné"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "hudba"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sociální sítě"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "obrázky"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videa"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "rádio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "zprávy"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapy"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "cibule"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "věda"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikace"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "slovníky"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "texty písní"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "balíčky"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "otázky a odpovědi"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repozitáře"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "softwarové wikipédie"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "vědecké publikace"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automaticky"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "světlý"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tmavý"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "černý"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Spolehlivost"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "O SearXNG"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Prům. teplota"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Pokrytí mraky"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Stav"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Aktuální stav"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Večer"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Pocitová teplota"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Vlhkost"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Max. teplota"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Min. teplota"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Ráno"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noc"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Poledne"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Tlak"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Východ slunce"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Západ slunce"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Teplota"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV index"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Viditelnost"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vítr"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Jasno"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Oblačno"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "odběratelé"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "příspěvky"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktivní uživatelé"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "komentáře"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "uživatel"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "komunita"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "body"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "název"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "otevřené"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "zavřené"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "zodpovězené"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nic nenalezeno"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "zdroj"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Chyba při načítání další stránky"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Neplatné nastavení, upravte své předvolby"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Neplatné nastavení"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "chyba vyhledávání"

#: searx/webutils.py:35
msgid "timeout"
msgstr "čas vypršel"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "chyba parsování"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "chyba HTTP protokolu"

#: searx/webutils.py:38
msgid "network error"
msgstr "síťová chyba"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "chyba SSL: ověření certifikátu selhalo"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "nečekaná chyba"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "chyba HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Chyba spojení HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "chyba proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "příliš mnoho požadavků"

#: searx/webutils.py:58
msgid "access denied"
msgstr "přístup odepřen"

#: searx/webutils.py:59
msgid "server API error"
msgstr "chyba API serveru"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Pozastaveno"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "před {minutes} minutami"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "před {hours} hodinami, {minutes} minutami"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generování náhodných hodnot"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Vypočítat {func} argumentů"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Zobrazit trasu na mapě .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ZASTARALÉ)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Tato položka byla nahrazena položkou"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanál"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "datový tok"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "hlasy"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "kliknutí"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Jazyk"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citace z roku {firstCitationVelocityYear} do "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Nelze načíst url adresu obrázku. Příčinou může být nepodporovaný formát "
"souboru. TinEye podporuje pouze obrázky ve formátu JPEG, PNG, GIF, BMP, "
"TIFF nebo WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Obrázek je příliš jednoduchý na to, aby bylo možné najít shody. TinEye "
"vyžaduje základní úroveň vizuálních detailů pro úspěšnou identifikaci."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Obrázek se nepodařilo stáhnout."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Hodnocení knih"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Množství souborů"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Seznam blokování Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrovat výsledky onion, které se objeví na seznamu zakázaných Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Základní kalkulačka"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Vypočítejte matematické výrazy pomocí vyhledávací lišty"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Doplněk hashe"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Převádí řetězce na různé hash hodnoty."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hash hodnota"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Doplněk hostitelských jmen"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Přepsat hostitelská jména, odstranit výsledky nebo je prioritizovat na "
"základě hostitelského jména"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Přesměrování na Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Automaticky přesměrovat na volně přístupné verze publikací místo "
"placených, pokud je to možné"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informace o sobě"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Zobrazí vaší IP, pokud je dotaz „ip“ a váš uživatelský agent, pokud je "
"dotaz „user-agent“."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Vaše IP: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Váš uživatelský agent: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Doplněk kontroly TORu"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Tento doplněk kontroluje, zda je adresa požadavku výstupním uzlem sítě "
"Tor, a informuje uživatele, pokud tomu tak je; jako check.torproject.org,"
" ale od SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nepodařilo se stáhnout seznam výstupních uzlů sítě Tor z"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Používáte Tor a zdá se, že máte externí IP adresu"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Nepoužíváte Tor a máte externí IP adresu"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Odstraňovač sledovacích URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Odstranit sledovací parametry z načtených URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Doplněk převodu jednotek"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Převod mezi jednotkami"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Stránka nenalezena"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Přejít na %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "stránka vyhledávání"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Dar"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Nastavení"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Poháněno softwarem"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "otevřený, metavyhledávající engine, respektující soukromí"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Zdrojový kód"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Hlášení chyb"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statistiky vyhledávače"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Veřejné instance"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Zásady soukromí"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontaktujte správce instance"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Vyhledávání provedete kliknutím na lupu"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Délka"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Zhlédnutí"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "archivovaná verze"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Začněte přidávat novou chybu na Githubu"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Zkontrolujte prosím existující chyby tohoto enginu na GitHubu"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Potvrzuji, že neexistuje žádná chyba týkající se problému, se kterým se "
"setkávám"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Pokud je tohle veřejná instance, prosím specifikujte URL v náhlášení chyby"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Odeslání nového problému na Github včetně výše uvedených informací"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Žádné HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Zobrazit ladící záznamy a poslat hlášení o chybě"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang pro tento vyhledávač"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang pro své kategorie"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Medián"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Neúspešné testy zkoušečů: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Chyby:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Obecné"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Základní kategorie"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Uživatelské rozhraní"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Soukromí"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Vyhledávače"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Aktuálně používané vyhledávače"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Zvláštní dotazy"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Počet výsledků"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informace"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Nahoru"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Předchozí stránka"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Další stránka"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Zobrazit úvodní stránku"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Hledat…"

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "vyčistit"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "vyhledat"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Aktuálně nejsou k dispozici žádná data. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Jméno vyhledávače"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Skóre"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Počet výsledků"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Čas odpovědi"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Spolehlivost"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Celkem"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Zpracovávám"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Varování"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Chyby a výjimky"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Výjimka"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Zpráva"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Procenta"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametr"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Název souboru"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkce"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kód"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Zkoušeč"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Test selhal"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komentář(e)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Příklady"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definice"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonyma"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Odpovědi"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Stáhnout výsledky vyhledávání"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Zkuste vyhledat:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Hlášení vyhledávačů"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekund"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL vyhledávání"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Zkopírováno"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopírovat"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Návrhy"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Jazyk hledání"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Výchozí jazyk"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Automatická detekce"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Bezpečné vyhledávání"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Přísné"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Střední"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Vypnuto"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Čásový interval"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Kdykoli"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Dnes"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Tento týden"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Měsíc"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Rok"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informace!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "aktuálně nejsou definované žádné cookies."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Omlouváme se!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Nebyly nalezeny žádné výsledky. Můžete zkusit:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Nenašli jsme žádné další výsledky. Můžete zkusit:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Znovu načíst stránku."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Vyhledat jinou frázi nebo vybrat jinou kategorii (výše)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Změnit použitý vyhledávač v předvolbách:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Přepnout na jinou instanci:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Vyhledat jiný dotaz nebo vybrat jinou kategorii."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Vrátit se zpět na předchozí stranu."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Povolit"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Klíčová slova (první slovo v dotazu)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Název"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Popis"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Toto je seznam našeptávačů SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Toto je seznam doplňků."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Našeptávač"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Vyhledávat během psaní"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Zarovnání na střed"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Zobrazí výsledky uprostřed stránky (rozložení Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Toto je seznam cookies a jejich hodnoty, které si SearXNG ukládá na vašem"
" počítači."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "S pomocí tohoto seznamu můžete určit transparentnost SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Název cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Hodnota"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL pro vyhledávání s aktuálně uloženými předvolbami"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Poznámka: zadaní vlastních nastavení v URL může snížit soukromí "
"prozrazením dat při kliknutí na výsledky hledání."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL k obnovení vašich preferencí v jiném prohlížeči"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Adresa URL obsahující vaše předvolby. Tuto adresu lze použít k obnovení "
"vašich nastavení na jiném zařízení."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopírovat hash předvoleb"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Pro obnovení vložte zkopírovaný hash předvoleb (bez adresy URL)"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash předvoleb"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital Object Identifier (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Web pro přesměrování na Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Výběr služby použité při přepisu DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Tato karta v uživatelském rozhraní neexistuje, ale můžete v těchto "
"vyhledávačích vyhledávat podle jejích !bang."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Povolit vše"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Zakázat vše"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Podporuje vybraný jazyk"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Váha"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Max. čas"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Zobrazit ikony"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Zobrazit ikony webů vedle výsledků vyhledávání"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Tato nastavení jsou uložena ve vašich cookies, což nám umožňuje tyto data"
" neuchovávat nikde jinde."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Tyto cookies existují výhradně pro vaše pohodlí, nepoužíváme je ke "
"sledování."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Uložit"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Obnovit výchozí"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Zpět"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Klávesové zkratky"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Podobné editoru Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Procházejte výsledky vyhledávání klávesovými zkratkami (vyžadován "
"JavaScript). Pro nápovědu stiskněte na hlavní stránce nebo stránce s "
"výsledky klávesu „H“."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Nepřímé vyhledávání obrázků"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Používat SearXNG jako prostředníka pro vyhledávání obrázků"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Nekonečné výsledky"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Automaticky načítat další výsledky při rolování"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Jaký jazyk vyhledávání upřednostňujete?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Zvolte Automaticky detekovat, aby SearXNG detekoval jazyk vašeho dotazu."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Metoda HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Změnit způsob odesílání dotazů"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Dotaz v titulku stránky"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Titulek stránky s výsledky bude obsahovat váš vyhledávací dotaz, pokud "
"toto zapnete. Váš prohlížeč může tento titulek zaznamenat"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Výsledky na novém panelu"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Otevírat výsledky na novém panelu prohlížeče"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrovat obsah"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Spustit vyhledávání při výběru kategorie"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Pokud je vybrána kategorie, ihned provést vyhledávání. Zakažte pro "
"vybrání několika kategorií"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Vzhled"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Změnit vzhled SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Styl vzhledu"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Zvolte automaticky pro sledování nastavení vašeho prohlížeče"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokeny vyhledávačů"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Přístupové tokeny pro soukromé vyhledávače"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Jazyk rozhraní"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Nastavit jazyk rozhraní"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formátování adres URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Pěkné"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Plné"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Hostitel"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Změnit formátování adres URL výsledků"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repozitář"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "ukázat média"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "skrýt média"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Tato stránka nemá žádný popis."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Velikost"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Datum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Typ"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Rozlišení"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formát"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Vyhledávač"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Zobrazit zdroj"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresa"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "zobrazit mapu"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "skrýt mapu"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Verze"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Správce"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Aktualizováno"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Značky"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularita"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licence"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Domovská stránka projektu"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Datum vydání"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Časopis"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editor"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Nakladatel"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "odkaz magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "soubor torrentu"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeder"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Počet souborů"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "zobrazit video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "skrýt video"

#~ msgid "Engine time (sec)"
#~ msgstr "Strojový čas (s)"

#~ msgid "Page loads (sec)"
#~ msgstr "Načítání stránky (s)"

#~ msgid "Errors"
#~ msgstr "Chyby"

#~ msgid "CAPTCHA required"
#~ msgstr "Je vyžadována CAPTCHA"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Přesměrovat HTTP na HTTPS, pokud je to možné"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Ve výchozím nastavení se výsledky "
#~ "zobrazují v aktuálním okně. Tento "
#~ "zásuvný modul umožňuje otevírat odkazy v"
#~ " novém panelu/okně (vyžaduje JavaScript)."

#~ msgid "Color"
#~ msgstr "Barva"

#~ msgid "Blue (default)"
#~ msgstr "Modrá (výchozí)"

#~ msgid "Violet"
#~ msgstr "Fialová"

#~ msgid "Green"
#~ msgstr "Zelená"

#~ msgid "Cyan"
#~ msgstr "Modrozelená"

#~ msgid "Orange"
#~ msgstr "Oranžová"

#~ msgid "Red"
#~ msgstr "Červená"

#~ msgid "Category"
#~ msgstr "Kategorie"

#~ msgid "Block"
#~ msgstr "Zakázat"

#~ msgid "original context"
#~ msgstr "původní kontext"

#~ msgid "Plugins"
#~ msgstr "Zásuvné moduly"

#~ msgid "Answerers"
#~ msgstr "Odpovídači"

#~ msgid "Avg. time"
#~ msgstr "Prům. čas"

#~ msgid "show details"
#~ msgstr "zobrazit podrobnosti"

#~ msgid "hide details"
#~ msgstr "skrýt podrobnosti"

#~ msgid "Load more..."
#~ msgstr "Načíst více…"

#~ msgid "Loading..."
#~ msgstr "Načítání…"

#~ msgid "Change searx layout"
#~ msgstr "Motiv rozhraní searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Načítat výsledky vyhledávání obrázků přes proxy"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr ""
#~ "Toto je seznam modulů vyhledávače searx,"
#~ " které poskytují okamžité odpovědi."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Toto je seznam cookies a jejich "
#~ "hodnot, které searx ukládá na vašem "
#~ "počítači."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Pomocí něj můžete posoudit, jestli je searx transparentní."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Zdá se, že používáte searx poprvé."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Zkuste to prosím později, nebo použijte jinou instanci searx."

#~ msgid "Themes"
#~ msgstr "Motivy"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Dotazovací metoda"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Pokročilá nastavení"

#~ msgid "Close"
#~ msgstr "Zavřít"

#~ msgid "Language"
#~ msgstr "Jazyk"

#~ msgid "broken"
#~ msgstr "rozbitý"

#~ msgid "supported"
#~ msgstr "podporováno"

#~ msgid "not supported"
#~ msgstr "nepodporováno"

#~ msgid "about"
#~ msgstr "o nás"

#~ msgid "Avg."
#~ msgstr "Prům."

#~ msgid "User Interface"
#~ msgstr "Uživatelské rozhraní"

#~ msgid "Choose style for this theme"
#~ msgstr "Styl daného motivu"

#~ msgid "Style"
#~ msgstr "Styl"

#~ msgid "Show advanced settings"
#~ msgstr "Zobrazit rozšířená nastavení"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""
#~ "Ve výchozím stavu zobrazovat na domovské"
#~ " stránce panel pokročilého nastavení"

#~ msgid "Allow all"
#~ msgstr "Povolit vše"

#~ msgid "Disable all"
#~ msgstr "Zakázat vše"

#~ msgid "Selected language"
#~ msgstr "Váš jazyk vyhledávání"

#~ msgid "Query"
#~ msgstr "Dotaz"

#~ msgid "save"
#~ msgstr "uložit"

#~ msgid "back"
#~ msgstr "zpět"

#~ msgid "Links"
#~ msgstr "Odkazy"

#~ msgid "RSS subscription"
#~ msgstr "Odběr RSS"

#~ msgid "Search results"
#~ msgstr "Výsledky vyhledávání"

#~ msgid "next page"
#~ msgstr "další stránka"

#~ msgid "previous page"
#~ msgstr "předchozí stránka"

#~ msgid "Start search"
#~ msgstr "Začít hledat"

#~ msgid "Clear search"
#~ msgstr "Smazat vyhledávání"

#~ msgid "Clear"
#~ msgstr "Smazat"

#~ msgid "stats"
#~ msgstr "statistiky"

#~ msgid "Heads up!"
#~ msgstr "Jen krátká informace!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Zdá se, že jste použili SearXNG poprvé."

#~ msgid "Well done!"
#~ msgstr "Výborně!"

#~ msgid "Settings saved successfully."
#~ msgstr "Nastavení úspěšně uloženo."

#~ msgid "Oh snap!"
#~ msgstr "Ale ne!"

#~ msgid "Something went wrong."
#~ msgstr "Něco se nepovedlo."

#~ msgid "Date"
#~ msgstr "Datum"

#~ msgid "Type"
#~ msgstr "Typ"

#~ msgid "Get image"
#~ msgstr "Načíst obrázek"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "nastavení"

#~ msgid "Scores per result"
#~ msgstr "Skóre na výsledek"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "soukromí respektujícím, nastavitelným multivyhledávačem"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Pro tuto publikaci neexistuje žádný abstrakt."

#~ msgid "Self Informations"
#~ msgstr "Informace o sobě"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Určuje způsob odesílání formulářů. Informace"
#~ " o dotazovacích metodách <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">viz Wikipedie</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Tento plugin kontroluje, jestli adresa "
#~ "požadavku je výstupní uzel TORu, a "
#~ "infromuje uživatele pokud je, jako "
#~ "check.torproject.org ale od searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Seznam výstupních uzlů TOR "
#~ "(https://check.torproject.org/exit-addresses) je "
#~ "nedostupný."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Používáte TOR. Zdá se, že vaše IP adresa je: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Nepoužíváte TOR. Zdá se, že vaše IP adresa je: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Automaticky rozpoznaný jazyk vyhledávání"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Automaticky detekuj jazyk vyhledávaného dotazu a přepni se do něj."

#~ msgid "others"
#~ msgstr "ostatní"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Tato záložka se nezobrazuje ve "
#~ "výsledcích vyhledávání, ale můžete použít "
#~ "vyhledávače zde uvedené pomocí vykřičníku."

#~ msgid "Shortcut"
#~ msgstr "Zkratka"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Tato karta v uživatelském rozhraní "
#~ "neexistuje, ale můžete v těchto "
#~ "vyhledávačích vyhledávat podle jejích !bang."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Načtení výsledků vyhledávači se nezdařilo."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Prosím zkuste to znovu později, nebo si najděte jinou instanci SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Přesměrovat na volně přístupné verze "
#~ "publikací, pokud jsou k dispozici "
#~ "(vyžaduje zásuvný modul)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Změna způsobu odesílání formulářů, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">naočit se více o metodách"
#~ " žádání</a>"

#~ msgid "On"
#~ msgstr "Zapnuto"

#~ msgid "Off"
#~ msgstr "Vypnuto"

#~ msgid "Enabled"
#~ msgstr "Zapnuto"

#~ msgid "Disabled"
#~ msgstr "Vypnuto"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Provede hledání okamžitě při výběru "
#~ "kategorie. Pokud je tato funkce vypnuta,"
#~ " je možné vybrat více kategorií "
#~ "(vyžaduje JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Klávesové zkratky jako v textovém editoru Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Procházení výsledků pomocí kláves stejných "
#~ "jako v textovém editoru Vim (vyžaduje"
#~ " JavaScript). Stisknutím klávesy \"h\" na"
#~ " hlavní stránce či stránce výsledků "
#~ "zobrazíte nápovědu."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Nenašli jsme žádné výsledky. Použijte "
#~ "prosím jiný dotaz nebo hledejte ve "
#~ "více kategoriích."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Přepsat adresy serverů nebo odstranit výsledky podle adresy"

#~ msgid "Bytes"
#~ msgstr "bajtů"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Nahrazení adresy serveru"

#~ msgid "Error!"
#~ msgstr "Chyba!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Vyhledávače nemohou získat výsledky"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Začněte přidávat novou chybu na Githubu"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generátor náhodných hodnot"

#~ msgid "Statistics functions"
#~ msgstr "Statistické funkce"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Výpočet funkcí {functions} pro daný argument"

#~ msgid "Get directions"
#~ msgstr "Získat pokyny"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Umožňuje hledat informace o sobě: \"ip\""
#~ " zobrazí vaši IP adresu a \"user "
#~ "agent\" zobrazí identifikátor prohlížeče."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Nelze stáhnout seznam výstupních uzlů "
#~ "Tor z: https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Používáte Tor a vypadá to, že máte tuto externí IP adresu: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Nepoužíváte Tor a máte tuto externí IP adresu: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Klíčová slova"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Zadání vlastních nastavení v URL "
#~ "předvoleb lze použít k synchronizaci "
#~ "předvoleb mezi zařízeními."

#~ msgid "proxied"
#~ msgstr "přes proxy"
