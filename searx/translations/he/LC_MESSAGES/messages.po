# Hebrew translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2017,2019
# <AUTHOR> <EMAIL>, 2020
# pointhi, 2014
# rike, 2014
# <AUTHOR> <EMAIL>, 2014
# <PERSON> <<EMAIL>>, 2022, 2023.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>,,
# 2025.
# 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-01 06:39+0000\n"
"Last-Translator: ngf <<EMAIL>>\n"
"Language-Team: Hebrew <https://translate.codeberg.org/projects/searxng/"
"searxng/he/>\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n == 1) ? 0 : ((n == 2) ? 1 : ((n > 10 && "
"n % 10 == 0) ? 2 : 3));\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "ללא תת-קבוצה נוספת"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "אחר"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "קבצים"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "כללי"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "מוזיקה"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "מדיה חברתית"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "תמונות"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "וידאו"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "רדיו"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "טלויזיה"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "טכנולוגיה"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "חדשות"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "מפות"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "שכבות בצל"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "מדע"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "אפליקציות"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "מילונים"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "מילות שיר"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "חבילות"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "שו״ת"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "מאגרים"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "ויקי"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "רשת"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "מחקרים מדעיים"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "אוטומטית"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "בהיר"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "כהה"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "שחור"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "זמינות"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "אודות"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "טמפ' ממוצעת"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "כיסוי עננים"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "תנאים"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "תנאים כרגע"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "ערב"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "מרגיש כמו"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "לחות"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "טמפ' מקסימלית"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "טמפ' מינימלית"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "בוקר"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "לילה"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "צהריים"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "לחץ"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "זריחה"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "שקיעה"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "טמפרטורה"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "מדד קרינת UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "נראות"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "רוח"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "מעונן"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "סביר"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "ערפל"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "גשם כבד ורעם"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "ממטרי גשם עזים ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "ממטרי גשם עזים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "גשם כבד"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "שלג עז ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "ממטרי שלג ורעמים כבדים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "ממטרי שלג עזים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "שלג עז"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "שלג כבד ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "ממטרי שלג כבדים ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "ממטרי שלג כבדים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "שלג כבד"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "גשם קל ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "ממטרי גשם קלים ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "ממטרי גשם קלים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "גשם קל"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "שלג קל ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "ממטרי שלג קלים ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "ממטרי שלג קלים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "שלג קל"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "שלג קל ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "ממטרי שלג קלים ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "מעונן חלקית"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "גשם ורעמים"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "גשם"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "שלג"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "מנויים"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "פוסטים"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "משתמשים פעילים"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "הערות"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "משתמש"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "קהילה"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "נקודות"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "כותרת"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "מחבר"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "פתוח"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "סגור"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "נענו"

#: searx/webapp.py:292
msgid "No item found"
msgstr "לא נמצא פריט"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "מקור"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "שגיאה בטעינת העמוד הבא"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "הגדרות לא תקינות, עליך לתקן את ההעדפות שלך"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "הגדרות לא תקינות"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "שגיאת חיפוש"

#: searx/webutils.py:35
msgid "timeout"
msgstr "פקיעת זמן"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "שגיאת ניתוח"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "שגיאת פרוטוקול HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "שגיאת רשת תקשורת"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "שגיאת SSL: אימות התעודה נכשל"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "קריסה לא צפויה"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "שגיאת HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "שגיאת חיבור HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "שגיאת פרוקסי"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "יותר מדי בקשות"

#: searx/webutils.py:58
msgid "access denied"
msgstr "הגישה נדחתה"

#: searx/webutils.py:59
msgid "server API error"
msgstr "שגיאת API שרת"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "מושהה"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "לפני {minutes} דקות"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "לפני {hours} שעות, {minutes} דקות"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "מייצרת ערכים אקראיים שונים"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "חשב {func} של הארגומנטים"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "הצג מסלול במפה .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLETE)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "רשומה זו הוחלפה על ידי"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "ערוץ"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "קצב נתונים"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "הצבעות"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "לחיצות"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "שפה"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} אזכורים מ {firstCitationVelocityYear} עד "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"לא ניתן היה לקרוא את כתובת האתר של התמונה. ייתכן שהסיבה לכך היא פורמט "
"קובץ שאינו נתמך. TinEye תומך רק בתמונות שהן JPEG, PNG, GIF, BMP, TIFF או "
"WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"התמונה הזו הינה יותר מידי פשוטה מכדי למצוא התאמות. TinEye צריך רמה בסיסית"
" של פרטים חזותיים כדי להצליח למצוא התאמות."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "אי אפשר להוריד את תמונה זו."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "דירוג ספרים"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "איכות קובץ"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "הרשימה השחורה של Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "מסנן תוצאות onion שמופיעות ברשימת השחורה של Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "מחשבון בסיסי"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "חשב ביטויים מתמטיים באמצעות שורת החיפוש"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "תוסף גיבוב"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "ממיר מחרוזות לתוך hash digests (לקט גיבוב) שונים."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "דגימת האש"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "תוסף כתובות"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "שכתוב כתובות, מחיקת תוצאות או תעדוף לפי הכתובת"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "שכתוב Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"הימנע מגרסאות paywall על ידי הכוונה מחודשת לגרסאות כניסה-חופשית של "
"כתבי-עת כאשר ישנן"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "מידע עצמי"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"מציג את ה-IP שלך אם השאילתא היא \"ip\" ואת סוכן המשתמש שלך אם השאילתא היא"
" \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "ה-IP שלך הוא: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "סוכן המשתמש שלך הוא: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "טור בודק תוסף"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"תוסף זה בודק אם הכתובת של הבקשה היא צומת יציאה של TOR, ומודיע למשתמש אם "
"כן, כמו check.torproject.org אבל מ-SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "לא ניתן להוריד את רשימת נקודות היציאה של Tor מ-"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "הינך משתמש ב-Tor ונראה שברשותך כתובת ה-IP החיצונית"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "אינך משתמש ב-Tor וברשותך כתובת ה-IP החיצונית"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "הסרת Tracker URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "הסר ארגומנטי איתור מתוך URL מוחזר"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "תוסף המרה של יחידות"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "המר בין יחידות"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{מיקום}: {טמפרטורה}, {מזג אוויר}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "עמוד לא נמצא"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "המשך לעמוד %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "עמוד חיפוש"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "תרומות"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "העדפות"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "מופעל באמצעות"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "מנוע מטא-חיפוש בקוד חופשי המכבד את פרטיותך"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "קוד מקור"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "דווח על בעיה"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "סטטיסטיקת מנוע חיפוש"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "שרתים מקבילים"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "פוליסת פרטיות"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "צור קשר עם מפעיל השירת"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "לחץ על זכוכית המגדלת כדי לחפש"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "אורך"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "צפיות"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "מחבר"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "מוטמן"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "התחל להגיש גיליון חדש ב- GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "בדוק אם קיימים באגים לגבי מנוע זה ב-GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "אני מאשר שאין באג קיים לגבי הבעיה שאני נתקל בה"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "אם זה מופע ציבורי, אנא ציין את כתובת האתר בדוח הבאג"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "שלח בעיה חדשה ב-Github הכוללת את המידע לעיל"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "בלי HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "צפה ביומני שגיאה ושלח דיווח על בעיה"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang למנוע זה"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang לקטגוריות"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "חציון"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "מבחני בודק שכשלו: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "שגיאות:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "כללי"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "קטגוריות עיקריות"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "ממשק משתמש"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "פרטיות"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "מנועים"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "מנועי חיפוש שמופעלים כעת"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "שאילתות מיוחדות"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "עוגיות"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "מספר תוצאות"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "מידע"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "בחזרה למעלה"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "עמוד קודם"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "עמוד הבא"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "הצג את העמוד הראשי"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "הקלד מילות חיפוש..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "נקה"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "חפש"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "אין כעת מידע זמין. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "שם מנוע"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "דירוג"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "ספירת תוצאות"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "זמן תגובה"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "מהימנות"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "סה״כ"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "עיבוד"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "אזהרות"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "שגיאות וחריגים"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "חריג"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "הודעה"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "אחוז"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "פרמטר"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "שם קובץ"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "פונקציה"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "קוד"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "בודק"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "מבחן נכשל"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "הערות"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "דוגמאות"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "הגדרות"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "מילים נפרדות"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "מרגיש כמו"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "תשובות"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "הורד תוצאות"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "נסה לחפש:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "הודעות ממנועי החיפוש"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "שניות"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "כתובת URL חיפוש"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "הועתק"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "העתק"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "הצעות"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "שפת חיפוש"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "שפה ברירת מחדל"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "זיהוי אוטומטי"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "חיפוש בטוח"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "מחמיר"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "מתון"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "כבוי"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "טווח זמן"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "כל זמן"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "מהיום האחרון"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "מהשבוע האחרון"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "מהחודש האחרון"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "מהשנה האחרונה"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "מידע!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "ברגע זה, אין עוגיות מוגדרות."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "סליחה!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "אין תוצאות לחיפוש. שווה לנסות את הצעד הבא:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "אין תוצאות נוספות. שווה לנסות את הדבר הבא:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "טעינת הדף מחדש."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "שווה לנסות שאילתה או קטגוריה אחרת (למעלה)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "שינוי מנוע החיפוש בהגדרות:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "להחלפת שרת:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "חפש שאילתה אחרת או בחר קטגוריה אחרת."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "חזור לדף הקודם באמצעות כפתור הדף הקודם."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "הפעל"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "מילות מפתח (מילה ראשונה בשאילתה)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "שם"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "תיאור"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "זוהי רשימת המודולים של המענה המיידי של SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "זוהי הרשימת של תוספות."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "השלמה אוטומטית"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "מצא טקסט תוך כדי הקלדה"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "יישור מרכז"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "מציג תוצאות במרכז העמוד (פריסת אוסקר)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "זוהי רשימת העוגיות וערכיהן אשר SearXNG מאחסן על המחשב שלך."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "בעזרת רשימה זאת, באפשרותך לגשת אל SearXNG transparency."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "שם עוגייה"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "ערך"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "כתובת URL חיפוש של ההעדפות שנשמרו"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"הערה: ציון ערכים מותאמים בתוך URL חיפוש יכול להפחית פרטיות תוך כדי הדלפת "
"מידע לאתרים שלחצת עליהם בעמוד התוצאות."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "כתובת אתר לשחזור ההעדפות שלך בדפדפן אחר"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"כתובת URL המכילה את ההעדפות שלך. ניתן להשתמש בכתובת URL זו כדי לשחזר את "
"ההגדרות שלך במכשיר אחר."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Hash העדפות העתקה"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "הכנס hash העדפות מועתק (ללא URL) על מנת לשחזר"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash העדפות"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "מזהה אוביקט דיגיטלי (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "מפענח Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "בחר שירות המשתמש בשכתוב DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"כרטיסייה זו לא קיימת בממשק המשתמש, אבל תוכל לחפש במנועי החיפוש הבאים "
"באמצעות !bangs"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "אפשר הכל"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "בטל הכל"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "תומך בשפה נבחרת"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "משקל"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "זמן מירבי"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "מפענח Favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "הצג Favicons קרוב לתוצאות החיפוש"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"הגדרות אלו מאוחסנות בתוך העוגיות שלך, אלו מאפשרות לנו להימנע מלאחסן את "
"מידע זה אודותיך."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr "עוגיות אלו משרתות את נוחותך הבלעדית, אנחנו לא משתמשים בהן כדי לעקוב אחריך."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "שמור"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "אפס העדפות"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "חזור"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "קיצורי-דרך"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "דמוי-Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"נווט בתוצאות החיפוש באמצעות קיצורי דרך (יש צורך ב-JavaScript). לחץ \"h\" "
"בדף הראשי או בדף התוצאות על מנת לקבל עזרה."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "פרוקסי תמונה"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "העבר תוצאות תמונה דרך פרוקסי מבעד SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "גלילה אינסופית"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "טען אוטומטית של העמוד הבא בעת גלילה לתחתית העמוד"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "באיזו שפה ברצונך לחפש?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "הפעל זיהוי-אוטומטי על מנת ש-SearXNG יזהה את שפת השאילתה שלך."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "שיטת HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "שנה את אופן הגשת הטפסים"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "שאילתא בכותרת העמוד"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"כאשר אפשרות זאת פעילה, כותרת עמוד התוצאות תכיל את השאילתא שלך. הדפדפן שלך"
" יכול לתעד את כותרת זאת"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "תוצאות בכרטיסיות חדשות"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "פתח קישורי תוצאה בתוך כרטיסיות דפדפן חדשות"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "סנן תוכן"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "חפש בעת בחירת קטגוריה"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr "בצע חיפוש מיידי אם נבחרה קטגוריה. בטל על מנת לבחור מספר קטגוריות"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "מוטיב"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "שנה את מערך SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "סגנון מוטיב"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "בחר אוטומטית כדי לשמור על התאמה עם הגדרות הדפדפן שלי"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "קוד (token) מנוע"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "קוד גישה (access token) למנועים פרטיים"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "שפת ממשק"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "שנה את שפת הממשק"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "עיצוב כתובת אתר"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "יפה"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "מלא"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "מארח"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "שנה עיצוב כתובת תוצאות"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "מאגרים"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "הצג מדיה"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "הסתר מדיה"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "אתר זה לא סיפק תיאור."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "גודל קובץ"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "תאריך"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "סוג"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "רזולוציה"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "פורמט"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "מנוע"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "צפה במקור"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "כתובת"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "הצג מפה"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "הסתר מפה"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "גרסה"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "משמר"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "עודכן ב"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "תגים"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "פופולאריות"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "רשיון"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "פרויקט"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "דף בית הפרויקט"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "תאריך פרסום"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "יומן"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "עורך"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "מפרסם"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "קישור magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "קובץ torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "זורעים"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "יונקים"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "מספר קבצים"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "הצג וידאו"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "הסתר וידאו"

#~ msgid "Engine time (sec)"
#~ msgstr "זמן מנוע (שניות)"

#~ msgid "Page loads (sec)"
#~ msgstr "עומס עמוד (שניות)"

#~ msgid "Errors"
#~ msgstr "שגיאות"

#~ msgid "CAPTCHA required"
#~ msgstr "נדרש אימות CAPTCHA"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "שכתוב קישורי HTTP לקישורי HTTPS כאשר ניתן"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "תוצאות נפתחות בתוך אותו חלון באופן "
#~ "שגרתי. תוסף זה משכתב את ההתנהגות "
#~ "השגרתית כדי לפתוח קישורים בתוך "
#~ "כרטיסיות/חלונות חדשים. (JavaScript נדרש)"

#~ msgid "Color"
#~ msgstr "צבע"

#~ msgid "Blue (default)"
#~ msgstr "כחול (שגרתי)"

#~ msgid "Violet"
#~ msgstr "סגול"

#~ msgid "Green"
#~ msgstr "ירוק"

#~ msgid "Cyan"
#~ msgstr "ציאן"

#~ msgid "Orange"
#~ msgstr "כתום"

#~ msgid "Red"
#~ msgstr "אדום"

#~ msgid "Category"
#~ msgstr "קטגוריה"

#~ msgid "Block"
#~ msgstr "חסום"

#~ msgid "original context"
#~ msgstr "הקשר מקורי"

#~ msgid "Plugins"
#~ msgstr "תוספים"

#~ msgid "Answerers"
#~ msgstr "תשובות"

#~ msgid "Avg. time"
#~ msgstr "זמן ממוצע"

#~ msgid "show details"
#~ msgstr "הצג פרטים"

#~ msgid "hide details"
#~ msgstr "הסתר פרטים"

#~ msgid "Load more..."
#~ msgstr "טען עוד..."

#~ msgid "Loading..."
#~ msgstr "כעת בטעינה..."

#~ msgid "Change searx layout"
#~ msgstr "שינוי ממשק searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "שליפת תוצאות תמונה דרך searx (מבעד Proxy)"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr ""

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "זוהי רשימה של עוגיות וערכיהן אשר searx מאחסנת על המחשב שלך."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "בעזרת רשימה זו, באפשרותך לגשת אל searx transparency."

#~ msgid "It look like you are using searx first time."
#~ msgstr "נראה כי אתם משתמשים ב-searx בפעם הראשונה."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "בבקשה, נסו מאוחר יותר. לחלופין, ניתן להיעזר בשירות searx אחר."

#~ msgid "Themes"
#~ msgstr "עיצובים"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "שיטה"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "לשונית זאת לא מופיעה לצורך תוצאות "
#~ "חיפוש, אולם באפשרותך לחפש במנועים "
#~ "הרשומים כאן דרך bang."

#~ msgid "Advanced settings"
#~ msgstr "הגדרות מתקדמות"

#~ msgid "Close"
#~ msgstr "סגור"

#~ msgid "Language"
#~ msgstr "שפה"

#~ msgid "broken"
#~ msgstr "שבור"

#~ msgid "supported"
#~ msgstr "נתמך"

#~ msgid "not supported"
#~ msgstr "לא נתמך"

#~ msgid "about"
#~ msgstr "אודות"

#~ msgid "Avg."
#~ msgstr "ממוצע"

#~ msgid "User Interface"
#~ msgstr "ממשק משתמש"

#~ msgid "Choose style for this theme"
#~ msgstr "בחר סגנון עבור עיצוב זה"

#~ msgid "Style"
#~ msgstr "סגנון"

#~ msgid "Show advanced settings"
#~ msgstr "הצג הגדרות מתקדמות"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "הצג לוח הגדרות מתקדמות בעמוד הבית כברירת מחדל"

#~ msgid "Allow all"
#~ msgstr "הפעל הכל"

#~ msgid "Disable all"
#~ msgstr "כבה הכל"

#~ msgid "Selected language"
#~ msgstr "שפה נבחרת"

#~ msgid "Query"
#~ msgstr "שאילתא"

#~ msgid "save"
#~ msgstr "שמור"

#~ msgid "back"
#~ msgstr "חזור"

#~ msgid "Links"
#~ msgstr "קישורים"

#~ msgid "RSS subscription"
#~ msgstr "הרשמת RSS"

#~ msgid "Search results"
#~ msgstr "תוצאות חיפוש"

#~ msgid "next page"
#~ msgstr "עמוד הבא"

#~ msgid "previous page"
#~ msgstr "עמוד קודם"

#~ msgid "Start search"
#~ msgstr "התחל חיפוש"

#~ msgid "Clear search"
#~ msgstr "נקה חיפוש"

#~ msgid "Clear"
#~ msgstr "נקה"

#~ msgid "stats"
#~ msgstr "סטטיסטיקה"

#~ msgid "Heads up!"
#~ msgstr "זהירות!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "נראה שאתה משתמש לראשונה בשירות של SearXNG."

#~ msgid "Well done!"
#~ msgstr "כל הכבוד!"

#~ msgid "Settings saved successfully."
#~ msgstr "הגדרות נשמרו בהצלחה."

#~ msgid "Oh snap!"
#~ msgstr "אבוי!"

#~ msgid "Something went wrong."
#~ msgstr "משהו השתבש."

#~ msgid "Date"
#~ msgstr "תאריך"

#~ msgid "Type"
#~ msgstr "טיפוס"

#~ msgid "Get image"
#~ msgstr "השג תמונה"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "העדפות"

#~ msgid "Scores per result"
#~ msgstr "דירוג לפי תוצאה"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "מנוע מטא-חיפוש המקפיד על פרטיות המשתמש (קוד פתוח)"

#~ msgid "No abstract is available for this publication."
#~ msgstr "אין תקציר זמין עבור כתב-עת זה."

#~ msgid "Self Informations"
#~ msgstr "מידע עצמי"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "שנה את האופן אשר בו טפסים נשלחים,"
#~ " <a "
#~ "href=\"https://he.wikipedia.org/wiki/Hypertext_Transfer_Protocol#.D7.A9.D7.99.D7.98.D7.95.D7.AA_.D7.91.D7.A7.D7.A9.D7.94\""
#~ " rel=\"external\">למידע נוסף אודות שיטות "
#~ "בקשה (request methods)</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "תוסף זה בודק אם הכתובת של הבקשה"
#~ " היא צומת יציאה של TOR, ומודיע "
#~ "למשתמש אם כן, כמו check.torproject.org "
#~ "אבל מ-searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "רשימת צומת היציאה של TOR "
#~ "(https://check.torproject.org/exit-addresses) אינה "
#~ "ניתנת לגישה."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "אתה משתמש ב-TOR. נראה שכתובת ה-IP שלך היא: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "אתה לא משתמש ב-TOR. נראה שכתובת ה-IP שלך היא: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "זיהוי שפת חיפוש אוטומטי"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "זהה אוטומטית את שפת החיפוש והחלף אליה בהתאם."

#~ msgid "others"
#~ msgstr "אחרים"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "כרטיסייה זאת לא מוצגת עבור תוצאות "
#~ "חיפוש, אולם באפשרותך לחפש את המנועים "
#~ "המנויים כאן בעזרת bang."

#~ msgid "Shortcut"
#~ msgstr "קיצור דרך"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "מנועים לא מסוגלים לאחזר תוצאות."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "באפשרותך לנסות שוב מאוחר יותר או לנסות שרת SearXNG אחר."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr "העבר מחדש לגרסאות open-access של כתבי-עת כאשר ישנן (נדרש Plugin)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "פועל"

#~ msgid "Off"
#~ msgstr "כבוי"

#~ msgid "Enabled"
#~ msgstr "מאופשר"

#~ msgid "Disabled"
#~ msgstr "מנוטרל"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "בצע חיפוש בלחיצה על קטגוריה. עליך "
#~ "לנטרל את תוסף זה אם ברצונך לבחור"
#~ " קטגוריות מרובות. (נדרש JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "מקשים חמים סגנון-Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "נווט בתוצאות בעזרת מקשים חמים כמו "
#~ "Vim (נדרש JavaScript). לחץ על מקש "
#~ "\"h\" במסך ראשי או תוצאות כדי לקבל"
#~ " עזרה."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr "לא מצאנו תוצאות. אנא נסו שאילתא אחרת או חפשו בתוך יותר קטגוריות."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "שכתב hostname של תוצאות או הסר תוצאות בהתבסס על hostname"

#~ msgid "Bytes"
#~ msgstr "בייטים"

#~ msgid "kiB"
#~ msgstr "קי״ב"

#~ msgid "MiB"
#~ msgstr "מי״ב"

#~ msgid "GiB"
#~ msgstr "גי״ב"

#~ msgid "TiB"
#~ msgstr "טי״ב"

#~ msgid "Hostname replace"
#~ msgstr "החלפת Hostname"

#~ msgid "Error!"
#~ msgstr "שגיאה!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "מנועים לא מסוגלים לאחזר תוצאות"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "התחל להגיש גיליון חדש ב- GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "מפיק ערך אקראי"

#~ msgid "Statistics functions"
#~ msgstr "פונקציות סטטיסטיקה"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "מחשבת {functions} של הארגומנטים"

#~ msgid "Get directions"
#~ msgstr "קבל כיוונים"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "מציגה כתובת IP המשוייכת לך אם "
#~ "השאילתא היא \"ip\" וגם סוכן משתמש "
#~ "אם השאילתא מכילה \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "לא ניתן להוריד את רשימת צמתי "
#~ "היציאה של טור מ: https://check.torproject.org"
#~ "/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "אתה משתמש בטור וזה נראה שיש לך את הIP הזה: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "אינך משתמש/ת ב Tor וזוהי כתובתך: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "מילות מפתח"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "ניתן להשתמש בציון הגדרות מותאמות אישית"
#~ " בכתובת ההעדפות כדי לסנכרן העדפות בין"
#~ " מכשירים."

#~ msgid "proxied"
#~ msgstr "פרוקסי"
