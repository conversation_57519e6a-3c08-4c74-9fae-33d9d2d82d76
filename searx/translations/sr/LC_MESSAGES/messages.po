# Serbian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON>, 2019
# jugi1, 2017
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-03-31 18:08+0000\n"
"Last-Translator: return42 <<EMAIL>>\n"
"Language: sr\n"
"Language-Team: Serbian "
"<https://translate.codeberg.org/projects/searxng/searxng/sr/>\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "без даљег подгруписања"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "други"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "датотеке"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "уопштено"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "музика"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "друштвене мреже"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "слике"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "видео снимци"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "радио"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "телевизија"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "ит"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "новости"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "мапа"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "лук"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "наука"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "апликације"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "речници"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "текст песме"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "пакети"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "q&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "репозиторијуми"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "софтверске енциклопедије"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "мрежа"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "Научне објаве"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "аутоматски"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "светло"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "мрачно"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "црно"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Време рада"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "О нама"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Просечна температура"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Облачност"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Стање"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Тренутно стање"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Вече"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Осећај"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Влажност"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Највиша темп."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Најмања темп."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Јутро"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Ноћ"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Подне"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Притисак"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Излазак Сунца"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Залазак Сунца"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Температура"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV индекс"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Видљивост"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Ветар"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "претплатници"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "пост"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "активни корисници"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "коментари"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "корисник"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "заједница"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "поени"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "наслов"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "аутор"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "отворено"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "затворено"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "одговорено"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Ставка није пронађена"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Извор"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Грешка приликом учитавања следеће странице"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Неважеће поставке, молимо уредите свој избор"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Неважећа подешавања"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "грешка у претрази"

#: searx/webutils.py:35
msgid "timeout"
msgstr "тајмаут"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "грешка при парсирању"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "грешка у HTTP протоколу"

#: searx/webutils.py:38
msgid "network error"
msgstr "грешка на мрежи"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL грешка: валидација сертификата није успела"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "неочекиван престанак рада"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP грешка"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "проблем при HTTPS конекцији"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "прокси грешка"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "превише захтева"

#: searx/webutils.py:58
msgid "access denied"
msgstr "није дозвољен приступ"

#: searx/webutils.py:59
msgid "server API error"
msgstr "серверска API грешка"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Суспендован"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "пре {minutes} минут(у,е,а)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "пре {hours} час(a) и {minutes} минут(у,е,а)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Генеришите различите случајне вредности"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Израчунај {func} за дате аргументе"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Прикажи руту на мапи .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ЗАСТАРЕЛО)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Овај унос је заменио"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Канал"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "битрата"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "гласови"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "кликови"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Језик"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} цитата од {firstCitationVelocityYear} до "
"{lastCitationVelocityYear} године"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Није могуће прочитати УРЛ те слике. Ово може бити због неподржаног "
"формата датотеке. ТинЕие подржава само слике које су ЈПЕГ, ПНГ, ГИФ, БМП,"
" ТИФФ или ВебП формата."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Слика је превише једноставна за проналажење подударања. ТинЕие захтева "
"основни ниво визуелних детаља да би успешно идентификовао подударања."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Није могуће преузети слику."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Оцена књиге"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Квалитет датотеке"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Израчунај математичке изразе кроз поље за претрагу"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Хеш плагин"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Претвара стринг у другачије хешеве."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "Излаз хеш функције"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Хостнејмс плагин"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "Преуреди домене, уклони или промени приоритет резултата према домену"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Отворени приступ DOI преписа"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "Избегните плаћање у случају да је доступна бесплатна публикација"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Licna Informacija"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Приказује Вашу IP адресу ако је упит \"ip\" и Вашег корисничког агента "
"ако је упит \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Ваш IP је: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Ваш кориснички агент је: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Додатак за проверу Тор-а"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Овај додатак проверава да ли је адреса захтева излазни чвор ТОР-а и "
"обавештава корисника ако јесте, као check.torproject.org али са SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Није могуће преузети листу торових излазних нодова од"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Користите Тор и могуће је да имате екстерну IP адресу"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Ne kористите Тор и имате екстерну IP адресу"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Уклони трекер URL адресе"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Уклања аргументе трекера од повратне URL адресе"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Конвертуј јединице"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Страница није пронађена"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Иди на %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "Претражи страницу"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Донирај"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Подешавања"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Покреће"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "Javni meta pretrazivac koji postuje privatnost"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Изворни код"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Трагач проблема"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Статистика"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Јавне инстанце"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Политика приватности"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Контактирај домара инстанце"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Кликни на лупу за претрагу"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Дужина"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Прегледи"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Аутор"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "кеширано"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Почните да шаљете ново издање на ГитХуб-у"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Проверите да ли постоје грешке у вези са овим енџином на ГитХуб-у"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Потврђујем да не постоји грешка у вези са проблемом на који наилазим"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Ако је ово јавна инстанца, наведите УРЛ у извештају о грешци"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Пријавите нову грешку на Гитхабу укључујући следеће инфоржације"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Нема HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Погледајте информације о грешки и пријавите"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang за овај мотор"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang за своје категорије"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Медијана"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "П80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "П95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Неуспели тест(ови) провере: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Грешке:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Уопштено"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Подразумеване категорије"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Кориснички интерфејс"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Приватност"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Претраживачи"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Тренутно коришћени претраживачи"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Посебни упити"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Колачићи"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Број резултата"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Информације"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Назад на врх"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Претходна страница"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Следећа страница"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Прикажи насловну страну"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Тражи ..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "очисти"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "Претрага"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Тренутно нема доступних података."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Име претраживача"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Резултати"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Број резултата"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Време одзива"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Поузданост"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Укупно"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "ХТТП"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Обрада"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Упозорења"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Грешке и изузеци"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Изузетак"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Порука"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Проценат"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Параметар"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Назив документа"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Функција"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Код"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Проверник"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Неуспели тест"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Коментар(и)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Примери"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Дефиниције"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Синоними"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Одговори"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Резултати преузимања"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Покушај да нађеш:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Поруке из претраживача"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Тражи URL адресу"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Копирано"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Копирај"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Предлози"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Језик претраге"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Подразумевани језик"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Аутоматски откриј"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Безбедна Претрага"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Стриктно"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Умерено"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Ништа"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Временски опсег"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Било када"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Последњи дан"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Последња недеља"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Последњи месец"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Последња година"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Информације!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "тренутно, нема дефинисаних колачића."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Опростите!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Нису пронађени резултати. Можете покушати да:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Нема више резултата. Можете покушати да:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Освежите страницу."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Потражите други упит или да изаберете другу категорију (изнад)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Промените претраживач који се користи у подешавањима:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Пребаците се на другу инстанцу:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Потражите други упит или изаберите другу категорију."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Вратите се на претходну страницу помоћу дугмета за претходну страницу."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Допусти"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Кључне речи (прва реч у упиту)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Име"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Опис"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Ово је листа СеарКСНГ-ових модула за тренутно јављање."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Ово је листа додатака."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Ауто попуњавање"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Пронађите док куцате"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Поравнање по средини"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Приказује резултате на центру странице (Оскар распоред)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Ово је листа колачића и њихових вредности које СеарКСНГ чува на вашем "
"рачунару."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Са том листом можете проценити транспарентност СеарКСНГ-а."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Име колачића"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Вредност"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Pretražite URL адресу тренутно сачуваних поставки"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Напомена: навођење прилагођених поставки у URL претрази може смањити "
"приватност цурењем података кликнутих страница."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "УРЛ да бисте вратили своја подешавања у другом претраживачу"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Копирај хеш преференци"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Унесите копирани хеш преференци (без УРЛ-а) за повратак"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Хеш преференци"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Дигитални Идентификатор Објеката (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Отворени приступ DOI решења"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Изаберите услугу коју користи ДОИ изновопис"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ова картица не постоји у корисничком интерфејсу, али можете претраживати "
"у овим машинама по његовим !bangs-има."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Омогући све"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Онемогући све"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Подржава изабрани језик"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Тежина"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Макс. време"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Резолвер иконица сајтова"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Прикажи иконице поред резултата претраге"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Ова подешавања се чувају у вашим колачићима, што нам омогућава да не "
"сачувамо ове податке о вама."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Ови колачићи служе Вашој погодности, ми не користимо ове колачиће да вас "
"пратимо."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Сачувати"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Врати на подразумевано"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Назад"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Пречаци"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Слично Вим-у"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Крећите се кроз резултате претраге помоћу пречаћних тастера (потребан је "
"JavaScript). Притисните тастер \"х\" на главној страници или страници са "
"резултатима да бисте добили помоћ."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Прокси слика"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Прокси резултат слике преко СеарКСНГ"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Бесконачно померање"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Аутоматско учитавање следеће странице приликом померања на дно текуће "
"странице"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Који језик преферирате за претрагу?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Одаберите Ауто-детецт да бисте дозволили SearXNG да открије језик вашег "
"упита."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "ХТТП метода"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Промените начин слања образаца"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Упит у наслову странице"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Када је омогућено, наслов странице са резултатима садржи ваш упит. Ваш "
"претраживач може да сними овај наслов"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Резултати на картицама"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Отворите линкове са резултатима на новим картицама претраживача"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Филтрирајте садржај"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Тражите категорију избора"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Извршите претрагу одмах ако је одабрана категорија. Онемогућите да бисте "
"изабрали више категорија"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Тема"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Промените изглед СеарКСНГ-а"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Изглед теме"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Изаберите аутоматски да бисте пратили подешавања вашег претраживача"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Енџин жетони"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Приступите жетонима за приватне енџине"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Језик интерфејса"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Промените језик сајта"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL форматирање"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Лепо"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Целокупно"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Изворно"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Измени формат приказивања URL резултата"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "репозиторијуми"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "покажи медије"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "сакриј медије"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Овај сајт није дао никакав опис."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "величина фајла"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Датум"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tip"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Резолуција"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Формат"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Енџин"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Види извор"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "адреса"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "покажи мапу"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "сакриј мапу"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Верзија"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Одржавалац"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Обновљен у"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tagovi"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Популарност"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Лиценца"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Пројекат"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Главна страница пројекта"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Датум објављивања"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Дневник"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Едитор"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Издавач"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "ДОИ"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ИССН"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ИСБН"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "магнет линк"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "торент фајл"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Хранилац"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Личер"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Број фајлова"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "покажи видео"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "сакриј видео"

#~ msgid "Engine time (sec)"
#~ msgstr "Време претраге (сек)"

#~ msgid "Page loads (sec)"
#~ msgstr "Учитавање странице (сек)"

#~ msgid "Errors"
#~ msgstr "Грешке"

#~ msgid "CAPTCHA required"
#~ msgstr "Потребна је ЦАПТЦХА"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Препишите HTTP линкове у HTTPS ако је могуће"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Резултати се отварају у истом прозору."
#~ " Овај додатак преписује подразумевано "
#~ "понашање како би отворио везе на "
#~ "новим картицама / прозорима. (Потребан "
#~ "је JavaScript )"

#~ msgid "Color"
#~ msgstr "Боја"

#~ msgid "Blue (default)"
#~ msgstr "Плава (подразумевано)"

#~ msgid "Violet"
#~ msgstr "Љубичаста"

#~ msgid "Green"
#~ msgstr "Зелена"

#~ msgid "Cyan"
#~ msgstr "Цијан"

#~ msgid "Orange"
#~ msgstr "Наранџаста"

#~ msgid "Red"
#~ msgstr "Црвена"

#~ msgid "Category"
#~ msgstr "Категорија"

#~ msgid "Block"
#~ msgstr "Блокирај"

#~ msgid "original context"
#~ msgstr "оригинални садржај"

#~ msgid "Plugins"
#~ msgstr "Додаци"

#~ msgid "Answerers"
#~ msgstr "Одговори"

#~ msgid "Avg. time"
#~ msgstr "Просечно време"

#~ msgid "show details"
#~ msgstr "покажи детаље"

#~ msgid "hide details"
#~ msgstr "сакриј детаље"

#~ msgid "Load more..."
#~ msgstr "Учитај више..."

#~ msgid "Loading..."
#~ msgstr "Учитавање..."

#~ msgid "Change searx layout"
#~ msgstr "Промените изглед searx сајта"

#~ msgid "Proxying image results through searx"
#~ msgstr "Прокси слике преко searx-а"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Ово је листа searx инстант одговора."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "Ово је листа колачића и њихова вредност се снима на вашем рачунару."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Са овом листом можете бити searx транспаренти"

#~ msgid "It look like you are using searx first time."
#~ msgstr "Изгледа да први пут користите searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Молимо, покушајте поново касније."

#~ msgid "Themes"
#~ msgstr "Теме"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Метода"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Напредне поставке"

#~ msgid "Close"
#~ msgstr "Затвори"

#~ msgid "Language"
#~ msgstr "Језик"

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "подржано"

#~ msgid "not supported"
#~ msgstr "неподржано"

#~ msgid "about"
#~ msgstr "О сајту"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Изаберите стил за ову тему"

#~ msgid "Style"
#~ msgstr "Стил"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "Дозволи све"

#~ msgid "Disable all"
#~ msgstr "Онемогући све"

#~ msgid "Selected language"
#~ msgstr "Изабрани језик"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "сними"

#~ msgid "back"
#~ msgstr "назад"

#~ msgid "Links"
#~ msgstr "Линкови"

#~ msgid "RSS subscription"
#~ msgstr "РСС претплата"

#~ msgid "Search results"
#~ msgstr "Резултати претраге"

#~ msgid "next page"
#~ msgstr "наредна страница"

#~ msgid "previous page"
#~ msgstr "претходна страница"

#~ msgid "Start search"
#~ msgstr "Почни претрагу"

#~ msgid "Clear search"
#~ msgstr "Очистите претражилац"

#~ msgid "Clear"
#~ msgstr "Очистите"

#~ msgid "stats"
#~ msgstr "статистика"

#~ msgid "Heads up!"
#~ msgstr "Главу горе!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Одлично!"

#~ msgid "Settings saved successfully."
#~ msgstr "Подешавања успешно сачувана."

#~ msgid "Oh snap!"
#~ msgstr "Упс!"

#~ msgid "Something went wrong."
#~ msgstr "Нешто је пошло наопако."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Узми слику"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "ПОДЕШАВАЊА"

#~ msgid "Scores per result"
#~ msgstr "Остварени резултати"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "мета-претраживач који поштује приватност"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Абстракт није доступан за ову публикацију."

#~ msgid "Self Informations"
#~ msgstr "Информације о себи"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Промените начин слања форме, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">сазнајте више о методама "
#~ "захтева</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Овај додатак проверава да ли је "
#~ "адреса захтева излазни чвор ТОР-а и "
#~ "обавештава корисника ако јесте, као "
#~ "check.torproject.org али са searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Листа излазних чворова ТОР-а "
#~ "(https://check.torproject.org/exit-addresses) је "
#~ "недоступна."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Користите ТОР. Ваша ИП адреса је: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Не користите ТОР. Ваша ИП адреса је: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "остали"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Ова картица није приказана за резултате"
#~ " претраге, али можете претраживати енџине"
#~ " наведене овде преко шишких."

#~ msgid "Shortcut"
#~ msgstr "Пречица"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Нема резултата."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Молимо, покушајте касније или пронађите другу СеарКСНГ инстанцу."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Преусмери на верзије публикација отвореног "
#~ "приступа кад је доступно (потребан је"
#~ " плагин)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Promeni nacin slanja formi, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">saznaj vise o request "
#~ "metodama</a>"

#~ msgid "On"
#~ msgstr "Укључено"

#~ msgid "Off"
#~ msgstr "Искључено"

#~ msgid "Enabled"
#~ msgstr "Омогућено"

#~ msgid "Disabled"
#~ msgstr "Онемогућено"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Одмах извршите претрагу ако је изабрана"
#~ " категорија. Онемогућите да би изабрали "
#~ "више категорија. (Потребан је JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim стил пречице"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Померите резултате претраге помоћу Vim-пречица"
#~ " (потребан је JavaScript ). Притисните "
#~ "тастер \"h\" на главној или резултатној"
#~ " страници да бисте добили помоћ."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "нема никавих резултата претраге. Молимо "
#~ "покишајте другу претрагу или категорију."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Поново упишите име хостинга или "
#~ "избришите резултате базиране на имену "
#~ "хостинга"

#~ msgid "Bytes"
#~ msgstr "Бајта"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Замени име хостинга"

#~ msgid "Error!"
#~ msgstr "Грешка!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Не може повратити резултате"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Почните да шаљете ново издање на ГитХуб-у"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Генератор случајних вредности"

#~ msgid "Statistics functions"
#~ msgstr "Статистичке функције"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Израчунајте {functions} аргумената"

#~ msgid "Get directions"
#~ msgstr "Упутства за правац"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Прикажите своју IP адресу ако је "
#~ "упит \"ip\" и ако кориснички агент "
#~ "садржи \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Није могуће преузети листу Тор излазних"
#~ " чворова са: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Koristis Tor i izgleda da je ovo"
#~ " tvoja externlana IP addresa : "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Не користите Тор и имате ову спољну ИП адресу: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Кључне речи"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Одређивање прилагођених подешавања у УРЛ-у "
#~ "може да се користи за синхронизацију "
#~ "подешавања на свим уређајима."

#~ msgid "proxied"
#~ msgstr "прокси"

