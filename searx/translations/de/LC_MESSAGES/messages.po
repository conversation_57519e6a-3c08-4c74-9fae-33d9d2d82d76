# German translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON>mstam, 2017
# <PERSON>, 2014-2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <jona.ab<PERSON><PERSON>@gmail.com>, 2016
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2017
# Bamstam, 2019
# <AUTHOR> <EMAIL>, 2015
# pointhi, 2014
# rike, 2014
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2014
# <PERSON>, 2016-2017
# <PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-01 06:39+0000\n"
"Last-Translator: return42 <<EMAIL>>\n"
"Language-Team: German <https://translate.codeberg.org/projects/searxng/"
"searxng/de/>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "ohne weitere Untergruppierung"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "Andere"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "Dateien"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "Allgemein"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "Musik"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "Soziale Medien"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "Bilder"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "Videos"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "Radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "TV"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "Nachrichten"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "Karte"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "Wissenschaft"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "Apps"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "Lexika"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "Songtexte"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "Pakete"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "Q&A"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "Repositories"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "Software Wikis"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "WEB"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "wissenschaftliche Veröffentlichungen"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatisch"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "hell"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "dunkel"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "black"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Laufzeit"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Über"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Mittlere Temp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Bewölkung"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Bedingung"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Aktuelle Bedingung"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Abends"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Gefühlt wie"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Luftfeuchtigkeit"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Max."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Min."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Morgens"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nachts"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Mittags"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Luftdruck"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Sonnenaufgang"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Sonnenuntergang"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatur"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV-Index"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Sichtweite"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Wind"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "klarer Himmel"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "bewölkt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "heiter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Nebel"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Starkregen und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Starke Regenschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Starke Regenschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Starkregen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Starker Schneeregen und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Heftige Schneeregenschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Starke Schneeregenschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Starker Schneeregen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Starker Schneefall und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Starke Schneeschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Starke Schneeschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Starker Schneefall"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Leichter Regen und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Leichte Regenschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Leichte Regenschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Leichter Regen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Leichter Schneeregen und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Leichte Schneeregenschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Leichte Schneeregenschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Leichter Schneeregen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Leichter Schneefall und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Leichte Schneeschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Leichte Schneeschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Leichter Schneefall"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Teilweise bewölkt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Regen und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Regenschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Regenschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Regen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Schneeregen und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Schneeregenschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Schneeregenschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Schneeregen"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Schnee und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Schneeschauer und Gewitter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Schneeschauer"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Schnee"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Abonnenten"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "Beiträge"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktive Nutzer"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "Kommentare"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "Benutzer"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "Community"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "Punkte"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "Titel"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "Autor/-in"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "offen"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "geschlossen"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "beantwortet"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Keine Einträge gefunden"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Quelle"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Fehler beim Laden der nächsten Seite"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ungültige Einstellungen, bitte Einstellungen ändern"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ungültige Einstellungen"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "Suchfehler"

#: searx/webutils.py:35
msgid "timeout"
msgstr "Zeitüberschreitung"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "Fehler beim Parsen"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-Protokollfehler"

#: searx/webutils.py:38
msgid "network error"
msgstr "Netzwerkfehler"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL Fehler: Zertifikatsprüfung ist fehlgeschlagen"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "unerwarteter Absturz"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-Fehler"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-Verbindungsfehler"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "Proxy-Fehler"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "zu viele Anfragen"

#: searx/webutils.py:58
msgid "access denied"
msgstr "Zugriff verweigert"

#: searx/webutils.py:59
msgid "server API error"
msgstr "Server-API-Fehler"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Ausgesetzt"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "vor {minutes} Minute(n)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "vor {hours} Stunde(n), {minutes} Minute(n)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Erzeugt diverse Zufallswerte"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Berechne {func} zu den Argumenten"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Routenplaner .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLET)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Dieser Eintrag wurde überschrieben von"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "Bitrate"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "Stimmen"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "Clicks"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Sprache"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} Zitierungen in den Jahren {firstCitationVelocityYear} bis "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Die URL von dem Bild konnte nicht gelesen werden. Dies kann auf ein nicht"
" unterstütztes Dateiformat zurückzuführen sein. TinEye unterstützt nur "
"Bilder im Format JPEG, PNG, GIF, BMP, TIFF oder WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Das Bild ist zu einfach um Übereinstimmungen zu finden. TinEye benötigt "
"ein grundlegendes Maß an visuellen Details, um erfolgreich "
"Übereinstimmungen zu erkennen."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Das Bild konnte nicht heruntergeladen werden."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Buchbewertung"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Dateiqualität"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia blacklist"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtern der Onion Links, die in der schwarzen Liste von Ahmia erscheinen."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Taschenrechner"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Rechne mathematische Ausdrücke mit der Suchleiste aus"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash Werte berechnen"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Konvertiert Zeichenketten in verschiedene Hashwerte."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "Hashwert"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Hostnames plugin"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Umschreiben von Hostnamen, Entfernen von Ergebnissen oder Priorisieren "
"von Ergebnissen auf der Grundlage des Hostnamens"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Open-Access-DOI umschreiben"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Bezahlbeschränkungen durch die Weiterleitung zu der verfügbaren Open-"
"Access-Version vermeiden"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Selbstauskunft"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Zeigt Ihre IP an, wenn die Abfrage \"ip\" lautet, und Ihren User-Agent, "
"wenn die Abfrage \"user-agent\" lautet."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "IP: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "User-Agent: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor Prüf-Plugin"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Dieses Plugin prüft, ob die Adresse der Anfrage ein Tor-Exit-Node ist, "
"und informiert den Benutzer, wenn dies der Fall ist; wie "
"check.torproject.org, aber von SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Konnte die Liste der Tor-Exit-Nodes nicht herunterladen von"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""
"Sie benutzen Tor und es sieht so aus, als hätten Sie die externe IP-"
"Adresse"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Sie benutzen kein Tor und haben die externe IP-Adresse"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Tracker-URL-Entferner"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Tracker-Argumente von den zurückgegebenen URLs entfernen"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Einheitenumrechner"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Einheiten umrechnen"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Seite nicht gefunden"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Gehe zu %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "Suchseite"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Spenden"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Einstellungen"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Betrieben mit"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "Eine privatsphären-respektierende, offene Metasuchmaschine"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Quellcode"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Bugtracker"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Suchmaschinenstatistiken"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Öffentliche Instanzen"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Datenschutzerklärung"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontakt zum Betreuer der Instanz"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "klicke auf die Lupe, um die Suche zu starten"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Länge"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Aufrufe"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "Im Cache"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Fehlerbericht auf GitHub erstellen"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Überprüfe bitte auf bereits existierende Fehlereinträge auf GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Ich bestätige, dass es für das Problem, auf das ich stoße, keinen "
"existierenden Fehlereintrag gibt"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Wenn es sich um eine öffentliche Instanz handelt, gib bitte die URL in "
"dem Fehlerbericht an"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Erstelle mit den oben stehenden Informationen auf GitHub einen neuen "
"Problembericht"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Kein HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Fehlerprotokolle einsehen und einen Fehlerbericht einreichen"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang für diese Suchmaschine"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang für ihre Kategorien"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Median"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Fehlgeschlagene(r) Checker-Test(s): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Fehler:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Allgemein"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Standardkategorien"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Benutzeroberfläche"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privatsphäre"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Suchmaschinen"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Aktuell benutzte Suchmaschinen"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Besondere Abfragen"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Trefferanzahl"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Information"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Zurück zum Anfang"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Vorherige Seite"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Nächste Seite"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Zur Startseite wechseln"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Suche nach..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "leeren"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "suchen"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Es sind derzeit keine Daten vorhanden. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Suchmaschinenname"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Punkte"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Ergebnisanzahl"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Antwortzeit"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Zuverlässigkeit"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Insgesamt"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Verarbeitung"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Warnungen"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Fehler und Ausnahmen"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Ausnahmefehler"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Meldung"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Prozentsatz"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Dateiname"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funktion"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Code"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Checker"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Test fehlgeschlagen"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Kommentar(e)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Beispiele"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definitionen"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonyme"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Gefühlt wie"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Antworten"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Ergebnisse herunterladen"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Suche nach:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Meldungen der Suchmaschinen"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "Sek."

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Such-URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "kopiert"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "kopieren"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Vorschläge"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Such-Sprache/-Region"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Standardsprache"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Spracherkennung"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Sichere Suche"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Streng"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderat"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Keine"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Zeitbereich"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "beliebiger Zeitpunkt"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Letzter Tag"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Letzte Woche"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Letzter Monat"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Letztes Jahr"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Information!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "Derzeit sind keine Cookies gespeichert."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Entschuldigung!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Es konnten keine Suchergebnisse ermittelt werden:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Es gibt keine weiteren Ergebnisse zu dem Suchbegriff:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Die Seite neuladen."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Einen anderen Suchbegriff verwenden oder die Kategorie (oben) wechseln."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Ändern der verwendeten Suchmaschinen in den Einstellungen:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Wechseln zu einer anderen SearXNG instanz:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Suchbegriff ändern oder Kategorie wechseln."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Zurück zur vorherigen Seite über unten stehenden Button."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Erlauben"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Schlüsselwort (erstes Wort in der Suchanfrage)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Name"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Beschreibung"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Dies ist die Liste der in SearXNG verfügbaren Module für Sofortantworten."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Dies ist die Liste der Plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Autovervollständigung"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Die Autovervollständigung zeigt Vorschläge während der Eingabe an"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Mittig"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Zeigt die Ergebnisse in der Mitte der Seite an."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Die nachfolgende Liste zeigt alle Cookies, die SearXNG auf deinem "
"Computer speichert."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Mit dieser Liste können Sie die Transparenz von SearXNG einschätzen."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Cookie-Name"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Wert"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Such-URL für die aktuell gespeicherten Einstellungen"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Hinweis: Das Festlegen eigener Einstellungen in der Such-URL kann Ihre "
"Privatsphäre reduzieren, weil gegebenenfalls ungewollt Daten an die "
"ausgewählten Ergebnisseiten übermittelt werden."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL zur Wiederherstellung der Einstellungen in einem anderen Browser"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"URL die Ihre Einstellungen enthält. Diese URL kann verwendet werden, um "
"Ihre Einstellungen auf einem anderen Gerät wiederherzustellen"

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Einstellungen kopieren"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Fügen Sie den kopierten Einstellungen (ohne URL) zum Wiederherstellen ein"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Einstellungen (ohne URL)"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital Object Identifier (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI resolver"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Wähle den Dienst für DOI Rewrite"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Diese Registerkarte ist in der Benutzeroberfläche nicht vorhanden, aber "
"in Suchmaschinen kann mittels !bang gesucht werden."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Alle aktivieren"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Alle deaktivieren"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Sprachen"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Gewichtung"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "max. Zeit"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon Anbieter"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Anzeigen der Favicons neben dem Suchergebnis"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Diese Informationen werden in Cookies auf Ihrem Rechner gespeichert, "
"damit wir keine Ihrer persönlichen Daten speichern müssen."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Diese Cookies dienen einzig Ihrem Komfort. Wir verwenden sie nicht, um "
"Sie zu überwachen."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Speichern"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Zurücksetzen"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Zurück"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Funktionstasten"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim Stil"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigiere die Suchergebnisse mit Hotkeys (JavaScript benötigt). Drücke "
"\"h\" auf der Haupt- oder Ergebnisseite für Hilfe."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Bilder-Proxy"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Bilder über den Proxy von SearXNG laden"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Unendliches Scrollen"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Lädt automatisch die nächste Seite, wenn das Ende der aktuellen Seite "
"erreicht wurde"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Welche Sprache oder Region soll bei der Suche bevorzugt werden?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Mit der Spracherkennung wird die Sprache automatisch erkannt."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Methode"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Ändere wie Formulare übertragen werden"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Suchbegriff im Titel anzeigen"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Bei Aktivierung wird der Suchbegriff im Titel und der Historie des "
"Browsers angezeigt"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Ergebnisse in neuem Tab"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Links in einem neuen Browser-Tab öffnen"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Inhalte filtern"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Suche starten, wenn Kategorie angeklickt wird"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Suche direkt nach dem Wechseln der Kategorie ausführen. Um mehrere "
"Kategorien auswählen zu können deaktivieren"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Design"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Ändere das Aussehen von SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Designstil"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Wähle auto um die Browsereinstellungen zu übernehmen"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Maschinentoken"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Zugangstoken für private Suchmaschinen"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Oberflächensprache"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "ändere die Sprache des Layouts"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL Anzeige"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Formatiert"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Vollständig"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Hostname"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Ändern der URL Anzeige in den Ergebnissen"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "Repository"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "Medien anzeigen"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "Medien verstecken"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Diese Seite besitzt keine Beschreibung."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Dateigröße"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Datum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Medium"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Auflösung"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Suchmaschine"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Seite besuchen"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "Adresse"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "Karte anzeigen"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "Karte verstecken"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Version"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Betreuer"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Letzte Aktualisierung"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Schlagwörter"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularität"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lizenz"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projekt Homepage"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Erscheinungsdatum"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Fachzeitschrift"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redaktion"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Herrausgeber"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "Magnet Link"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "Torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeder"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Anzahl der Dateien"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "Video anzeigen"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "Video verstecken"

#~ msgid "Engine time (sec)"
#~ msgstr "Suchmaschinen Zeit (sek)"

#~ msgid "Page loads (sec)"
#~ msgstr "Ladezeit (sek)"

#~ msgid "Errors"
#~ msgstr "Fehler"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA erforderlich"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Wandelt wenn möglich HTTP-Links in HTTPS-Links um"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Links werden normalerweise im gleichen "
#~ "Fenster geöffnet. Dieses Plugin überschreibt"
#~ " dieses Verhalten und öffnet Links in"
#~ " einem neuen Tab bzw. Fenster.\n"
#~ "(JavaScript wird benötigt)"

#~ msgid "Color"
#~ msgstr "Farbe"

#~ msgid "Blue (default)"
#~ msgstr "Blau (Standard)"

#~ msgid "Violet"
#~ msgstr "Violett"

#~ msgid "Green"
#~ msgstr "Grün"

#~ msgid "Cyan"
#~ msgstr "Cyan"

#~ msgid "Orange"
#~ msgstr "Orange"

#~ msgid "Red"
#~ msgstr "Rot"

#~ msgid "Category"
#~ msgstr "Kategorie"

#~ msgid "Block"
#~ msgstr "Blockieren"

#~ msgid "original context"
#~ msgstr "ursprüngliche Seite"

#~ msgid "Plugins"
#~ msgstr "Erweiterungen"

#~ msgid "Answerers"
#~ msgstr "Antworten"

#~ msgid "Avg. time"
#~ msgstr "mittlere Zeit"

#~ msgid "show details"
#~ msgstr "Details anzeigen"

#~ msgid "hide details"
#~ msgstr "Details verstecken"

#~ msgid "Load more..."
#~ msgstr "Lade mehr..."

#~ msgid "Loading..."
#~ msgstr "Lade..."

#~ msgid "Change searx layout"
#~ msgstr "ändere das Aussehen von searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Bilder über einen Proxy an Searx weiterleiten"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Dies ist die Liste der in searx verfügbaren Module für Sofortantworten."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Die nachfolgende Liste zeigt alle "
#~ "Cookies, die searx auf deinem Computer"
#~ " speichert."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Mit dieser Liste können Sie die Transparenz von searx einschätzen."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Es sieht so aus, als würden Sie searx zum ersten Mal verwenden."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Bitte später nochmals versuchen oder eine andere Instanz verwenden."

#~ msgid "Themes"
#~ msgstr "Designs"

#~ msgid "Reliablity"
#~ msgstr "Zuverlässigkeit"

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Methode"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Diese Registerkarte zeigt keine Suchergebnisse"
#~ " an, aber Sie können die hier "
#~ "aufgelisteten Suchmaschinen über bangs (!) "
#~ "durchsuchen."

#~ msgid "Advanced settings"
#~ msgstr "Erweiterte Einstellungen"

#~ msgid "Close"
#~ msgstr "Schließen"

#~ msgid "Language"
#~ msgstr "Sprache"

#~ msgid "broken"
#~ msgstr "kaputt"

#~ msgid "supported"
#~ msgstr "Unterstützt"

#~ msgid "not supported"
#~ msgstr "Nicht unterstützt"

#~ msgid "about"
#~ msgstr "Über uns"

#~ msgid "Avg."
#~ msgstr "Avg."

#~ msgid "User Interface"
#~ msgstr "Benutzeroberfläche"

#~ msgid "Choose style for this theme"
#~ msgstr "Stil für dieses Thema auswählen"

#~ msgid "Style"
#~ msgstr "Aussehen"

#~ msgid "Show advanced settings"
#~ msgstr "Erweiterte Einstellungen anzeigen"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""
#~ "Standardmäßig das Panel für erweiterte "
#~ "Einstellungen auf der Startseite anzeigen"

#~ msgid "Allow all"
#~ msgstr "Alle zulassen"

#~ msgid "Disable all"
#~ msgstr "Alle deaktivieren"

#~ msgid "Selected language"
#~ msgstr "Ausgewählte Sprache"

#~ msgid "Query"
#~ msgstr "Abfrage"

#~ msgid "save"
#~ msgstr "Speichern"

#~ msgid "back"
#~ msgstr "Zurück"

#~ msgid "Links"
#~ msgstr "Links"

#~ msgid "RSS subscription"
#~ msgstr "RSS-Abonnement"

#~ msgid "Search results"
#~ msgstr "Suchergebnisse"

#~ msgid "next page"
#~ msgstr "nächste Seite"

#~ msgid "previous page"
#~ msgstr "vorherige Seite"

#~ msgid "Start search"
#~ msgstr "Suche starten"

#~ msgid "Clear search"
#~ msgstr "Suche löschen"

#~ msgid "Clear"
#~ msgstr "löschen"

#~ msgid "stats"
#~ msgstr "Statistiken"

#~ msgid "Heads up!"
#~ msgstr "Achtung!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Es sieht so aus, als würden Sie SearXNG zum ersten Mal verwenden."

#~ msgid "Well done!"
#~ msgstr "Gut gemacht!"

#~ msgid "Settings saved successfully."
#~ msgstr "Einstellungen wurden erfolgreich gespeichert."

#~ msgid "Oh snap!"
#~ msgstr "Oh nein!"

#~ msgid "Something went wrong."
#~ msgstr "Irgendetwas ist falsch gelaufen."

#~ msgid "Date"
#~ msgstr "Datum"

#~ msgid "Type"
#~ msgstr "Typ"

#~ msgid "Get image"
#~ msgstr "Bild ansehen"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "Einstellungen"

#~ msgid "Scores per result"
#~ msgstr "Punkte pro Treffer"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "eine privatsphären-respektierende, hackbare Metasuchmaschine"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Keine Zusammenfassung für die Veröffentlichung verfügbar."

#~ msgid "Self Informations"
#~ msgstr "Selbstauskunft"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "ändere wie Formulare übertragen werden, "
#~ "<a "
#~ "href=\"https://de.wikipedia.org/wiki/Hypertext_Transfer_Protocol"
#~ "#HTTP-Anfragemethoden\" rel=\"external\">lerne mehr "
#~ "über Anfragemethoden</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Dieses Plugin prüft, ob es sich "
#~ "bei der Adresse der Anfrage um "
#~ "einen TOR-Exit-Knoten handelt und "
#~ "informiert den Benutzer, wenn dies der"
#~ " Fall ist. Vergleichbar mit "
#~ "check.torproject.org aber innerhalb von "
#~ "SearXNG."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Die Liste der TOR Exit-Nodes kann"
#~ " nicht geladen werden "
#~ "(https://check.torproject.org/exit-addresses)."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Sie verwenden TOR. Die IP Adresse ist: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Sie verwenden kein TOR. Die IP Adresse ist: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Automatische Erkennung der Suchsprache"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""
#~ "Automatische Erkennung der Suchsprache und "
#~ "Umschaltung auf diese Sprache."

#~ msgid "others"
#~ msgstr "Andere"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Auf dieser Registerkarte werden keine "
#~ "Suchergebnisse angezeigt, aber Sie können "
#~ "die hier aufgelisteten Suchmaschinen über "
#~ "bangs durchsuchen."

#~ msgid "Shortcut"
#~ msgstr "Abkürzung"

#~ msgid "!bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Diese Registerkarte ist in der "
#~ "Benutzeroberfläche nicht vorhanden, aber in"
#~ " Suchmaschinen kann mittels !bang gesucht"
#~ " werden."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Suchmaschinen können die Ergebnisse nicht empfangen."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Bitte versuche es später noch einmal "
#~ "oder wähle eine andere SearXNG Instanz."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Weiterleitung zu frei zugänglichen Versionen"
#~ " von Veröffentlichungen, wenn verfügbar "
#~ "(Plugin benötigt)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "ändere wie Formulare übertragen werden, "
#~ "<a "
#~ "href=\"https://de.wikipedia.org/wiki/Hypertext_Transfer_Protocol"
#~ "#HTTP-Anfragemethoden\" rel=\"external\">lerne mehr "
#~ "über Anfragemethoden</a>"

#~ msgid "On"
#~ msgstr "Ein"

#~ msgid "Off"
#~ msgstr "Aus"

#~ msgid "Enabled"
#~ msgstr "Aktiviert"

#~ msgid "Disabled"
#~ msgstr "Deaktiviert"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Die Suche sofort starten, wenn eine "
#~ "Kategorie ausgewählt wird. Es ist dann"
#~ " nicht mehr möglich, mehrere Kategorien "
#~ "auszuwählen. (JavaScript wird benötigt)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "An Vim angelehnte Tastenkombinationen"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "In der Ergebnisseite mit Vim-ähnlichen"
#~ " Tastaturkombinationen navigieren (es wird "
#~ "JavaScript benötigt). Auf der Start- "
#~ "bzw. Ergebnisseite \"h\" drücken, um ein"
#~ " Hilfe-Fenster anzuzeigen."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Es konnten keine Suchergebnisse gefunden "
#~ "werden. Bitte nutze einen anderen "
#~ "Suchbegriff, oder suche das gewünschte "
#~ "in einer anderen Kategorie."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Umschreiben des Hostnamen oder sperren "
#~ "von Hostnamen in den Such-Ergebnissen"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kB"

#~ msgid "MiB"
#~ msgstr "MB"

#~ msgid "GiB"
#~ msgstr "GB"

#~ msgid "TiB"
#~ msgstr "TB"

#~ msgid "Hostname replace"
#~ msgstr "Hostnamen ändern"

#~ msgid "Error!"
#~ msgstr "Fehler!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Die folgenden Suchmaschinen können die Ergebnisse nicht empfangen"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Fehlerbericht auf GitHub erstellen"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Zufallswertgenerator"

#~ msgid "Statistics functions"
#~ msgstr "Statistikfunktionen"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "{functions} der Argumente berechnen"

#~ msgid "Get directions"
#~ msgstr "Route berechnen"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Zeigt deine IP-Adresse an, wenn "
#~ "die Suchabfrage \"ip\" lautet, und "
#~ "deinen User-Agent, wenn deine "
#~ "Suchabfrage \"user agent\" beinhaltet."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Die Liste der Tor-Exit-Nodes "
#~ "konnte nicht heruntergeladen werden von: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Du benutzt Tor und es sieht so "
#~ "aus, als hättest du diese externe "
#~ "IP-Adresse: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Du benutzt Tor und es sieht so "
#~ "aus, als hättest du diese externe "
#~ "IP-Adresse: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Schlüsselwörter"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Durch Aufrufen dieses Links in einem "
#~ "anderen Browser werden die aktuellen "
#~ "Einstellungen in dem anderen Browser "
#~ "gespeichert (Cookie)."

#~ msgid "proxied"
#~ msgstr "proxy"
