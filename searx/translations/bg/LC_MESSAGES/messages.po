# Bulgarian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2015
# <AUTHOR> <EMAIL>, 2016-2017
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <PERSON><PERSON> <<EMAIL>>, 2023, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: thenack0 <<EMAIL>>\n"
"Language-Team: Bulgarian <https://translate.codeberg.org/projects/searxng/"
"searxng/bg/>\n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "без още подгрупиране"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "други"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "файлове"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "общо"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "музика"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "социална мрежа"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "изображения"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "видео"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "радио"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "телевизия"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "новини"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "карта"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "наука"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "приложения"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "речници"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "текстове на песни"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "пакети"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "въпроси и отговори"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "репозиторита"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "софтуерни уикита"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "мрежа"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "научни публикации"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "автоматичен"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "светло"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "тъмно"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "черно"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Време на работа"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Относно"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Средна темп."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Облачно"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Обстановка"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Сегашна обстановка"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Вечер"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Усеща се като"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Влажност"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Максилмална темп."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Минимална темп."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Сутрин"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Нощ"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Обяд"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Налягане"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Изгрев"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Залез"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Температура"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV индекс"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Видимост"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Вятър"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Ясно небе"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Облачно"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Ясно"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Мъгла"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Проливен дъжд и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Проливен дъжд и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Проливен дъжд"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Проливен дъжд"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Силна суграшица и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Силна суграшица и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Силна суграшица"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Силна суграшица"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Силен снеговалеж и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Силен снеговалеж и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Силен снеговалеж"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Силен снеговалеж"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Слаб дъжд и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Слаби превалявания от дъжд и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Слаби превалявания от дъжд"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Слаб дъжд"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Слаба градушка и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Слаби превалявания от градушка и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Слаби превалявания от градушка"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Слаба градушка"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Слаб сняг и гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Слаби превалявания от сняг с гръмотевици"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Абонати"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "Публикации"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "активни потребители"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "Коментари"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "Потребител"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "общност"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "Точки"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "Заглавие"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "Автор"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "отворено"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Затворено"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "Отговорено"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Не е намерен артикул"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Източник"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Грешка при зареждането на следващата страница"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Неправилни настройки, моля редактирайте предпочитанията си"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Невалидни настройки"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "Грешка при търсенето"

#: searx/webutils.py:35
msgid "timeout"
msgstr "изчакване"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "грешка при анализа"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Грешка в протокола HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "мрежова грешка"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL грешка: проверката на сертификата е неуспешна"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "неочакван срив"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP грешка"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP грешка във връзката"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "прокси грешка"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "твърде много повиквания"

#: searx/webutils.py:58
msgid "access denied"
msgstr "отказан достъп"

#: searx/webutils.py:59
msgid "server API error"
msgstr "грешка в API на сървъра"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "преустановен"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "преди {minutes} минута(минути)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "преди {hours} час(ове), {minutes} минута(минути)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Генерирайте различни произволни стойности"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Изчислете {func} на аргументите"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Покажи маршрута в картата.."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ОСТАРЯЛО)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Този запис е заменен от"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Канал"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "Скорост"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "Гласове"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "клика"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Език"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} цитати от годината {firstCitationVelocityYear} до "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"URL адресът на изображението не можа да бъде прочетен. Това може да се "
"дължи на неподдържан файлов формат. TinEye поддържа само изображения, "
"които са JPEG, PNG, GIF, BMP, TIFF или WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Изображението е твърде просто за намиране на съвпадения. TinEye изисква "
"основно ниво на визуална детайлност за успешно идентифициране на "
"съвпадения."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Снимката не може да бъде свалена."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Рейтинг на книги"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Качество на файл"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Изчеслете математически изрази през лентата за търсене"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Преобразува низове в различни хаш-извлечение."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "хеш извлечение"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Добавка за Хостинг имена"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Пренапиши хост имената, премахни резултати или ги приоритизирай, въз "
"основа на имената на хостовете"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Отворен достъп DOI пренаписване"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Избягвайте заплатите, като пренасочвате към версии с отворен достъп на "
"публикации, когато са налични"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Лична информация"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Показва твоят IP адрес и твоят потребителски агент, ако заявката е "
"\"потребителски агент\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Твоето IP е: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Вашият потребителски агент е: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Проверка на Tor приставката"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Тази добавка проверява дали адресът на заявката е изходен възел на TOR и "
"осведомява потребителя ако е - като check.torproject.org, но от SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Не може да се изтегли списъкът с изходни възли на Tor от"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Използвате Tor и изглежда, че имате външен IP адрес"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Не използвате Tor и имате външен IP адрес"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Премахвач на URL тракери"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Премахни следящите аргументи от върнатия URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Превръщане между единици"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Страницата не е намерена"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Отиди на %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "търси страница"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Дарете"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Предпочитания"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "С подкрепата на"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "отворена метатърсачка, уважаваща поверителността на потребителя"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Код на SearXNG"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Търсачка на проблеми"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Статистика на търсачката"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Публични сървъри"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Политика за поверителност"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Контакт за връзка с поддържащия публичния сървър"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Кликнете лупичката, за да изпълните търсене"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Дължина"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Изгледи"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Автор"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "кеширана"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Предявете нов проблем в GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Моля проверете за съществуващи бъгове на търсачката в GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Потвърждавам, че няма съществуващи бъогве за проблема, който срещнах"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Ако това е публична инстанция, моля предоставете URL адресът в отзива за "
"бъга"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Изпращане на сигнал за нов проблем на Github, съдържащ горепосочената "
"информация"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Без HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Виж грешката и я докладвай"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang за тази търсачка"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang за категориите"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Медиaна"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Провалили се тест(ове) на проверяващия: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Грешки:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Общи"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Първоначални категории"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Потребителски интерфейс"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Поверителност"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Търсачки"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Използвани търсачки в момента"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Специялни Запитвания"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Бисквитки"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Брой резултати"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Инф."

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Обратно към началото"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Предишна страница"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Следваща страница"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Покажи начална страница"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Търси за..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "изчисти"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "търси"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Няма налична достъпна информация."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Име на търсачка"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Резултати"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Брой резултати"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Време за отговор"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Надеждност"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Общо"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Обработка"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Предупреждения"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Грешки и изключения"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Изключение"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Съобщение"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Процент"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Параметър"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Име на файла"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Функция"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Код"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Проверител"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Неуспешен тест"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Коментар (и)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Примери"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Дефиниции"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Синоними"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Чувства се като"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Отговори"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Свали резултатите"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Пробвайте да потърсите:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Съобщения от търсачките"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "секунди"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Адрес на търсенето"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "копирано"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Копирайте"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Предложения"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Език на търсене"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Основен език"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Автоматично разпознаване"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Безопасно търсене"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Стриктно"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Умерено"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Нищо"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Времева зона"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "По всяко време"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Последен ден"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Миналата седмица"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Миналия месец"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Миналата година"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Информация!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "В момента няма налични бисквитки."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Съжалявам!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Няма намерени резултати. Може да опитате да:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Няма повече резултати. Може да опитате да:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Опресни страницата."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Направете друго търсене или изберете друга категория (по-отгоре)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Сменете използваната търсачка от Предпочитания:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Премини на друг сървър:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Потърсете друга заявка или изберете друга категория."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""
"Върнете се към предишната страница, като използвате бутона \"Предишна "
"страница\"."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Позволи"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Ключови думи (първата дума в заявката)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Име"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Описание"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Това е листа, съдържащ моментално-отговарящите модули на SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Това е листа с добавки."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Автоматично допълване"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Намери докато пишеш"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Централно подреждане"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Показване на резултатите в средата на страницата (Оскарово оформление)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Това е листът с бисквитките и техните стойност които SearXNG запазва на "
"компютъра ви."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "С този лист можете да оцените прозрачността на SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Име на бисквитката"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Стойност"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Потърсете URL на запазените предпочитания"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Бележка: специфичните персонализирани настройки в URL-то за търсене може "
"да намалят поверителността Ви като издадат данни към кликнатите сайтове "
"при търсене."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL да възстановите предпочитанията си в друг браузър"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Копиране на хеш на предпочитанията"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Вмъкнете копирания хеш на предпочитанията (без URL), за да ги възстановите"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Хеш на предпочитанията"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Идентификатор на цифров обект (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Дигитален идентификатор на обекти с отворен достъп"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr ""
"Изберете услуга използвана от \"Идентификатор на дигитален обект“ (DOI) "
"пренаписване"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Този раздел несъществува в потребителския интерфейс, но може да търсиш "
"със следните търсачки по !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Разрешаване на всички"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Деактивиране на всички"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Поддържка на избраният език"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Тегло"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Максимално време"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Тези настройки се съхраняват във вашите бисквитки. Това ни позволява да "
"не съхраняваме тази информация за вас."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Тези бисквитки служат за ваше удобство. Ние не ги използваме, за да ви "
"следим."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Запази"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Върни първоначалните"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Назад"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Клавишни комбинации"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Подобно на Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Навигирай резултати чрез клавишни комбинации (необходим е JavaScript). За"
" помощ натисни клавиша \"h\" на главната страница или на страницата с "
"резултатите."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Прокси на изображения"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Прекарване на получените изображения през прокси на SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Списък без страници"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Автоматично зареждане на следващата страница"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Кой език предпочитате за търсене?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Избери автоматично разпознаване, за да може SearXNG да разпознае езика на"
" който пишеш."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Метод"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Промени как формуларите да бъдат изпращани"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Запитване в заглавието на страницата"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Когато включено, резултата от заглавието на страницата съдържа вашето "
"запитване. Браузърът ви може да записва това"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Резултати на нови раздели"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Отвори връзките в нов раздел"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Филтрирай съдържание"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Търси при избор на категория"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Извършване на търсене веднага, ако е избрана категория. Деактивирайте, за"
" да изберете няколко категории"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Тема"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Смяна на оформлението на SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Тематичен стил"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Изберете автоматични настойки, за да следвате настройките на браузъра си"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Жетони на търсачката"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Жетони за достъп до частни търсачки"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Език на интерфейса"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Промени езика на оформлението"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "репозитори"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "покажи медия"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "скрий медия"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Този сайт не предостави никакво описание."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Размер на файла"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Дата"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Тип"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Резолюция"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Формат"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Търсачка"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Покажи източник"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "адрес"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "покажи карта"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "скрий картата"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Версия"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Поддържащ"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Обновено в"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Етикети"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Популярност"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Лиценз"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Проект"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Начална страница на проекта"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Дата на публикуване"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Дневник"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Редактор"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Издател"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "Дигитален идентификатор на обекти"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "магнитна връзка"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "торент файл"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Сийдър"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Лийчър"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Брой на Файлове"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "покажи видео"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "скрий видеото"

#~ msgid "Engine time (sec)"
#~ msgstr ""

#~ msgid "Page loads (sec)"
#~ msgstr "Страницата зарежда (сек)"

#~ msgid "Errors"
#~ msgstr "Грешки"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Поправи HTTP връзки на HTTPS, ако е възможно"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr "Отвори връзките в нов прозорец."

#~ msgid "Color"
#~ msgstr "Цвят"

#~ msgid "Blue (default)"
#~ msgstr "Синьо (първоначален)"

#~ msgid "Violet"
#~ msgstr "Виолетов"

#~ msgid "Green"
#~ msgstr "Зелено"

#~ msgid "Cyan"
#~ msgstr "зелено-синьо"

#~ msgid "Orange"
#~ msgstr "Оранжево"

#~ msgid "Red"
#~ msgstr "Червено"

#~ msgid "Category"
#~ msgstr "Категория"

#~ msgid "Block"
#~ msgstr "Забрани"

#~ msgid "original context"
#~ msgstr "оригинален контекст"

#~ msgid "Plugins"
#~ msgstr "Добавки"

#~ msgid "Answerers"
#~ msgstr "Отговори"

#~ msgid "Avg. time"
#~ msgstr "Средно време"

#~ msgid "show details"
#~ msgstr "покажи детайлите"

#~ msgid "hide details"
#~ msgstr "скрий детайлите"

#~ msgid "Load more..."
#~ msgstr "Зареди още..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Промени оформлението на searx"

#~ msgid "Proxying image results through searx"
#~ msgstr ""

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr ""

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Това е списък на бисквитки с "
#~ "техните стойности, които searx съхранява "
#~ "на вашия компютър."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr ""

#~ msgid "It look like you are using searx first time."
#~ msgstr "Изглежда използвате searx за първи път."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""

#~ msgid "Themes"
#~ msgstr "Облик"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Метод"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Допълнителни настройки"

#~ msgid "Close"
#~ msgstr "Затвори"

#~ msgid "Language"
#~ msgstr "Език"

#~ msgid "broken"
#~ msgstr "развален"

#~ msgid "supported"
#~ msgstr "поддържан"

#~ msgid "not supported"
#~ msgstr "неподдържан"

#~ msgid "about"
#~ msgstr "относно"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr "Потребителски интерфейс"

#~ msgid "Choose style for this theme"
#~ msgstr "Избери стил за избрания облик"

#~ msgid "Style"
#~ msgstr "Стил"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr ""

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "запази"

#~ msgid "back"
#~ msgstr "назад"

#~ msgid "Links"
#~ msgstr "Връзки"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Резултати от търсенето"

#~ msgid "next page"
#~ msgstr "следваща страница"

#~ msgid "previous page"
#~ msgstr "предишна страница"

#~ msgid "Start search"
#~ msgstr "Започни търсене"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "статистики"

#~ msgid "Heads up!"
#~ msgstr "Внимание!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Изглежда, че използвате SearXNG за първи път."

#~ msgid "Well done!"
#~ msgstr "Браво!"

#~ msgid "Settings saved successfully."
#~ msgstr "Настройките са успешно запазени."

#~ msgid "Oh snap!"
#~ msgstr "Да му се не види!"

#~ msgid "Something went wrong."
#~ msgstr "Нещо се обърка."

#~ msgid "Date"
#~ msgstr "Дата"

#~ msgid "Type"
#~ msgstr "Вид"

#~ msgid "Get image"
#~ msgstr "Вземи изображение"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "предпочитания"

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "за спазване на поверителността, хакерска метатърсачка"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Няма резюме за тази публикация."

#~ msgid "Self Informations"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Променете начина, по който се изпращат"
#~ " формите, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Тази добавка проверява дали адресът на"
#~ " заявката е изходен възел на TOR "
#~ "и осведомява потребителя ако е - "
#~ "като check.torproject.org, но от searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Листа с изходните възли на TOR "
#~ "(https://check.torproject.org/exit-addresses) е "
#~ "недостижим."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Използвате TOR. Вашият IP адрес изглежда е: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Не използвате TOR. Вашият IP адрес изглежда е: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "други"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Този раздел не се показва за "
#~ "резултатите от търсенето, но можете да"
#~ " прегледате търсачките, изброени тук."

#~ msgid "Shortcut"
#~ msgstr "Пряк път"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Търсачките не могат да извлекат резултати."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Моля, опитайте отново по-късно или намерете друг сървър SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Пренасочване към версий на публикации с"
#~ " отворен достъп, когато са достъпни "
#~ "(Изисква допълнение)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Промяна на начина на подаване на "
#~ "формуляри, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">научете повече за методите "
#~ "на заявка</a>"

#~ msgid "On"
#~ msgstr "Включено"

#~ msgid "Off"
#~ msgstr "Изключено"

#~ msgid "Enabled"
#~ msgstr "Включено"

#~ msgid "Disabled"
#~ msgstr "Изключено"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Търси веднага при избрана категория. "
#~ "Изключи за избор на няколко категории."
#~ " (Необходим е JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim наподобяващи клавишни комбинации"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Навигирайте резултатите от търсенето с "
#~ "Vim-подобни горещи клавиши (изисква се "
#~ "JavaScript). Натиснете клавиша \"h\" на "
#~ "главната или резултатната страница, за "
#~ "да получите помощ."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "не намерихме резултати. Моля пробвайте "
#~ "други ключови думи или търсете в "
#~ "повече категории."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Пренапишете имената на хостове на "
#~ "резултатите или премахнете резултатите въз "
#~ "основа на името на хоста"

#~ msgid "Bytes"
#~ msgstr "Байта"

#~ msgid "kiB"
#~ msgstr "килобайт"

#~ msgid "MiB"
#~ msgstr "мегабайт"

#~ msgid "GiB"
#~ msgstr "гигабайт"

#~ msgid "TiB"
#~ msgstr "терабайт"

#~ msgid "Hostname replace"
#~ msgstr "Замяна на името на хоста"

#~ msgid "Error!"
#~ msgstr "Грешка!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Търсачките не можаха да намерят резултати"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Предявете нов проблем в GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Генератор на произволни стойности"

#~ msgid "Statistics functions"
#~ msgstr "Функции за статистика"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Изчислете {functions} на аргументите"

#~ msgid "Get directions"
#~ msgstr "Вземете упътвания"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr "Показва IP-то ви и др. инфо, ако търсенето е \"ip\" или \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Не можe да се изтегли списъка с"
#~ " mаршрутизатори/рутери на Tor от: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "В момента използваш Tor и твоят IP адрес е: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "В момента не използваш Tor и твоят IP адрес е: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Ключови думи"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Специфицирането на персонализирани настройки в"
#~ " URL-то за предпочитания може да "
#~ "позволи синхронизация между различни "
#~ "устройства."

#~ msgid "proxied"
#~ msgstr "прекарана"
