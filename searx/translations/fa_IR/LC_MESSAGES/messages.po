# Persian (Iran) translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# Aurora, 2018
# d92c08ec808c392054abf37312c77481_5b152be
# <f35b42cd6a58f8316d31e810229a558d_657277>, 2017
# <PERSON><PERSON><PERSON> <ahanga<PERSON><EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2022, 2023.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-05-06 12:53+0000\n"
"Last-Translator: ehsanrs2 <<EMAIL>>\n"
"Language: fa_IR\n"
"Language-Team: Persian "
"<https://translate.codeberg.org/projects/searxng/searxng/fa/>\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "بدون زیر گروه بندی بیشتر"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "دیگر"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "فایل‌ها"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "عمومی"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "موسیقی"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "شبکهٔ اجتماعی"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "تصاویر"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "ویدیوها"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "رادیو"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "تلویزیون"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "فناوری اطلاعات"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "اخبار"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "نقشه"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "پیازها"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "علم"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "برنامه ها"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "لغت نامه ها"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "متن ترانه"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "بسته‌ها"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "پرسش و پاسخ"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "مخازن"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "ویکی‌های نرم‌افزارها"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "وب"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "انتشارات علمی"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "خودکار"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "روشن"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "تاریک"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "سیاه"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "زمان به کار سرور"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "درباره"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "میانگین دما"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "‍پوشش ابری"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "وضعت"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "وضع کنونی"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "عصر"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "حس می‌دهد مانند"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "رطوبت"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "نهایت دما"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "حداقل دما"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "صبح"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "شب"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "ظهر"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "فشار"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "طلوع"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "غروب"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "دما"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "مقدار اشعه UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "دید"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "باد"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "دنبال کننده‌ها"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "پست ها"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "کاربران فعال"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "نظر ها"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "کاربر"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "جمعیت"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "امتیاز‌ات"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "تیتر"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "نگارنده"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "باز"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "بسته شده"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "جواب داده شده"

#: searx/webapp.py:292
msgid "No item found"
msgstr "چیزی پیدا نشد"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "منبع"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "خطا در بارگزاری صفحه جدید"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "تنظیمات نادرست است، لطفا تنظیمات جستجو را تغییر دهید"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "تنظیمات نادرست"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "خطای جست‌وجو"

#: searx/webutils.py:35
msgid "timeout"
msgstr "مهلت پاسخ‌دهی به پایان رسید"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "خطای تجزیه"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "خطای پروتکل HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "خطای شبکه"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "ارور SSL:اعتبار سنجی گواهی امنیتی SSL ناموفق بود"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "مشکل غیرمنتظره"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "خطای HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "خطای اتصال HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "خطای پروکسی"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "کپچا"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "درخواست‌های زیاد"

#: searx/webutils.py:58
msgid "access denied"
msgstr "دسترسی مجاز نیست"

#: searx/webutils.py:59
msgid "server API error"
msgstr "خطای API سرور"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "تعلیق‌شده"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} دقیقه پیش"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} ساعت و {minutes} دقیقه پیش"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "ایجاد مقادیر تصادفی متفاوت"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Compute {func} of the arguments"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "دیدن مسیر در نقشه"

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (منسوخ شده)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "این ورودی معلق شده است، توسط"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "کانال"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "بیت ریت"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "رای ها"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "کلیک ها"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "زبان"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} نقل قول از سال {firstCitationVelocityYear} تا "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"نمی‌توان آدرسِ ‎URL‎ تصویر را خواند. این ممکن است به دلیل فرمت فایل "
"پشتیبانی نشده ای باشد. TinEye فقط تصویر های با فرمت JPEG، PNG، GIF، BMP، "
"TIFF یا WebP را پشتیبانی می‌کند."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"تصویر برای یافتن موارد منطبق بسیار ساده است. TinEye برای شناسایی موفق به "
"سطح اولیه جزئیات بصری نیاز دارد."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "تصویر نمیتواند دانلود شود."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "رتبه بندی کتاب"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "کیفیت فایل"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "لیست سیاه Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "نتایج onion که در لیست سیاه Ahmia ظاهر می‌شوند را فیلتر کنید."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "ماشین حساب اولیه"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "محاسبه عبارت‌های ریاضی در نوار جست و جو"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "پلاگین هَش"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "رشته‌ها را به چکیده‌های هش تبدیل می‌کند."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "چکیدهٔ هش"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "افزونه های نام دامنه"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "بازنویسی نام‌های دامنه، حذف نتایج یا مرتب کردن آن‌ها بر اساس نام دامنه"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "بازنویسی DOI Access را باز کنید"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"با هدایت مجدد به نسخه‌های دسترسی آزاد انتشارات در صورت وجود، از دیوارهای "
"پرداخت اجتناب کنید"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "اطلاعات شخصی"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"اگر درخواست «ip» باشد، IP شما را نمایش می‌دهد و user agent را نمایش "
"می‌دهد اگر درخواست «user-agent» باشد."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "آی‌پی شما: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "یوزر-ایجنت شما: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "افزونه بررسی Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"این افزونه بررسی می کند که آیا آدرس درخواست یک گره خروجی Tor است یا خیر، "
"و در صورت وجود آن به کاربر اطلاع می دهد. مانند check.torproject.org، اما "
"از SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "نتوانستم لیست گره‌های خروجی Tor را از اینجا دانلود کنم"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "شما در حال استفاده از تور هستید و به نظر می‌رسد آی‌پی خارجی دارید"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "شما از تور استفاده نمی‌کنید و آی‌پی خارجی دارید"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "حذف کننده URL ردیاب"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "آرگومان های ردیاب ها را از URL برگشتی حذف کنید"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "افزونه تبدیل واحد"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "تبدیل بین واحد‌ها"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "صفحه پیدا نشد"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "برو به ‎%(search_page)s‎."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "صفحهٔ جست‌وجو"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "اهداء کردن"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "تنظیمات"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "قدرت گرفته از<br>"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "یک موتور فراجستجوی آزاد که به حریم خصوصی احترام می گذارد"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "کد منبع"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "ردیاب مشکل"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "وضعیت موتور"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "نمونه‌های عمومی"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "سیاست حفظ حریم خصوصی"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "تماس با نگهدارنده نمونه"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "برای انجام جست‌وجو روی ذره‌بین کلیک کنید"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "طول"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "بازدید‌ها"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "نویسنده"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "جاسازی‌شده"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "شروع ارائه ی یک مشکل در گیتهاب"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "لطفاً اشکالات موجود در مورد این موتور جستجو را در گیت‌هاب بررسی کنید"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"من تأیید می کنم که هیچ اشکال گزارش شده مشابه مشکلی که با آن روبرو هستم "
"وجود ندارد"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "اگر این یک نمونه عمومی است، لطفاً URL را در گزارش اشکال مشخص کنید"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "در گیتهاب مشکل جدید را با توجه به اطلاعات فوق ثبت نمایید"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "بدون HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "مشاهدهٔ رخدادهای خطا و ثبت یک گزارش اشکال"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!بنگ برای این موتور"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!بنگ برای دسته های آن"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "میانه"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "پی۸۰"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "پی۹۵"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "آزمایش(های) بررسی‌گر شکست‌خورده: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "خطاها:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "کلی"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "دسته‌بندی‌های پیش‌گزیده"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "رابط کاربری"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "حریم شخصی"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "موتورها"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "موتور جستجو های در حال استفاده"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "مقدارهای ویژه"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "کلوچک‌ها"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "تعداد نتایج"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "اطلاعات"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "برگشتن با بالا"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "صفحهٔ پیشین"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "صفحهٔ بعدی"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "نمایش صفحه جلویی"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "جست‌وجو برای..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "پاک‌سازی"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "جست‌وجو"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "در حال حاضر هیچ داده‌ای در دسترس نیست."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "نام موتور"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "نمره‌ها"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "تعداد نتیجه ها"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "زمان پاسخ‌دهی"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "اعتمادپذیری"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "همه"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "در حال پردازش"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "اخطارها"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "خطاها و استثناها"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "استثنا"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "پیام"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "درصد"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "شاخص"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "نام پرونده"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "تابع"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "کد"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "بررسی‌گر"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "آزمایش ناموفق"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "نظر(ها)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "مثال‌ها"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "تعریف‌ها"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "مترادف‌ها"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "پاسخ‌ها"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "نتایج بارگیری"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "برای این جست‌وجو تلاش کنید:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "پیام های موتور جستجوها"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "ثانیه‌ها"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL جست‌وجو"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "کپی شد"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "رونوشت"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "پیشنهادها"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "زبان جست‌وجو"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "زبان پیش‌گزیده"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "انتخاب خودکار"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "جست‌وجوی امن"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "سخت‌گیر"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "متعادل"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "هیچ"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "بازهٔ زمانی"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "هر زمان"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "روز گذشته"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "هفتهٔ گذشته"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "ماه گذشته"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "سال گذشته"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "دانستنی‌ها!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "در حال حاضر کلوچکی تعریف نشده است."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "متاسف!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "نتیجه‌ای یافت نشد. می‌توانید موارد زیر را امتحان کنید:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "نتیجه دیگری یافت نشد. میتونی امتحان کنی:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "صفحه را تازه کنید."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "جست‌وجو برای ورودی دیگر یا انتخاب دسته بندی دیگر(در بالا)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "موتور جست‌وجو رو در تنظیمات تغییر دهید:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "تغییر به نمونه ای دیگر:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "جست‌وجو برای ورودی دیگر یا انتخاب دسته بندی دیگر."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "برو به سفحه قبل، با استفاده از کلید رفتن به صفحه قبل."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "اجازه"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "کلمات کلیدی (اولین کلمه در درخواست)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "نام"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "توصیف"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "این فهرست ماژول‌های پاسخ‌گوی فوری SearXNG است."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "این فهرست افزونه‌هاست."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "تکمیل خودکار"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "یافتن مطالب هنگام نوشتن"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "وسط چین"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "نمایش نتایج در مرکز صفحه(طرح بندی اسکار)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "این فهرست کلوچک‌ها و مقدارهایی است که SearXNG در رایانهٔ شما نگه می‌دارد."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "با این فهرست، می‌توانید شفافیت SearXNG را بیازمایید."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "نام کلوچک"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "مقدار"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL جست‌وجوی تنظیمات ذخیره‌شدهٔ کنونی"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"توجه: تعیین تنظیمات سفارشی در URL جستجو می‌تواند حریم خصوصی را با نشت "
"داده‌ها به سایت‌های نتیجه کلیک شده کاهش دهد."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL برای بازیابی تنظیمات مورد نظر خود در مرورگر دیگری"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"یک URL حاوی تنظیمات برگزیده شما. از این URL می‌توان برای بازیابی تنظیمات "
"شما در دستگاه دیگری استفاده کرد."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "کپی هش تنظیمات"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "هش تنظیمات کپی شده را وارد کنید(بدون URL) برای بازیابی"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "هش تنظیمات"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "شناسه شئ دیجیتال (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "واگردان DOI دسترسی آزاد"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "سرویس مورد استفاده توسط بازنویسی DOI را انتخاب کنید"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"این تب در رابط کاربری وجود ندارد، اما می توانید در این موتورها با !بنگ "
"های آن جستجو کنید."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "فعال‌سازی همه"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "غیرفعال‌سازی همه"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!بنگ"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "پشتیبانی از زبان انتخاب شده"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "وزن"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "زمان بیشینه"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "حل کننده فاویکون"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"این تنظیمات در کلوچک‌های شما ذخیره می‌شوند و به ما توانایی ذخیرهٔ این "
"دادهٔ مربوط به شما را نداریم."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"این کلوچک‌ها تنها برای آسودگی شما هستند و ما از این کلوچک‌ها برای ردیابی "
"شما استفاده نمی‌کنیم."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "ذخیره"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "بازنشانی پیش‌فرض‌ها"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "بازگشت"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "کلید های میانبر"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "مانند-‎Vim‎"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"هدایت نتایج جست‌وجو با کلید های میانبر (نیازمند ‎JavaScript‎). برای "
"راهنمایی، کلید «h» را در صفحه اصلی یا صفحه نتایج فشار دهید."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "پروکسی تصویر"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "گذر تصویر از پروکسی به‌وسیلهٔ SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "پایین رفتن بی‌پایان"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "هنگام پیمایش به پایین صفحه فعلی، صفحه بعدی به صورت خودکار بارگیری می شود"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "چه زبانی را برای جست‌وجو می‌پسندید؟"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "تشخیص خودکار را انتخاب کنید تا SearXNG زبان شما را تشخیص دهد."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "روش HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "نحوه ارسال فرم ها را تغییر دهید"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "ورودی در عنوان صفحه"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"هنگام فعال بودن، عنوان صفحهٔ نتیجه، ورودی شما را در بر می‌گیرد. مرورگر "
"شما می‌تواند این عنوان را ذخیره کند"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "نتایج در برگه‌های جدید"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "بازکردن پیوندهای نتیجه در زبانه‌های جدید مرورگر"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "فیلتر کردن محتوا"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "جست‌وجو در انتخاب دسته بندی"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"انجام دادن جست‌وجو درجا درصورت انتخاب یک دسته بندی. برای انتخاب بیش از یک"
" دسته بندی غیر فعال کنید"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "پوسته"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "تغییر طرح‌بندی SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "شیوهٔ پوسته"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "برای پیروی از تنظیمات مرورگرتان خودکار را انتخاب کنید"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "توکن‌های موتور"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "توکن‌های دسترسی برای موتورهای خصوصی"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "زبان رابط کاربری"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "تغییر زبان رابط کاربری"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "فرمت URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "قشنگ"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "کامل"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "هاست"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "تغییر فرمت URL نتایج"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "مخازن"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "نمایش رسانه"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "پنهان‌سازی رسانه"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "این سایت هیچ توصیفی ندارد."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "اندازهٔ پرونده"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "تاریخ"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "نوع"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "رزولیشن"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "قالب"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "موتور"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "نمایش منبع"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "نشانی"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "نمایش نقشه"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "پنهان‌سازی نقشه"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "ورژن"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "مسئول‌نگهداری"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "بارگذاری‌شده در"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "تگ ها"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "محبوبیت"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "لایسنس"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "پروژه"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "صفحه خانه پروژه"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "تاریخ انتشار"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "مجله"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "ویرایشگر"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "ناشر"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "پیوند مگنت"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "پروندهٔ تورنت"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "بذرپاش"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "مکنده"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "تعداد پرونده‌ها"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "نمایش ویدئو"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "پنهان‌سازی ویدئو"

#~ msgid "Engine time (sec)"
#~ msgstr "زمان موتور(ثانیه)<br>"

#~ msgid "Page loads (sec)"
#~ msgstr "زمان بارگذاری صفحه (ثانیه)"

#~ msgid "Errors"
#~ msgstr "خطاها"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "تغییر پیوند های HTTP به HTTPS در صورت امکان"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "به طور پیش‌فرض، نتایج در پنجره ی"
#~ " کنونی باز می‌شوند. این افزونه، رفتار"
#~ " پیش‌فرض را برای بازشدن پیوند در "
#~ "پنجره/برگه جدید تغییر می‌دهد. (نیازمند "
#~ "جاوااسکریپت)"

#~ msgid "Color"
#~ msgstr "رنگ"

#~ msgid "Blue (default)"
#~ msgstr "آبی (پیش‌فرض)"

#~ msgid "Violet"
#~ msgstr "بنفش"

#~ msgid "Green"
#~ msgstr "سبز<br>"

#~ msgid "Cyan"
#~ msgstr "فیروزه‌ای"

#~ msgid "Orange"
#~ msgstr "نارنجی"

#~ msgid "Red"
#~ msgstr "قرمز"

#~ msgid "Category"
#~ msgstr "دسته"

#~ msgid "Block"
#~ msgstr "انسداد<br>"

#~ msgid "original context"
#~ msgstr "متن اصلی<br>"

#~ msgid "Plugins"
#~ msgstr "افزونه ها"

#~ msgid "Answerers"
#~ msgstr "پاسخگو ها<br>"

#~ msgid "Avg. time"
#~ msgstr "زمان میانگین"

#~ msgid "show details"
#~ msgstr "نمایش جزئیات"

#~ msgid "hide details"
#~ msgstr "پنهان‌سازی جزئیات"

#~ msgid "Load more..."
#~ msgstr "بیشتر…<br>"

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "رابط کاربری searx را تغییر دهید<br>"

#~ msgid "Proxying image results through searx"
#~ msgstr "پراکسی کردن نتایج تصویری از طریق searx<br>"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "این، فهرست ماژول‌های پاسخ بلادرنگ searx است."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "این، لیست کوکی‌ها و مقادیری است که searx روی دستگاه شما ذخیره می‌کند."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "با آن لیست، می‌توانید شفافیت searx را ارزیابی کنید."

#~ msgid "It look like you are using searx first time."
#~ msgstr "به نظر می‌رسد اولین باری است که از searx استفاده می‌کنید."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""
#~ "لطفا بعدا دوباره تلاش کنید و یا"
#~ " به دنبال نمونه‌ای دیگری از searx "
#~ "بگردید."

#~ msgid "Themes"
#~ msgstr "تم ها<br>"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "روش"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "تنظیمات پیشرفته"

#~ msgid "Close"
#~ msgstr "بستن"

#~ msgid "Language"
#~ msgstr "زبان"

#~ msgid "broken"
#~ msgstr "خراب"

#~ msgid "supported"
#~ msgstr "پشتیبانی شده"

#~ msgid "not supported"
#~ msgstr "پشتیبانی نشده"

#~ msgid "about"
#~ msgstr "درباره"

#~ msgid "Avg."
#~ msgstr "فارسی"

#~ msgid "User Interface"
#~ msgstr "رابط کاربری"

#~ msgid "Choose style for this theme"
#~ msgstr "شیوه این پوسته را انتخاب کنید"

#~ msgid "Style"
#~ msgstr "شیوه"

#~ msgid "Show advanced settings"
#~ msgstr "نمایش تنظیمات پیشرفته"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "نمایش تختهٔ تنظیمات پیشرفته در صفحهٔ خانه به‌صورت پیش‌فرض"

#~ msgid "Allow all"
#~ msgstr "اجازه به همه"

#~ msgid "Disable all"
#~ msgstr "غیرفعال‌سازی همه"

#~ msgid "Selected language"
#~ msgstr "زبان انتخابی"

#~ msgid "Query"
#~ msgstr "پرس و جو"

#~ msgid "save"
#~ msgstr "ذخیره"

#~ msgid "back"
#~ msgstr "قبلی"

#~ msgid "Links"
#~ msgstr "پیوندها"

#~ msgid "RSS subscription"
#~ msgstr "اشتراک RSS"

#~ msgid "Search results"
#~ msgstr "نتایج جست‌وجو"

#~ msgid "next page"
#~ msgstr "صفحهٔ بعدی"

#~ msgid "previous page"
#~ msgstr "صحهٔ پیشین"

#~ msgid "Start search"
#~ msgstr "آغاز جست‌وجو"

#~ msgid "Clear search"
#~ msgstr "پاک‌سازی جست‌وجو"

#~ msgid "Clear"
#~ msgstr "پاک‌سازی"

#~ msgid "stats"
#~ msgstr "آمار"

#~ msgid "Heads up!"
#~ msgstr "بالأخره!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "به‌نظر می‌رسید برای نخستین بار از SearXNG استفاده می‌کنید."

#~ msgid "Well done!"
#~ msgstr "آفرین!"

#~ msgid "Settings saved successfully."
#~ msgstr "تنظیمات با موفقیت ذخیره شد."

#~ msgid "Oh snap!"
#~ msgstr "گندش بزنن!"

#~ msgid "Something went wrong."
#~ msgstr "یک چیزی کار نکرد."

#~ msgid "Date"
#~ msgstr "تاریخ"

#~ msgid "Type"
#~ msgstr "گونه"

#~ msgid "Get image"
#~ msgstr "دریافت تصویر"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "پیش‌فرض‌ها"

#~ msgid "Scores per result"
#~ msgstr "نمره‌های هر نتیجهٔ"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "یک ابرموتور جست‌وجوی حافظ حریم شخصی"

#~ msgid "No abstract is available for this publication."
#~ msgstr "هیچ چکیده‌ای برای این انتشار در دسترس نیست."

#~ msgid "Self Informations"
#~ msgstr "اطلاعات خوداظهاری"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "تغییر در چگونگی ارسال فرم‌ها، <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">راجع به شیوه‌های درخواست "
#~ "بیشتر بیاموزید.</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "این افزونه بررسی می‌کند که آیا "
#~ "آدرس درخواست یک node خروجی TOR است"
#~ " یا خیر، و به کاربر اطلاع "
#~ "می‌دهد مانند check.torproject.org اما از "
#~ "searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr "لیست گره خروجی TOR، غیر قابل دسترسی است."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""
#~ "شما از TOR استفاده می کنید. به "
#~ "نظر می رسد آدرس IP شما این "
#~ "است: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""
#~ "شما از TOR استفاده نمی کنید. به"
#~ " نظر می رسد آدرس IP شما این "
#~ "است: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "تشخصیص خودکار زبان جستجو"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "تشخیص خودکار زبان کوئری جستجو و انتخاب کردن آن."

#~ msgid "others"
#~ msgstr "دیگر"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "این برگه برای نتایج جستجو نمایش "
#~ "داده نمی شود، اما می توانید "
#~ "موتورهای فهرست شده در اینجا را از"
#~ " طریق bangs جستجو کنید."

#~ msgid "Shortcut"
#~ msgstr "میان‌بر"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "موتورها نمی‌توانند نتایج را دریافت کنند."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "لطفاً دوباره تلاش کنید یا شاهد SearXNG دیگری را بیابید."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr "هدایت به نسخه‌های دسترسی آزاد انشارات در صورت امکان (نیازمند افزونه)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "نحوه ارسال فرم ها را تغییر دهید،"
#~ " <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\"> در مورد روش های "
#~ "درخواست بیشتر بیاموزید </a>"

#~ msgid "On"
#~ msgstr "روشن"

#~ msgid "Off"
#~ msgstr "خاموش"

#~ msgid "Enabled"
#~ msgstr "فعال"

#~ msgid "Disabled"
#~ msgstr "غیرفعال"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "در صورت انتخاب یک دسته فورا جستجو"
#~ " را انجام دهید. برای انتخاب چندین "
#~ "دسته غیرفعال کنید. (جاوا اسکریپت مورد"
#~ " نیاز است)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "کلیدهای میانبر مانند vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "جابجایی در نتایج با کلیدهای میان‌بر "
#~ "مشابه Vim (نیازمند جاوااسکریپت). در صفحه"
#~ " اصلی و یا صفحه نتیجه، دکمه h"
#~ " را برای نمایش راهنما فشار دهید."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "چیزی پیدا نشد. لطفاً ورودی دیگری "
#~ "را بیازمایید یا در دسته‌‌های بیش‌تری "
#~ "جست‌وجو کنید."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "نام میزبان نتایج را بازنویسی کنید "
#~ "یا نتایج را بر اساس نام میزبان "
#~ "حذف کنید"

#~ msgid "Bytes"
#~ msgstr "بایت"

#~ msgid "kiB"
#~ msgstr "کیلوبایت"

#~ msgid "MiB"
#~ msgstr "مگابایت"

#~ msgid "GiB"
#~ msgstr "گیگابایت"

#~ msgid "TiB"
#~ msgstr "ترابایت"

#~ msgid "Hostname replace"
#~ msgstr "جایگزینی نام میزبان"

#~ msgid "Error!"
#~ msgstr "خطا!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "موتورها توانایی دریافت نتایج را ندارند"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "شروع ارائه ی یک مشکل در گیتهاب"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "ایجادگر مقدار تصادفی"

#~ msgid "Statistics functions"
#~ msgstr "توابع آماری"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "پردازش {functions} از آرگومان ها"

#~ msgid "Get directions"
#~ msgstr "دستورهای دریافت"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "اگر پرس و جو \"ip\" باشد IP "
#~ "شما و اگر پرس و جو حاوی "
#~ "\"عامل کاربر\" باشد، عامل کاربری شما "
#~ "را نشان می دهد."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "نمی توان لیست گره های خروج Tor "
#~ "را از: https://check.torproject.org/exit-addresses"
#~ " دانلود کرد"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "شما از Tor استفاده می کنید و "
#~ "به نظر می رسد این آدرس IP "
#~ "خارجی را دارید: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "شما از Tor استفاده نمی کنید و این آدرس IP خارجی را دارید: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "کلیدواژه‌ها"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "تعیین تنظیمات سفارشی در URL تنظیمات "
#~ "برگزیده می‌تواند برای همگام‌سازی تنظیمات "
#~ "برگزیده در بین دستگاه‌ها استفاده شود."

#~ msgid "proxied"
#~ msgstr "پروکسی‌شده"

