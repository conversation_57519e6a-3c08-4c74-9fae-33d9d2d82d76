# Swedish translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# efef6ec5b435a041fce803c7f8af77d2_2341d43, 2016-2017
# efef6ec5b435a041fce803c7f8af77d2_2341d43, 2018-2020
# efef6ec5b435a041fce803c7f8af77d2_2341d43, 2017-2018
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON><PERSON><PERSON> <waldemar.b<PERSON><PERSON><PERSON>@unfnorrbotten.se>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: AndersNordh <<EMAIL>>\n"
"Language-Team: Swedish <https://translate.codeberg.org/projects/searxng/"
"searxng/sv/>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "utan ytterligare undergruppering"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "annan"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "filer"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "allmänt"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musik"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sociala medier"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "bilder"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videor"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "it"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "nyheter"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "karta"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "lökar"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "vetenskap"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "appar"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "uppslagsverk"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "låttext"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paket"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "frågor och svar"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "kodförråd"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "mjukvaruwikier"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "webb"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "vetenskapliga publiceringar"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "auto"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "ljus"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "mörk"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "svart"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Upptid"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Om"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Medeltemperatur"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Molntäcke"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Skick"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Nuvarande tillstånd"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Kväll"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Känns som"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Luftfuktighet"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Högsta temperatur"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Lägsta temperatur"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Morgon"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Natt"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Middag"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Lufttryck"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Soluppgång"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Solnedgång"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatur"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV-index"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Sikt"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vind"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Klar himmel"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Molnigt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Bra"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Dimma"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Kraftigt regn och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Kraftiga regnskurar och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Kraftiga regnskurar"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Kraftigt regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Kraftigt snöblandat regn och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Kraftiga skurar av snöblandat regn och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Kraftiga skurar av snöblandat regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Kraftigt snöblandat regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Kraftigt snöfall och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Kraftiga skurar med snö samt åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Kraftiga skurar med snö"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Kraftig snö"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Lätt regn och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Lätta regnskurar och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Lätta regnskurar"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Lätt regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Lätt snöblandat regn samt åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Lätta skurar med snöblandat regn samt åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Lätta skurar med snöblandat regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Lätt snöblandat regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Lätt snöfall och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Lätta snöskurar och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Lätta snöskurar"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Lätt snöfall"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Delvis molnigt"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Regn och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Regnskurar och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Regnskurar"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Snöblandat regn och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Skurar med snöblandat regn och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Skurar av snöblandat regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Snöblandat regn"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Snö och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Skurar med snö och åska"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Skurar med snö"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Snö"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "prenumeranter"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "inlägg"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktiva användare"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kommentarer"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "användare"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "gemenskap"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "poäng"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "titel"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "författare"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "öppna"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "stängd"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "svarad"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Inga artiklar hittade"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Källa"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Kunde inte ladda nästa sida"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ogiltiga inställningar, vänligen redigerar dina inställningar"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ogiltiga inställningar"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "sökfel"

#: searx/webutils.py:35
msgid "timeout"
msgstr "avbrott"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "tolkningsfel"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-protokollfel"

#: searx/webutils.py:38
msgid "network error"
msgstr "nätverksfel"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL-fel: Valideringsfel av certifikatet"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "oförutsedd krasch"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-fel"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-uppkopplingsfel"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxyfel"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "för många förfrågningar"

#: searx/webutils.py:58
msgid "access denied"
msgstr "åtkomst nekad"

#: searx/webutils.py:59
msgid "server API error"
msgstr "server API-fel"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Avstängd"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minut(er) sedan"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} timm(e/ar), {minutes} minut(er) sedan"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generera olika slumpmässiga värden"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Beräkna {func} för argument"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Visa rutt på karta ..."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (FÖRÅLDRAD)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Detta inlägg har ersatts av"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bithastighet"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "röster"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klickar"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Språk"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citat från år {firstCitationVelocityYear} till "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Kunde inte läsa bildens webbadress. Detta kan bero på ett filformat som "
"inte stöds. TinEye stöder endast bilder som är JPEG, PNG, GIF, BMP, TIFF "
"eller WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Bilden är för enkel för att hitta matchningar. TinEye kräver en "
"grundläggande nivå av visuell detalj för att framgångsrikt kunna "
"identifiera matchningar."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Det gick inte att ladda ner bilden."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Bokbetyg"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Filkvalitet"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia svartlistning"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrera bort onion-resultat som visas i Ahmia:s svartlistning."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Enkel kalkylator"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Beräkna matematiska uttryck med sökfältet"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin för hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Konverterar strängar till olika hashvärden."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hashvärde"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Värdnamn plugin"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Skriva om värdnamn, ta bort resultat eller prioritera dem baserat på "
"värdnamnet"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Open Access DOI-omskrivning"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Undvik betalväggar genom att omdirigera till öppen tillgång versioner av "
"publikationer när de är tillgängliga"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Egen information"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Visar din IP-adress om frågan är \"ip\" och din användaragent är \"user-"
"agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Din IP address är: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Din användaragent är: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor kontroll plugin"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Denna plugin kontrollerar om adressen för begäran är en Tor-utgångsnod, "
"och informerar användaren om det är; som check.torproject.org, men från "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Kunde inte ladda ned listan över Tor exitnoder från"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Du använder Tor och det ser ut som du har den externa IP-adressen"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Du använder inte Tor och du har den externa IP-adressen"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Ta bort URL för tracker"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Ta bort tracker-argument från den återgivna webbadressen"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Plugin för enhetskonvertering"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konvertera mellan enheter"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Sidan hittades inte"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Gå till %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "söksida"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Donera"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Inställningar"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Drivet av"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "en öppen metasökmotor som respekterar din integritet"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Källkod"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Ärendehanterare"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Sökmotor statistik"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Publika instanser"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Integritetspolicy"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontakta instansens underhållare"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Klicka på förstoringsglaset för att utföra sökning"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Längd"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Visningar"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Upphovsman"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "cachad"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Börja rapportera ett problem på Github"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Vänligen kolla efter befintliga buggar om denna motor på GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Jag bekräftar att det inte finns någon befintlig bugg om problemet jag "
"stöter på"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Om detta är en offentlig instans, vänligen ange URL:en i felrapporten"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Skicka in ett nytt nummer på Github inklusive ovanstående information"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Ingen HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Visa felmeddelande och skicka en bugrapport"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang för denna sökmotor"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang för deras kategorier"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Median"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Underkända checker test(s): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Fel:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Allmänt"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Standardkategorier"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Användargränssnitt"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Sekretess"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Sökmotorer"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "För närvarande använda sökmotorer"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Särskilda förfrågningar"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Kakor"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Antal resultat"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informera"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Tillbaka till början"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Föregående sida"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Nästa sida"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Visa förstasidan"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Sök efter..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "rensa"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "sök"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Det finns för närvarande ingen data tillgänglig. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Sökmotorns namn"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Poäng"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Antal resultat"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Svarstid"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Pålitlighet"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Bearbetar"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Varningar"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Fel och undantag"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Undantag"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Meddelande"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Procentsats"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Filnamn"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funktion"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kod"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Kontrollera"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Misslyckade testet"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Kommentar(er)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Exempel"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definitioner"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonymer"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Känns som"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Svar"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Nedladdningsresultat"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Försök söka efter:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Meddelanden från sökmotorerna"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "sekunder"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Sök webbadress"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopierat"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopiera"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Förslag"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Sökspråk"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Standardspråk"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Upptäck automatiskt"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Säker sökning"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Strikt"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Måttlig"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Inga"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Tidsintervall"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Närsom"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Igår"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Förra veckan"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Förra månaden"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Förra året"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Information!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "för närvarande finns det inga kakor definierade."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Ursäkta!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Inga resultat hittades. Du kan prova att:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Det finns inga mer resultat. Du kan testa:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Uppdatera sidan."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Sök efter en annan query eller välj en annan kategori (ovanför)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Ändra sökmotorn som används i inställningarna:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Byt till en annan instans:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Sök med en annan query eller välj en annan kategori."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Gå till den förra sidan via knappen för förra sidan."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Tillåt"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Nyckelord (första ordet i frågan)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Namn"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Beskrivning"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Detta är listan med SearXNG:s direktsvarande moduler."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Detta är listan med plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Slutför automatiskt"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Hitta saker medan du skriver"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centrera"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Centrera resultat på sidan (Oscar layout)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Detta är listan med kakor och dess värden som SearXNG förvarar på din "
"dator."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Med den listan kan du bedöma SearXNG-transparens."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Kaknamn"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Värde"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Sökadressen för de för nuvarande sparade inställningarna"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Obs: Att ange anpassade inställningar i sökadressen kan minska sekretess "
"genom att läcka data till de klickade resultatwebbplatserna."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL för att återställa dina inställningar i en annan webbläsare"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"En URL innehållande dina inställningar. Denna URL kan användas för att "
"återställa dina inställningar på en annan enhet."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "kopiera inställningars hash"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Infoga kopierad inställningshash (utan URL) för att återställa"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "inställningar hash"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital objektidentifierare (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI-lösare"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Välj tjänst för DOI-omskrivning"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Den här fliken finns inte i gränssnittet, men du kan söka i dessa "
"sökmotorer med deras !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Aktivera samtliga"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Inaktivera samtliga"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Stöder valda språket"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Vikt"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Max tid"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon resolver"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Visa faviconer intill sökresultat"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Dessa inställningar lagras i dina kakor, vilket gör att vi inte lagrar "
"data om dig."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Dessa kakor tjänar din egen bekvämlighet, vi använder inte dessa kakor "
"för att spåra dig."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Spara"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Återställ standardvärden"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Tillbaka"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Snabbtangenter"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-liknande"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigera sök resultaten med snabbkommandon (behöver JavaScript). Tryck "
"\"h\" tangenten på huvud eller resultat sidan för att få hjälp."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Bildproxy"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Använder proxy för bildresultat via SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Oändlig bläddring"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Ladda automatiskt nästa sida när du bläddrar till botten av aktuell sida"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Vilket språk föredrar du för att söka?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Välj Upptäck automatiskt för att tillåta SearXNG att upptäcka språket för"
" din fråga."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP metod"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Ändra hur formulär skickas"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Förfrågan i sidans titel"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"När det är aktiverat innehåller resultatsidans titel din fråga. Din "
"webbläsare kan spara denna titel"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultat i nya flikar"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Öppna resultat länkar i nya webbläsarflikar"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrera innehåll"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Sök vid val av kategori"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Utför sökning omedelbart om en kategori väljs. Inaktivera för att välja "
"flera kategorier"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Ändra SearXNG-layout"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Tema stil"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Välj auto för att använda webbläsarens inställningar"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Motortoken"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Åtkomsttoken för privata motorer"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Gränssnittspråk"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Ändra språk för layouten"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL-formatering"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Fin"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Full"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Värd"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Ändra resultat av URL-formatering"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "kodförråd"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "visa media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "göm media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Denna sida gav ingen beskrivning."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Filstorlek"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Datum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Typ"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Upplösning"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Sökmotor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Visa källa"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "address"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "visa karta"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "göm karta"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Version"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Underhållare"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Uppdaterad vid"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Taggar"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularitet"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licens"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projekt hemsida"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Publicerat datum"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Journal"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Ändrare"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Publicerare"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "Digital identifierare"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnetlänk"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrentfil"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Distributör"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Reciprokör"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Antal filer"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "visa video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "göm video"

#~ msgid "Engine time (sec)"
#~ msgstr "Sökmotor tid (sek)"

#~ msgid "Page loads (sec)"
#~ msgstr "Sidan laddas (sek)"

#~ msgid "Errors"
#~ msgstr "Fel"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA krävs"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Omskriv HTTP-länkar till HTTPS om möjligt"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Resultat öppnas i samma fönster som "
#~ "standard. Denna insticksmodul skriver över "
#~ "standardbeteende för att öppna länkar i"
#~ " nya flikar/fönster. (JavaScript krävs)"

#~ msgid "Color"
#~ msgstr "Färg"

#~ msgid "Blue (default)"
#~ msgstr "Blå (standard)"

#~ msgid "Violet"
#~ msgstr "Violett"

#~ msgid "Green"
#~ msgstr "Grön"

#~ msgid "Cyan"
#~ msgstr "Turkos"

#~ msgid "Orange"
#~ msgstr "Orange"

#~ msgid "Red"
#~ msgstr "Röd"

#~ msgid "Category"
#~ msgstr "Kategori"

#~ msgid "Block"
#~ msgstr "Blockera"

#~ msgid "original context"
#~ msgstr "ursprungliga sammanhang"

#~ msgid "Plugins"
#~ msgstr "Insticksmoduler"

#~ msgid "Answerers"
#~ msgstr "Besvarare"

#~ msgid "Avg. time"
#~ msgstr "Genomsnittstid"

#~ msgid "show details"
#~ msgstr "visa detaljer"

#~ msgid "hide details"
#~ msgstr "göm detaljer"

#~ msgid "Load more..."
#~ msgstr "Ladda fler..."

#~ msgid "Loading..."
#~ msgstr "Läser in..."

#~ msgid "Change searx layout"
#~ msgstr "Ändra layout för searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Proxya bildresultat genom searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Detta är en lista över searxs snabbsvarsmoduler."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Detta är en lista över kakor och"
#~ " deras värden som searx lagrar på "
#~ "din dator."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Med denna lista kan du bedöma searx öppenhet."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Det ser ut som om du använder searx första gången."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Försök igen eller hitta en annan searx-instans."

#~ msgid "Themes"
#~ msgstr "Tema"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metod"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Avancerade inställningar"

#~ msgid "Close"
#~ msgstr "Stäng"

#~ msgid "Language"
#~ msgstr "Språk"

#~ msgid "broken"
#~ msgstr "sönder"

#~ msgid "supported"
#~ msgstr "stöds"

#~ msgid "not supported"
#~ msgstr "stöds inte"

#~ msgid "about"
#~ msgstr "om"

#~ msgid "Avg."
#~ msgstr "Medelvärde"

#~ msgid "User Interface"
#~ msgstr "Användargrännssnitt"

#~ msgid "Choose style for this theme"
#~ msgstr "Välj stil för detta tema"

#~ msgid "Style"
#~ msgstr "Stil"

#~ msgid "Show advanced settings"
#~ msgstr "Visa avancerade inställningar"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "Tillåt alla"

#~ msgid "Disable all"
#~ msgstr "Inaktivera alla"

#~ msgid "Selected language"
#~ msgstr "Valt språk"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "spara"

#~ msgid "back"
#~ msgstr "tillbaka"

#~ msgid "Links"
#~ msgstr "Länkar"

#~ msgid "RSS subscription"
#~ msgstr "RSS-prenumeration"

#~ msgid "Search results"
#~ msgstr "Sökresultat"

#~ msgid "next page"
#~ msgstr "nästa sida"

#~ msgid "previous page"
#~ msgstr "föregående sida"

#~ msgid "Start search"
#~ msgstr "Starta sökning"

#~ msgid "Clear search"
#~ msgstr "Rensa sökningen"

#~ msgid "Clear"
#~ msgstr "Rensa"

#~ msgid "stats"
#~ msgstr "statistik"

#~ msgid "Heads up!"
#~ msgstr "Se upp!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Det ser ut som att du använder SearXNG för första gången."

#~ msgid "Well done!"
#~ msgstr "Bra gjort!"

#~ msgid "Settings saved successfully."
#~ msgstr "Inställningar sparats."

#~ msgid "Oh snap!"
#~ msgstr "Oh plötsligt!"

#~ msgid "Something went wrong."
#~ msgstr "Något gick fel."

#~ msgid "Date"
#~ msgstr "Datum"

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Hämta bild"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "inställningar"

#~ msgid "Scores per result"
#~ msgstr "Poäng per resultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "en integritetsrespekterande, hackningsbar metasökmotor"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Inget abstract är tillgänglig för denna publikation."

#~ msgid "Self Informations"
#~ msgstr "Självinformation"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ändra hur formulär inlämnas, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">lär dig mera om "
#~ "förfrågningsmetoder</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Denna plugin kollar om IP-adressen "
#~ "av TOR-requesten är en TOR exit"
#~ " node, och informerar användaren om "
#~ "den är det, som till exempel "
#~ "check.torproject.org men från searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "TOR exit node listan "
#~ "(https://check.torproject.org/exit-addresses) går "
#~ "inte at nås för tillfället."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Du använder TOR. Din IP-adress verkar vara: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Du använder inte TOR. Din IP-adress verkar vara: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Upptäck sökspråk automatiskt"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Upptäck automatiskt webbsökningens språk och byt till det."

#~ msgid "others"
#~ msgstr "andra"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Den här fliken visas inte för "
#~ "sökresultat, men du kan söka på "
#~ "motorerna som listas här via bangs."

#~ msgid "Shortcut"
#~ msgstr "Genväg"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Den här fliken finns inte i "
#~ "gränssnittet, men du kan söka i "
#~ "dessa sökmotorer med deras !bangs."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Sökmotorer kan inte hämta resultat."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Var snäll och försök igen senare eller hitta annan SearXNG-instans."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Omdirigera till öppna versioner av "
#~ "publikationer när de är tillgängliga "
#~ "(tillägg krävs)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ändra hur formulär skickas, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">läs mer om "
#~ "förfrågningsmetoder</a>"

#~ msgid "On"
#~ msgstr "På"

#~ msgid "Off"
#~ msgstr "Av"

#~ msgid "Enabled"
#~ msgstr "Aktiverad"

#~ msgid "Disabled"
#~ msgstr "Inaktiverad"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Utför sökning omedelbart om en kategori"
#~ " är vald. Inaktivera att välja flera"
#~ " kategorier. (JavaScript krävs)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim-liknande snabbtangenter"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navigera sökresultat med Vim-liknande "
#~ "snabbtangenter (JavaScript krävs). Tryck på"
#~ " \"h\"-tangenten på huvud- eller "
#~ "resultatsida för att få hjälp."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "vi hittade inte några resultat. Använd"
#~ " en annan förfråga eller sök i "
#~ "flera kategorier."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Skriv om resultat värdnamn eller ta bort resultat baserat på värdnamnet"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Värdnamn satt"

#~ msgid "Error!"
#~ msgstr "Fel!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Sökmotorerna kan inte hämta resultat"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Rapportera ett nytt problem på GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Slumpvärdesgenerator"

#~ msgid "Statistics functions"
#~ msgstr "Statistikfunktioner"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Beräkna {functions} av argumenten"

#~ msgid "Get directions"
#~ msgstr "Få vägbeskrivningar"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Visar din IP om förfrågan är "
#~ "\"ip\" och din användaragent om "
#~ "förfrågan innehåller \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Det gick inte att ladda ner listan"
#~ " över Tor-utgångsnoder från: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Du använder Tor och det ser ut "
#~ "som att du har denna externa "
#~ "IP-adress: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Du använder inte Tor och du har denna externa IP-adress: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Nyckelord"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Dina anpassade inställningar i "
#~ "inställningarnas URL kan användas för "
#~ "att synkronisera inställningar mellan olika"
#~ " enheter."

#~ msgid "proxied"
#~ msgstr "proxade"
