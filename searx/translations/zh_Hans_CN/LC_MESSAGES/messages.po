# Chinese (Simplified, China) translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# RainSlide, 2018
# J<PERSON>hon, 2019
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON>, 2018
# wenke, 2015
# wenke, 2015-2018
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# Jones<PERSON><PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# err_connection_closed
# <<EMAIL>>, 2023, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-01 06:39+0000\n"
"Last-Translator: Outbreak2096 <<EMAIL>>\n"
"Language-Team: Chinese (Simplified Han script) <https://"
"translate.codeberg.org/projects/searxng/searxng/zh_Hans/>\n"
"Language: zh_Hans_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "无需进一步分组"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "其他"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "文件"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "综合"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "音乐"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "社交媒体"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "图片"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "视频"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "电台"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "电视"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "信息技术"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "新闻"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "地图"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "洋葱网络"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "科学"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "应用"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "字典"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "歌词"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "程序包"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "问答"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "软件仓库"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "软件维基"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "网页"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "学术文章"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "自动"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "浅色"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "深色"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "黑色"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "运行时间"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "关于"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "平均温度"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "云量"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "条件"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "当前状况"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "傍晚"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "体感温度"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "湿度"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "最高温度"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "最低温度"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "上午"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "夜晚"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "中午"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "气压"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "日出"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "日落"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "温度"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "紫外线强度"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "能见度"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "风"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "晴朗"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "多云"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "晴"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "雾"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "大雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "大阵雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "大阵雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "大雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "大冻雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "大冻雨阵雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "大冻雨阵雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "大冻雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "大雪伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "大阵雪伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "大阵雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "大雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "小雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "小阵雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "小阵雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "小雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "小冻雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "小冻雨阵雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "小冻雨阵雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "小冻雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "小雪伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "小阵雪伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "小阵雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "小雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "局部多云"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "阵雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "阵雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "冻雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "冻雨阵雨伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "冻雨阵雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "冻雨"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "雪伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "阵雪伴有雷声"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "阵雪"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "雪"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "订阅者"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "帖子"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "活跃用户"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "评论"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "用户"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "版块"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "积分"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "标题"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "作者"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "打开"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "已关闭"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "回答"

#: searx/webapp.py:292
msgid "No item found"
msgstr "未找到项目"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "来源"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "载入下个页面时发生错误"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "无效设置，请编辑您的首选项"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "无效设置"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "搜索错误"

#: searx/webutils.py:35
msgid "timeout"
msgstr "超时"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "解析错误"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP 协议错误"

#: searx/webutils.py:38
msgid "network error"
msgstr "网络错误"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL 错误：证书校验失败"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "意外崩溃"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP 错误"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP 连接错误"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "代理错误"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "验证码"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "请求过于频繁"

#: searx/webutils.py:58
msgid "access denied"
msgstr "拒绝访问"

#: searx/webutils.py:59
msgid "server API error"
msgstr "服务器 API 错误"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "暂停服务"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} 分钟前"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} 小时 {minutes} 分钟前"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "生成不同的随机数"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "计算参数的 {func}"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "在地图上显示路线…"

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} （已过时）"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "此条目已被以下内容取代"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "频道"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "比特率"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "投票数"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "点击数"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "语言"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{firstCitationVelocityYear} 年至 {lastCitationVelocityYear} 年间总计 "
"{numCitations} 次引用"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr "无法读取该图片网址。这可能是由于文件格式不受支持。TinEye 仅支持 JPEG、PNG、GIF、BMP、TIFF 或 WebP 格式的图像。"

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr "图像过于简单，无法找到匹配项。TinEye 需要基本级别的视觉细节才能成功识别匹配项。"

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "无法下载该图像。"

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "书籍评分"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "文件质量"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia 黑名单"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "过滤掉出现在 Ahmia 黑名单中的洋葱结果。"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "基础计算器"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "通过搜索栏计算数学表达式"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "散列插件"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "将字符串转换为不同的散列（hash）摘要值。"

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "散列摘要值"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "主机名插件"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "重写主机名、删除结果或根据主机名确定优先级"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "开放访问 DOI 重写"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "尽可能重定向到开放访问的版本以免被要求付费"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "自身信息"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr "如果查询是“ip”，则显示您的 IP；如果查询是“user-agent”，则显示您的用户代理。"

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "您的 IP 是： "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "您的用户代理是： "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor 网络检测插件"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr "此插件检查请求地址是否为 Tor 出口节点，若是则告知用户。这是由 SearXNG 提供的类似 check.torproject.org 的服务。"

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "无法从以下位置下载 Tor 出口节点列表"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "您正在使用 Tor，并且您似乎有外部 IP 地址"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "您未使用 Tor，并且您有外部 IP 地址"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "URL 跟踪参数移除工具"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "从返回的 URL 中移除跟踪参数"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "单位转换器插件"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "单位间转换"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}：{temperature}，{condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "未找到网页"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "前往 %(search_page)s。"

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "搜索页面"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "捐款"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "首选项"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "功能来自"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "尊重隐私的开源元搜索引擎"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "源代码"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "问题跟踪系统"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "搜索引擎统计"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "公共实例"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "隐私政策"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "联系实例维护者"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "点击放大镜按钮开始搜索"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "长度"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "查看次数"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "作者"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "快照"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "在 GitHub 上开始提交新问题"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "请在 GitHub 上检查有关此引擎的现有错误"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "我确定提交的 Bug 没有与现存 Issue 重复"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "如果这是公共实例，请在错误报告中指定 URL"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "在 GitHub 上提交包含上述信息的 Issue"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "无 HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "查看错误日志并提交错误报告"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "对于这个引擎的 !bang"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "对于这个类别的 !bang"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "中位数"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "检查程序测试失败： "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "错误："

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "常规"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "默认类别"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "用户界面"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "隐私"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "搜索引擎"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "目前使用的搜索引擎"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "特殊查询"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookie"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "结果个数"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "信息"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "返回顶部"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "上一页"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "下一页"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "显示前端页面"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "搜索…"

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "清除"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "搜索"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "目前没有可用的数据。 "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "引擎名称"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "得分"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "结果数量"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "响应时间"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "可靠性"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "总计"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "正在处理"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "警告"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "错误和异常"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "异常"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "消息"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "百分比"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "参数"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "文件名"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "函数"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "代码"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "检查程序"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "测试未通过"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "注释"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "示例"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "定义"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "近义词"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "体感温度"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "答案"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "下载结果"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "尝试搜索："

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "来自搜索引擎的消息"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "秒"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "搜索 URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "已复制"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "复制"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "搜索建议"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "搜索语言"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "默认语言"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "自动检测"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "安全搜索"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "严格"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "中等"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "无"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "时间范围"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "不限时间"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "一天内"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "一周内"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "一月内"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "一年内"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "信息！"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "目前还没有在 Cookie 中存储任何信息。"

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "抱歉！"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "未找到结果，您可以尝试："

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "没有更多结果，您可以尝试："

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "刷新页面。"

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "（在上方）对其他查询进行搜索，或选择其他类别。"

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "更改“首选项”中使用的搜索引擎："

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "切换至另一个 SearXNG 实例："

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "对其他查询进行搜索，或选择其他类别。"

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "使用返回按钮按钮返回上一页。"

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "允许"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "关键词（查询中的第一个词）"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "名称"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "描述"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "这是 SearXNG 即时回应模块的列表。"

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "这是插件列表。"

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "自动补全"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "自动补全字词"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "居中对齐"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "在页面中心显示结果（Oscar 布局）。"

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "这是 SearXNG 在您的计算机上存储的 Cookie 的列表及相应的值。"

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "您可以凭此列表评估 SearXNG 的透明度。"

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Cookie 名称"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "值"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "当前保存的首选项的搜索 URL"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"注意：当您从 SearXNG 搜索结果页点击进入一个网站时，SearXNG 搜索结果页的 URL 将会在请求头的 Referer "
"字段中发送给目标网站服务器。如果您的设置了自定义搜索，URL 中将会包含您的个性化设置参数（如语言等），它们会被目标网站得知，这不利于您的隐私。"

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "用于在其他浏览器上还原首选项的 URL"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr "包含您首选项的 URL。此 URL 可用于在其他设备上还原您的设置。"

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "复制首选项散列"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "插入复制的首选项散列（不带 URL）进行还原"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "首选项散列"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "数字对象唯一标识符（DOI）"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "开放访问 DOI 解析器"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "选择 DOI 重写使用的服务"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr "此标签页在用户界面中不存在，但您可以使用 !bang 在这些引擎中进行搜索。"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "启用所有"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "禁用所有"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "支持选定的语言"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "权重"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "最大用时"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "网站图标"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "在搜索结果附近显示网站图标"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr "这些设置被存储在您的 Cookie 中，这种保存设置的方式使我们不必保存您的设置数据。"

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr "这些 Cookie 信息用于辅助您便捷地使用本服务，我们绝不利用这些信息来跟踪您。"

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "保存"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "恢复默认设置"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "返回"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "热键"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim 布局"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr "使用热键进行导航（需要 JavaScript）。在主页或搜索结果页按“h”键获取帮助。"

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "图片代理"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "通过 SearXNG 代理访问图片结果"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "无限滚动（瀑布流）"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "滚动到当前页面底部时自动加载下一页"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "您偏好搜索哪种语言？"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "选择自动检测可使 SearXNG 检测您的检索关键词的语言。"

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP 方法"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "更改表格提交的方式"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "页面标题显示查询关键词"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr "启用后，结果页的标题会包含您的查询。您的浏览器可以记录此标题"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "在新标签页打开搜索结果"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "在新标签页打开搜索结果中的链接"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "内容过滤"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "在选中类别时立即进行搜索"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr "当一个类别被指定时，立即执行搜索。禁用以选择多个类别"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "主题"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "更改 SearXNG 布局"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "主题样式"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "选择自动以跟随您的浏览器设置"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "引擎令牌"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "私有引擎的访问令牌"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "界面语言"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "更改界面语言"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL 格式"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "美观"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "完整"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "主机"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "更改结果 URL 格式"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "软件仓库"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "显示媒体"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "隐藏媒体"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "此站点未提供任何描述。"

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "文件大小"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "日期"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "输入"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "分辨率"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "格式"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "引擎"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "查看来源"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "地址"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "显示地图"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "隐藏地图"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "版本"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "维护者"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "更新于"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "标签"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "流行"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "许可证"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "项目"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "项目主页"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "发布日期"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "杂志"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "编者"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "出版者"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "磁力链接"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "种子文件"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "做种用户"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "下载用户"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "文件数"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "显示视频"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "隐藏视频"

#~ msgid "Engine time (sec)"
#~ msgstr "搜索引擎时间（秒）"

#~ msgid "Page loads (sec)"
#~ msgstr "页面加载（秒）"

#~ msgid "Errors"
#~ msgstr "错误"

#~ msgid "CAPTCHA required"
#~ msgstr "要求验证码"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "将支持 HTTPS 的 HTTP 链接改为 HTTPS 链接"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr "搜索结果默认在原窗口打开。此插件能使其在新标签页或新窗口打开。（需启用 JavaScript）"

#~ msgid "Color"
#~ msgstr "颜色"

#~ msgid "Blue (default)"
#~ msgstr "蓝色（默认）"

#~ msgid "Violet"
#~ msgstr "紫色"

#~ msgid "Green"
#~ msgstr "绿色"

#~ msgid "Cyan"
#~ msgstr "青色"

#~ msgid "Orange"
#~ msgstr "橙色"

#~ msgid "Red"
#~ msgstr "红色"

#~ msgid "Category"
#~ msgstr "类别"

#~ msgid "Block"
#~ msgstr "阻止"

#~ msgid "original context"
#~ msgstr "原始上下文"

#~ msgid "Plugins"
#~ msgstr "插件"

#~ msgid "Answerers"
#~ msgstr "智能答复"

#~ msgid "Avg. time"
#~ msgstr "平均时间"

#~ msgid "show details"
#~ msgstr "显示详细信息"

#~ msgid "hide details"
#~ msgstr "隐藏详细信息"

#~ msgid "Load more..."
#~ msgstr "载入更多……"

#~ msgid "Loading..."
#~ msgstr "正在加载..."

#~ msgid "Change searx layout"
#~ msgstr "改变 searx 布局"

#~ msgid "Proxying image results through searx"
#~ msgstr "通过 searx 代理图片结果"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "这是 searx 的即时回答模块列表。"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "此列表展示了 searx 在您设备上存储的 cookie 信息。"

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "您可以基于此表格来评估 searx 的透明度。"

#~ msgid "It look like you are using searx first time."
#~ msgstr "看来这是您第一次使用 searx。"

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "请稍后再试，或寻找其它的 searx 实例替代。"

#~ msgid "Themes"
#~ msgstr "主题"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "方法"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "高级设置"

#~ msgid "Close"
#~ msgstr "关闭"

#~ msgid "Language"
#~ msgstr "语言"

#~ msgid "broken"
#~ msgstr "故障"

#~ msgid "supported"
#~ msgstr "支持"

#~ msgid "not supported"
#~ msgstr "不支持"

#~ msgid "about"
#~ msgstr "关于"

#~ msgid "Avg."
#~ msgstr "平均"

#~ msgid "User Interface"
#~ msgstr "用户界面"

#~ msgid "Choose style for this theme"
#~ msgstr "选择此主题的样式"

#~ msgid "Style"
#~ msgstr "样式"

#~ msgid "Show advanced settings"
#~ msgstr "显示高级设置"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "首页默认显示高级设置面板"

#~ msgid "Allow all"
#~ msgstr "全部允许"

#~ msgid "Disable all"
#~ msgstr "全部禁用"

#~ msgid "Selected language"
#~ msgstr "选择语言"

#~ msgid "Query"
#~ msgstr "查询"

#~ msgid "save"
#~ msgstr "保存"

#~ msgid "back"
#~ msgstr "返回"

#~ msgid "Links"
#~ msgstr "链接"

#~ msgid "RSS subscription"
#~ msgstr "RSS 订阅"

#~ msgid "Search results"
#~ msgstr "搜索结果"

#~ msgid "next page"
#~ msgstr "下一页"

#~ msgid "previous page"
#~ msgstr "上一页"

#~ msgid "Start search"
#~ msgstr "开始搜索"

#~ msgid "Clear search"
#~ msgstr "清除搜索"

#~ msgid "Clear"
#~ msgstr "清除"

#~ msgid "stats"
#~ msgstr "统计"

#~ msgid "Heads up!"
#~ msgstr "小心！"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "这似乎是您首次使用 SearXNG。"

#~ msgid "Well done!"
#~ msgstr "很不错！"

#~ msgid "Settings saved successfully."
#~ msgstr "设置保存成功。"

#~ msgid "Oh snap!"
#~ msgstr "哦，糟糕！"

#~ msgid "Something went wrong."
#~ msgstr "出了些问题。"

#~ msgid "Date"
#~ msgstr "日期"

#~ msgid "Type"
#~ msgstr "类型"

#~ msgid "Get image"
#~ msgstr "获取图片"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "首选项"

#~ msgid "Scores per result"
#~ msgstr "各结果得分"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "一个尊重隐私、可二次开发的元搜索引擎"

#~ msgid "No abstract is available for this publication."
#~ msgstr "此出版物没有可用的摘要。"

#~ msgid "Self Informations"
#~ msgstr "自身信息"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "更改提交表单时使用的请求方法，<a "
#~ "href=\"https://zh.wikipedia.org/wiki/超文本传输协议#请求方法\" "
#~ "rel=\"external\">深入了解请求方法</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "该插件检查请求的 IP 地址是否为 Tor 出口节点，如果是则通知用户，可视为 "
#~ "searxng 中的 check.torproject.org 。"

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr "无法获取 Tor 出口节点列表（https://check.torproject.org/exit-addresses）。"

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "你在使用 Tor。你的 IP 地址应该是{ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "你没有在使用 Tor。你的 IP 地址应该是{ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "自动检测搜索语言"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "自动检测查询搜索语言并切换到它。"

#~ msgid "others"
#~ msgstr "其他"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr "这个标签页不会显示在搜索结果中，但您可以通过 ! 搜索这里列出的引擎。"

#~ msgid "Shortcut"
#~ msgstr "快捷键"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr "这个标签页在用户界面中不存在，但你可以用 !bang 对这些引擎进行搜索"

#~ msgid "Engines cannot retrieve results."
#~ msgstr "引擎无法检索到结果。"

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "请稍后再试，或换用其他 SearXNG 站点试试看。"

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr "尽可能重定向到出版物的开放访问版本（需要插件）"

#~ msgid "Bang"
#~ msgstr "快捷方式"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "更改表单的提交方式， <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">详细了解请求方法</a>"

#~ msgid "On"
#~ msgstr "启用"

#~ msgid "Off"
#~ msgstr "禁用"

#~ msgid "Enabled"
#~ msgstr "启用"

#~ msgid "Disabled"
#~ msgstr "禁用"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr "选择一个类别后立即开始搜索。禁用后可以一次选中多个类别。（需启用 JavaScript）"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim 式快捷键"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr "使用 Vim 式快捷键浏览搜索结果（需启用 JavaScript）。在主页或结果页面按“h”键获取帮助。"

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr "我们没有找到任何结果。请使用其他关键词，或在更多类别中搜索。"

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "重写结果的主机名或基于主机名移除结果"

#~ msgid "Bytes"
#~ msgstr "字节"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "主机名替换"

#~ msgid "Error!"
#~ msgstr "错误！"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "引擎无法检索到结果"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "在 GitHub 上提交 Issue"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "随机数生成器"

#~ msgid "Statistics functions"
#~ msgstr "统计功能"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "计算 {functions} 参数"

#~ msgid "Get directions"
#~ msgstr "获取路线"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr "当您搜索“ip”时，这将会显示您的 IP 地址；同理，在搜索“user agent”时，将会显示您的 User Agent。"

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr "未能从此地址下载 Tor 出口节点的列表： https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "您似乎在使用 Tor，您的外部 IP 地址为： {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "您并未使用 Tor，您的外部 IP 地址为： {ip_address}"

#~ msgid "Keywords"
#~ msgstr "关键词"

#~ msgid "/"
#~ msgstr "/"

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr "在首选项 URL 中指定可跨设备同步的偏好设置。"

#~ msgid "proxied"
#~ msgstr "已代理"
