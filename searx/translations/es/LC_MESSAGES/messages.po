# Spanish translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON>, 2014
# <PERSON>, 2014-2018
# <PERSON>, 2016
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2017
# <PERSON>, 2016
# <PERSON>, 2018,2020
# O <b204fbaf817497f9ea35edbcc051de81_265921>, 2015
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022, 2023.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-06 05:50+0000\n"
"Last-Translator: realkendrick_fr <<EMAIL>>\n"
"Language-Team: Spanish <https://translate.codeberg.org/projects/searxng/"
"searxng/es/>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "sin más subgrupos"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "otro"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "archivos"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "general"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "música"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "redes sociales"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "imágenes"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "vídeos"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "Informática"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "noticias"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "ciencia"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplicaciones"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "diccionarios"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "letras"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paquetes"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "preguntas y respuestas"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repositorios"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wikis de software"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "artículos científicos"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automático"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "claro"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "oscuro"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "negro"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Tiempo de actividad"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Acerca de"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Temperatura promedio"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Cubierto de nubes"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Condición"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Condición actual"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Tarde"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Sensación"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Humedad"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Temperatura máxima"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Temperatura mínima"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Mañana"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noche"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Mediodía"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Presión"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Amanecer"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Atardecer"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Índice UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Visibilidad"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Viento"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Cielo despejado"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Nublado"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Bueno"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Niebla"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Lluvia pesada y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Lluvias pesadas y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Lluvias intensas"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Lluvia intensa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Granizo intenso y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Lluvia de granizo intenso y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Lluvia de granizo intenso"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Granizo intenso"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Nieve pesada y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Lluvia de nieve intensa y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Lluvia de nieve intensa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Nieve intensa"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Lluvia ligera y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Lluvias ligeras y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Lluvias ligeras"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Lluvia ligera"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Granizo ligero y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Lluvia de granizo ligero y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Lluvia de granizo ligero"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Granizo ligero"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Nieve ligera y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Lluvia de nieve ligera y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Lluvia de nieve ligera"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Nieve ligera"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Parcialmente nublado"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Lluvia y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Lluvias y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Lluvias"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Lluvia"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Granizo y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Lluvia de granizo y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Lluvia de granizo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Granizo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Nieve y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Lluvia de nieve y truenos"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Lluvia de nieve"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Nieve"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "suscriptores"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "publicaciones"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "usuarios activos"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "comentarios"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "usuario"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "comunidad"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "puntos"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "título"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "abrir"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "cerrar"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "contestado"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Ningún artículo encontrado"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Fuente"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Error al cargar la siguiente página"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Ajustes inválidos, por favor, cambia tus preferencias"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Ajustes inválidos"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "error en la búsqueda"

#: searx/webutils.py:35
msgid "timeout"
msgstr "expirado"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "error de análisis"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Error de protocolo HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "error de red"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Error SSL: la validación del certificado ha fallado"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "cierre inesperado"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "Error de HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Error de conexión HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "error de proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "demasiadas peticiones"

#: searx/webutils.py:58
msgid "access denied"
msgstr "acceso denegado"

#: searx/webutils.py:59
msgid "server API error"
msgstr "error en la API del servidor"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspendido"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "hace {minutes} minuto(s)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "hace {hours} hora(s) y {minutes} minuto(s)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generar varios valores aleatorios"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Calcular {func} de los argumentos"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Ver Ruta en el mapa .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLETO)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Esta entrada ha sido sustituida por"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Canal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitrate"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "votos"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "clics"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Idioma"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} referencias desde el año {firstCitationVelocityYear} hasta"
" {lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"No se pudo leer la URL de esa imagen. Esto puede deberse a un formato de "
"archivo no compatible. TinEye solo admite imágenes que son JPEG, PNG, "
"GIF, BMP, TIFF o WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"La imagen es demasiado simple para encontrar coincidencias. TinEye "
"requiere más detalle visual para identificar con éxito las coincidencias."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "No se pudo descargar la imagen."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Valoración del libro"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Calidad del archivo"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Lista negra de Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrar resultados de onion que aparezcan en la lista negra de Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Calculadora básica"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Calcula expresiones matemáticas a través de la barra de búsqueda"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin de hash"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Convierte cadenas de texto a diferentes resúmenes hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "resumen de hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Plugin del hostname"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Reescribir los hostnames, remover los resultados o priorizarlos segundo "
"sus hostnames"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Reescribir DOI (Identificador de objeto digital) de Open Access"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Evitar barreras de pago redireccionando a las versiones de acceso libre "
"de las publicaciones cuando estén disponibles"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Información propia"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Muestra tu IP si la consulta es \"ip\" y tu agente de usuario si la "
"consulta es \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Tu IP es: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Tu user-agent es: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Plugin de comprobación de Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Este plug-in comprueba si la dirección de las solicitudes son nodo de "
"salida de Tor, e informa al usuario si lo es; como check.torproject.org, "
"pero desde SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "No se pudo descargar la lista de nodos de salida de Tor desde"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Estás utilizando Tor y parece que tienes la dirección IP externa"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "No estás utilizando Tor y tienes la dirección IP externa"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Removedor de URL rastreadora"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Remueve los argumentos de rastreadores de la URL devuelta"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Plugin conversor de unidades"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Convertir unidades"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Página no encontrada"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Ir a %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "página de búsqueda"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Donar"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferencias"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Desarrollado por"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "Un metabuscador de código abierto que respeta la privacidad"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Código fuente"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Rastreador de problemas"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Estadísticas del motor de búsqueda"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instancias públicas"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Politica de privacidad"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Contactar al mantenedor de la instancia"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Haz clic en la lupa para realizar la búsqueda"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Longitud"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Visualizaciones"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "en caché"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Enviar un nuevo problema a GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Por favor revisa si ya existe un problema con este motor en GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Confirmo que no existe un bug relacionado al problema que encontré"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Si esta es una instancia pública, por favor especifíca la URL en el "
"reporte del bug"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Enviar un nuevo problema a Github que incluya la información de arriba"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "No HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Ver los logs de errores y enviar un informe de error"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang para este motor de búsqueda"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang para estas categorías"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Media"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Prueba de verificación fallida. "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Errores:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "General"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Categorías predeterminadas"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interfaz de usuario"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privacidad"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Motores"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Motores de búsqueda actualmente en uso"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Consultas Especiales"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Número de resultados"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Información"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Inicio"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Página anterior"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Siguiente página"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Mostrar La Página Principal"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Buscar..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "limpiar"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "buscar"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Actualmente no hay datos disponibles. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nombre del motor de búsqueda"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Puntuaciones"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Resultados"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Tiempo de respuesta"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Fiabilidad"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Procesando"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Alertas"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Errores y excepciones"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Excepción"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mensaje"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Porcentaje"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parámetro"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nombre de archivo"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Función"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Código"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Verificador"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Prueba fallida"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Comentario(s)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Ejemplos"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definiciones"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinónimos"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Se Siente Como"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Respuestas"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Descargar resultados"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Intenta buscar:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Mensajes de los motores de búsqueda"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "segundos"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL de la búsqueda"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Copiado"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copiar"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Sugerencias"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Idioma de búsqueda"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Idioma por defecto"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Detección automática"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Búsqueda segura"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Estricto"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderado"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Ninguno"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Rango de tiempo"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "En cualquier momento"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Último día"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Última semana"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Último mes"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Último año"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "¡Información!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "No existen cookies definidas actualmente."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "¡Lo sentimos!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "No se encontraron resultados. Puedes intentar:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "No hay más resultados. Puedes probar a:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Recarga la página."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Haz otra consulta o selecciona otra categoría (arriba)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Cambiar el motor de búsqueda utilizado en las preferencias:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Cambiar a otra instancia:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Realiza otra consulta o selecciona otra categoría."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Vuelve a la página anterior usando el botón de página anterior."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Permitir"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Palabras clave (primera palabra en la consulta)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nombre"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Descripción"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Esta es la lista de módulos de respuestas instantáneas de SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Esta es la lista de plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Autocompletar"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Buscar mientras escribes"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Alineación central"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Muestra los resultados en el centro de la página (diseño Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Esta es la lista de cookies y sus valores que SearXNG está almacenando en"
" tu ordenador."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Con esa lista, puedes comprobar la transparencia de SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nombre de la cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valor"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Buscar URL de las preferencias guardadas actualmente"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Nota: especificar configuraciones personalizadas en la URL de búsqueda "
"puede reducir la privacidad por filtrar datos a los sitios de resultados "
"en los que se ha hecho clic."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL para restaurar sus preferencias en otro navegador"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Una URL conteniendo sus preferencias. Esta URL puede ser usada para "
"restaurar sus ajustes en un dispositivo diferente."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copiar el hash de preferencias"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Inserte el hash de preferencias copiado (sin URL) para restaurar"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash de preferencias"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Identificador de Objeto Digital (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Resolutor de DOI de acceso abierto"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Elije el servicio utilizado para reescribir DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Esta pestaña no existe en la interfaz de usuario, pero puedes buscar en "
"estos motores por sus !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Activar todo"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Desactivar todo"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Soporta el idioma seleccionado"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Peso"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Tiempo máximo"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Buscador de favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Mostrar los favicons al lado de los resultados de búsqueda"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Esta configuración se guarda en sus cookies, lo que nos permite no "
"almacenar dicha información sobre usted."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Estas cookies son para su propia comodidad, no las utilizamos para "
"rastrearte."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Guardar"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Restablecer configuración por defecto"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Atrás"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Atajo de teclado"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Similar a Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navega por los resultados de la búsqueda con las teclas de acceso rápido "
"(se requiere JavaScript). Presiona la tecla \"h\" en la página principal "
"o de los resultados para obtener ayuda."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy de imágenes"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Cargando los resultados de imágenes a través de SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Deslizamiento infinito"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Cargar automáticamente la siguiente página al deslizarse hasta el final "
"de la página actual"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "¿Qué idioma prefieres para la búsqueda?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Seleccione Detección automática para que SearXNG detecte el idioma de su "
"consulta."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Método HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Cambiar cómo se envían los formularios"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Petición en el titulo de la pagina"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Cuando se activa, la página de resultados contendrá tu búsqueda. Tu "
"buscador puede guardar este título"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultados en nuevas pestañas"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Abrir los resultados en nuevas pestañas del navegador"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtro de contenido"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Buscar en la categoría seleccionada"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Realiza la búsqueda inmediatamente si se selecciona una categoría. "
"Desactivar para seleccionar varias categorías"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Cambiar la interfaz de SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Estilo del tema"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Escoge automático para seguir la configuración de tu navegador"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Llaves de motores"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Llaves de acceso para motores privados"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Idioma de la interfaz"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Cambiar idioma de la interfaz"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formatacion de la URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Bonito"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Lleno"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Anfitrion"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Cambiar el formato de la URL del resultado"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repositorios"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "mostrar multimedia"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "ocultar multimedia"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Este sitio no provee ninguna descripción."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Tamaño de archivo"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Fecha"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tipo"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolución"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formato"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Ver fuente"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "dirección"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "mostrar mapa"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ocultar mapa"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versión"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Administrador"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Actualizado en"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etiquetas"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularidad"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licencia"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Proyecto"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Página de inicio del proyecto"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Fecha de Publicación"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Periódicos"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editor"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Publicador"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "enlace magnético"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "archivo torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeders"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leechers"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Número de archivos"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "mostrar vídeo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ocultar video"

#~ msgid "Engine time (sec)"
#~ msgstr "Motor de tiempo (seg)"

#~ msgid "Page loads (sec)"
#~ msgstr "Tiempo de carga (segundos)"

#~ msgid "Errors"
#~ msgstr "Errores"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA obligatorio"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Cambiar los enlaces HTTP a HTTPS si es posible"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Los resultados se abren en la "
#~ "misma ventana por defecto. Este plugin"
#~ " sobrescribe el comportamiento por defecto"
#~ " para abrir enlaces en nuevas "
#~ "pestañas / ventanas. (es necesario "
#~ "JavaScript)"

#~ msgid "Color"
#~ msgstr "Color"

#~ msgid "Blue (default)"
#~ msgstr "Azul (predeterminado)"

#~ msgid "Violet"
#~ msgstr "Violeta"

#~ msgid "Green"
#~ msgstr "Verde"

#~ msgid "Cyan"
#~ msgstr "Cian"

#~ msgid "Orange"
#~ msgstr "Naranja"

#~ msgid "Red"
#~ msgstr "Rojo"

#~ msgid "Category"
#~ msgstr "Categoría"

#~ msgid "Block"
#~ msgstr "Bloquear"

#~ msgid "original context"
#~ msgstr "contexto original"

#~ msgid "Plugins"
#~ msgstr "Plugins"

#~ msgid "Answerers"
#~ msgstr "Respondedores"

#~ msgid "Avg. time"
#~ msgstr "Tiempo promedio"

#~ msgid "show details"
#~ msgstr "ver detalles"

#~ msgid "hide details"
#~ msgstr "ocultar detalles"

#~ msgid "Load more..."
#~ msgstr "Cargar más"

#~ msgid "Loading..."
#~ msgstr "Cargando..."

#~ msgid "Change searx layout"
#~ msgstr "Cambiar aspecto de searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Filtrado de resultados de imágenes en searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Esta es la lista de los módulos de respuesta inmediata de searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Esta es la lista de cookies y "
#~ "sus valores que searx está almacenando"
#~ " en tu ordenador."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Con esa lista puedes valorar la transparencia de searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Parece que estás usando searx por primera vez."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""
#~ "Por favor, inténtelo de nuevo más "
#~ "tarde o busque otra instancia de "
#~ "searx."

#~ msgid "Themes"
#~ msgstr "Temas"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Método"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Ajustes avanzados"

#~ msgid "Close"
#~ msgstr "Cerrar"

#~ msgid "Language"
#~ msgstr "Lenguaje"

#~ msgid "broken"
#~ msgstr "roto"

#~ msgid "supported"
#~ msgstr "soportado"

#~ msgid "not supported"
#~ msgstr "no soportado"

#~ msgid "about"
#~ msgstr "acerca de"

#~ msgid "Avg."
#~ msgstr "Media"

#~ msgid "User Interface"
#~ msgstr "Interfaz de usuario"

#~ msgid "Choose style for this theme"
#~ msgstr "Elige un estilo para este tema"

#~ msgid "Style"
#~ msgstr "Estilo"

#~ msgid "Show advanced settings"
#~ msgstr "Mostrar las opciones avanzadas"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""
#~ "Mostrar el panel de opciones avanzadas"
#~ " en la página principal por defecto"

#~ msgid "Allow all"
#~ msgstr "Permitir todo"

#~ msgid "Disable all"
#~ msgstr "Deshabilitar todo"

#~ msgid "Selected language"
#~ msgstr "Idioma elegido"

#~ msgid "Query"
#~ msgstr "Petición"

#~ msgid "save"
#~ msgstr "Guardar"

#~ msgid "back"
#~ msgstr "Atrás"

#~ msgid "Links"
#~ msgstr "Enlaces"

#~ msgid "RSS subscription"
#~ msgstr "Suscripción RSS"

#~ msgid "Search results"
#~ msgstr "Resultados de búsqueda"

#~ msgid "next page"
#~ msgstr "Página siguiente"

#~ msgid "previous page"
#~ msgstr "Página anterior"

#~ msgid "Start search"
#~ msgstr "Comenzar búsqueda"

#~ msgid "Clear search"
#~ msgstr "Limpiar búsqueda"

#~ msgid "Clear"
#~ msgstr "Limpiar"

#~ msgid "stats"
#~ msgstr "Estadísitcas"

#~ msgid "Heads up!"
#~ msgstr "¡Atención!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Parece que estás utilizando SearXNG por primera vez."

#~ msgid "Well done!"
#~ msgstr "¡Bien hecho!"

#~ msgid "Settings saved successfully."
#~ msgstr "Configuración guardada correctamente."

#~ msgid "Oh snap!"
#~ msgstr "¡Mecachis!"

#~ msgid "Something went wrong."
#~ msgstr "Algo ha ido mal."

#~ msgid "Date"
#~ msgstr "Fecha"

#~ msgid "Type"
#~ msgstr "Tipo"

#~ msgid "Get image"
#~ msgstr "Obtener imagen"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferencias"

#~ msgid "Scores per result"
#~ msgstr "Puntuaciones por resultado"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "un metabuscador hackeable que respeta la privacidad"

#~ msgid "No abstract is available for this publication."
#~ msgstr "No hay resúmenes disponibles para esta publicación."

#~ msgid "Self Informations"
#~ msgstr "Información propia"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Modifica cómo se envían los formularios"
#~ " <a "
#~ "href=\"http://es.wikipedia.org/wiki/Hypertext_Transfer_Protocol#M.C3.A9todos_de_petici.C3.B3n\""
#~ " rel=\"external\">más información sobre métodos"
#~ " de peticiones</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Este plugin verifica si la dirección "
#~ "de la solicitud es un nodo de "
#~ "salida TOR e informa al usuario si"
#~ " lo es, como check.torproject.org pero "
#~ "desde searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Imposible de acceder a la lista de"
#~ " nodos de salida de TOR "
#~ "(https://check.torproject.org/exit-addresses)."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Estás usando TOR. Tu dirección IP parece ser: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "No estás usando TOR. Tu dirección IP parece ser: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Detectar el lenguaje de búsqueda automáticamente"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Detectar el lenguaje de búsqueda automáticamente y usarlo."

#~ msgid "others"
#~ msgstr "otros"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Esta pestaña no se muestra para "
#~ "los resultados de búsqueda, pero puedes"
#~ " buscar con los motores aquí listado"
#~ " mediante bangs."

#~ msgid "Shortcut"
#~ msgstr "Atajo"

#~ msgid "!bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Esta pestaña no existe en la "
#~ "interfaz de usuario, pero puedes buscar"
#~ " en estos motores por sus !bangs."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Los motores no pueden obtener resultados."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Por favor, prueba más tarde o encuentra otra instancia de SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Redireccionar a versiones de acceso "
#~ "abierto de las publicaciones cuando "
#~ "estén disponibles (se requiere plugin)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Modifica cómo se envían los formularios"
#~ " <a "
#~ "href=\"http://es.wikipedia.org/wiki/Hypertext_Transfer_Protocol#M.C3.A9todos_de_petici.C3.B3n\""
#~ " rel=\"external\">más información sobre métodos"
#~ " de peticiones</a>"

#~ msgid "On"
#~ msgstr "Activado"

#~ msgid "Off"
#~ msgstr "Desactivado"

#~ msgid "Enabled"
#~ msgstr "Activado"

#~ msgid "Disabled"
#~ msgstr "Desactivado"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Realizar una búsqueda inmediatamente si "
#~ "se ha seleccionado una categoría. "
#~ "Desactivar para seleccionar varias categorías."
#~ " (Se requiere JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Teclas de acceso rápido como Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navegar por los resultados de búsqueda"
#~ " con las teclas de acceso rápido "
#~ "como-Vim (es necesario JavaScript). "
#~ "Pulse la tecla \"h\" en la página"
#~ " principal o en el resultado para "
#~ "obtener ayuda."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "No encontramos ningún resultado. Por "
#~ "favor, formule su búsqueda de otra "
#~ "forma o busque en más categorías."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Reescribir los nombres de host de "
#~ "los resultados o eliminar los resultados"
#~ " en función del nombre de host"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "KiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Sustituir el nombre de host"

#~ msgid "Error!"
#~ msgstr "¡Error!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Los motores no pueden obtener resultados"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Enviar un nuevo problema a GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generador de valores aleatorios"

#~ msgid "Statistics functions"
#~ msgstr "Funciones de estadística"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Calcular las funciones {functions} de parámetros dados"

#~ msgid "Get directions"
#~ msgstr "Obtener indicaciones"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Muestra tu dirección IP si la "
#~ "consulta es \"ip\" y tu Agente de"
#~ " Usuario si la consulta contiene "
#~ "\"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "No se pudo descargar la lista de"
#~ " nodos de salida de Tor desde: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Estás usando Tor y parece que "
#~ "tienes esta dirección IP externa: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "No estás usando Tor y tienes esta dirección IP externa: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Palabras clave"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Especificar ajustes personalizados en la "
#~ "URL de preferencias puede usarse para"
#~ " sincronizar las preferencias entre "
#~ "dispositivos."

#~ msgid "proxied"
#~ msgstr "por un proxy"
