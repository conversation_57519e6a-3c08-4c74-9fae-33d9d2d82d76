# Catalan translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON>ron <<EMAIL>>, 2019
# jmontane, 2018
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <PERSON> <<EMAIL>>, 2023, 2025.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-01-06 15:52+0000\n"
"Last-Translator: sserra <<EMAIL>>\n"
"Language: ca\n"
"Language-Team: Catalan "
"<https://translate.codeberg.org/projects/searxng/searxng/ca/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "sense agrupació"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "altres"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "fitxers"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "general"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "música"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "xarxes socials"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "imatges"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "vídeos"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "tic"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "notícies"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "cebes"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "ciència"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplicacions"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "diccionaris"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "lletres"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paquets"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "preguntes i respostes"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repositoris"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wikis de programari"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "articles científics"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automàtic"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "clar"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "fosc"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "negre"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Temps actiu"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Quant a"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Temperatura mitjana"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Ennuvolat"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "condicions"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Condicions actuals"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Vespre"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Sensació tèrmica"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Humitat"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Temp. Max."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Temp. Min."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Matí"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nit"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Migdia"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Pressió"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Sortida de sol"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Posta de sol"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Índex UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Visibilitat"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vent"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "subscriptors"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "entrades"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "usuaris actius"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "comentaris"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "usuari"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "comunitat"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "punts"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "títol"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "obert"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "tancat"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "contestat"

#: searx/webapp.py:292
msgid "No item found"
msgstr "No s'ha trobat cap element"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Origen"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "S'ha produït un error en carregar la següent pàgina"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "La configuració no és vàlida, editeu-la"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "La configuració no és vàlida"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "error de cerca"

#: searx/webutils.py:35
msgid "timeout"
msgstr "expirat"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "error de processament"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "error del protocol HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "error de xarxa"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "error de SSL: la validació del certificat ha fallat"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "error inesperat"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "error de HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "error de connexió HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "error del servidor intermediari"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "masses peticions"

#: searx/webutils.py:58
msgid "access denied"
msgstr "accés denegat"

#: searx/webutils.py:59
msgid "server API error"
msgstr "error en l'API del servidor"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspès"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "fa {minutes} minut(s)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "fa {hours} hores i {minutes} minut(s)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Genera diferents valors aleatoris"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr ""

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLET)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Aquesta entrada ha estat substituïda per"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Canal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "tasa de bits"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "vots"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "clics"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Llengua"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} cites des de l'any {firstCitationVelocityYear} fins a "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"No s'ha pogut llegir l'URL de la imatge. Això pot ser a causa d'un format"
" de fitxer no compatible. TinEye només admet imatges en format JPEG, PNG,"
" GIF, BMP, TIFF o WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"La imatge és massa senzilla per trobar coincidències. TinEye requereix un"
" mínim de complexitat visual per identificar amb èxit les coincidències."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "No s'ha pogut baixar la imatge."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Valoració de llibre"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Qualitat del fitxer"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Calcular expressions matemàtiques a través de la barra de cerca"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Converteix cadenes en diferents empremtes de hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "resum del hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Plugin de noms de host"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Reescriure els noms de host, eliminar resultats o prioritzar segons el "
"nom de host"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Reescriptura de l'Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Evita els llocs de pagament redirigint a versions d'accés obert de les "
"publicacions quan estiguin disponibles"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informació pròpia"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "La teva IP és: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "El teu agent d'usuari és: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Plugin de comprovació de Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Aquest plugin comprova si l'adreça de la sol·licitud és un node de "
"sortida TOR i informa a l'usuari si ho és, com check.torproject.org però "
"des de SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Suprimeix l'URL de rastreig"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Suprimeix els arguments de rastreig dels URL retornats"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Convertir entre unitats"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "No s'ha trobat la pàgina"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Ves a %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "pàgina de cerca"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Donar"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferències"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Funciona amb"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "metacercador obert, que respecta la privacitat"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Codi font"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Gestor d'incidències"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Estadístiques del cercador"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instàncies públiques"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Política de privacitat"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Contacteu amb el mantenidor de la instància"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Feu clic en la lupa per a executar la cerca"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Longitud"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Vistes"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "en memòria cau"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Obriu una incidència a GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Comproveu si existeix alguna incidència oberta amb aquest cercador a "
"GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Confirmo que no hi ha cap incidència relacionada amb el problema que "
"presento"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Si aquesta és una instància pública, indiqueu l'URL a la incidència"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Creeu una incidència a GitHub incloent la informació anterior"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Sense HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Mostra els informes d'error i envia una incidència"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang per a aquest motor de cerca"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang per a les seves categories"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mitjà"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Proves de control fallides: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Errors:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "General"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Categories predeterminades"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interfície de l'usuari"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privadesa"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Motors de cerca"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Cercadors usats actualment"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Consultes especials"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Galetes"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Nombre de resultats"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informació"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Torna al capdemunt"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Pàgina anterior"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Pàgina següent"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Mostra la pàgina principal"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Cerca..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "buida"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "cerca"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Actualment no hi ha dades disponibles. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nom del cercador"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Valoració"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Resultats"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Temps de resposta"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Fiabilitat"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "S'està processant"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Avisos"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Errors i excepcions"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Excepció"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Missatge"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Percentatge"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Paràmetre"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nom de fitxer"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funció"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Codi"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Comprovador"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Prova fallida"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Comentaris"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Exemples"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Sinònims"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Respostes"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Baixa els resultats"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Proveu a cercar:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Missatges dels motors de cerca"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "segons"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL de cerca"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Copiat"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copiar"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Suggeriments"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Llengua de cerca"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Llengua predeterminada"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Detecció automàtica"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Cerca segura"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Estricta"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Moderada"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Desactivat"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Interval de temps"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Qualsevol moment"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Les 24 darreres hores"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "La setmana passada"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "El darrer mes"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "El darrer any"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informació!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "actualment, no hi ha definida cap galeta."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Disculpeu!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "No s'han trobat resultats. Pots provar:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "No hi ha més resultats. Pots intentar:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Refresca la pestanya."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Busca una altre consulta o selecciona una alte categoria (més amunt)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Canvia el motor de cerca utilitzat a les preferències:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Canvia a una altra instància:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Realitza una altra consulta o selecciona una altra categoria."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Torna a la pàgina anterior usant el botó de pàgina anterior."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Permetre"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nom"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Descripció"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Aquesta és la llista dels mòduls de resposta instantània de SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Aquest és el llistat de les extensions."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Compleció automàtica"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Troba coses tal com escriu"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centrat central"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Mostrar els resultats en el centre de la pàgina (disseny Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Això és la llista de les galetes, i els seus valors, que el SearXNG "
"emmagatzema en el vostre ordinador."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Amb aquesta llista, podeu comprovar la transparència de SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nom de la galeta"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valor"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL de cerca de les preferències desades actualment"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Nota: si indiqueu configuracions personalitzades en l'URL de cerca, podeu"
" reduir-ne la privadesa i filtrar dades, en fer clic en els llocs dels "
"resultats."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL per a recuperar les preferències en un altre navegador"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copiar el hash de preferències"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Inserta el hash de preferències copiat (sense URL) per restaurar"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash de preferències"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Resolució del DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Selecciona el servei utilitzat per a la reescriptura del DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Aquesta pestanya no existeix en la interfície d'usuari, però pots buscar "
"en aquests motors de cerca mitjançant els seus !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Habilitar tots"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Deshabilitar tot"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Suporta la llengua seleccionada"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Pes"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Temps màxim"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Resolvedor Favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Mostra els favicons a prop dels resultats de la cerca"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Aquesta configuració es desa en les galetes. Això ens permet no "
"emmagatzemar les vostres dades."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Aquestes galetes només són per a la vostra comoditat. No les usem per a "
"rastrejar-vos."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Desa"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Restaura els valors predeterminats"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Enrere"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Dreceres"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Com a Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navega pels resultats amb dreceres (requereix JavaScript). Prem la tecla "
"\"h\" en una cerca per mostrar ajuda."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Servidor intermediari d'imatges"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Les imatges es carreguen via un servidor intermediari SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Desplaçament infinit"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Carrega automàticament la pàgina següent en desplaçar-se al final de la "
"pàgina actual"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "En quina llengua preferiu cercar?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Selecciona Detecció automàtica per permetre que SearXNG detecti l'idioma "
"de la teva cerca."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Mètode HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Modifica la forma d'enviar les cerques"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Consulta en el títol de la pàgina"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Si està activat, el títol de la pàgina conté la consulta. El navegador "
"pot enregistrar aquest títol"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Resultats en pestanyes noves"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Obre els enllaços en una nova pestanya"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtra el contingut"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Cerca en la selecció de categories"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Buscar immediatament si s'ha seleccionat una categoria. Deshabilita per a"
" seleccionar múltiples categories"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Canvia l'aparença de SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Estil del tema"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Seleccioneu automàtic per seguir la configuració del navegador"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Claus de motors"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Claus d'accés per a motors privats"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Idioma de la interfície"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Canvia la llengua de la disposició"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formatacio URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Bonico"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Ple"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Amfitrio"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Canvia el format de l'URL del resultat"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repositori"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "mostra el contingut multimèdia"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "oculta el contingut multimèdia"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Aquest lloc no proporciona cap descripció."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Mida del fitxer"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Data"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tipus"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resolució"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Cercador"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Veure el codi font"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adreça"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "mostra el mapa"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "oculta el mapa"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versió"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Mantenidor"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Actualitzat a"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etiquetes"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularitat"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Llicència"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projecte"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Pàgina principal del projecte"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Data de publicació"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Diari"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redactor"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Editor"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "enllaç magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "fitxer torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Font"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Descarregador"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Nombre de fiters"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "mostra el vídeo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "oculta el vídeo"

#~ msgid "Engine time (sec)"
#~ msgstr "Temps del motor (segons)"

#~ msgid "Page loads (sec)"
#~ msgstr "Càrrega de la pàgina (segons)"

#~ msgid "Errors"
#~ msgstr "Errors"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Reescriu els enllaços HTTP cap a HTTPS si és possible"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "De forma predeterminada, els resultats "
#~ "s'obren en la mateixa finestra. Aquest"
#~ " connector canvia el comportament "
#~ "predeterminat i obre els enllaços en "
#~ "una finestra o pestanya nova. (Cal "
#~ "JavaScript)"

#~ msgid "Color"
#~ msgstr "Color"

#~ msgid "Blue (default)"
#~ msgstr "Blau (predeterminat)"

#~ msgid "Violet"
#~ msgstr "Violat"

#~ msgid "Green"
#~ msgstr "Verd"

#~ msgid "Cyan"
#~ msgstr "Cian"

#~ msgid "Orange"
#~ msgstr "Taronja"

#~ msgid "Red"
#~ msgstr "Vermell"

#~ msgid "Category"
#~ msgstr "Categoria"

#~ msgid "Block"
#~ msgstr "Bloca"

#~ msgid "original context"
#~ msgstr "context original"

#~ msgid "Plugins"
#~ msgstr "Connectat"

#~ msgid "Answerers"
#~ msgstr "Resposter"

#~ msgid "Avg. time"
#~ msgstr "Temps amitjanat"

#~ msgid "show details"
#~ msgstr "mostra els detalls"

#~ msgid "hide details"
#~ msgstr "amaga els detalls"

#~ msgid "Load more..."
#~ msgstr "Carrega'n més..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Canvia la disposició del searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Envia els resultats d'imatges via el servidor intermediari del searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Aquest és el llistat dels mòduls de resposta ràpida del searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Aquest és el llistat de les "
#~ "galetes, i els seu valor, que el"
#~ " searx té desats en el vostre "
#~ "equip."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Amb aquest llistat, podeu avaluar la transparència del searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Sembla que esteu usant searx per primer cop."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Torneu-ho a intentar més tard o useu una altra instància del searx."

#~ msgid "Themes"
#~ msgstr "Temes"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Mètode"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Configuració avançada"

#~ msgid "Close"
#~ msgstr "Tanca"

#~ msgid "Language"
#~ msgstr "Llengua"

#~ msgid "broken"
#~ msgstr "trencat"

#~ msgid "supported"
#~ msgstr "suportat"

#~ msgid "not supported"
#~ msgstr "no suportat"

#~ msgid "about"
#~ msgstr "quant a"

#~ msgid "Avg."
#~ msgstr "Mitjana"

#~ msgid "User Interface"
#~ msgstr "Interfície d'usuari"

#~ msgid "Choose style for this theme"
#~ msgstr "Trieu un estil per a aquest tema"

#~ msgid "Style"
#~ msgstr "Estil"

#~ msgid "Show advanced settings"
#~ msgstr "Mostra els paràmetres avançats"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""
#~ "Mostra el taulell de configuració "
#~ "avançada en la pàgina d'inicia de "
#~ "forma predeterminada"

#~ msgid "Allow all"
#~ msgstr "Permet-ho tot"

#~ msgid "Disable all"
#~ msgstr "Desactiva-ho tot"

#~ msgid "Selected language"
#~ msgstr "Llengua seleccionada"

#~ msgid "Query"
#~ msgstr "Consulta"

#~ msgid "save"
#~ msgstr "desa"

#~ msgid "back"
#~ msgstr "enrere"

#~ msgid "Links"
#~ msgstr "Enllaços"

#~ msgid "RSS subscription"
#~ msgstr "Subscripció RSS"

#~ msgid "Search results"
#~ msgstr "Resultats de la cerca"

#~ msgid "next page"
#~ msgstr "pàgina següent"

#~ msgid "previous page"
#~ msgstr "pàgina anterior"

#~ msgid "Start search"
#~ msgstr "Comença la cerca"

#~ msgid "Clear search"
#~ msgstr "Buida la cerca"

#~ msgid "Clear"
#~ msgstr "Buida"

#~ msgid "stats"
#~ msgstr "estadístiques"

#~ msgid "Heads up!"
#~ msgstr "Atenció!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Sembla que feu servir el SearXNG per primera vegada."

#~ msgid "Well done!"
#~ msgstr "Ben fet!"

#~ msgid "Settings saved successfully."
#~ msgstr "La configuració s'ha desat correctament."

#~ msgid "Oh snap!"
#~ msgstr "Cagundena!"

#~ msgid "Something went wrong."
#~ msgstr "Alguna cosa ha anat malament."

#~ msgid "Date"
#~ msgstr "Data"

#~ msgid "Type"
#~ msgstr "Tipus"

#~ msgid "Get image"
#~ msgstr "Obtén la imatge"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferències"

#~ msgid "Scores per result"
#~ msgstr "Valoració segons el resultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "un meta motor de cerca personalitzable i respectuós amb la privadesa"

#~ msgid "No abstract is available for this publication."
#~ msgstr "No hi ha resum disponible per a aquesta publicació."

#~ msgid "Self Informations"
#~ msgstr "Informació pròpia"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Canvia com es trameten els formularis,"
#~ " <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">més informació sobre els "
#~ "mètodes de petició</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Aquest plugin comprova si l'adreça de"
#~ " la sol·licitud és un node de "
#~ "sortida TOR i informa a l'usuari "
#~ "si ho és, com check.torproject.org però"
#~ " des de searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "No es pot accedir a la llista "
#~ "de nodes de sortida TOR "
#~ "(https://check.torproject.org/exit-addresses)."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Esteu utilitzant TOR. La vostra adreça IP sembla ser: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "No esteu utilitzant TOR. La vostra adreça IP sembla ser: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Detecta automàticament la llengua de cerca"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "altres"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Aquesta pestanya no es mostra per "
#~ "als resultats de la cerca, però "
#~ "podeu cercar en els motors llistats "
#~ "aquí amb els bangs."

#~ msgid "Shortcut"
#~ msgstr "Drecera"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Els motors no poden obtenir cap resultat."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Torneu a intentar-ho més tard, o"
#~ " proveu amb una altra instància "
#~ "SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Redirigeix cap a versions d'accés obert"
#~ " de les publicacions si són "
#~ "disponibles (cal un connector)"

#~ msgid "Bang"
#~ msgstr "Bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Canvia com es trameten els formularis,"
#~ " <a "
#~ "href=\"https://ca.wikipedia.org/wiki/Protocol_de_transferència_d'hipertext#Mètodes_de_petició[2]\""
#~ " rel=\"external\">més informació sobre els "
#~ "mètodes de petició</a>"

#~ msgid "On"
#~ msgstr "Activat"

#~ msgid "Off"
#~ msgstr "Desactivat"

#~ msgid "Enabled"
#~ msgstr "Activat"

#~ msgid "Disabled"
#~ msgstr "Desactivat"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Executa la cerca immediatament si hi "
#~ "ha seleccionada una categoria. Desactiveu-"
#~ "ho per a seleccionar més d'una "
#~ "categoria. (Cal JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Dreceres de teclat del Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navegació pels resultats de la cerca "
#~ "amb les dreceres a l'estil Vim "
#~ "(cal JavaScript). Pitgeu la tecla «h»"
#~ " en la pàgina principal o de "
#~ "resultats per a obtenir ajuda."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "no hem trobat cap resultat. Feu "
#~ "una consulta diferent o cerqueu en "
#~ "més categories."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Reescriu o suprimeix resultats basant-se en els noms d'amfitrió"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Substitució del nom de l'amfitrió"

#~ msgid "Error!"
#~ msgstr "Error!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Els cercadors no poden obtenir cap resultat"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Obriu una incidència a GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generador de valors aleatoris"

#~ msgid "Statistics functions"
#~ msgstr "Funcions estadístiques"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Calcula {functions} dels arguments"

#~ msgid "Get directions"
#~ msgstr "Obtén indicacions"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Mostra la vostra IP si la consulta"
#~ " és «ip» i el valor «user "
#~ "agent» del navegador si la consulta "
#~ "conté «user agent»."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "No s'ha pogut descarregar la llista "
#~ "de nodes de sortida de Tor des "
#~ "de: https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Estàs usant Tor i sembla que tens aquesta adreça IP: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "No estàs usant Tor i tens aquesta adreça IP: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Paraules clau"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Especificar els paràmetres personalitzats en"
#~ " l'URL de preferències pot usar-se"
#~ " per sincronitzar entre dispositius."

#~ msgid "proxied"
#~ msgstr "en servidor intermediari"

