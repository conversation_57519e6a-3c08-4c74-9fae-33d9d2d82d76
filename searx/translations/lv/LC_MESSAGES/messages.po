# Latvian translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-05-30 07:10+0000\n"
"Last-Translator: sandijs <<EMAIL>>\n"
"Language: lv\n"
"Language-Team: Latvian "
"<https://translate.codeberg.org/projects/searxng/searxng/lv/>\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 0 || n % 100 >= 11 && n % 100"
" <= 19) ? 0 : ((n % 10 == 1 && n % 100 != 11) ? 1 : 2);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "bez turpinošas grupēšanas"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "cits"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "faili"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "viss"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "mūzika"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sociālie tīkli"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "attēli"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "video"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "it"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "ziņas"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "karte"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "sīpoli"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "zinātne"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikācijas"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "vārdnīcas"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "dziesmu vārdi"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pakotnes"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "j&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repo"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "programmatūras wiki"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "tīmeklis"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "zinātnisku publikāciji"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "auto"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "gaišs"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tumšs"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "melns"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Darbspējas laiks"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Par"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Vidējā temp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Mākoņu klājums"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Stāvoklis"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Pašreizējais stāvoklis"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Vakara"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Pēc sajūtām"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Mitrums"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maksimālā temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Minimālā temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Rīts"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nakts"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Pusdiena"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Spiediens"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Saullēkts"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Saulriets"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatūra"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV indekss"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Redzamība"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Vējš"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "abonenti"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "ziņojumi"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktīvi lietotāji"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "komentāri"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "lietotājs"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "kopiena"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "punkti"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "virsraksts"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autors"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "atvērts"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "aizvērts"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "atbildēja"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nav atrasts neviens vienums"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Avots"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Kļūda lādējot nākošo lapu"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Nepareizi iestatījumi, lūdzu rediģējiet savas preferences"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Nederīgi iestatījumi"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "meklēšanas kļūda"

#: searx/webutils.py:35
msgid "timeout"
msgstr "noildze"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "parsēšanas kļūda"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP protokola kļūda"

#: searx/webutils.py:38
msgid "network error"
msgstr "tīkla kļūda"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL kļūda: certifikāta validācija neizdevās"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "negaidīta avārija"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP kļūda"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP savienojuma kļūda"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "starpniekservera kļūda"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "pārāk daudz pieprasījumu"

#: searx/webutils.py:58
msgid "access denied"
msgstr "piekļuve aizliegta"

#: searx/webutils.py:59
msgid "server API error"
msgstr "servera API kļūda"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Apturēts"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "pirms {minutes} minūtes(-ēm)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "pirms {hours} stundas(-ām) un {minutes} minūtēm(-es)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Ģenerēt citas nejaušas vērtības"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Rādīt maršrutu kartē .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (NOVECOJIS)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Šis ieraksts ir ticis aizstāts ar"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanāls"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitu pārraide"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "balsis"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikšķi"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Valoda"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citāti no {firstCitationVelocityYear} līdz "
"{lastCitationVelocityYear} gada"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Nevar nolasīt šo attēla url. Tas var būt saistīts ar neatbalstītu faila "
"formātu. TinEye atbalsta tikai JPEG, PNG, GIF, BMP, TIFF vai WebP "
"attēlus."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Attēls ir pārāk vienkāršs, lai atrastu atbilstību. Lai veiksmīgi noteiktu"
" sakritības, TinEye ir nepieciešams pamata vizuālo detaļu līmenis."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Attēlu neizdevās lejupielādēt."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "grāmatu vērtējums"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Failu kvalitāte"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia melnais saraksts"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Izfiltrēt \"sīpolu\" rezultātus, kas parādās Ahmia melnajā sarakstā."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Parasts kalkulators"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Izrēķināt matemātiskas izteiksmes, izmantojot meklētāju"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Jaucējvērtības spraudnis"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Pārvērš virknes (strings) par dažādiem jaucējkoda īssavilkumiem."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "jaucējkoda sašķelšana"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Saimniekvārdu spraudnis"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Pārrakstīt saimniekvārdus, noņemt rezultātus vai prioritizēt tos, "
"pamatojoties uz saimniekvārdu"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Atvērtās piekļuves DOI pārrakstīšana"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Izvairieties no maksas sienām, novirzot uz publikāciju atvērtās piekļuves"
" versijām, ja tās ir pieejamas"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informācija par sevi"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Tiek izvadīta Jūsu IP, ja pieprasījumā norādīts “ip”, un Jūsu "
"lietotājaģents, ja pieprasījumā norādīts “user-agent”."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Jūsu IP ir: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Jūsu lietotājaģents ir: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Pārbaudiet Tor spraudni"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Šis spraudnis pārbauda vai pieprasītā adrese ir Tor izejas mezgls un "
"informē lietotāju, ja tas tā ir; piemēram, check.torproject.org, bet no "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nevarēja lejupielādēt Tor izejas mezglu sarakstu no"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Jūs lietojat Tor un izskatās, ka Jums ir ārējā IP adrese"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Jūs nelietojat Tor un Jums ir ārējā IP adrese"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Izsekošanas URL noņemšanas līdzeklis"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Noņemt izsekotāju argumentus no atgrieztā URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Vienību pārveidotāja spraudnis"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Konvertēt starp vienībām"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Lapa nav atrasta"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Doties uz %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "meklēšanas lapa"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Ziedo"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Opcijas"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Darbojas ar"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "privātumu respektējoša, atvērta meta-meklētājprogramma"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Pirmkods"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Problēmu izsekotājs"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Dzinēja statistika"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Publiskās instances"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Privātuma politika"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr ""

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Noklikšķiniet uz lupas, lai veiktu meklēšanu"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Garums"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr ""

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autors"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr ""

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr ""

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Iesniedziet jaunu problēmjautājumu iekš Github, ieskaitot augstāk minēto "
"informāciju"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr ""

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr ""

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr ""

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr ""

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr ""

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr ""

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr ""

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Kļūdas:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Vispārīgi"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Noklusējuma kategorijas"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Lietotāja saskarne"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privātums"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Dzinēji"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Pašlaik izmantotās meklētājprogrammas"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr ""

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr ""

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Rezultātu skaits"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informācija"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Atpakaļ uz augšu"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Iepriekšējā lapa"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Nākamā lapa"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Rādīt sākuma lapu"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Meklēt..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "notīrīt"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "meklēt"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Dati šobrīd nav pieejami. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Meklētāja nosaukums"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr ""

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Atbildes laiks"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr ""

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr ""

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr ""

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr ""

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr ""

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr ""

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr ""

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr ""

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr ""

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr ""

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr ""

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr ""

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr ""

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr ""

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr ""

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr ""

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Piemēri"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr ""

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Atbildes"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Lejupielādes rezultāti"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Mēģiniet meklēt:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr ""

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr ""

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Meklēšanas URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr ""

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr ""

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Ieteikumi"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Meklēšanas valoda"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr ""

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Stingrs"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr ""

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Neviens"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Laika diapazons"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr ""

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr ""

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr ""

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr ""

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr ""

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr ""

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr ""

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr ""

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr ""

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr ""

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr ""

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr ""

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Atļaut"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Vārds"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Apraksts"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Šis ir SearXNG tūlītējās atbildēšanas moduļu saraksts."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Šis ir spraudņu saraksts."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automātiskā pabeigšana"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Atrast lietas rakstīšanas laikā"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centra līdzinājums"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Parāda rezultātus lapas centrā (Oskara izkārtojums)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Izmantojot šo sarakstu, var novērtēt SearXNG pārredzamību."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Vērtība"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Pašlaik saglabāto preferenču meklēšanas URL"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Piezīme: Nosakot pielāgotus iestatījumus meklēšanas URL, var samazināt "
"konfidencialitāti (privātumu), izplūstot datiem uz rezultātu vietnēm, uz "
"kurām tika noklikšķināts."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL, lai atjaunotu savas preferences citā pārlūkprogrammā"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr ""

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr ""

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Atbalsta atlasīto valodu"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr ""

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maksimālais laiks"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Saglabāt"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Atiestatīt noklusējuma"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Atpakaļ"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr ""

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Attēla starpniekserveris"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Attēlu rezultātu starpniekservera izmantošana, caur SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Bezgalīgā ritināšana"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Automātiski ielādēt nākamo lappusi, ritinot uz pašreizējās lappuses beigām"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Kādai valodai dodat priekšroku priekš meklēšanas?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Metode"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr ""

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr ""

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Rezultāti jaunās cilnēs"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Atvērt rezultātu saites jaunās pārlūka cilnēs"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr ""

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr ""

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tēma"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Mainīt SearXNG izkārtojumu"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Tēmas stils"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Izvēlies auto, lai sekotu saviem pārluka iestatījumiem"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr ""

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr ""

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr ""

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Izkārtojuma valodas maiņa"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr ""

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr ""

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr ""

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr ""

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Faila lielums"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr ""

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr ""

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr ""

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr ""

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr ""

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr ""

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr ""

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr ""

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projektus"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:23
#, fuzzy
msgid "Publisher"
msgstr "Publicētājs"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr ""

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Sēklotājs"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Sūcējs"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Failu skaits"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "rādīt video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "slēpt video"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr ""

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr ""

#~ msgid "No abstract is available for this publication."
#~ msgstr "Šai publikācijai nav pieejams kopsavilkums."

#~ msgid "Self Informations"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Mainīt veidu, kā veidlapas tiek "
#~ "iesniegtas, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">uzzināt vairāk par pieprasījuma"
#~ " metodēm</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Jūs izlieto TOR. Jūsu IP adrese šķist būtu: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Jūs neizlieto TOR. Jūsu IP adrese šķist būtu: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "citi"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "Saīsne"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr ""

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "Ieslēgts"

#~ msgid "Off"
#~ msgstr "Izslēgts"

#~ msgid "Enabled"
#~ msgstr "Iespējots"

#~ msgid "Disabled"
#~ msgstr "Atspējots"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Nekavējoties veikt meklēšanu, ja ir "
#~ "atlasīta kategorija. Atspējot, lai atlasītu"
#~ " vairākas kategorijas. (nepieciešams JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr ""

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Pārrakstīt rezultātu saimniekvārdus vai noņemt"
#~ " rezultātus, pamatojoties uz saimniekvārdu"

#~ msgid "Bytes"
#~ msgstr "Biti"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Resursdatora vārda nomaiņa"

#~ msgid "Error!"
#~ msgstr "Kļūme!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Meklētāji nevarēja iegūt rezultātus"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr ""

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Nejaušu vērtību ģenerators"

#~ msgid "Statistics functions"
#~ msgstr "Statistikas funkcijas"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Aprēķināt argumentu {functions}"

#~ msgid "Get directions"
#~ msgstr "Saņemt norādījumus"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Tiek parādīts jūsu IP, ja pieprasījums"
#~ " ir \"ip\", un jūsu lietotāja aģents,"
#~ " ja pieprasījumā ir \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Jūs izmantojat TOR un izskatās ka jūsu ārējā IP adrese ir:{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""

#~ msgid "Keywords"
#~ msgstr "Atslēgvārdi"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""

#~ msgid "proxied"
#~ msgstr ""

