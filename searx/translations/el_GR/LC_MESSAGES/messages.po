# Greek (Greece) translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2017-2018
# <AUTHOR> <EMAIL>, 2015
# <PERSON> <<EMAIL>>, 2022, 2023.
# <PERSON> <<PERSON><PERSON>@acg.edu>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-03 21:59+0000\n"
"Last-Translator: sakistzimas <<EMAIL>>\n"
"Language: el_GR\n"
"Language-Team: Greek "
"<https://translate.codeberg.org/projects/searxng/searxng/el/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "χωρίς περαιτέρω ομαδοποίηση"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "λοιπά"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "αρχεία"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "γενικά"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "μουσική"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "κοινωνικά δίκτυα"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "εικόνες"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "Βίντεο"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "ράδιο"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "Τηλεόραση"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "Πληροφορική"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "νέα"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "χάρτης"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "Σελίδες .onion (tor)"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "επιστήμη"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "Εφαρμογές"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "λεξικά"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "Στίχοι"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "πακέτα"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "ερωταπαντήσεις"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "αποθετήρια"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "Wiki λογισμικού"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "Ιστός"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "Επιστημονικά δημοσιεύματα"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "Αυτόματα"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "φωτεινό"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "σκοτεινό"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "μαύρο"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "χρόνο λειτουργίας"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Σχετικά με το SearXNG"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Μέση θερμοκρασία."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Νεφοκάλυψη"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Κατάσταση"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Τωρινή κατάσταση"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Βράδυ"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Αίσθηση"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Υγρασία"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Μέγιστη θερμοκρασία."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Ελάχιστη Θερμοκρασία"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Πρωί"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Βράδι"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Μεσημέρι"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Πίεση"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Ανατολή ηλίου"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Η δυση του ηλιου"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Θερμοκρασία"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Δείκτης UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Ορατότητα"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Ανεμος"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "συνδρομητές"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "αναρτήσεις"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "ενεργούς χρήστες"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "σχόλια"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "χρήστης"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "κοινότητα"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "σημεία"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "τίτλος"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "συγγραφέας"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Άνοιξε"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "κλειστό"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "απάντησε"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Δεν βρέθηκαν αντικείμενα"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Πηγή"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Σφάλμα φόρτωσης της επόμενης σελίδας"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Μη έγκυρες ρυθμίσεις, παρακαλούμε ελέγξτε τις προτιμήσεις σας"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Μη έγκυρες ρυθμίσεις"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "σφάλμα αναζήτησης"

#: searx/webutils.py:35
msgid "timeout"
msgstr "Λήξη χρόνου"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "σφάλμα ανάλυσης"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Σφάλμα πρωτοκόλλου HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "Σφάλμα δικτύου"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Σφάλμα SSL: η επικύρωση του πιστοποιητικού απέτυχε"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "Απροσδόκητο σφάλμα"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "Σφάλμα HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Σφάλμα σύνδεσης HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "Σφάλμα διακομιστή μεσολάβησης"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "υπέρβαση ορίου αιτημάτων"

#: searx/webutils.py:58
msgid "access denied"
msgstr "Άρνηση πρόσβασης"

#: searx/webutils.py:59
msgid "server API error"
msgstr "Σφάλμα API διακομιστή"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Σε αναστολή"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} λεπτά πριν"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} ώρα(-ες), {minutes} λεπτό(-ά) πριν"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Δημιουργία διαφορετικών τυχαίων τιμών"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Υπολογίστε τη {func} των ορισμάτων"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Εμφάνιση διαδρομής στον χάρτη .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ΠΑΡΩΧΗΜΕΝΟΣ)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Αυτή η καταχώριση έχει αντικατασταθεί από"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Κανάλι"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "ρυθμός μετάδοσης"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "ψήφους"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "κλικ"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Γλώσσα"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} αναφορές απο τα έτη {firstCitationVelocityYear} εώς "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Αποτυχία ανάγνωσης του συνδέσμου της εικόνας. Αυτό μπορεί να οφείλεται σε"
" μη υποστηριζόμενη μορφή αρχείου. Το TinEye υποστηρίζει μόνο εικόνες που "
"είναι JPEG, PNG, GIF, BMP, TIFF ή WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Η εικόνα είναι πολύ απλή για να βρεθούν αντιστοιχίες. Το TinEye απαιτεί "
"ένα στοιχειώδης επίπεδο λεπτομέρειας για τον επιτυχή εντοπισμό "
"αντιστοιχιών."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Αποτυχία μεταφόρτωσης εικόνας."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Βαθμολογία βιβλίου"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Ποιότητα αρχείου"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Η μαύρη λίστα της Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""
"Φιλτράρισμα των αποτελεσμάτων onion που εμφανίζονται στη μάυρη λίστα της "
"Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Βασική Αριθμομηχανή"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Υπολογίστε μαθηματικές εκφράσεις μέσω της γραμμής αναζήτησης"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash plugin"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Μετατρέπει κείμενο σε διαφορετικές συναρτήσεις κατατεμαχισμού."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "συνάρτηση κατατεμαχισμού"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Προσθήκη ονομάτων κεντρικού υπολογιστή"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Ξαναγράψτε ονόματα κεντρικών υπολογιστών, αφαιρέστε τα αποτελέσματα ή "
"δώστε προτεραιότητα σε αυτά με βάση το όνομα κεντρικού υπολογιστή"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Ανοίξτε την επανεγγραφή DOI της Access"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Αποφυγή τοίχων πληρωμής με ανακατεύθυνση σε ανοικτές εκδόσεις των "
"δημοσιεύσεων όταν είναι διαθέσιμες"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Αυτοπληροφορίες"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Εμφανίζει την IP σας εάν το ερώτημα είναι \"ip\" και τον παράγοντα χρήστη"
" σας εάν το ερώτημα είναι \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Η IP σας είναι: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Ο χρήστης-πράκτοράς σας είναι: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Πρόσθετο ελέγχου Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Αυτό το πρόσθετο ελέγχει εάν η διεύθυνση του χρήστη είναι διεύθυνση "
"εξόδου του δικτύου Tor και ενημερώνει τον χρήστη εάν είναι έτσι. Όπως στο"
" check.torproject.org, αλλά από το SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Δεν ήταν δυνατή η λήψη της λίστας των κόμβων εξόδου Tor από"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Χρησιμοποιείτε Tor και φαίνεται ότι έχετε την εξωτερική διεύθυνση IP"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Δεν χρησιμοποιείτε Tor και έχετε την εξωτερική διεύθυνση IP"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Αφαίρεση ιχνηλατών από συνδέσμους"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Αφαίρεση ιχνηλατών από τους επιστρεφόμενους συνδέσμους"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Πρόσθετο μετατροπέας μονάδων"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Μετατροπή μεταξύ μονάδων"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Η σελίδα δεν βρέθηκε"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Μετάβαση στο %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "σελίδα αναζήτησης"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Κάνε δωρεά"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Προτιμήσεις"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Με την υποστήριξη του"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "μια ανοικτή μηχανή μετα-αναζήτησης που σέβεται την ιδιωτικότητα"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Πηγαίος κώδικας"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Παρακολούθηση ζητημάτων"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Στατιστικά μηχανής"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Δημόσιες εκφάνσεις"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Πολιτική απορρήτου"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Επικοινωνήστε με τον συντηρητή αυτής της σελίδας"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Κάντε κλικ στο μεγεθυντικό φακό για να πραγματοποιήσετε αναζήτηση"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Μήκος"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Προβολές"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Συγγραφέας"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "προσωρινά αποθηκευμένο"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Ξεκινήστε την υποβολή ενός νέου ζητήματος στο GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Παρακαλούμε ελέγξτε για υπάρχοντα σφάλματα σχετικά με αυτή τη μηχανή "
"αναζήτησης στο GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"Επιβεβαιώνω ότι δεν υπάρχει υπάρχον σφάλμα σχετικά με το πρόβλημα που "
"αντιμετωπίζω"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Εάν πρόκειται για μια δημόσια σελίδα (SearXNG instance), παρακαλούμε "
"αναφέρετε τη διεύθυνση URL στην αναφορά σφάλματος"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Υποβολή νέου ζητήματος στο Github με τις παραπάνω πληροφορίες"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Όχι HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Προβολή αρχείων καταγραφής σφαλμάτων και υποβολή αναφοράς σφάλματος"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang γι' αυτή τη μαχανή αναζήτησης"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang για τις κατηγορίες της"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Διάμεσος"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Αποτυχημένα δοκιμαστικά τεστ: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Σφάλματα:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Γενικά"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Προεπιλεγμένες κατηγορίες"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Διεπαφή χρήστη"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Ιδιωτικότητα"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Μηχανές"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Μηχανές αναζήτησης που χρησιμοποιούνται"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Ειδικά Ερωτήματα"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Αριθμός αποτελεσμάτων"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Πληροφορίες"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Επιστροφή στην κορυφή"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Προηγούμενη σελίδα"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Επόμενη σελίδα"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Εμφάνιση της αρχικής σελίδας"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Αναζήτηση για..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "καθαρισμός"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "αναζήτηση"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Δεν υπάρχουν διαθέσιμα δεδομένα. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Όνομα μηχανής"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Βαθμολογίες"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Αριθμός αποτελεσμάτων"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Χρόνος απόκρισης"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Αξιοπιστία"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Σύνολο"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Επεξεργασία"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Προειδοποιήσεις"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Σφάλματα και εξαιρέσεις"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Εξαίρεση"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Μήνυμα"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Ποσοστό"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Παράμετρος"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Όνομα αρχείου"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Συνάρτηση"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Κώδικας"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Ελεγκτής"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Αποτυχημένη δοκιμή"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Σχόλιο(α)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Παραδείγματα"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Ορισμοί"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Συνώνυμα"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Απαντήσεις"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Λήψη αποτελεσμάτων"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Δοκιμάστε αναζήτηση για:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Μηνύματα από μηχανές αναζήτησης"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Σύνδεσμος αναζήτησης"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Αντιγράφηκε"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Αντιγραφή"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Προτάσεις"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Γλώσσα αναζήτησης"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Προεπιλεγμένη γλώσσα"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Αυτόματη αναγνώριση της γλώσσας"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Ασφαλής Αναζήτηση"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Αυστηρό"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Μέτριο"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Κανένα"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Εύρος χρόνου"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Οποιαδήποτε στιγμή"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Τελευταία μέρα"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Τελευταία βδομάδα"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Τελευταίος μήνας"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Τελευταίο έτος"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Πληροφορίες!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "προς το παρόν, δεν έχουν οριστεί cookies."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Συγνώμη!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Δεν βρέθηκαν αποτελέσματα. Μπορείτε να δοκιμάσετε:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Δεν υπάρχουν άλλα αποτελέσματα. Μπορείτε να προσπαθήσετε να:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Ανανέωση σελίδας."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Υποβάλετε νέα αναζήτηση ή επιλέξτε άλλη κατηγορία (επάνω)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Αλλάξτε την μηχανή αναζήτησης που χρησιμοποιείται στις ρυθμίσεις:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Αλλαγή σε άλλη έκδοση:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Αναζητήστε άλλο ερώτημα ή επιλέξτε άλλη κατηγορία."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""
"Επιστρέψτε στην προηγούμενη σελίδα χρησιμοποιώντας το κουμπί της "
"προηγούμενης σελίδας."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Επέτρεψε"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Λέξεις-κλειδιά (πρώτη λέξη στο ερώτημα)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Όνομα"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Περιγραφή"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Αυτός είναι ο κατάλογος των ενοτήτων άμεσης απάντησης του SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Αυτός είναι ο κατάλογος των πρόσθετων."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Αυτόματη συμπλήρωση"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Εύρεση όρων κατά την πληκτρολόγηση"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Κεντρική ευθυγράμμιση"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Εμφάνιση αποτελεσμάτων στο κέντρο της σελίδας (διάταξη Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Αυτός είναι ο κατάλογος των cookies και των τιμών τους που αποθηκεύει η "
"SearXNG στον υπολογιστή σας."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Με αυτόν τον κατάλογο, μπορείτε να αξιολογήσετε τη διαφάνεια του SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Όνομα cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Τιμή"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Σύνδεσμος αναζήτησης των αποθηκευμένων προτιμήσεων"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Σημείωση: ο καθορισμός προσαρμοσμένων ρυθμίσεων στη διεύθυνση URL "
"αναζήτησης μπορεί να μειώσει την ιδιωτικότητα διαρρέοντας δεδομένα στους "
"ιστότοπους των αποτελεσμάτων που εσείς κάνετε κλίκ."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "Σύνδεσμος για επαναφορά προτιμήσεων σε διαφορετικό περιηγητή"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Ένα URL που περιέχει τις προτιμήσεις σας. Αυτό το URL μπορεί να "
"χρησιμοποιηθεί για να επαναφέρει τις ρυθμίσεις σας σε διαφορετική "
"συσκευή."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Αντιγραφή κατακερματισμού προτιμήσεων"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""
"Εισαγάγετε αντιγραμμένο κατακερματισμό προτιμήσεων (χωρίς URL) για "
"επαναφορά"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Κατακερματισμός προτιμήσεων"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Ψηφιακό αναγνωριστικό αντικειμένου (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Επιλυτής DOI ανοικτής πρόσβασης"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Επιλέξτε την υπηρεσία που θα χρησιμοποιηθεί απ' το DOI rewrite"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Αυτή η καρτέλα δεν υπάρχει για την ιστοσελίδα, αλλά μπορείτε να "
"αναζητήσετε απ' τις !bangs της."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Ενεργοποίηση όλων"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Απενεργοποίηση όλων"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Υποστηρίζει την επιλεγμένη γλώσσα"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Βάρος"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Μέγιστος χρόνος"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Ευρετής Εικόνων Σελιδών"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Εμφάνιση εικονιδίων σελιδών δίπλα από τα αποτελέσματα αναζήτησης"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Αυτές οι ρυθμίσεις αποθηκεύονται στα cookies σας, με αυτόν τον τρόπο δεν "
"χρειάζεται να αποθηκέυονται στους δικούς μας διακομιστές."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Αυτά τα cookies υπάρχουν αποκλειστικά για την εξυπηρέτησή σας, δεν τα "
"χρησιμοποιούμε για να σας παρακολουθούμε."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Αποθήκευση"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Επαναφορά προεπιλογών"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Πίσω"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Πλήκτρα συντόμευσης"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Σαν του Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Πλοήγηση αποτελεσμάτων με πλήκτρα συντόμευσης (αναγκαία η χρήση "
"Javascript). Πατήστε το πλήκτρο\"h\" στην κύρια σελίδα ή την σελίδα "
"αποτελεσμάτων για οδηγίες."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Διακομιστής μεσολάβησης εικόνων"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Διαμεσολάβιση φόρτωσης αποτελεσμάτων εικόνων μέσω του SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Άπειρη κύλιση"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Αυτόματη φόρτωση της επόμενης σελίδας κατά την κύλιση στο κάτω μέρος της "
"τρέχουσας σελίδας"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Τι γλώσσα προτιμάτε για αναζήτηση;"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Επιλέξτε αυτόματη αναγνώριση για να αφήσετε το SearXNG να αναγνωρίσει την"
" γλώσσα του ερωτήματος σας αυτόματα."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Μέθοδος HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Αλλαγή τρόπου υποβολής φόρμας"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Ερώτημα στον τίτλο της σελίδας"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Όταν ενεργό, ο τίτλος της σελίδας αποτελεσμάτων περιέχει το ερώτημά σας. "
"Το πρόγραμμα περιήγησής σας μπορεί να καταγράψει αυτόν τον τίτλο"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Αποτελέσματα σε νέες καρτέλες"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Άνοιξε τους συνδέσμους των αποτελεσμάτων σε νέα καρτέλα περιηγητή"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Φιλτράρισμα περιεχομένου"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Αναζήτηση κατά την επιλογή κατηγορίας"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Άμεση αναζήτηση κατά την επιλογή κατηγορίας. Απενεργοποιήστε για να "
"διαλέξετε πολλαπλές κατηγορίες"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Θέμα"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Τροποποίηση διάταξης του SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Στυλ θέματος"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr ""
"Επιλέξτε αυτόματο για να τηρήσετε τις ρυθμίσεις του προγράμματος "
"περιήγησης"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokens μηχανών αναζήτησης"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tokens πρόσβασης για ιδιωτικές μηχανές"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Γλώσσα διεπαφής"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Αλλαγή γλώσσας της διάταξης"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Τρόπος εμφάνισης ηλεκτρονικού συνδέσμου"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Όμορφο"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Πλήρες"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Διακομιστής"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Αλλαγή τρόπου εμφάνισης ηλεκτρονικών συνδέσμων"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "αποθετήρια"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "προβολή πολυμέσων"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "απόκρυψη πολυμέσων"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Αυτός ο ιστότοπος δεν παρείχε καμία περιγραφή."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Μέγεθος αρχείου"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Ημερομηνία"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Τύπος"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Ανάλυση"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Μορφή"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Μηχανή"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Προβολή πηγής"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "διεύθυνση"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "προβολή χάρτη"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "απόκρυψη χάρτη"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Έκδοση"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Συντηρητής"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Ενημερώθηκε στις"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Σημάνσεις"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Δημοτικότητα"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Άδεια"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Έργο"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Αρχική σελίδα του έργου"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Ημερομηνία δημοσίευσης"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Περιοδικό"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Συντάκτης"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Εκδότης"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "Σύνδεσμος magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "Αρχείο torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeder"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Αριθμός Αρχείων"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "προβολή βίντεο"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "απόκρυψη βίντεο"

#~ msgid "Engine time (sec)"
#~ msgstr "Χρόνος μηχανής (δευτ)"

#~ msgid "Page loads (sec)"
#~ msgstr "Φόρτωση σελίδας (δευτ)"

#~ msgid "Errors"
#~ msgstr "Λάθη"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Επανεγγραφή συνδέσμων HTTP σε HTTPS αν είναι δυνατό"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""

#~ msgid "Color"
#~ msgstr "Χρώμα"

#~ msgid "Blue (default)"
#~ msgstr "Μπλε (προεπιλεγμένο)"

#~ msgid "Violet"
#~ msgstr "Βιολετί"

#~ msgid "Green"
#~ msgstr "Πράσινο"

#~ msgid "Cyan"
#~ msgstr "Κυανό"

#~ msgid "Orange"
#~ msgstr "Πορτοκαλί"

#~ msgid "Red"
#~ msgstr "Κόκκινο"

#~ msgid "Category"
#~ msgstr "Κατηγορία"

#~ msgid "Block"
#~ msgstr "Αποκλεισμός"

#~ msgid "original context"
#~ msgstr ""

#~ msgid "Plugins"
#~ msgstr "Πρόσθετα"

#~ msgid "Answerers"
#~ msgstr ""

#~ msgid "Avg. time"
#~ msgstr ""

#~ msgid "show details"
#~ msgstr "προβολή λεπτομερειών"

#~ msgid "hide details"
#~ msgstr "απόκρυψη λεπτομερειών"

#~ msgid "Load more..."
#~ msgstr "Φόρτωση περισσότερων..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr ""

#~ msgid "Proxying image results through searx"
#~ msgstr ""

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr ""

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr ""

#~ msgid "It look like you are using searx first time."
#~ msgstr "Φαίνεται ότι χρησιμοποιείται το searx για πρώτη φορά."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""

#~ msgid "Themes"
#~ msgstr "Θέματα"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Μέθοδος"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Ρυθμίσεις για προχωρημένους"

#~ msgid "Close"
#~ msgstr "Κλείσιμο"

#~ msgid "Language"
#~ msgstr "Γλώσσα"

#~ msgid "broken"
#~ msgstr "Κατεστραμένο"

#~ msgid "supported"
#~ msgstr "Υποστηρίζεται"

#~ msgid "not supported"
#~ msgstr "Δεν υποστηρίζεται"

#~ msgid "about"
#~ msgstr "Σχετικά"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr ""

#~ msgid "Style"
#~ msgstr ""

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "Επιλεγμένη γλώσσα"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "αποθήκευση"

#~ msgid "back"
#~ msgstr "πίσω"

#~ msgid "Links"
#~ msgstr "Σύνδεσμοι"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Αποτελέσματα αναζήτησης"

#~ msgid "next page"
#~ msgstr "επόμενη σελίδα"

#~ msgid "previous page"
#~ msgstr "προηγούμενη σελίδα"

#~ msgid "Start search"
#~ msgstr "Έναρξη αναζήτησης"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "στατιστικά"

#~ msgid "Heads up!"
#~ msgstr ""

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Πολύ καλά!"

#~ msgid "Settings saved successfully."
#~ msgstr "Οι ρυθμίσεις αποθηκεύτηκαν επιτυχώς."

#~ msgid "Oh snap!"
#~ msgstr "Φτου!"

#~ msgid "Something went wrong."
#~ msgstr "Κάτι πήγε στραβά."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr ""

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "Προτιμήσεις"

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "Μια χακέψιμη μεταμηχανή αναζήτησης , που σέβεται την ιδιωτικότητα"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Δεν υπάρχει διαθέσιμη σύνοψη για αυτήν την έκδοση."

#~ msgid "Self Informations"
#~ msgstr "Αυτοπληροφορίες"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Αλλαγή του τρόπου υποβολής φορμών, <a"
#~ " "
#~ "href=\"https://el.wikipedia.org/wiki/%CE%A0%CF%81%CF%89%CF%84%CF%8C%CE%BA%CE%BF%CE%BB%CE%BB%CE%BF_%CE%9C%CE%B5%CF%84%CE%B1%CF%86%CE%BF%CF%81%CE%AC%CF%82_%CE%A5%CF%80%CE%B5%CF%81%CE%BA%CE%B5%CE%B9%CE%BC%CE%AD%CE%BD%CE%BF%CF%85#%CE%9C%CE%AD%CE%B8%CE%BF%CE%B4%CE%BF%CE%B9_%CE%B1%CE%AF%CF%84%CE%B7%CF%83%CE%B7%CF%82_%CF%84%CE%BF%CF%85_HTTP\""
#~ " rel=\"external\">μάθετε περισσότερα για τις "
#~ "μεθόδους αίτησης</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Αυτό το πρόσθετο ελέγχει αν η "
#~ "διεύθυνση της αίτησης είναι ένας κόμβος"
#~ " εξόδου TOR και ενημερώνει τον χρήστη"
#~ " αν είναι, όπως το check.torproject.org "
#~ "αλλά από το searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Η λίστα κόμβων εξόδου TOR "
#~ "(https://check.torproject.org/exit-addresses) δεν "
#~ "είναι διαθέσιμη."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Χρησιμοποιείτε το TOR. Η διεύθυνση IP σας είναι: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Δεν χρησιμοποιείτε το TOR. Η διεύθυνση IP σας είναι: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "άλλα"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Αυτή η καρτέλα δεν εμφανίζεται για "
#~ "τα αποτελέσματα αναζήτησης, αλλά μπορείτε "
#~ "να αναζητήσετε τις μηχανές που "
#~ "παρατίθενται εδώ μέσω bangs."

#~ msgid "Shortcut"
#~ msgstr "Συντόμευση"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Οι μηχανές δε μπορούν να φέρουν αποτελέσματα."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Παρακαλώ, προσπαθήστε ξανά αργότερα ή "
#~ "βρείτε ένα άλλο instance του SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Ανακατεύθυνση σε εκδόσεις ανοικτής πρόσβασης"
#~ " των δημοσιεύσεων όταν είναι διαθέσιμες "
#~ "(απαιτείται πρόσθετο)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Αλλαγή του τρόπου υποβολής φορμών, <a"
#~ " "
#~ "href=\"https://el.wikipedia.org/wiki/%CE%A0%CF%81%CF%89%CF%84%CF%8C%CE%BA%CE%BF%CE%BB%CE%BB%CE%BF_%CE%9C%CE%B5%CF%84%CE%B1%CF%86%CE%BF%CF%81%CE%AC%CF%82_%CE%A5%CF%80%CE%B5%CF%81%CE%BA%CE%B5%CE%B9%CE%BC%CE%AD%CE%BD%CE%BF%CF%85#%CE%9C%CE%AD%CE%B8%CE%BF%CE%B4%CE%BF%CE%B9_%CE%B1%CE%AF%CF%84%CE%B7%CF%83%CE%B7%CF%82_%CF%84%CE%BF%CF%85_HTTP\""
#~ " rel=\"external\">μάθετε περισσότερα για τις "
#~ "μεθόδους αίτησης</a>"

#~ msgid "On"
#~ msgstr "Ενεργό"

#~ msgid "Off"
#~ msgstr "Ανενεργό"

#~ msgid "Enabled"
#~ msgstr "Ενεργοποιημένο"

#~ msgid "Disabled"
#~ msgstr "Απενεργοποιημένο"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Άμεση αναζήτηση κατά την επιλογή "
#~ "κατηγορίας. Απενεργοποιήστε για να διαλέξετε"
#~ " πολλαπλές κατηγορίες. (απαιτείται JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Πλήκτρα συντόμευσης τύπου Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Πλοήγηση στα αποτελέσματα αναζήτησης με "
#~ "πλήκτρα συντόμευσης τύπου Vim (απαιτείται "
#~ "JavaScript). Πατήστε το πλήκτρο \"h\" "
#~ "στην κύρια σελίδα ή στη σελίδα "
#~ "αποτελεσμάτων για να λάβετε βοήθεια."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "δε βρέθηκαν αποτελέσματα. Παρακαλούμε "
#~ "χρησιμοποιήστε άλλη αναζήτηση ή ψάξτε σε"
#~ " περισσότερες κατηγορίες."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Αντικατάσταση hostname των αποτελεσμάτων ή "
#~ "αφαίρεση των αποτελεσμάτων με βάση το"
#~ " hostname"

#~ msgid "Bytes"
#~ msgstr "Bytes"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Αντικατάσταση hostname"

#~ msgid "Error!"
#~ msgstr "Λάθος!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Οι μηχανές δε μπορούν να φέρουν αποτελέσματα"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Ξεκινήστε την υποβολή ενός νέου ζητήματος στο GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Γεννήτρια τυχαίων τιμών"

#~ msgid "Statistics functions"
#~ msgstr "Λειτουργίες στατιστικής"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Υπολογισμός {functions} των παραμέτρων"

#~ msgid "Get directions"
#~ msgstr "Πάρτε οδηγίες"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Προβολή της IP διεύθυνσης αν η "
#~ "αναζήτηση είναι \"ip\" και το user "
#~ "agent αν η αναζήτηση περιέχει \"user "
#~ "agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Δεν ήταν δυνατή η λήψη της λίστας"
#~ " διευθύνσεων εξόδου του δικτύου Tor "
#~ "από το: https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Χρησιμοποιείτε το δίκτυο Tor και "
#~ "φαίνεται πως η εξωτερική σας διεύθυνση"
#~ " είναι η: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Δεν χρησιμοποιείτε το δίκτυο Tor. Η "
#~ "εξωτερική σας διεύθυνση είναι: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Λέξεις κλειδιά"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Ο καθορισμός προσαρμοσμένων ρυθμίσεων στον "
#~ "σύνδεσμο προτιμήσεων μπορεί να χρησιμοποιηθεί"
#~ " για το συγχρονισμό των προτιμήσεων "
#~ "σας σε όλες τις συσκευές."

#~ msgid "proxied"
#~ msgstr "Διαμεσολαβημένα"

