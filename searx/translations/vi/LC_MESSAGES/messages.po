# Vietnamese translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>,
# 2024.
# <AUTHOR> <EMAIL>,
# 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-05-12 21:53+0000\n"
"Last-Translator: cduon010 <<EMAIL>>\n"
"Language: vi\n"
"Language-Team: Vietnamese "
"<https://translate.codeberg.org/projects/searxng/searxng/vi/>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "mà không cần tách nhóm thêm"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "khác"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "các thư mục"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "tổng quát"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "âm nhạc"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "mạng xã hội"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "hình ảnh"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "băng hình"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "máy radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tivi"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "CNTT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "tin tức"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "bản đồ"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "định tuyến onion"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "khoa học"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "Ứng dụng"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "Từ điển"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "Lời bài hát"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "gói kiện/gói hàng"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "hỏi đáp"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "kho"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "wiki về phần mềm"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "mạng lưới/mạng"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "các công bố khoa học"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "Tự động"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "Sáng"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "Tối"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "màu đen"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Thời gian hoạt động liên tục"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Thông tin"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Nhiệt độ trung bình."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Mây che phủ"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Điều kiện"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "tình hình hiện tại"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Buổi chiều"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Cảm thấy"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Độ ẩm"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Nhiệt độ tối đa"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Nhiệt độ thấp nhất"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Buổi sáng"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Buổi tối"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Buổi trưa"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Áp suất"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Mặt trời mọc"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "mặt trời lặn"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Nhiệt độ"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Chỉ số UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Tầm nhìn"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Gió"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "người đăng ký"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "những bài đăng"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "Người dùng hoạt động"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "Bình luận"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "người dùng"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "cộng đồng"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "điểm"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "tiêu đề"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "tác giả"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "mở"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "đóng"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "đã trả lời"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Không tìm thấy gì"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Nguồn"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Không thể tải trang kế tiếp"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Cài đặt không hợp lệ, xin xem lại tuỳ chỉnh"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Cài đặt không hợp lệ"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "lỗi tìm kiếm"

#: searx/webutils.py:35
msgid "timeout"
msgstr "Hết thời gian"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "lỗi phân tích"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Lỗi giao thức HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "Lỗi mạng"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Lỗi SSL: xác thực chứng chỉ không thành công"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "sập đột ngột"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "Lỗi HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Lỗi kết nối HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "Lỗi proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "quá nhiều yêu cầu"

#: searx/webutils.py:58
msgid "access denied"
msgstr "Truy cập bị từ chối"

#: searx/webutils.py:59
msgid "server API error"
msgstr "Lỗi máy chủ API"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Treo/gián đoạn/chặn"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} phút() trước"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} giờ, {minutes} phút trước"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Tạo các giá trị ngẫu nhiên khác nhau"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Tính toán {func} của các tham số"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Hiển thị đường đi trên bản đồ .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (HẾT HẠN)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Mục này đã được thay thế bởi"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kênh"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "tốc độ bit"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "bình chọn"

#: searx/engines/radio_browser.py:155
#, fuzzy
msgid "clicks"
msgstr "nhấp chuột"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Ngôn ngữ"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} nguồn trích dẫn từ năm {firstCitationVelocityYear} đến năm"
" {lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Không thể đọc URL của hình ảnh. Đây có thể là do hình ảnh sử dụng định "
"dạng không được hỗ trợ. TinEye chỉ hỗ trợ ảnh ở định dạng JPEG, PNG, GIF,"
" BMP, TIFF hoặc WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Hình ảnh này quá đơn giản để tìm ra kết quả. TinEye cần mức độ chi tiết "
"hình ảnh cơ bản để tìm thấy kết quả thành công."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Hình ảnh không thể được hiển thị."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Đánh giá của sách"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Chất lượng tệp"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "máy tính"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Tính toán bằng thanh tìm kiếm"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin băm"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Chuyển các chuỗi thành các hash băm khác nhau."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hash băm"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Bổ trợ tên máy chủ"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "Viết lại máy chủ, xoá các kểt quả tìm kiếm hoặc sắp xếp dựa trên máy chủ"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Viết lại DOI Truy Cập Miễn Phí"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Tránh việc trả phí bằng cách chuyển hướng đến các phiên bản truy cập miễn"
" phí của ấn phẩm khi có thể"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Thông Tin Cá Nhân"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Hiển thị IP của bạn nếu truy vấn là \"ip\" và user agent của bạn nếu truy"
" vấn là \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Địa chỉ IP của bạn: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Tác nhân người dùng của bạn là: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Kiểm tra Tor plugin"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Plugin này kiểm tra xem địa chỉ của yêu cầu này có phải là một nút thoát "
"của Tor hay không và sẽ báo cáo người dùng nếu đúng như vậy; giống như "
"check.torproject.org, nhưng từ SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Không thể tải xuống danh sách các đoạn trích xuất từ TOR từ"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Bạn đang sử dụng Tor và có vẻ bạn có địa chỉ IP bên ngoài"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Bạn đang không sử dụng Tor và bạn có địa chỉ IP bên ngoài"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Trình loại bỏ URL theo dõi"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Loại bỏ các đối số theo dõi từ URL trả về"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Chuyển đổi giữa các đại lượng"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Không tìm thấy trang"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Đi đến %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "tìm kiếm trang"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Ủng hộ"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Tuỳ chỉnh"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Được cung cấp bởi"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "một siêu công cụ tìm kiếm mã nguồn mỡ và tôn trọng quyền riêng tư"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Mã nguồn"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "công cụ theo dõi các trục trặc"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Các thông số về trình tìm kiếm"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Những thực thể công khai"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Chính sách bảo mật"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Liên hệ người bảo toàn thực thể"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Nhấp vào hình kính lúp để tiến hành tìm kiếm"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Độ dài"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Lượt xem"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Tác giả"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "đã lưu cache"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Bắt đầu đăng một vấn đề mới trên Github"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Vui lòng kiểm tra các lỗi đang tồn tại của công cụ tìm kiếm này trên "
"Github"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Tôi xác nhận rằng không có lỗi nào đang tồn tại về vấn đề mà tôi gặp phải"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Nếu đây là một thực thể công khai, vui lòng nêu rõ địa chỉ trong bản báo "
"cáo lỗi"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Đăng một vấn đề mới trên Github cùng với các thông tin trên"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Không hỗ trợ HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Xem nhật ký lỗi và đăng một bản báo cáo lỗi"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang cho công cụ tìm kiếm này"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang cho các danh mục của nó"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Trung vị"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Số bài kiểm định đã thất bại của công cụ kiểm tra: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Số lỗi:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Tổng quát"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Các danh mục mặc định"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Giao diện người dùng"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Quyền riêng tư"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Các trình tìm kiếm"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Các trình tìm kiếm đang được dùng"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Các truy vấn đặc biệt"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Các cookie"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Số lượng kết quả"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Thông tin"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Lên đầu trang"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Trang trước"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Trang sau"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Hiển thị trang đầu"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Tìm kiếm về..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "Xoá"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "tìm kiếm"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Hiện không có dữ liệu nào."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Tên trình tìm kiếm"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Điểm số"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Số lượng kết quả"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Thời gian phản hồi"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Độ tin cậy"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Tổng"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Xử lý"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Cảnh báo"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Lỗi và ngoại lệ"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Ngoại lệ"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Tin nhắn"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Phần trăm"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Tham số"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Tên file"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Chức năng"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Code"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Người kiểm duyệt"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Bài kiểm tra không đạt"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Bình luận"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Các ví dụ"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Định nghĩa"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Đồng nghĩa"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Các đáp án"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Tải về các kết quả"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Thử tìm kiếm:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Tin nhắn từ công cụ tìm kiếm"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "giây"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL Tìm kiếm"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Đã sao chép"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Sao chép"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Các gợi ý"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Ngôn ngữ tìm kiếm"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Ngôn ngữ mặc định"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Tự động phát hiện"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Tìm Kiếm An Toàn"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Nghiêm ngặt"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Vừa phải"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Không"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Khoảng thời gian"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Bất kỳ lúc nào"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Hôm trước"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Tuần trước"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Tháng trước"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Năm ngoái"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Thông tin!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "hiện tại không có cookie nào."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Xin lỗi!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Không tìm thấy kết quả. Bạn có thể thử:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Không còn kết quả phù hợp. Bạn có thể thử:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Tải lại trang."

#: searx/templates/simple/messages/no_results.html:20
#, fuzzy
msgid "Search for another query or select another category (above)."
msgstr "Tìm kiếm bằng truy vấn khác hoặc chọn lại một trong những mục ở trên."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Thay đổi công cụ tìm kiếm sẽ được dùng trong phần tùy chọn:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Đổi sang phiên bản khác:"

#: searx/templates/simple/messages/no_results.html:24
#, fuzzy
msgid "Search for another query or select another category."
msgstr "Tìm kiếm bằng truy vấn khác hoặc chọn mục khác."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Quay lại trang t rước bằng nút bấm trang trước"

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Cho phép"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Từ khoá (từ đầu tiên trong truy vấn)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Tên"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Mô tả"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Danh sách các mô-đun trả lời nhanh của SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Danh sách các plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Gợi ý tự động"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Tìm kiếm ngay khi gõ"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Căn giữa"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Hiện các kết quả ở giữa trang (theo bố cục Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Danh sách tên và giá trị của những cookies mà SearXNG lưu trữ trên máy "
"tính của bạn."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Qua danh sách này, bạn có thể đánh giá sự minh bạch của SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Tên cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Giá trị"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "URL tìm kiếm của tuỳ chỉnh được lưu hiện tại"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Ghi chú: việc định rõ cài đặt cá nhân trong URL tìm kiếm có thể làm suy "
"giảm mức độ riêng tư vì nó chuyển dữ liệu đến các trang kết quả được nhấp"
" chọn."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr ""
"URL dùng để khôi phục những lựa chọn ưu tiên của bạn trong một trình "
"duyệt khác"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Sao chép những mã băm được ưu tiên"

#: searx/templates/simple/preferences/cookies.html:57
#, fuzzy
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Chèn những mã băm được ưu tiên (không bao gồm URL) để khôi phục"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Mã băm được ưu tiên"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Định danh đối tượng kỹ thuật số (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Trình xử lý DOI Truy Cập Miễn Phí"

#: searx/templates/simple/preferences/doi_resolver.html:18
#, fuzzy
msgid "Select service used by DOI rewrite"
msgstr "Chọn dịch vụ được dùng bởi DOI rewrite"

#: searx/templates/simple/preferences/engines.html:9
#, fuzzy
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Tab không tồn tại trong giao diện người dùng, nhưng bạn có thể tìm kiếm "
"bằng !bangs của nó."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Bật tất cả"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Tắt tất cả"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Có hỗ trợ ngôn ngữ được chọn"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Tỉ trọng"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Thời gian tối đa"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Bộ phân giải biểu tượng web"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Hiển thị biểu tượng web gần kết quả tìm kiếm"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Những cài đặt này được lưu trữ trong các cookie, điều này cho phép chúng "
"tôi không phải lưu các dữ liệu về bạn."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Những cookie này chỉ phục vụ cho chính bạn, chúng tôi không sử dụng chúng"
" để theo dõi bạn."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Lưu"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Đưa về mặc định"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Quay lại"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Phím tắt"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-like"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Điều hướng kết quả tìm kiếm bằng phím tắt (yêu cầu JavaScript). Tại trang"
" chủ hoặc tại trang tìm kiếm, nhấn phím \"h\" để nhận trợ giúp."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy hình ảnh"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proxy hóa các kết quả bức ảnh tìm kiếm được thông qua SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Cuộn liên tục"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Tự động tải trang kế tiếp khi cuộn đến cuối trang hiện tại"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Bạn muốn tìm kiếm bằng ngôn ngữ nào?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "Chọn Tự đông phát hiện để SearXNG dò ra ngôn ngữ thuộc tìm kiếm của bạn."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Phương thức HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Thay đổi cách gửi biểu mẫu"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Truy vấn đặt ở tiêu đề trang"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Nếu bật lên, truy vấn sẽ nằm ở tiêu đề của trang kết quả. Trình duyệt có "
"thể ghi lại tiêu đề này"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Hiện kết quả trên các thẻ mới"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Mở kết quả trên những thẻ trình duyệt mới"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Lọc các nội dung"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Tìm kiếm khi chọn danh mục đơn"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Thực hiện tìm kiếm ngay lập tức ngay khi chọn một danh mục. Tắt cài đặt "
"này để lựa chọn nhiều danh mục"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Chủ đề màu"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Thay đổi bố cục của SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Phong cách của chủ đề màu"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Chọn tự động để tuân thủ cài đặt của trình duyệt"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Các vé của công cụ tìm kiếm"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Truy cập các vé cho các công cụ tìm kiếm riêng tư"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Ngôn ngữ giao diện"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Thay đổi ngôn ngữ giao diện"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Định dạng URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Đẹp"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Đầy đủ"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Máy chủ"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Thay đổi kết quả định dạng URL"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "kho"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "hiện nội dung"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "ẩn nội dung"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Trang web này không cung cấp bất kỳ mô tả."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Kích thước tập tin"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Ngày"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Loại"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Độ phân giải"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Định dạng"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Công cụ tìm kiếm"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Xem nguồn"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "Địa chỉ"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "hiện bản đồ"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ẩn bản đồ"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Phiên bản"

#: searx/templates/simple/result_templates/packages.html:18
#, fuzzy
msgid "Maintainer"
msgstr "Người bảo trì"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Cập nhật lúc"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Thẻ"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Độ phổ biến"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Giấy phép"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Dự án"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Trang chủ dự án"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Ngày phát hành"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Tạp chí"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Người chỉnh sửa"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Nhà xuất bản"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "liên kết magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "tập tin torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeder"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Số lượng tập tin"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "hiện"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ẩn phim"

#~ msgid "Engine time (sec)"
#~ msgstr "Thời gian trình tìm kiếm (giây)"

#~ msgid "Page loads (sec)"
#~ msgstr "Tải trang (giây)"

#~ msgid "Errors"
#~ msgstr "Các lỗi"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Viết lại các liên kết HTTP thành HTTPS khi có thể"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Theo mặc định thì các kết quả "
#~ "được mở trên cùng một cửa sổ. "
#~ "Phần mở rộng này sẽ ghi đè "
#~ "lên hành vi mặc định đó để "
#~ "mở các liên kết trên các thẻ/cửa"
#~ " sổ mới. (yêu cầu JavaScript)"

#~ msgid "Color"
#~ msgstr "Màu sắc"

#~ msgid "Blue (default)"
#~ msgstr "Xanh lam (mặc định)"

#~ msgid "Violet"
#~ msgstr "Tím"

#~ msgid "Green"
#~ msgstr "Xanh lục"

#~ msgid "Cyan"
#~ msgstr "Lục lam"

#~ msgid "Orange"
#~ msgstr "Cam"

#~ msgid "Red"
#~ msgstr "Đỏ"

#~ msgid "Category"
#~ msgstr "Danh mục"

#~ msgid "Block"
#~ msgstr "Chặn"

#~ msgid "original context"
#~ msgstr "ngữ cảnh gốc"

#~ msgid "Plugins"
#~ msgstr "Các phần mở rộng"

#~ msgid "Answerers"
#~ msgstr "Trình trả lời nhanh"

#~ msgid "Avg. time"
#~ msgstr "Thời gian trung bình"

#~ msgid "show details"
#~ msgstr "hiện chi tiết"

#~ msgid "hide details"
#~ msgstr "ẩn chi tiết"

#~ msgid "Load more..."
#~ msgstr "Tải thêm..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Thay đổi giao diện searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Proxy kết quả hình ảnh qua searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Đây là danh sách các module trả lời nhanh của searx"

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Đây là danh sách các cookie và "
#~ "giá trị của chúng mà searx đang"
#~ " lưu trữ trên máy tính của bạn."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Với danh sách này, bạn có thể đánh giá tính minh bạch của searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Có vẻ như bạn mới sử dụng searx lần đầu."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Xin thử lại lần nữa hoặc tìm một server searx khác"

#~ msgid "Themes"
#~ msgstr "Nền"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Phương pháp"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Cài đặt nâng cao"

#~ msgid "Close"
#~ msgstr "Đóng"

#~ msgid "Language"
#~ msgstr ""

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "có hỗ trợ"

#~ msgid "not supported"
#~ msgstr "không hỗ trợ"

#~ msgid "about"
#~ msgstr "thông tin về"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Chọn phong cách cho nền này"

#~ msgid "Style"
#~ msgstr "Phong cách"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "Ngôn ngữ được chọn"

#~ msgid "Query"
#~ msgstr "Truy vấn"

#~ msgid "save"
#~ msgstr "lưu"

#~ msgid "back"
#~ msgstr "trở về"

#~ msgid "Links"
#~ msgstr "Các liên kết"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Kết quả tìm kiếm"

#~ msgid "next page"
#~ msgstr "trang tiếp theo"

#~ msgid "previous page"
#~ msgstr "trang liền trước"

#~ msgid "Start search"
#~ msgstr "Bắt đầu tìm kiếm"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "các thông số"

#~ msgid "Heads up!"
#~ msgstr "Cẩn thận!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Tốt lắm!"

#~ msgid "Settings saved successfully."
#~ msgstr "Lưu cài đặt thành công."

#~ msgid "Oh snap!"
#~ msgstr "Quái quỷ thật!"

#~ msgid "Something went wrong."
#~ msgstr "Đã có sự cố."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Xem hình ảnh"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "tuỳ chỉnh"

#~ msgid "Scores per result"
#~ msgstr "Điểm số cho từng kết quả"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "một trình tìm kiếm đa nguồn, dễ tuỳ biến và tôn trọng quyền riêng tư"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Không có bản tóm tắt nào cho ấn phẩm này."

#~ msgid "Self Informations"
#~ msgstr "thông tin bản thân"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Thay đổi cách thức các cụm từ "
#~ "tìm kiếm được gửi đi, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">tìm hiểu thêm về các "
#~ "phương thức tìm kiếm</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Plugin này kiểm tra nếu một địa"
#~ " chỉ được yêu cầu có phải là"
#~ " một TOR exit node hay không, "
#~ "và thông báo lại cho người dùng."
#~ " Giống như check.torproject.org nhưng từ"
#~ " SearXNG."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Danh sách TOR exit node "
#~ "(https://check.torproject.org/exit-addresses) không "
#~ "thể được tiếp cận."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Bạn đang dùng TOR. Địa chỉ IP của bạn có thể là: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Tự động phát hiện ngôn ngữ tìm kiếm"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Tự động phát hiện ngôn ngữ tìm kiếm và chuyển sang ngôn ngữ đó."

#~ msgid "others"
#~ msgstr "người khác"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "Lối tắt"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Các trình tìm kiếm không nhận được kết quả."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Chuyển hướng đến các phiên bản "
#~ "truy cập miễn phí của ấn phẩm "
#~ "khi có thể (yêu cầu phần mở "
#~ "rộng)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Thay đổi cách các biểu mẫu được"
#~ " đăng, <a "
#~ "href=\"https://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">tìm hiểu thêm về các "
#~ "phương pháp yêu cầu HTTP</a>"

#~ msgid "On"
#~ msgstr "Bật"

#~ msgid "Off"
#~ msgstr "Tắt"

#~ msgid "Enabled"
#~ msgstr "Đã"

#~ msgid "Disabled"
#~ msgstr "Đã tắt"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Thực thi tìm kiếm ngay khi chọn"
#~ " một danh mục. Tắt đi để chọn"
#~ " nhiều danh mục. (yêu cầu JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Các phím tắt Vim-like"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Điều hướng các kết quả tìm kiếm"
#~ " với các phím tắt giống phần "
#~ "mềm Vim (yêu cầu JavaScript). Nhấn "
#~ "phím \"h\" trên trang chính hoặc "
#~ "trang kết quả để xem trợ giúp."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "chúng tôi không tìm thấy kết quả"
#~ " nào. Xin gõ cụm từ khác hoặc"
#~ " tìm kiếm trong nhiều danh mục "
#~ "hơn."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Viết lại kết quả tên của các "
#~ "máy chủ hoặc loại bỏ kết quả "
#~ "dựa trên tên của máy chủ"

#~ msgid "Bytes"
#~ msgstr "Byte"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Thay đổi tên máy chủ"

#~ msgid "Error!"
#~ msgstr "Lỗi!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Các trình tìm kiếm không nhận được kết quả"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Bắt đầu đăng một vấn đề mới trên Github"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Trình tạo giá trị ngẫu nhiên"

#~ msgid "Statistics functions"
#~ msgstr "Các hàm thống kê"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Tính toán {functions} của các đối số"

#~ msgid "Get directions"
#~ msgstr "Nhận điều hướng"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr "Hiện IP của bạn khi gõ \"ip\" và hiện user agent khi gõ \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Không thể tải xuống danh sách của"
#~ " những nút thoát Tor từ: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Bạn đang sử dụng Tor và hình "
#~ "như bạn có địa chỉ IP ngoại "
#~ "tiếp này: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Bạn hiện không sử dụng Tor và đây là địa chỉ IP của bạn: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Các từ khoá"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Chỉ định cài đặt tùy chỉnh cho "
#~ "những tùy chọn URL có thể dùng "
#~ "để đồng bộ tùy chọn giữa nhiều "
#~ "thiết bị."

#~ msgid "proxied"
#~ msgstr "đã proxy"

