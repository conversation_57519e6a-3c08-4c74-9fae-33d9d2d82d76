# Finnish translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2022, 2023.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-05-27 03:58+0000\n"
"Last-Translator: artnay <<EMAIL>>\n"
"Language: fi\n"
"Language-Team: Finnish "
"<https://translate.codeberg.org/projects/searxng/searxng/fi/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "Ilman lisäryhmitystä"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "muut"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "tiedostot"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "yleiset"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musiikki"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sosiaalinen media"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "kuvat"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videot"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "it"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "uutiset"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "kartta"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onion-linkit"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "tiede"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "sovellukset"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "sanakirjat"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "lyriikat"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketit"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "q&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repot"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "ohjelmistowikit"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "Tieteelliset Julkaisut"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automaattinen"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "vaalea"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "tumma"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "musta"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Käytettävyysaika"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Tietoa SearXNG:stä"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Keskilämpötila."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Pilvipeite"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Tilanne"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Tämän hetkinen tilanne"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Ilta"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Tuntuu kuin"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Kosteus"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maksimi lämpötila."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Minimi lämpötila."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Aamu"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Yö"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Päivä"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Ilmanpaine"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Auringonnousu"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Auringonlasku"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Lämpötila"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV indeksi"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Näkyvyys"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Tuuli"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "tilaajat"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "postaukset"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktiiviset käyttäjät"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kommentit"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "käyttäjä"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "yhteisö"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "pisteet"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "Otsikko"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "tekijä"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Avaa"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "suljettu"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "vastattu"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Tietuetta ei löytynyt"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Lähde"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Virhe ladattaessa seuraavaa sivua"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Virheelliset asetukset, muokkaa siis asetuksia"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Virheelliset asetukset"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "hakuvirhe"

#: searx/webutils.py:35
msgid "timeout"
msgstr "aikakatkaistu"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "jäsentämisvirhe"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-protokollavirhe"

#: searx/webutils.py:38
msgid "network error"
msgstr "verkkovirhe"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL-virhe: sertifikaatin vahvistus epäonnistui"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "odottamaton kaatuminen"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-virhe"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-yhteysvirhe"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxy-virhe"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "liian monta pyyntöä"

#: searx/webutils.py:58
msgid "access denied"
msgstr "pääsy kielletty"

#: searx/webutils.py:59
msgid "server API error"
msgstr "palvelimen API-virhe"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Keskeytetty"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minuutti(a) sitten"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} tunti(a), {minutes} minuutti(a) sitten"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generoi satunnaislukuja"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Laske argumenteista {func}"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Näytä reitti kartalla .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (VANHENTUNUT)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Tämän kohdan on korvannut"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanava"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bittinopeus"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "ääntä"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikkaukset"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Kieli"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} Sitaatit vuodesta {firstCitationVelocityYear} vuoteen "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Kuvan url-osoitetta ei voitu lukea. Tämä saattaa johtua tiedostomuodosta,"
" jota ei tueta. TinEye tukee vain kuvia, jotka ovat JPEG, PNG, GIF, BMP, "
"TIFF tai WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Kuva on liian yksinkertainen löytääkseen osumia. TinEye vaatii "
"visuaalisen tarkkuuden perustason, jotta osumien tunnistaminen onnistuu."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Tätä kuvaa ei voida ladata."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Kirjan arvostelu"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Tiedoston laatu"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia-estolista"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Suodata pois Ahmian estolistalla olevat onion-tulokset."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Peruslaskin"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Laske matemaattisia lausekkeita hakupalkissa"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hajautus-liitännäinen"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Muuntaa merkkijonot erilaisiksi hash-digesteiksi."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hash-digest"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Isäntänimien liitännäinen"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Kirjoita isäntänimiä uudelleen, poista tuloksia tai priorisoi ne "
"isäntänimen perusteella"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Open Access DOI -uudelleenkirjoitus"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Vältä maksumuureja ohjaamalla julkaisujen avoimiin versioihin jos "
"mahdollista"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Tietojasi"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Näyttää IP-osoitteesi, jos kysely on \"ip\", ja käyttäjäagenttisi, jos "
"kysely on \"user-agent\"."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "IP-osoitteesi: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Selaimesi tunnistetiedot: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor-verkon tarkistusliitännäinen"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Tämä liitännäinen tarkistaa, tuleeko pyyntö TOR-poistumissolmusta, ja "
"ilmoittaa käyttäjälle, jos niin on; samalla tavalla kuin "
"check.torproject.org, mutta SearXNGista."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Tor-poistumissolmujen luetteloa ei voitu ladata kohteesta"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Käytät Toria ja näyttää siltä, että sinulla on ulkoinen IP-osoite"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Et käytä Toria ja sinulla on ulkoinen IP-osoite"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Seurantapalvelimen osoitteen poistaja"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Poista seurantapalvelinten argumentit palautetusta osoitteesta"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Yksikkömuuntajan liitännäinen"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Muunna yksiköiden välillä"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Sivua ei löytynyt"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Siirry %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "hakusivulle"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Lahjoita"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Asetukset"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Taustavoimana"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "yksityisyyttä kunnioittava, avoin metahakukone"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Lähdekoodi"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Ongelmien seuranta"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Hakukoneen tilastot"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Julkiset palvelimet"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Tietosuojakäytäntö"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Ota yhteyttä palvelun ylläpitäjään"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Napsauta suurennuslasia suorittaaksesi haun"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Pituus"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Näkymät"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Tekijä"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "välimuistissa"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Avaa uusi issue GitHubissa"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Tarkista tämän hakukoneen tämänhetkiset ongelmat GitHubista"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Vahvistan, että tästä bugista ei ole olemassaolevaa issue:ta"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Jos kyseessä on julkinen instanssi, määritä sen URL-osoite "
"vikailmoituksessa"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Lähetä uusi ongelma Githubiin sisältäen edellä mainitut tiedot"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Ei HTTPS-yhteyttä"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Katso virhelokit ja lähetä virhe raportti"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang tälle hakukoneelle"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang sen kategorioille"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediaani"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Epäonnistuneet tarkistustestit: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Virheet:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Yleiset"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Oletusluokat"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Käyttöliittymä"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Yksityisyys"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Hakukoneet"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Nyt käytetyt hakukoneet"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Erityiset kyselyt"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Evästeet"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Tulosten määrä"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Tiedot"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Takaisin huipulle"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Edellinen sivu"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Seuraava sivu"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Näytä etusivu"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Etsi..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "tyhjennä"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "haku"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Tietoja ei ole juuri nyt saatavilla. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Hakukoneen nimi"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Pisteet"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Tulosten määrä"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Vastausaika"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Luotettavuus"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Yhteensä"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Käsitellään"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Varoitukset"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Virheet ja poikkeukset"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Poikkeus"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Viesti"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Prosentti"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametri"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Tiedoston nimi"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funktio"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Koodi"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Tarkistaja"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Epäonnistunut testi"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Kommentit"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Esimerkit"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Määritelmät"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonyymit"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Vastaukset"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Lataa tulokset"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Yritä etsiä:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Viestit hakukoneilta"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Haun osoite"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopioitu"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopioi"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Ehdotukset"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Haun kieli"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Oletuskieli"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Havaitse automaattisesti"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Turvahaku"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Tiukka"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Keskitaso"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Ei mitään"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Aikaväli"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Milloin tahansa"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Viimeinen päivä"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Viimeinen viikko"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Viimeinen kuukausi"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Viimeinen vuosi"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Huomio!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "Evästeitä ei ole määritetty tällä hetkellä."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Pahoittelut!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Tuloksia ei löytynyt. Voit:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Tuloksia ei ole enään lisää. Voit yrittää:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Päivittää sivun."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Etsiä eri hakuehdolla tai valita toisen luokan (yläpuolella)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Vaihtaa käytettävää hakukonetta asetuksista:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Vaihtaa toiseen instanssiin:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Hae toista kyselyä tai valitse toinen luokka."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Palaa edelliselle sivulle käyttäen edellinen sivu-nappia."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Salli"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Avainsanat (ensimmäinen sana kyselyssä)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nimi"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Kuvaus"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Tämä on luettelo SearXNG:n pikavastausmoduuleista."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Tämä on lista liitännäisistä."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automaattinen täydentäminen"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Löydä tuloksia kirjoittaessasi"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Keskipisteen kohdistus"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Näyttää tulokset sivun keskellä (Oscar-asettelu)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Tämä on luettelo evästeistä ja niiden arvoista, joita SearXNG tallentaa "
"tietokoneellesi."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Tämän luettelon avulla voit arvioida SearXNG:n läpinäkyvyyttä."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Evästeen nimi"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Arvo"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Nykyisten asetusten hakuosoite"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Huomio: mukautettujen asetusten hakuosoite voi heikentää yksityisyyttä "
"vuotamalla tietoja niille sivustoille, joihin kohdistuvia tuloksia "
"napsautetaan."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL-osoite asetusten palauttamiseksi toisessa selaimessa"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Asetuksesi sisältävä URL-osoite. Tätä URL-osoitetta voi käyttää asetusten"
" palauttamiseen eri laitteella."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopioi asetusten tiiviste"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Syötä kopioitu asetusten tiiviste (ilman URL-osoitetta) palautusta varten"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Asetusten tiiviste"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digitaalinen objektin tunniste (Digital Object Identifier (DOI))"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Open Access DOI -selvitin"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Valitse palvelu, jota käytetään DOI:n uudelleenkirjoituksessa"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Tätä välilehteä ei ole käyttöliittymässä, mutta voit tehdä haun näillä "
"moottoreilla käyttämällä niiden erikoismerkkejä (!bangs)."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Käytä kaikkia"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Poista kaikki käytöstä"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Tukee valittua kieltä"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Paino"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Enimmäisaika"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon-ratkaisija"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Näytä Faviconit hakutulosten lähellä"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Nämä asetukset tallennetaan evästeisiisi. Näin Searxin ei tarvitse "
"tallentaa sinuun liittyviä henkilökohtaisia tietoja."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Kyseiset evästeet palvelevat ainoastaan sinua, eikä niitä käytetä "
"seuraamiseesi."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Tallenna"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Palauta oletukset"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Takaisin"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Pikanäppäimet"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-kaltainen"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Liiku hakutulosten välillä pikanäppäimillä (JavaScript vaaditaan). Paina "
"\"h\" pääsivulla tai tulossivulla nähdäksesi ohjeen."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Kuvat välityspalvelimen kautta"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Kuvatulosten välitys SearXNG:n kautta"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Loputon vieritys"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Lataa automaattisesti seuraava sivu, kun nykyisen sivun loppu saavutetaan"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Millä kielellä haluat etsiä ensisijaisesti?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Valitse \"Havaitse automaattisesti\", jotta SearXNG havaitsee haun kielen"
" automaattisesti."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP-menetelmä"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Vaihda tapaa, miten lomakkeet lähetetään"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Haun nimi sivun otsikossa"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Kun tämä on käytössä, sivun otsikko sisältää kyselysi. Selaimesi voi "
"tallentaa tämän otsikon"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Tulokset uusiin välilehtiin"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Avaa tulokset uusiin välilehtiin"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Suodata hakutulosten sisältöä"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Etsi valitsemalla luokka"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Suorita haku välittömästi, jos luokka on valittu. Poista käytöstä "
"valitaksesi useita luokkia"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Teema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Vaihda SearXNG:n käyttöliittymä"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Teeman tyyli"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Valitse auto seurataksesi selaimesi asetuksia"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Hakukonetokenit"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Pääsytunnukset yksityisiin hakukoneisiin"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Käyttöliittymän kieli"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Vaihda asettelun kieltä"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL-muotoilu"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Nätti"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Täysi"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Isäntä"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Vaihda tulosten URL-muotoilua"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repot"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "näytä media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "piilota media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Tämä sivu ei antanut mitään kuvausta."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Tiedostokoko"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Päivämäärä"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tyyppi"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Resoluutio"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Muoto"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Hakukone"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Näytä lähde"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "osoite"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "näytä kartta"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "piilota kartta"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versio"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Ylläpitäjä"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Päivitetty"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tägit"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Suosio"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lisenssi"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekti"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projektin sivusto"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Julkaisupäivä"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Journaali"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editori"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Julkaisija"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet-linkki"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent-tiedosto"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Lähettäjä"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Lataaja"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Tiedostojen määrä"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "näytä video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "piilota video"

#~ msgid "Engine time (sec)"
#~ msgstr "Hakukoneen aika (s)"

#~ msgid "Page loads (sec)"
#~ msgstr "Sivun lataus (s)"

#~ msgid "Errors"
#~ msgstr "Virheet"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Muuta HTTP-linkit HTTPS-muotoon jos mahdollista"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Tulokset avataan oletuksena samaan ikkunaan."
#~ " Tämä lisäosa korvaa oletustoiminnan "
#~ "avaamalla linkit uusiin välilehtiin tai "
#~ "ikkunoihin. (JavaScript vaaditaan)"

#~ msgid "Color"
#~ msgstr "Väri"

#~ msgid "Blue (default)"
#~ msgstr "Sininen (oletus)"

#~ msgid "Violet"
#~ msgstr "Violetti"

#~ msgid "Green"
#~ msgstr "Vihreä"

#~ msgid "Cyan"
#~ msgstr "Syaani"

#~ msgid "Orange"
#~ msgstr "Oranssi"

#~ msgid "Red"
#~ msgstr "Punainen"

#~ msgid "Category"
#~ msgstr "Luokka"

#~ msgid "Block"
#~ msgstr "Estä"

#~ msgid "original context"
#~ msgstr "alkuperäinen konteksti"

#~ msgid "Plugins"
#~ msgstr "Lisäosat"

#~ msgid "Answerers"
#~ msgstr "Vastaajat"

#~ msgid "Avg. time"
#~ msgstr ""
#~ "Keskimääräinen\n"
#~ "aika"

#~ msgid "show details"
#~ msgstr "näytä tiedot"

#~ msgid "hide details"
#~ msgstr "piilota tiedot"

#~ msgid "Load more..."
#~ msgstr "Lataa lisää..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Muuta searxin asettelua"

#~ msgid "Proxying image results through searx"
#~ msgstr "Kuvatulokset välitetään searxin välityspalvelimen kautta"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Tämä on luettelo searxin vastaajamoduuleista."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Ohessa on lista evästeistä ja niiden "
#~ "arvoista, joita searx tallentaa "
#~ "tietokoneellesi."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Tämän luettelon avulla voit arvioida searxin läpinäkyvyyden."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Vaikuttaa siltä, että käytät searxia ensimmäistä kertaa."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Yritä myöhemmin uudelleen tai kokeile toista searx-instanssia."

#~ msgid "Themes"
#~ msgstr "Teemat"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Tapa"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Lisäasetukset"

#~ msgid "Close"
#~ msgstr "Sulje"

#~ msgid "Language"
#~ msgstr "Kieli"

#~ msgid "broken"
#~ msgstr "rikki"

#~ msgid "supported"
#~ msgstr "tuettu"

#~ msgid "not supported"
#~ msgstr "ei tuettu"

#~ msgid "about"
#~ msgstr "tietoja"

#~ msgid "Avg."
#~ msgstr "N."

#~ msgid "User Interface"
#~ msgstr "Käyttöliittymä"

#~ msgid "Choose style for this theme"
#~ msgstr "Valitse tyyli tälle teemalle"

#~ msgid "Style"
#~ msgstr "Tyyli"

#~ msgid "Show advanced settings"
#~ msgstr "Näytä edistyneet asetukset"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Näytä edistyneet asetukset kotisivulla oletuksena"

#~ msgid "Allow all"
#~ msgstr "Salli kaikki"

#~ msgid "Disable all"
#~ msgstr "Poista kaikki käytöstä"

#~ msgid "Selected language"
#~ msgstr "Valittu kieli"

#~ msgid "Query"
#~ msgstr "Kysely"

#~ msgid "save"
#~ msgstr "tallenna"

#~ msgid "back"
#~ msgstr "takaisin"

#~ msgid "Links"
#~ msgstr "Linkit"

#~ msgid "RSS subscription"
#~ msgstr "RSS-tilaus"

#~ msgid "Search results"
#~ msgstr "Hakutulokset"

#~ msgid "next page"
#~ msgstr "seuraava sivu"

#~ msgid "previous page"
#~ msgstr "edellinen sivu"

#~ msgid "Start search"
#~ msgstr "Aloita haku"

#~ msgid "Clear search"
#~ msgstr "Tyhjennä haku"

#~ msgid "Clear"
#~ msgstr "Tyhjennä"

#~ msgid "stats"
#~ msgstr "tilastot"

#~ msgid "Heads up!"
#~ msgstr "Pää pystyyn!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Näyttää siltä, että käytät SearXNG:tä ensimmäistä kertaa."

#~ msgid "Well done!"
#~ msgstr "Hyvin tehty!"

#~ msgid "Settings saved successfully."
#~ msgstr "Asetukset tallennettiin onnistuneesti."

#~ msgid "Oh snap!"
#~ msgstr "Voi ei!"

#~ msgid "Something went wrong."
#~ msgstr "Jokin meni pieleen."

#~ msgid "Date"
#~ msgstr "Päivämäärä"

#~ msgid "Type"
#~ msgstr "Tyyppi"

#~ msgid "Get image"
#~ msgstr "Avaa kuva"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "asetukset"

#~ msgid "Scores per result"
#~ msgstr "Pisteet per tulos"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "yksityisyyttä kunnioittava, muokattava metahakukone"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Tästä julkaisusta ei ole yhteenvetoa."

#~ msgid "Self Informations"
#~ msgstr "Itsetiedot"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Muuta tapaa, miten lomaketiedot välitetään."
#~ " <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">Lisätietoja eri välitystavoista.</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Tämä lisäosa tarkistaa, tuleeko pyyntö "
#~ "TOR exit nodesta, ja ilmoittaa "
#~ "käyttäjälle, jos se on, samalla tavalla"
#~ " kuin check.torproject.org, mutta searxngista."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "TOR exit node listaan "
#~ "(https://check.torproject.org/exit-addresses) ei "
#~ "saada yhteyttä."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Sinä käytät TOR:ia. Sinun IP-osoitteesi näyttää olevan: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Sinä et käytä TOR:ia. Sinun IP-osoitteesi näyttää olevan: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Tunnista hakukieli automaattisesti"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Automaattisesti huomaa hakukieli, ja vaihda siihen."

#~ msgid "others"
#~ msgstr "Toiset"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Tämä välilehti ei näy hakutuloksissa, "
#~ "mutta voit tehdä hakuja täällä "
#~ "luetelluista moottoreista \"bangien\" kautta."

#~ msgid "Shortcut"
#~ msgstr "Oikoreitti"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Moottorit eivät voi palauttaa tuloksia."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Yritä uudelleen tai etsi toinen SearXNG-palvelin."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Uudelleenohjaa julkaisujen open-access-"
#~ "versioihin kun mahdollista (vaatii "
#~ "liitännäisen)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Vaihda tapaa miten lomakkeet lähetetään, "
#~ "<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">lisätietoja eri "
#~ "pyyntömenetelmistä</a>"

#~ msgid "On"
#~ msgstr "Päällä"

#~ msgid "Off"
#~ msgstr "Pois"

#~ msgid "Enabled"
#~ msgstr "Käytössä"

#~ msgid "Disabled"
#~ msgstr "Ei käytössä"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Suorita haku välittömästi, jos luokka "
#~ "valitaan. Poista käytöstä valitaksesi useita"
#~ " luokkia. (JavaScript vaaditaan)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim-kaltaiset pikanäppäimet"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Liiku hakutulossivuilla Vim-kaltaisin "
#~ "näppäinkomennoin (JavaScript vaaditaan). Paina "
#~ "\"h\" pää- tai hakutulossivulla nähdäksesi "
#~ "ohjeet."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Yhtäkään hakuasi vastaavaa tulosta ei "
#~ "löytynyt. Etsi käyttäen eri hakuehtoja "
#~ "tai ulota hakusi nykyistä useampiin eri"
#~ " luokkiin."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Kirjoita tuloksien isäntänimiä uudelleen tai"
#~ " poista tulokset isäntänimen perusteella"

#~ msgid "Bytes"
#~ msgstr "Tavua"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Isäntänimen korvaus"

#~ msgid "Error!"
#~ msgstr "Virhe!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Moottorit eivät voi palauttaa tuloksia"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Avaa uusi issue GitHubissa"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Satunnaisluvun generaattori"

#~ msgid "Statistics functions"
#~ msgstr "Tilastolliset funktiot"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Laske argumenttien {functions}"

#~ msgid "Get directions"
#~ msgstr "Reittiohjeet"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Näyttää IP-osoitteesi jos hakuehtosi on"
#~ " \"ip\" ja selaimen tunnistetiedot jos "
#~ "hakuehtosi sisältää sanat \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Lopetuspisteiden luettelo Tor-verkon "
#~ "poistumisreiteistä ei voitu ladata "
#~ "osoitteesta: https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Käytät Tor-verkkoa ja vaikuttaa siltä,"
#~ " että sinulla on tämä ulkoinen IP-"
#~ "osoite: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Et käytä Tor-verkkoa ja sinulla on"
#~ " tämä ulkoinen IP-osoite: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Avainsanat"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Määrittämällä mukautettuja asetuksia asetusten "
#~ "URL-osoitteessa voidaan käyttää "
#~ "synkronoimiseen asetuksia eri laitteissa."

#~ msgid "proxied"
#~ msgstr "välityspalvelimella"

