# Polish (Poland) translations for .
# Copyright (C) 2017 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023, 2024, 2025.
# m<PERSON><PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# B<PERSON>owny <<EMAIL>>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# daniels<PERSON>w<PERSON>uk <danielszew<PERSON><EMAIL>>,,
# 2025.
# 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-05-09 07:09+0000\n"
"Last-Translator: polskiecus <<EMAIL>>\n"
"Language: pl\n"
"Language-Team: Polish "
"<https://translate.codeberg.org/projects/searxng/searxng/pl/>\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && "
"(n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && "
"n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "bez dalszego podgrupowania"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "inne"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "pliki"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "ogólne"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muzyka"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "media społecznościowe"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "obrazy"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "filmy"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "technologia"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "wiadomości"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapa"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "linki .onion"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "nauka"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikacje"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "słowniki"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "teksty piosenek"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pakiety"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "q&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repozytoria"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "dokumentacje aplikacji"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "internet"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "publikacje naukowe"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatycznie"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "jasny"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "ciemny"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "czarny"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "czas działania"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Informacje o"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Średnia temp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Zachmurzenie"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Warunki pogodowe"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Aktualna pogoda"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Wieczorem"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Odczuwalna"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Wilgotność"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maksymalna temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Minimalna temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Rano"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noc"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Południe"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Ciśnienie"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Wschód słońca"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Zachód słońca"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Indeks UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Widoczność"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Wiatr"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "subskrybenci"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "wpisy"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktywni użytkownicy"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "komentarze"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "użytkownik"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "społeczność"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "punkty"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "tytuł"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "autor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Otwórz"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Zamknięty"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "odebrany"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nie znaleziono elementu"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Źródło"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Błąd wczytywania następnej strony"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Nieprawidłowe ustawienia, zmień swoje preferencje"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Nieprawidłowe ustawienia"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "błąd wyszukiwania"

#: searx/webutils.py:35
msgid "timeout"
msgstr "przekroczenie czasu"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "błąd przetwarzania"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "błąd protokołu HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "błąd sieci"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Błąd SSL: nie udało się zweryfikować certyfikatu"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "niespodziewana awaria"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "błąd HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "błąd połączenia HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "błąd serwera proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "za dużo zapytań"

#: searx/webutils.py:58
msgid "access denied"
msgstr "odmowa dostępu"

#: searx/webutils.py:59
msgid "server API error"
msgstr "błąd serwera API"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Zawieszone"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minut(y) temu"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} godzin(y), {minutes} minut(y) temu"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Wygeneruj różne wartości losowe"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Oblicz {func} dla argumentów"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Pokaż trasę na mapie .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (PRZESTARZAŁY)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Ten wpis został zastąpiony przez"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanał"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitrate"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "głosy"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "kliknięcia"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Język"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} cytowań od {firstCitationVelocityYear} do "
"{lastCitationVelocityYear} roku"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Nie można odczytać obrazu z tego adresu URL. Może być to spowodowane "
"nieobsługiwanym formatem pliku. TinEye obsługuje jedynie obrazy w "
"formatach JPEG, PNG, GIF, BMP, TIFF i WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Zdjęcie jest za proste by znaleźć wyniki TinEye wymaga prostego poziomu "
"szczegółów wizualnych aby poprawnie zidentyfikować wyniki."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Nie można pobrać obrazu."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Ocena książki"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Jakość pliku"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Czarna lista wyszukiwarki Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""
"Pomiń serwisy .onion, które znajdują się na czarnej liście wyszkukiwarki "
"Ahmia"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Kalkulator Prosty"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Obliczaj wyrażenia matematyczne za pomocą paska wyszukiwania"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Wtyczka hashująca"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Konwertuje tekst na różne skróty hash."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "wartość hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Wtyczka Hostnames"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Przepisywanie nazw hostów, usuwanie wyników lub nadawanie im priorytetów "
"na podstawie nazwy hosta"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Nadpisywanie DOI z otwartym dostępem"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Unikaj opłat za dostęp, przekierowując do otwartych wersji publikacji, "
"gdy są dostępne"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informacje o sobie"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Wyświetla Twój adres IP, jeśli zapytanie to „ip”, oraz Twój user agent, "
"jeśli zapytanie to „user-agent”."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Twoje IP to: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Twój agent użytkownika to: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Sprawdzenie wtyczki TOR"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Ten plugin sprawdza, czy adres wysyłający zapytanie jest węzłem "
"wyjściowym sieci Tor, i powiadamia użytkownika jeśli jest, tak jak "
"check.torproject.org ale z searxng."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nie można pobrać listy węzłów wyjściowych Tor z"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Używasz Tora i wygląda na to, że masz zewnętrzny adres IP"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Nie używasz Tora i masz zewnętrzny adres IP"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Usuwanie elementów śledzących z linków"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Usuń argumenty elementów śledzących ze zwróconego adresu URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Plugin do konwersji jednostek"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Zamieniaj jednostki"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Strona nie znaleziona"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Przejdź do %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "strona wyszukiwania"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Wpłać"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Preferencje"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Obsługiwane przez"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "Respektujący prywatność, otwarty metasilnik wyszukiwania"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Kod źródłowy"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Śledzenie błędów"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statystyki wyszukiwarki"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Publiczne instancje"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Polityka prywatności"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Skontaktuj się z właścicielem instancji"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Kliknij na szkło powiększające, aby wykonać wyszukiwanie"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Długość"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Wyświetlenia"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Autor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "buforowane"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Zgłoś nowy problem na GitHubie"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Sprawdź istniejące błędy dotyczące tego silnika na GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Potwierdzam, że nie ma istniejącego błędu dotyczącego napotkanego problemu"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Jeśli jest to instancja publiczna, podaj adres URL w zgłoszeniu "
"dotyczącego tego błędu"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Zgłoś nowy problem na Githubie, podając powyższe informacje"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Brak HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Zobacz dziennik błędów i zgłoś je"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang dla tej wyszukiwarki"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang dla jej kategorii"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediana"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Test sprawdzający zakończony niepowodzeniem: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Błędy:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Ogólne"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Domyślne kategorie"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interfejs użytkownika"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Prywatność"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Wyszukiwarki"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Obecnie używane wyszukiwarki"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Specjalne Zapytania"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Ciasteczka"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Liczba wyników"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informacje"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Do góry"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "poprzednia strona"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "następna strona"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Wyświetl stronę główną"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Wyszukaj..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "wyczyść"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "wyszukaj"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Obecnie nie ma dostępnych danych. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nazwa wyszukiwarki"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Wyniki"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Ilość wyników"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Czas odpowiedzi"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Niezawodność"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Suma"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Przetwarzanie"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Ostrzeżenia"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Błędy i wyjątki"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Wyjątek"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Wiadomość"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Odsetek"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametr"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nazwa pliku"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkcja"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kod"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Weryfikacja"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Test zakończony niepowodzeniem"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komentarz(e)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Przykłady"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Definicje"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonimy"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Odpowiedzi"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Pobierz wyniki"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Spróbuj wyszukać:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Wiadomości z silnika wyszukiwania"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL wyszukiwania"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Skopiowane"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopiuj"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Propozycje"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Język wyszukiwania"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Domyślny język"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Automatyczne wykrywanie"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Bezpieczne wyszukiwanie"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Bezkompromisowe"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Umiarkowane"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Wyłączone"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Zakres czasu"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "W każdej chwili"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "W ostatnim dniu"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "W ostatnim tygodniu"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "W ostatnim miesiącu"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "W ostatnim roku"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informacja!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "obecnie nie zdefiniowano żadnych ciasteczek."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Przepraszamy!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Nie znaleziono żadnych wyników. Możesz spróbować:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Nie ma więcej wyników. Możesz spróbować:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Odśwież stronę."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Wyszukaj innego zapytania albo wybierz inną kategorie (powyżej)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Zmień używaną wyszukiwarkę w ustawieniach:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Zmień instancję:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Wyszukaj inne zapytanie lub wybierz inną kategorię."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Wróć do poprzedniej strony za pomocą przycisku."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Pozwól"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Słowa kluczowe (pierwsze słowo w zapytaniu)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nazwa"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Opis"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "To jest lista modułów \"natychmiastowych odpowiedzi\" SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "To jest list wtyczek."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Autouzupełnienie"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Szukaj podczas pisania"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Wyśrodkowanie"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Pokazuje wyniki na środku strony (układ Oscara)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"To jest lista plików cookies i ich zawartości, które SearXNG przechowuje "
"na twoim komputerze."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Dzięki tej liście, możesz ocenić przejrzystość SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nazwa ciasteczka"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Wartość"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Wyszukaj adres URL aktualnie zapisanych preferencji"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Uwaga: określanie ustawień niestandardowych w adresie URL wyszukiwania "
"może zmniejszyć prywatność, przenosząc dane do klikniętych stron z "
"wyników."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL do przywrócenia twoich ustawień w innej przeglądarce"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Adres URL zawierający twoje preferencje/ustawienia. Ten Adres URL pozwala"
" na odzyskanie/przeniesienie swoich ustawień na inne urządzenie"

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Skopiuj preferowany hash"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Wprowadź skopiowany hash (Bez URL) aby go przywrócić"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Preferowany Hash"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Cyfrowy identyfikator obiektu (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Podsystem DOI z otwartym dostępem"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Wybierz usługę używaną przez DOI rewrite"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ta zakładka nie istnieje w interfejsie użytkownika, ale możesz wyszukiwać"
" w tych silnikach po jej !bangach."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Włącz wszystkie"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Wyłącz wszystkie"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Obsługuje wybrany język"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Waga"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maksymalny czas"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Pobieranie favikony"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Wyświetlanie faviconów obok wyników wyszukiwania"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Ustawienia te są przechowywane w ciasteczkach, co pozwala nam nie "
"przechowywać tych danych o Tobie."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Te ciasteczka służą wyłącznie twojej wygodzie, nie używamy ich do "
"śledzenia."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Zapisz"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Przywróć domyślne"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Powrót"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Skróty klawiszowe"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Podobne do Vima"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Nawiguj wyniki wyszukiwania używając skrótów klawiszowych (Wymaga "
"JavaScript). Wciśnij klawisz \"h\" na stronie głównej lub stronie wyników"
" aby uzyskać pomoc."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy zdjęć"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Przepuść zdjęcia przez serwer SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Nieskończone przewijanie"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Automatycznie ładuj następną stronę podczas przewijania do dolnej części "
"bieżącej strony"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "W jakim języku wolisz wyszukiwać?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Wybierz automatyczne wykrywanie aby SearXNG wykrywał język twojego "
"wyszukiwania."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Metoda HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Zmień sposób wysyłania formularzy"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Wyszukiwanie w tytule strony"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Gdy włączone, tytuł strony z wynikiem będzie zawierał twoje zapytanie. "
"Twoja przeglądarka może widzieć ten tytuł"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Wyniki w nowych kartach"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Otwórz hiperłącza wyników w nowych kartach przeglądarki"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtruj treści"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Szukaj po wybraniu kategorii"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Natychmiast wykonaj wyszukiwanie, jeśli wybrano kategorię. Wyłącz, aby "
"wybrać wiele kategorii"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Motyw"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Zmień wygląd SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Styl motywu"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Wybierz auto by używać ustawień przeglądarki"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Tokeny wyszukiwarek"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Tokeny dostępu do prywatnych wyszukiwarek"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Język interfejsu"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Zmień język układu"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formatowanie adresu URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Ładne"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Pełne"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Host"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Zmień formatowanie adresów URL wyników"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repozytorium"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "pokaż media"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "ukryj media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ta strona nie podała żadnego opisu."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Rozmiar pliku"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Data"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Typ"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Rozdzielczość"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Wyszukiwarka"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Pokaż źródło"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adres"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "pokaż mapę"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "ukryj mapę"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Wersja"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Zarządca"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Zaktualizowany o"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tagi"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularność"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licencja"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Strona główna projektu"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Data publikacji"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Gazeta"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redaktor"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Wydawca"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "Identyfikator cyfrowy"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "hiperłącze magnetyczne"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "plik torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Udostępniający"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Pobierający"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Liczba plików"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "pokaż wideo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ukryj wideo"

#~ msgid "request exception"
#~ msgstr "wyjątek w żądaniu"

#~ msgid "Engine time (sec)"
#~ msgstr "Czas wyszukiwania (sek)"

#~ msgid "Page loads (sec)"
#~ msgstr "Ładowanie strony (sek)"

#~ msgid "Errors"
#~ msgstr "Błędy"

#~ msgid "{title}&nbsp;(OBSOLETE)"
#~ msgstr "{title}&nbsp;(PRZESTARZAŁE)"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Nadpisuj hiperłącza HTTP na HTTPS, jeśli to możliwe"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Wyniki są domyślnie otwierane w tym "
#~ "samym oknie. Ta wtyczka zastępuje "
#~ "domyślne zachowanie w celu otwarcia "
#~ "hiperłączy w nowych kartach/oknach. (Wymagany"
#~ " Javascript)"

#~ msgid "Color"
#~ msgstr "Kolor"

#~ msgid "Blue (default)"
#~ msgstr "Niebieski (domyślny)"

#~ msgid "Violet"
#~ msgstr "Fioletowy"

#~ msgid "Green"
#~ msgstr "Zielony"

#~ msgid "Cyan"
#~ msgstr "Turkusowy"

#~ msgid "Orange"
#~ msgstr "Pomarańczowy"

#~ msgid "Red"
#~ msgstr "Czerwony"

#~ msgid "Category"
#~ msgstr "Kategoria"

#~ msgid "Block"
#~ msgstr "Blokuj"

#~ msgid "original context"
#~ msgstr "oryginalny kontekst"

#~ msgid "Plugins"
#~ msgstr "Wtyczki"

#~ msgid "Answerers"
#~ msgstr "Respondenci"

#~ msgid "Avg. time"
#~ msgstr "Śr. czas"

#~ msgid "show details"
#~ msgstr "pokaż szczegóły"

#~ msgid "hide details"
#~ msgstr "ukryj szczegóły"

#~ msgid "Load more..."
#~ msgstr "Załaduj więcej..."

#~ msgid "Change searx layout"
#~ msgstr "Zmień układ searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Przesyłanie wyników obrazów poprzez proxy searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Oto lista modułów natychmiastowych odpowiedzi w searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Oto lista ciasteczek i ich wartości, "
#~ "które searx zapisuje na Twoim "
#~ "komputerze."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Za pomocą tej listy możesz ocenić przezroczystość searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Wygląda na to, że po raz pierwszy używasz searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Spróbuj ponownie później lub znajdź inną instancję searx."

#~ msgid "Themes"
#~ msgstr "Motywy"

#~ msgid "Reliablity"
#~ msgstr "Niezawodność"

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metoda"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Zaawansowane ustawienia"

#~ msgid "Close"
#~ msgstr "Zamknij"

#~ msgid "Language"
#~ msgstr "Język"

#~ msgid "broken"
#~ msgstr "zepsute"

#~ msgid "supported"
#~ msgstr "wspierane"

#~ msgid "not supported"
#~ msgstr "niewspierane"

#~ msgid "about"
#~ msgstr "O searx"

#~ msgid "Avg."
#~ msgstr "Śr."

#~ msgid "User Interface"
#~ msgstr "Interfejs Użytkownika"

#~ msgid "Choose style for this theme"
#~ msgstr "Wybierz styl dla tego motywu"

#~ msgid "Style"
#~ msgstr "Styl"

#~ msgid "Show advanced settings"
#~ msgstr "Pokaż ustawienia zaawansowane"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Zawsze pokazuj panel ustawień zaawansowanych na stronie głównej"

#~ msgid "Allow all"
#~ msgstr "Zezwól na wszystkie"

#~ msgid "Disable all"
#~ msgstr "Wyłącz wszystkie"

#~ msgid "Selected language"
#~ msgstr "Wybrany język"

#~ msgid "Query"
#~ msgstr "Zapytanie"

#~ msgid "save"
#~ msgstr "zapisz"

#~ msgid "back"
#~ msgstr "z powrotem"

#~ msgid "Links"
#~ msgstr "Hiperłącza"

#~ msgid "RSS subscription"
#~ msgstr "Subskrypcja RSS"

#~ msgid "Search results"
#~ msgstr "Wyniki wyszukiwania"

#~ msgid "next page"
#~ msgstr "następna strona"

#~ msgid "previous page"
#~ msgstr "poprzednia strona"

#~ msgid "Start search"
#~ msgstr "Rozpocznij wyszukiwanie"

#~ msgid "Clear search"
#~ msgstr "Wyczyść wyszukiwanie"

#~ msgid "Clear"
#~ msgstr "Wyczyść"

#~ msgid "stats"
#~ msgstr "statystyki"

#~ msgid "Heads up!"
#~ msgstr "Moment!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Wygląda na to, że używasz SearXNG po raz pierwszy."

#~ msgid "Well done!"
#~ msgstr "Dobra robota!"

#~ msgid "Settings saved successfully."
#~ msgstr "Ustawienia zostały pomyślnie zapisane."

#~ msgid "Oh snap!"
#~ msgstr "O rany!"

#~ msgid "Something went wrong."
#~ msgstr "Coś poszło nie tak."

#~ msgid "Date"
#~ msgstr "Data"

#~ msgid "Type"
#~ msgstr "Typ"

#~ msgid "Get image"
#~ msgstr "Pobierz obraz"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "preferencje"

#~ msgid "Scores per result"
#~ msgstr "Wyniki na rezultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "szanująca prywatność, hackowalna wyszukiwarka metasearch"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Streszczenie nie jest dostępne dla tej publikacji."

#~ msgid "Self Informations"
#~ msgstr "Informacje o sobie"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Zmień sposób przesyłania formularzy, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">dowiedz się więcej o "
#~ "metodach HTTP</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Ten plugin sprawdza, czy adres "
#~ "wysyłający zapytanie jest węzłem wyjściowym"
#~ " sieci Tor, i powiadamia użytkownika "
#~ "jeśli jest, tak jak check.torproject.org "
#~ "ale z searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Lista węzłów wyjsciowych sieci Tor "
#~ "(https://check.torproject.org/exit-addresses) jest "
#~ "nieosiągalna."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Używasz sieci TOR. Twoje IP widoczne z zewnątrz to {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Nie używasz TOR. Twój adres IP to: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Wykryj automatycznie język wyszukiwania"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Automatycznie wykryj język wyszukiwania i przełącz się na niego."

#~ msgid "others"
#~ msgstr "inne"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Wyszukiwarki z tej zakładki nie pokazują"
#~ " się w wynikach wyszukiwania, ale "
#~ "możesz ich używać przez bangs."

#~ msgid "Shortcut"
#~ msgstr "Skrót"

#~ msgid "!bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Ta zakładka nie istnieje w interfejsie"
#~ " użytkownika, ale możesz wyszukiwać w "
#~ "tych silnikach po jej !bangach."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Wyszukiwarki nie mogą pobrać wyników."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Proszę spróbować później albo znaleźć inną instancję SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Przekierowanie do otwartych wersji publikacji,"
#~ " gdy są dostępne (wymagana wtyczka)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Zmień sposób wysyłania formularzy, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">więcej o sposobach "
#~ "wysyłania</a>"

#~ msgid "On"
#~ msgstr "Włączone"

#~ msgid "Off"
#~ msgstr "Wyłączone"

#~ msgid "Enabled"
#~ msgstr "Włączone"

#~ msgid "Disabled"
#~ msgstr "Wyłączone"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Wykonaj wyszukiwanie natychmiast po wybraniu"
#~ " kategorii. Wyłącz, aby wybrać wiele "
#~ "kategorii. (Wymagany Javascript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Skróty podobne do Vima"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Poruszaj się po wynikach wyszukiwania za"
#~ " pomocą skrótów podobnych do Vima "
#~ "(wymagany Javascript). Naciśnij klawisz \"h\""
#~ " na stronie głównej lub stronie "
#~ "wyników, aby uzyskać pomoc."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "nie znaleźliśmy żadnych wyników. Użyj "
#~ "innego zapytania lub wyszukaj więcej "
#~ "kategorii."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Przepisz nazwy hostów w wynikach lub "
#~ "usuń wyniki na podstawie nazw hostów"

#~ msgid "Bytes"
#~ msgstr "Bajtów"

#~ msgid "kiB"
#~ msgstr "KiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Zastąp nazwę hosta"

#~ msgid "Error!"
#~ msgstr "Błąd!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Wyszukiwarki nie mogą pobrać wyników"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Zgłoś nowy problem na GitHubie"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generator wartości losowych"

#~ msgid "Statistics functions"
#~ msgstr "Funkcje statystyczne"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Oblicz {functions} argumentów"

#~ msgid "Get directions"
#~ msgstr "Pokaż wskazówki"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Wyświetla Twój adres IP, jeśli zapytanie"
#~ " to \"ip\", i Twojego agenta "
#~ "użytkownika, jeśli zapytanie zawiera \"user"
#~ " agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Nie można pobrać listy węzłów "
#~ "wyjściowych Tora z: https://check.torproject.org"
#~ "/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Używasz Tora i wygląda na to, że"
#~ " masz ten zewnętrzny adres IP: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Nie używasz Tora. Posiadasz ten zewnętrzny adres IP: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Słowa kluczowe"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Określanie własnych ustawień w adresie "
#~ "URL preferencji może służyć do "
#~ "synchronizowania preferencji między urządzeniami."

#~ msgid "proxied"
#~ msgstr "przesłane poprzez proxy"

