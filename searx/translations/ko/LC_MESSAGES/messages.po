# Korean translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON>@shwa.space>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-02-06 15:54+0000\n"
"Last-Translator: curtwheeler "
"<<EMAIL>>\n"
"Language: ko\n"
"Language-Team: Korean "
"<https://translate.codeberg.org/projects/searxng/searxng/ko/>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "미분류"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "기타"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "파일"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "일반"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "음악"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "소셜 미디어"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "이미지"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "비디오"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "라디오"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "뉴스"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "지도"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "어니언"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "과학"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "앱"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "사전"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "가사"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "패키지"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "Q&A"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "리포지토리"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "소프트웨어 위키"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "웹"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "과학 출판물"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "자동"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "라이트"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "다크"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "블랙"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "가동 시간"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "정보"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "평균 온도."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "운량"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "상태"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "현재 상태"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "저녁"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "체감"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "습도"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "최대 기온"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "최저 기온"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "아침"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "밤"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "정오"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "기압"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "일출"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "일몰"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "기온"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "자외선 지수"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "가시도"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "풍속"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "구독자"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "글"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "활동 사용자"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "댓글"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "사용자"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "커뮤니티"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "점수"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "제목"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "작성자"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "열기"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "닫힘"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "응답"

#: searx/webapp.py:292
msgid "No item found"
msgstr "검색 결과가 없습니다"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "소스"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "다음 페이지를 로드하는 동안 오류가 발생했습니다"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "잘못된 설정입니다, 설정을 수정하세요"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "잘못된 설정"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "검색 오류"

#: searx/webutils.py:35
msgid "timeout"
msgstr "대기 시간"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "구문 분석 오류"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP 프로토콜 오류"

#: searx/webutils.py:38
msgid "network error"
msgstr "네트워크 오류"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL 에러: 인증서 무효"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "예상치 못한 충돌"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP 오류"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP 연결 오류"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "프록시 오류"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "너무 많은 요청"

#: searx/webutils.py:58
msgid "access denied"
msgstr "액세스 거부"

#: searx/webutils.py:59
msgid "server API error"
msgstr "서버 API 오류"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "중단됨"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes}분 전"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours}시간 {minutes}분 전"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "다른 난수 생성"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "인수들의 {func}를 계산하세요"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "지도에서 경로 표시 .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (사용되지 않음)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "이 항목은 다음으로 대체되었습니다"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "채널"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "비트 레이트"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "표"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "클릭"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "언어"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{firstCitationVelocityYear}년부터 {lastCitationVelocityYear}년까지의 "
"{numCitations}회 인용"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"이미지 주소를 읽을 수 없습니다. 파일 포맷을 지원하지 않아 발생하는 문제일 수도 있습니다. TinEye는 JPEG, PNG, "
"GIF, BMP, TIFF 그리고 WebP 이미지만 지원합니다."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"이미지가 너무 단순해 일치하는 항목을 찾을 수 없습니다. TinEye가 일치하는 이미지를 성공적으로 식별하기 위해선 최소 수준의 "
"시각적 정보가 필요합니다;."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "다운로드할 수 없는 이미지입니다."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "책 평점"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "파일 품질"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "검색바를 통해 수학연산 계산하기"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "해시 플러그인"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "문자열을 다른 해시 다이제스트 값으로 변환합니다."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "해시 다이제스트"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "호스트 이름 플러그인"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "검색 결과에서 이 호스트 이름을 기준으로 삭제 또는 우선순위에 따라 재작성하기"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "오픈 액세스 DOI 재작성"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr "가능한 경우 공개 액세스 버전의 출판물로 리디렉션하여 페이월 방지"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "본인 정보"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr "쿼리가 'ip'이면 사용자의 IP를 표시하고, 'user-agent'이면 사용자 에이전트를 표시합니다."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "당신의 IP는: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "당신의 사용자 에이전트는: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor 검사 플러그인"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"이 플러그인은 요청의 주소가 토르 출구 노드 인지 확인하고 사용자에게 check.torproject.org와 같이 "
"SearchXNG의 주소인지 알려줍니다."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Tor 출구 노드 목록을 다운로드할 수 없습니다"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Tor를 사용 중이며 외부 IP 주소를 가진 것으로 보입니다"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Tor를 사용하고 있지 않으며 외부 IP 주소를 가지고 있습니다"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "추적기 URL 제거기"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "반환된 URL에서 추적기 매개변수 제거"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "단위 환산"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "페이지를 찾을 수 없음"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "%(search_page)s로 이동합니다."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "검색 페이지"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "기부"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "설정"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Powered by"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "개인정보를 존중하는 개방형 메타 검색 엔진"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "소스 코드"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "이슈 트래커"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "검색 엔진 상태"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "공개 인스턴스"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "개인 정보 정책"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "인스턴스 관리자에게 문의"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "돋보기를 클릭하여 검색을 시작하세요"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "길이"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "조회수"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "저자"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "캐시"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "GitHub 에서 새 이슈 시작하기"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "GitHub 에서 이 엔진에 대한 기존 버그를 확인하세요"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "제가 격은 버그에 대한 이슈가 존재하지 않은 것을 확인했습니다"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "공개 인스턴스라면 버그 보고서에 URL을 적어주세요"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Github에 위 정보를 포함한 새 이슈 작성하기"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "HTTPS 없음"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "오류 로그 보기 및 버그 보고서 제출"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "이 검색 엔진을 사용하기 위해 !bang"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "카테고리 사용을 위해 !bang"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "중앙값"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "실패한 검사기 테스트: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "오류:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "일반"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "기본 카테고리"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "사용자 인터페이스"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "개인정보 보호"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "검색엔진"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "현재 사용중인 검색 엔진"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "특수 쿼리"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "쿠키"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "결과 수"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "정보"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "위로 돌아가기"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "이전 페이지"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "다음 페이지"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "첫 페이지 표시"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "다음을 검색..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "지우기"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "검색"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "데이터가 존재하지 않습니다. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "검색엔진 이름"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "점수"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "결과 개수"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "응답시간"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "신뢰성"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "합계"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "처리"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "경고"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "에러와 예외"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "예외"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "메시지"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "백분율"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "매개변수"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "파일명"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "함수"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "코드"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "검사 프로그램"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "테스트 실패"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "댓글"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "예시"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "정의"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "동의어"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "답변"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "검색결과 다운로드"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "다음을 검색 해보세요:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "검색 엔진에서 발생한 메시지"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "초단위"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "검색 URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "복사됨"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "복사하기"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "제안"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "검색 언어"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "기본 언어"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "자동 감지"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "세이프서치"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "엄격"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "보통"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "없음"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "기간"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "모든 날짜"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "지난 1일"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "지난 1주"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "지난 1개월"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "지난 1년"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "알림!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "현재 정의된 쿠기가 없습니다."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "죄송합니다!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "검색 결과 없음. 다음을 시도해 보세요:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "더 이상 결과가 없습니다. 다음을 시도하십시오:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "페이지를 새로고침 하세요."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "다른 검색어를 사용하거나 위에서 다른 카테고리를 선택해 주세요."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "설정에서 사용할 검색엔진 바꾸기:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "다른 인스터스를 사용해 주세요:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "새 검색어를 찾아보거나 다른 분류를 선택하십시오."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "이전 페이지 버튼으로 이전 페이지로 돌아가십시오."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "허용"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "키워드 (쿼리의 첫 번째 단어)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "이름"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "설명"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "SearXNG의 즉각응답 모듈 목록입니다."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "플러그인 목록입니다."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "자동 완성"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "입력하는 대로 찾으세요"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "중앙 정렬"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "페이지 중앙에 결과 표시하기 (Oscar 레이아웃)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "SearXNG이 컴퓨터에 저장하는 쿠키 목록입니다."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "이 목록으로 SearXNG의 투명성을 판단할 수 있습니다."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "쿠키 이름"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "값"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "현재 설정이 적용된 검색 URL"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr "검색 URL에 사용자 설정을 명시하면 결과 사이트에 데이터가 유출되어 사생활 보호가 약해질 수 있습니다."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "다른 브라우저에서 설정을 복구하기 위한 URL"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "설정 해시 복사"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "(URL 제외하여) 설정 해시를 복사해 복원"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "설정 해시"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "디지털 객체 식별자 (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "오픈 엑세스 DOI 리졸버"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "DOI 재작성에 사용된 서비스 선택"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr "이 탭은 유저 인터페이스에 나타나지 않지만, !bang을 사용하여 이 검색엔진으로 검색할 수 있습니다."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "모두 적용"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "모두 해제"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "선택 언어 지원함"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "무게"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "최대시간"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr "설정은 쿠키에 저장 되기 때문에 서버측에서 설정 관련 사용자 정보를 저장하는 것은 불가능합니다."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr "이 쿠키는 오직 편의를 위해 쓰이며, 추적에 이용하지 않습니다."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "저장"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "기본값 복원"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "뒤로"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "단축키"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "vim 형식"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"단축키를 이용해 검색결과를 탐색합니다 (Javascript 필요). 검색결과 페이지나 홈페이지에서 'h'를 눌러 도움말을 볼수 "
"있습니다."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "이미지 우회"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "SearXNG를 통해 이미지 결과 우회"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "무한 스크롤"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "현재 페이지 하단까지 스크롤 했을 때 자동으로 다음 페이지 불러오기"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "어떤 언어로 검색하시겠습니까?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "SearXNG가 검색 언어를 자동감지 할 수 있도록 자동감지를 선택해 주세요."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP 메소드"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "요청이 전송되는 방법을 변경합니다"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "페이지 제목에 검색어 표시"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr "활성화 할 경우, 결과 페이지 제목에 검색어가 표시 됩니다. 페이지 제목은 브라우저가 기록할 수 있습니다"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "새 탭에서 결과 열기"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "새 탭에서 결과 링크를 엽니다"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "콘텐츠 필터링"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "특정 카테고리 검색"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr "카테고리 선택후 즉시 검색을 합니다. 한개 이상의 카테고리를 선택하면 비활성화됩니다"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "테마"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "SearXNG 레이아웃 변경"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "테마 스타일"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "'자동'을 선택하면 브라우저 기본설정에 맞춥니다"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "엔진토큰"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "사유 검색 엔진 엑세스 토큰"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "인터페이스 언어"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "레이아웃 언어 변경"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "리포지토리"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "미디어 표시"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "미디어 숨기기"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "사이트에서 소개를 제공하지 않았습니다."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "파일 크기"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "날짜"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "분류"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "해상도"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "포멧"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "검색엔진"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "소스 보기"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "주소"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "지도 표시"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "지도 숨기기"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "버전"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "관리자"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "갱신 시각"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "태그"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "인기"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "라이선스"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "프로젝트"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "프로젝트 홈페이지"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "발행일"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "발행처"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "편집자"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "발행자"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "마그넷 링크"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "토렌트 파일"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "시드"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "리치"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "파일 개수"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "비디오 표시"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "비디오 숨기기"

#~ msgid "Advanced settings"
#~ msgstr "고급 설정"

#~ msgid "Close"
#~ msgstr "닫기"

#~ msgid "Language"
#~ msgstr "언어/Language"

#~ msgid "broken"
#~ msgstr "끊김"

#~ msgid "supported"
#~ msgstr "지원됨"

#~ msgid "not supported"
#~ msgstr "지원되지 않음"

#~ msgid "about"
#~ msgstr "자세히"

#~ msgid "Avg."
#~ msgstr "평균"

#~ msgid "User Interface"
#~ msgstr "사용자 인터페이스"

#~ msgid "Choose style for this theme"
#~ msgstr "이 테마에 대한 스타일 선택"

#~ msgid "Style"
#~ msgstr "스타일"

#~ msgid "Show advanced settings"
#~ msgstr "고급 설정 열기"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "항상 시작 페이지에서 고급설정 패널 보기"

#~ msgid "Allow all"
#~ msgstr "모두 허용"

#~ msgid "Disable all"
#~ msgstr "모두 거부"

#~ msgid "Selected language"
#~ msgstr "선택언어 지원"

#~ msgid "Query"
#~ msgstr "검색어"

#~ msgid "save"
#~ msgstr "저장"

#~ msgid "back"
#~ msgstr "뒤로"

#~ msgid "Links"
#~ msgstr "링크"

#~ msgid "RSS subscription"
#~ msgstr "RSS 구독"

#~ msgid "Search results"
#~ msgstr "검색 결과"

#~ msgid "next page"
#~ msgstr "다음 페이지"

#~ msgid "previous page"
#~ msgstr "이전 페이지"

#~ msgid "Start search"
#~ msgstr "검색 시작"

#~ msgid "Clear search"
#~ msgstr "검색 지우기"

#~ msgid "Clear"
#~ msgstr "지우기"

#~ msgid "stats"
#~ msgstr "통계"

#~ msgid "Heads up!"
#~ msgstr "조심하세요!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "SearXNG를 처음 쓰시는 것 같군요."

#~ msgid "Well done!"
#~ msgstr "잘 하셨습니다!"

#~ msgid "Settings saved successfully."
#~ msgstr "설정을 성공적으로 저장했습니다."

#~ msgid "Oh snap!"
#~ msgstr "이런!"

#~ msgid "Something went wrong."
#~ msgstr "문제가 발생했습니다."

#~ msgid "Date"
#~ msgstr "날짜"

#~ msgid "Type"
#~ msgstr "형식"

#~ msgid "Get image"
#~ msgstr "이미지 가져오기"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "설정"

#~ msgid "Scores per result"
#~ msgstr "검색결과당 점수"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "개인 정보를 존중하는 맞춤형 메타 검색 엔진"

#~ msgid "No abstract is available for this publication."
#~ msgstr "이 출판물에 대한 초록이 없습니다."

#~ msgid "Self Informations"
#~ msgstr "본인 정보"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "폼 제출 방법 변경,<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">요청 메소드에 대해 더 알아보기</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "이 플러그인은 요청한 주소가 TOR 출구 노드인지 "
#~ "확인하고 사용자에게 알려줍니다, check.torproject.org 와 "
#~ "비슷하지만 searxng 에서 제공됩니다."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr "TOR 출구 노드 목록(https://check.torproject.org/exit-addresses)에 연결할 수 없습니다."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "TOR 를 사용하고 있습니다. 당신의 아이피 주소는 다음과 같습니다: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "TOR 를 사용하고 있지 않습니다. 당신의 아이피 주소는 다음과 같습니다: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "기타"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr "이 탭은 검색 결과에서 표시 되지 않으나, 느낌표을 입력하여 아래의 엔진으로 검색할 수 있습니다."

#~ msgid "Shortcut"
#~ msgstr "단축 키 워드"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "검색결과를 가져올 수 없습니다."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "다음에 시도하거나 다른 SearXNG 객체를 이용해주세요."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr "가능 하면 오픈 엑세스 출판물로 넘겨주기 (플러그인 필요함)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "폼 제출 방법 변경,<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">요청 메소드에 대해 더 알아보기</a>"

#~ msgid "On"
#~ msgstr "사용"

#~ msgid "Off"
#~ msgstr "사용안함"

#~ msgid "Enabled"
#~ msgstr "활성화됨"

#~ msgid "Disabled"
#~ msgstr "비활성화됨"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr "카테고리가 선택되면 즉시 검색을 수행합니다. 여러 카테고리를 선택하려면 비활성화합니다. (JavaScript 필요)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim 스타일 단축키"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Vim 스타일 단축키로 검색 결과를 찾아보세요(JavaScript "
#~ "필요). 도움말을 보려면 메인 페이지 또는 결과 "
#~ "페이지에서 \"h\" 키를 누르십시오."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr "검색결과를 찾을 수 없습니다. 다른 검색어로 검색하거나 검색 범주를 추가해주세요."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "결과의 호스트 이름을 재작성하거나 호스트 이름에 따라 결과를 삭제합니다"

#~ msgid "Bytes"
#~ msgstr "바이트"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "호스트 이름 변경"

#~ msgid "Error!"
#~ msgstr "오류!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "결과를 가져올 수 없습니다"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "GitHub 에서 새 이슈 시작하기"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "난수 생성기"

#~ msgid "Statistics functions"
#~ msgstr "통계 기능"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "{functions} 매개변수 계산"

#~ msgid "Get directions"
#~ msgstr "길찾기"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "쿼리가 \"ip\"인 경우 사용자의 IP를 표시하고 쿼리에"
#~ " \"user agent\"가 포함된 경우 사용자 에이전트를 "
#~ "표시합니다."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "https://check.torproject.org/exit-addresses 에서 토르"
#~ " 출구 노드를 다운로드 받는데 실패하였습니다"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Tor를 사용하고 있고 외부 IP 주소는 {ip_address} 입니다"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Tor를 사용하고 있지 않고 외부 IP 주소가 {ip_address}인 것 같습니다"

#~ msgid "Keywords"
#~ msgstr "키워드"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr "특정 설정이 들어간 URL은 장치 간에 설정을 동기화 하는데 사용할 수 있습니다."

#~ msgid "proxied"
#~ msgstr "프록시됨"

