# Bangla translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# Musfiquer<PERSON>hman <<EMAIL>>,
# 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-03-06 09:54+0000\n"
"Last-Translator: MonsoonFire <<EMAIL>>\n"
"Language: bn\n"
"Language-Team: Bengali "
"<https://translate.codeberg.org/projects/searxng/searxng/bn/>\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "কোনরকম সাবগ্রুপিং ছাড়া"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "অন্যান্য"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ফাইল"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "সাধারণ"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "গান"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "সামাজিক মাধ্যম"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "ছবি"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "ভিডিও"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "বেতার"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "দূরদর্শন"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "তথ্য প্রযুক্তি"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "খবর"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "মানচিত্র"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "অনিয়ন"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "বিজ্ঞান"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "অ্যাপ"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "অভিধান"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "লিরিক্স"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "প্যাকেজ"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "প্রশ্নোত্তর"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "ভাণ্ডার"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "সফটওয়্যার উইকিস"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "ওয়েব"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "বৈজ্ঞানিক প্রকাশনা"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "স্বয়ং"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "সাদা"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "অন্ধকার"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "কালো"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "চলনকাল"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "সম্বন্ধে"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "গড় তাপমাত্রা"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "মেঘলা"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "অবস্থা"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "বর্তমান অবস্থা"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "সন্ধ্যা"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "অনুভব হয়"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "আদ্রতা"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "সর্বোচ্চ তাপমাত্রা"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "সর্বনিন্ম তাপমাত্রা"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "সকাল"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "রাত"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "দুপুর"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "চাপ"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "সূর্যোদয়"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "সূর্যাস্ত"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "তাপমাত্রা"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "ইউ ভি ইনডেক্স"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "দৃশ্যগোচর"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "বায়ু"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "সাবস্ক্রাইবারস"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "পোস্টস"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "সক্রিয় ইউজারস"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "কমেন্ট"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "ইউজার"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "কমিউনিটি"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "পয়েন্টস"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "শিরোনাম"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "লেখক"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "খুলো"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "বন্ধ"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "উত্তরকৃত"

#: searx/webapp.py:292
msgid "No item found"
msgstr "কোন আইটেম পাওয়া যায়নি"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "উৎস"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "পরবর্তী পৃষ্ঠাটি লোড করায় ত্রুটি দেখা যাচ্ছে"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "অকেজো সেটিংস, অনুগ্রহ করে আপনার পছন্দগুলি সম্পাদনা করুন"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "অকেজো সেটিংস"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "সার্চ ত্রুটি"

#: searx/webutils.py:35
msgid "timeout"
msgstr "সময় শেষ"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "পার্স ত্রুটি"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP প্রোটোকল ত্রুটি"

#: searx/webutils.py:38
msgid "network error"
msgstr "নেটওয়ার্ক ত্রুটি"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL ত্রুটি: সার্টিফিকেট বৈধতা ব্যর্থ হয়েছে৷"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "অপ্রত্যাশিত ক্র্যাশ"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP ত্রুটি"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP সংযোগ ত্রুটি"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "প্রক্সি ত্রুটি"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "ক্যাপচা"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "অনেক বেশি অনুরোধ"

#: searx/webutils.py:58
msgid "access denied"
msgstr "প্রবেশ অগ্রাহ্য করা হল"

#: searx/webutils.py:59
msgid "server API error"
msgstr "সার্ভার API ত্রুটি"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "স্থগিত"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} মিনিট আগে"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} ঘণ্টা, {minutes} মিনিট আগে"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "বিভিন্ন এলোমেলো মান তৈরি করুন"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "{func} এই আদেশ কম্পিউট করো"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "মানচিত্রে সেরা রাস্তা দেখাও .।"

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (অচল)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "এই এনট্রিটি দ্বারা বাতিল করা হয়েছে৷"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "চ্যানেল"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "বিটরেট"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "ভোট"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "ক্লিক সংখ্যা"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "ভাষা"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} উদ্ধৃতি সাল {firstCitationVelocityYear} থেকে "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"ছবির url টি পড়তে পারা যাচ্ছে না । এটি হতে পারে ফাইল ফরম্যাট এর পড়তে না "
"পারার জন্যে। TinEye কেবল JPEG, PNG, GIF, BMP, TIFF আর WebP ফরম্যাট কে "
"পড়তে পারে।"

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"এই ছবিটি খুবই সাধারণ হওয়ায় কোন মিল পাওয়া যাচ্ছে না। TinEye এর একটু "
"চাক্ষুষ বিস্তর প্রয়োজন সফল ভাবে মিল পাওয়ার জন্যে ।"

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "ছবিটি ডাউনলোড করা যায়নি ।"

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "বই পর্যালোচনা"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "নথি মান"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "সার্চ বারের মাধমে গানিতিক সমীকরণ সমাধান করুন"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "হ্যাশ প্লাগিন"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "স্ট্রিংগুলিকে বিভিন্ন হ্যাশ ডাইজেস্টে রূপান্তর করে।"

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "হ্যাশ ডাইজেস্ট"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "হোস্টনেম প্লাগিন"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "হোস্টনাম পুনর্লিখন করো, ফলাফল অপসারণ করো বা হোস্টনামের ভিত্তিতে সাজাও"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "পুনর্লিখিত DOI উন্মুক্ত প্রবেশ"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Paywall এড়িয়ে চলতে প্রকাশন গুলির open-access সংস্করণে রিডাইরেক্ট করুন "
"উপলব্ধ থাকলে"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "নিজ তথ্য"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"তোমার আইপি দেখাও যদি তোমার অনুসন্ধান \"ip\" হয় ও তোমার ইউজার এজেন্ট দেখাও"
" যদি অনুসন্ধান \"user-agent\" হয়।"

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "তোমার আইপি: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "তোমার ইউজার-এজেন্ট: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "টর চেক প্লাগইন"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"এই প্লাগইনটি চেক করে যে অনুরোধের ঠিকানাটি একটি TOR প্রস্থান নোড কিনা এবং "
"ব্যবহারকারীকে জানিয়ে দেয় যে এটি কিনা, যেমন check.torproject.org কিন্তু "
"SearXNG থেকে।"

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "টর এক্সিট-নোডের তালিকা নামানো সম্ভব হয়নি এখান থেকে"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "তুমি টর ব্যবহার করছো এবং সম্ভবত বাইরের আইপি ঠিকানা তোমার"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "তুমি টর ব্যবহার করছো না এবং বাইরের আইপি ঠিকানা তোমার"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "ট্র্যাকার URL রিমুভার"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "ফিরে আসা URL থেকে ট্র্যাকার আর্গুমেন্টগুলি সরান৷"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "এক একক থেকে অন্য এককে রুপান্তর"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "পৃষ্ঠা খুঁজে পাওয়া যায়নি"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "%(search_page)s এ যান৷"

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "অনুসন্ধান পৃষ্ঠা"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "দান করুন"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "পছন্দসমূহ"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "দ্বারা চালিত"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "একটি গোপনীয়তা-সম্মানকারী, খোলা মেটাসার্চ ইঞ্জিন"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "সোর্স কোড"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "সমস্যা অনুসরণ"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "ইঞ্জিন পরিসংখ্যান"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "পাবলিক ইন্সট্যান্স"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "গোপনীয়তা নীতি"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "ইন্সট্যান্স রক্ষণাবেক্ষণকারীর সাথে যোগাযোগ করুন"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "অনুসন্ধান করতে ম্যাগনিফায়ার আইকনে ক্লিক করুন"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "দৈর্ঘ্য"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "ভিউ"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "লেখক"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "ক্যাশকৃত"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "GitHub এ একটি নতুন সমস্যা জমা দেওয়া শুরু করুন"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "GitHub এ এই ইঞ্জিন সম্পর্কে বিদ্যমান বাগগুলির জন্য দয়া করে পরীক্ষা করুন"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr ""
"আমি নিশ্চিত করছি যে আমি যে সমস্যার সম্মুখীন হচ্ছি সে সম্পর্কে কোন "
"বিদ্যমান বাগ নেই"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"যদি এটি একটি পাবলিক ইন্সট্যান্স হয়, তবে অনুগ্রহ করে বাগ রিপোর্টে URL টি "
"উল্লেখ করুন"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "উপরের তথ্য সহ Github এ একটি নতুন সমস্যা জমা দিন"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "HTTPS নেই"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "ত্রুটির লগগুলি দেখুন এবং একটি বাগ রিপোর্ট জমা দিন"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang এই ইঞ্জিনের জন্য"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang এই বিভাগের"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "মধ্যবর্তী"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "পরীক্ষা(গুলি) ব্যর্থ: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "ত্রুটি:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "সাধারণ"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "ডিফল্ট বিভাগ"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "ব‍্যবহারকারীর সম্মুখে প্রদর্শিত"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "গোপনীয়তা"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "ইঞ্জিন"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "বর্তমানে ব্যবহৃত সার্চ ইঞ্জিন"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "বিশেষ প্রশ্ন"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "কুকি"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "ফলাফলের সংখ্যা"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "তথ্য"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "উপরে ফিরে যান"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "পূর্ববর্তী পেইজ"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "পরবর্তী পেইজ"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "প্রথম পৃষ্ঠা দেখান"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "সার্চ করুন..।"

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "পরিষ্কার"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "অনুসন্ধান"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "বর্তমানে কোন তথ্য পাওয়া যায়নি."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "ইঞ্জিনের নাম"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "স্কোর"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "ফলাফল গণনা"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "প্রতিক্রিয়া সময়"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "নির্ভরযোগ্যতা"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "মোট"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "এইচটিটিপি"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "প্রক্রিয়ারত"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "সতর্কতা"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "ত্রুটি এবং ব্যতিক্রম"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "ব্যতিক্রম"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "বার্তা"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "শতাংশ"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "প্যারামিটার"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "ফাইলের নাম"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "ফাংশন"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "কোড"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "পরীক্ষক"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "পরীক্ষায় ব্যর্থ"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "মন্তব্য"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "উদাহরণ"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "সংজ্ঞা"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "সমার্থক শব্দ"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "উত্তর"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "ডাউনলোডগুলোর ফলাফল"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "এটি খোঁজার চেষ্টা করুন:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "সার্চ ইঞ্জিন থেকে বার্তা"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "সেকেন্ড"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "সার্চ ইউআরএল"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "অনুলিপি করা হয়েছে"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "অনুলিপি"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "প্রস্তাবিত"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "সার্চের ভাষা"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "পূর্বনির্ধারিত ভাষা"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "স্বয়ং সনাক্ত"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "নিরাপদ সার্চ"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "কঠোর"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "মধ্যম"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "নেই"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "সময়সীমা"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "যেকোনো সময়"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "শেষ দিন"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "শেষ সপ্তাহ"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "শেষ মাস"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "শেষ বছর"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "তথ্য!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "বর্তমানে, কোন কুকি সংজ্ঞায়িত নেই।"

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "দুঃখিত!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "কোন ফলাফল পাওয়া যায়নি। তুমি চেষ্টা করতে পারো:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "আর কোন ফলাফল নেই। তুমি চেষ্টা করতে পারো:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "পাতা হালনাগাদ করো।"

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "অন্য কোনো তথ্য খুঁজো বা অন্য কোনো বিভাগ নির্বাচন করো (উপরে)।"

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "অনুসন্ধান ইঞ্জিন পছন্দসমূহ থেকে পাল্টাও:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "অন্য ইন্সট্যান্স ব্যবহার করো:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "অন্য কোনো তথ্য খুঁজো বা অন্য কোনো বিভাগ নির্বাচন করো।"

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "আগের পাতায় ফিরত যেতে পিছনে যাওয়ার বোতামে টিপ দাও।"

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "অনুমোদন"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "বিশেষ শব্দ (কুয়েরির প্রথম শব্দ)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "নাম"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "বর্ণনা"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "এটি SearXNG এর তাৎক্ষনিক উত্তর মডিউলগুলির তালিকা।"

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "এটি প্লাগইনগুলির তালিকা।"

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "স্বয়ংক্রিয়ভাবে পূরণ"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "টাইপ করার সময় শব্দগুলো খুঁজুন"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "মধ‍্যবর্তী বিন‍্যাস"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "পেইজের মাঝখানে রেজাল্ট দেখান (অস্কার লেআউট)।"

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "এটি কুকিজের তালিকা এবং সেগুলির মান SearXNG আপনার কম্পিউটারে সংরক্ষণ করছে।"

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "সেই তালিকা দিয়ে, আপনি SearXNG স্বচ্ছতা মূল্যায়ন করতে পারেন।"

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "কুকির নাম"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "মান"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "বর্তমানে সংরক্ষিত পছন্দের সার্চ ইউআরএল"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"দ্রষ্টব্য: সার্চ ইউআরএলের কাস্টম সেটিংস নির্দিষ্ট করা হলে ক্লিক করা "
"ফলাফলের সাইটগুলিতে ডেটা ফাঁস করে গোপনীয়তা হ্রাস করতে পারে।"

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "ইউআরএল থেকে আপনার পছন্দগুলি অন্য ব্রাউজারে ফিরিয়ে নিন"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "পছন্দসমূহের হ্যাশ অনুলিপি"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "অনুলিপিকৃত হ্যাশ এখানে দাও (ইউআরএল ছাড়া) ফিরত আনতে"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "পছন্দসমূহের হ্যাশ"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "ডিজিটাল অবজেক্ট আইডেন্টিফায়ার (ডই)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "উন্মুক্ত প্রবেশাধিকারযুক্ত DOI সমাধানদাতা"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "পুনর্লিখিত DOI দ্বারা ব্যবহৃত সার্ভিস নির্বাচিত করুণ"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"এই ট্যাবটি ইউজার ইন্টারফেসে নেই, কিন্তু আপনি এই ইঞ্জিনের !bangs ব্যবহার "
"করে সার্চ করতে পারেন।"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "সব সক্রিয় করুন"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "সব নিস্ক্রিয় করুন"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "নির্বাচিত ভাষা সমর্থন করে"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "ওজন"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "সর্বোচ্চ সময়"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "ফ্যাভিকন আমদানিকারক"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "অনুসন্ধানকৃত ফলাফলের কাছে ফ্যাভিকন দেখাও"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"এই সেটিংস আপনার কুকিজ সংরক্ষণ করা হয়, এটি আমাদের আপনার সম্পর্কে এই তথ্য "
"সংরক্ষণ করার অনুমতি দেয় না।"

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"এই কুকিজগুলি আপনার একমাত্র সুবিধা প্রদান করে, আমরা আপনাকে ট্র্যাক করতে এই"
" কুকিগুলি ব্যবহার করি না।"

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "সংরক্ষণ"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "ডিফল্টে রিসেট করুন"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "পিছনে"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "হটকি"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "ভিম-সদৃশ"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"অনুসন্ধানের ফলাফল হটকি দিয়ে পরিভ্রমণ করো (জাভাস্ক্রিপ্ট প্রয়োজন)। মূল "
"পাতায় বা ফলাফল পাতায় \"h\" টিপ দাও সাহায্যের জন্য।"

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "ছবির প্রক্সি"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "SearXNG এর মাধ্যমে ছবির ফলাফল প্রক্সি করা হচ্ছে"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "অসীম স্ক্রল"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"বর্তমান পৃষ্ঠার নীচে স্ক্রোল করার সময় স্বয়ংক্রিয়ভাবে পরবর্তী পৃষ্ঠা "
"লোড করুন"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "আপনি অনুসন্ধানের জন্য কোন ভাষা পছন্দ করেন?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr "SearXNG কে আপনার প্রশ্নের ভাষা সনাক্ত করতে দিতে অটো-ডিটেক্ট বেছে নিন।"

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "এইচটিটিপি ধরণ"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "ফর্ম জমা দেওয়ার পদ্ধতি পরিবর্তন করুন"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "জিজ্ঞাসা পৃষ্ঠার শিরোনামে"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"সক্রিয় করা হলে, ফলাফল পৃষ্ঠার শিরোনামে আপনার জিজ্ঞাসা থাকে। আপনার "
"ব্রাউজার এই শিরোনাম রেকর্ড করতে পারেন"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "নতুন ট‍্যাবে রেজাল্ট রয়েছে"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "নতুন ব্রাউজার ট্যাবে ফলাফল লিঙ্ক খুলুন"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "বাছাইকৃত উপাত্ত"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "বিভাগ নির্বাচন খুঁজে দেখুন"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"বিভাগ নির্বাচনের সাথে সাথে অনুসন্ধান করো। অনেকগুলো বিভাগ অনুসন্ধান করতে "
"নিষ্ক্রিয় করো"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "থিম"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "SearXNG এর লেআউট পরিবর্তন করুন"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "থিমের ধরণ"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "আপনার ব্রাউজার সেটিংস অনুসরণ করার জন‍্য auto সিলেক্ট করুন"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "ইঞ্জিন টোকেন"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "প্রাইভেট ইঞ্জিনের জন‍্য প্রবেশাধিকার টোকেন"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "ইন্টারফেসের ভাষা"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "লে আউটের ভাষা পরিবর্তন করুন"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "ইউআরএল ফরম্যাটকরণ"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "সুন্দর"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "সম্পূর্ণ"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "হোস্ট"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "ইউআরএল ফরম্যাটের ফলাফল পাল্টাও"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "ভাণ্ডার"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "মিডিয়া দেখান"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "মিডিয়া লুকান"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "এই সাইট কোন বিবরণ প্রদান করেনি।"

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "ফাইলের আকার"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "তারিখ"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "ধরন"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "আকার"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "সজ্জা"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "ইঞ্জিন"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "উৎস দেখুন"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "ঠিকানা"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "মানচিত্র দেখান"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "মানচিত্র লুকান"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "সংস্করণ"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "পরিচালক"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "হালনাগাদের সময়কাল"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "ট্যাগসমূহ"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "জনপ্রিয়তা"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "লাইসেন্স"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "প্রকল্প"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "প্রকল্পের মূলপাতা"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "প্রকাশের তারিখ"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "দৈনিক সংবাদপত্র"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "সম্পাদক"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "প্রকাশক"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "পিডিএফ"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "ম্যাগনেট লিঙ্ক"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "টরেন্ট ফাইল"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "সিডার"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "লিচার"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "ফাইলের সংখ্যা"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "ভিডিও দেখান"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "ভিডিও লুকিয়ে ফেলুন"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "পছন্দসমূহ"

#~ msgid "Scores per result"
#~ msgstr ""

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr ""

#~ msgid "No abstract is available for this publication."
#~ msgstr "এই প্রকাশনার জন্য কোন বিমূর্ত উপলব্ধ নেই."

#~ msgid "Self Informations"
#~ msgstr "নিজের তথ্য"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "এই প্লাগইনটি চেক করে যে অনুরোধের "
#~ "ঠিকানাটি একটি TOR প্রস্থান নোড কিনা "
#~ "এবং ব্যবহারকারীকে জানিয়ে দেয় যে এটি"
#~ " কিনা, যেমন check.torproject.org কিন্তু "
#~ "searxng থেকে।"

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "TOR প্রস্থান নোড তালিকা "
#~ "(https://check.torproject.org/exit-addresses) পৌঁছানো "
#~ "যায় না।"

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "আপনি TOR ব্যবহার করছেন। আপনার আইপি ঠিকানা বলে মনে হচ্ছে: {ip_address}।"

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""
#~ "আপনি TOR ব্যবহার করছেন না। আপনার "
#~ "আইপি ঠিকানা বলে মনে হচ্ছে: {ip_address}।"

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "অন্যান্য"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "শর্টকাট"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr ""

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "যখন এভেইলেবল হবে তখন উন্মুক্ত "
#~ "প্রবেশাধিকার ভার্সনে ফিরিয়ে নিন (প্লাগিন "
#~ "বাধ‍্যতামূলক)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "চালু"

#~ msgid "Off"
#~ msgstr "বন্ধ"

#~ msgid "Enabled"
#~ msgstr "সক্রিয়"

#~ msgid "Disabled"
#~ msgstr "নিষ্ক্রিয়"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "যখন একটি বিভাগ নির্বাচন করা হয়, "
#~ "অনুসন্ধান অবিলম্বে সঞ্চালিত হয়. একাধিক "
#~ "বিভাগ নির্বাচন করতে অক্ষম করুন। "
#~ "(জাভাস্ক্রিপ্ট প্রয়োজন)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "ভিম-এর মতো হটকি"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Vim-এর মতো হটকি (জাভাস্ক্রিপ্ট প্রয়োজন) "
#~ "দিয়ে অনুসন্ধান ফলাফল নেভিগেট করুন। "
#~ "সাহায্য পেতে প্রধান বা ফলাফল পৃষ্ঠায়"
#~ " \"h\" কী চাপুন।"

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "আমরা কোন ফলাফল খুঁজে পাইনি. অনুগ্রহ "
#~ "করে অন্য কোনো প্রশ্ন ব্যবহার করুন "
#~ "বা আরও বিভাগে অনুসন্ধান করুন।"

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "ফলাফল হোস্টনাম পুনরায় লিখুন বা "
#~ "হোস্টনামের উপর ভিত্তি করে ফলাফল মুছে "
#~ "ফেলুন"

#~ msgid "Bytes"
#~ msgstr "বাইটস"

#~ msgid "kiB"
#~ msgstr "কিবা"

#~ msgid "MiB"
#~ msgstr "মিবা"

#~ msgid "GiB"
#~ msgstr "গিবা"

#~ msgid "TiB"
#~ msgstr "টেবা"

#~ msgid "Hostname replace"
#~ msgstr "হোস্টনাম প্রতিস্থাপন"

#~ msgid "Error!"
#~ msgstr "ত্রুটি!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "ইঞ্জিন ফলাফল পুনরুদ্ধার করতে পারেছেনা"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "GitHub এ একটি নতুন সমস্যা জমা দেওয়া শুরু করুন"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "এলোমেলো মান জেনারেটর"

#~ msgid "Statistics functions"
#~ msgstr "পরিসংখ্যান কার্যাবলী"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "আর্গুমেন্টগুলির {functions} গণনা করুন৷"

#~ msgid "Get directions"
#~ msgstr "দিকনির্দেশ পান"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "ক্যোয়ারীটি \"ip\" হলে আপনার আইপি এবং"
#~ " যদি ক্যোয়ারীতে \"ব্যবহারকারী এজেন্ট\" "
#~ "থাকে তাহলে আপনার ব্যবহারকারী এজেন্ট "
#~ "প্রদর্শন করে।"

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr "টর exit-node থেকে লিস্ট ডাউনলোড করা যায়নি"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "আপনি টর ব্যবহার করছেন এবং মনে "
#~ "হচ্ছে এটি আপনার বাহ্যিক আইপি অ্যাড্রেসঃ"
#~ " {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "আপনি টর ব্যবহার করছেন না এবং মনে"
#~ " হচ্ছে এটি আপনার বাহ্যিক আইপি "
#~ "অ্যাড্রেসঃ {ip_address}"

#~ msgid "Keywords"
#~ msgstr "মূলশব্দ"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "কাস্টম সেটিংস্‌গুলো প্রেফারেন্স ইউআরএলে "
#~ "উল্লেখিত করা হল যাতে করে আপনার "
#~ "প্রেফারেন্সগুলি ডিভাইস জুড়ে সিঙ্ক করে "
#~ "ব্যবহার করতে পারেন।"

#~ msgid "proxied"
#~ msgstr "প্রক্সিকৃত"

