# Esperanto translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2015-2016
# pizzaiolo, 2016
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON>y <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023, 2024.
# R<PERSON><PERSON><PERSON>ovic <<EMAIL>>, 2023.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <PERSON>je<PERSON> <<EMAIL>>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-05-09 07:09+0000\n"
"Last-Translator: return42 <<EMAIL>>\n"
"Language: eo\n"
"Language-Team: Esperanto "
"<https://translate.codeberg.org/projects/searxng/searxng/eo/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "sen plia subgrupiĝo"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "alia"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "dosieroj"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "ĝenerala"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "muziko"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sociaj retoj"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "bildoj"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videoj"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "televido"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "komputiko"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "novaĵoj"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "mapo"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "cepoj"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "scienco"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikaĵoj"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "vortaroj"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "kantotekstoj"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pakoj"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "dϗr"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "deponejoj"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "programaro vikioj"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "reto"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "sciencaj publikaĵoj"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "aŭtomate"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "hela"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "malhela"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "nigra"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Funkciadaŭro"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Pri"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Averaĝa temperaturo"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Nubokovro"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Kondiĉo"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Nuna veterstato"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Vespero"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Sentiĝas kiel"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Humideco"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr ""

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr ""

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Mateno"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nokto"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Tagmezo"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr ""

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Sunleviĝo"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr ""

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr ""

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr ""

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr ""

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr ""

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr ""

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr ""

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr ""

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr ""

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr ""

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr ""

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr ""

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr ""

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nenio trovita"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Fonto"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Eraro dum la ŝarĝado de la sekvan paĝon"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Nevalidaj agordoj, bonvolu redaktu viajn agordojn"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Nevalidaj agordoj"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "serĉa eraro"

#: searx/webutils.py:35
msgid "timeout"
msgstr "eltempiĝo"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "analiza eraro"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP-protokolo-eraro"

#: searx/webutils.py:38
msgid "network error"
msgstr "reta eraro"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL-eraro: atestila validigo malsukcesis"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "neatendita kraŝo"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP-eraro"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP-konekto-eraro"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "prokurilo-eraro"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "tro da petoj"

#: searx/webutils.py:58
msgid "access denied"
msgstr "aliro rifuzita"

#: searx/webutils.py:59
msgid "server API error"
msgstr "servilo-API-eraro"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspendigita"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "antaŭ {minutes} minuto(j)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "antaŭ {hours} horo(j), {minutes} minuto(j)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generi diversajn hazardajn valorojn"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr ""

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (MALAKTUALA)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Tiu ĉi enigo estis anstataŭigita per"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanalo"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bito-rapido"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "voĉoj"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klakoj"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Lingvo"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citaĵoj de la {firstCitationVelocityYear}-a jaro ĝis la "
"{lastCitationVelocityYear}-a jaro"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Neeblas legi la URL de ĉi tiun bildon. Ĝi povas esti pro nesubtenata "
"dosierformo. TineEye nur subtenas bildojn, kiuj estas JPEG, PNG, GIF, "
"BMP, TIFF aŭ WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"La bildo estas tro simpla por trovi kongruojn. TinEye bezonas bazan "
"levelon de detalo por sukcese identigi kongruojn."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "La bildo ne eblis elŝuti."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Taksado de libro"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Dosiera kvalito"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr ""

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Konvertas ĉenojn al malsamaj hash-digestoj."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "haketa mesaĝaro"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr ""

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Malfermalira COI-ŝanĝo"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Eviti pagomurojn per direkto al malfermaliraj versioj de eldonaĵoj, se "
"eblas"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Meminformoj"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr ""

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr ""

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor-kontrolo kromprogramo"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Ĉi tiu kromaĵo kontrolas ĉu la adreso de la peto estas Tor elir-nodo, kaj"
" informas la uzanton ĉu ĝi estas; kiel check.torproject.org, sed de "
"SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Forigilo de URL-spuriloj"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Forviŝi spurajn argumentojn el la ricevita URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr ""

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Paĝo ne trovita"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Iri al %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "Serĉopaĝo"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Donacu"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Agordoj"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Funkciigita per"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "privateco-respektanta, libera metaserĉilo"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Fontaĵo"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Spurilo de problemoj"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statistikoj pri la motoro"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Publikaj instancoj"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Regularo pri privateco"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontaktu instancon prizorganto"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Alklaku la lupeon por serĉi"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Longo"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr ""

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Verkisto"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "kaŝmemorigita"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Komencu sendi novan numeron en GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Bonvolu kontroli ekzistantajn cimojn pri ĉi tiu motoro en GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Mi konfirmas, ke ne ekzistas cimo pri la problemo, kiun mi renkontas"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Se ĉi tio estas publika kazo, bonvolu specifi la URL en la cimraporto"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Sendu novan numeron ĉe Github inkluzive de ĉi-supraj informoj"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Neniu HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Vidi erarprotokolojn kaj sendi erarraporton"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang por ĉi tiu serĉilo"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang por ĝiaj kategorioj"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Meza"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Malsukcesa(j) kontrolilo(j): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Eraroj:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Ĝenerala"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Defaŭltaj kategorioj"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Fasado"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Privateco"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Serĉiloj"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Aktuale uzataj serĉiloj"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Specialaj Demandoj"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Kuketoj"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Nombro da rezultoj"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Info"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Reen al supro"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Antaŭa paĝo"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Sekva paĝo"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Montru la ĉefpaĝon"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Serĉi..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "purigi"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "serĉi"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Nun ne estas datumoj disponeblaj."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nomo de serĉilo"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Poentaroj"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Rezultkalkulo"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Tempo de respondo"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Fidindeco"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Entute"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Prilaborado"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Avertoj"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Eraroj kaj esceptoj"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Escepton"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mesaĝo"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Procento"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametro"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Dosiernomo"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkcio"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Fontkodo"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Kontrolilo"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Malsukcesa testo"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komento(j)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Ekzemploj"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr ""

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Respondoj"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Elŝuti rezultojn"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Provu serĉi:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Mesaĝoj de la serĉiloj"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Serĉi URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopiita"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopii"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Sugestoj"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Serĉolingvo"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Defaŭlta lingvo"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Aŭtomate detekti"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "SekuraSerĉo"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Strikta"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Modera"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Neniu"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Tempa intervalo"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Iam ajn"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Pasinta tago"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Pasinta semajno"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Pasinta monato"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Pasinta jaro"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informoj!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "nun ne estas ajnaj kuketoj difinitaj."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Pardonu!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Neniuj rezultoj estis trovitaj. Vi povas provi:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Estas nenio plu rezultoj. Vi povas provi:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Refreŝigi la paĝon."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Serĉu alian demandon aŭ elektu alian kategorion (supre)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Ŝanĝi la serĉilon uzatan en la preferoj:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Ŝanĝi al alia instanco:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Ŝercu por alia demandon aŭ elektu alia kategorio."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Reiru al la antaŭa paĝon uzata la antaŭa paĝo butono."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Permesi"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nomo"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Priskribo"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Ĉi tiu estas la listo de la tujaj respondaj moduloj de SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Ĉi tiu estas la listo de kromaĵoj."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Aŭtomate kompletigi"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Trovi aferojn dum tajpado"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centra Vicigo"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Montras rezultojn en la centro de la paĝo."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Jen la listo de kuketoj kaj iliaj valoroj SearXNG konservas en via "
"komputilo."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Kun tiu listo, vi povas taksi SearXNG-travideblecon."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nomo de kuketo"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valoro"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Serĉo-URL kun aktuale konservitaj agordoj"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Rimarko: Precizigo de propraj agordoj en la serĉo-URL povas malaltigi "
"privatecon per nevola diskonigo de la datumoj al alklikantaj retejoj."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL por restarigi viajn preferojn en alia TTT-legilo"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Malfermalira COI-solvilo"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Elekti servon uzatan de DOI-reskribo"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ĉi tiu langeto ne ekzistas en la uzantinterfaco, sed vi povas serĉi en ĉi"
" tiuj serĉiloj per siaj !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr ""

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Subtenas elektitan lingvon"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Pezo"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maksimuma tempo"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Tiuj ĉi agordoj estas konservitaj en viaj kuketoj, kio ebligas al ni ne "
"konservi tiujn datumojn pri vi en nia servilo."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr "Tiuj kuketoj estas nur por via plaĉo, ni ne uzas ilin por spuri vin."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Konservi"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Reagordi al defaŭlto"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Reen"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Klavaraj ŝparvojoj"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-simila"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Navigu serĉrezultojn per klavaraj ŝparvojoj (JavaScript bezonata). Premu "
"\"h\" klavon sur ĉefa aŭ rezultpaĝo por ricevi helpon."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Prokurila servilo por bildoj"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Prokurado de bildaj rezultoj per SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Senfina rulumado"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Aŭtomate ŝarĝi sekvan paĝon rulumante al la subo de la nuna paĝo"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Kiun lingvon vi pli ŝatas por serĉi?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Elektu Aŭtomate-detekti por lasi SearXNG detekti la lingvon de via "
"demando."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP-Metodo"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Ŝanĝi kiel oni sendas formularojn"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Montru demandon en la titolo de la paĝo"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Kiam ĝi estas ebligita, la titolo de la rezultpaĝo enhavas vian demandon."
" Via TTT-legilo povas registri ĉi tiun titolon"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Rezultoj en novaj langetoj"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Malfermi rezultligilojn en novaj TTT-legilaj langetoj"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtri enhavon"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Serĉi en elektita kategorio"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Fari serĉon tuj se kategorio estas elektita. Malebligi elekti plurajn "
"kategoriojn"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Etoso"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Ŝanĝu SearXNG-aranĝon"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Temo stilo"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Elektu 'auto' por sekvi la agordojn de via TTT-legilo"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Serĉiloj ĵetonoj"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Alirĵetonoj por privataj serĉiloj"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Fasada lingvo"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Ŝanĝi lingvon de la fasono"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "Deponejo"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "montri aŭdvidaĵojn"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "kaŝi aŭdvidaĵojn"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ĉi tiu retejo ne disponigis ajnan priskribon."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Dosiergrandeco"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dato"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Tipo"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Distingivo"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formato"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Serĉilo"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Vidi fonton"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adreso"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "montri mapon"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "kaŝi mapon"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Versio"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etikedoj"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr ""

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licenco"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projektaj"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr ""

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Eldonita dato"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Revuo"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Redaktoro"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Eldonejo"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "COI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnetligilo"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torentodosiero"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Fonto"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Ricevanto"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Nombro da Dosieroj"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "montri videojn"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "kaŝi videojn"

#~ msgid "Engine time (sec)"
#~ msgstr "Motora tempo (s)"

#~ msgid "Page loads (sec)"
#~ msgstr "Paĝŝarĝo (sekundoj)"

#~ msgid "Errors"
#~ msgstr "Eraroj"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Ŝanĝi HTTP-ligilojn al HTTPS, se eblas"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Oni malfermas rezultojn en la sama "
#~ "langeto defaŭlte. Ĉi tiu aldonaĵo ŝanĝas"
#~ " la kutiman agmanieron por malfermi "
#~ "ligilojn en novaj langetoj/fenestroj. "
#~ "(ĜavoSkripto bezonata)"

#~ msgid "Color"
#~ msgstr "Koloro"

#~ msgid "Blue (default)"
#~ msgstr "Blua (defaŭlta)"

#~ msgid "Violet"
#~ msgstr "Viola"

#~ msgid "Green"
#~ msgstr "Verda"

#~ msgid "Cyan"
#~ msgstr "Bluverda"

#~ msgid "Orange"
#~ msgstr "Oranĝa"

#~ msgid "Red"
#~ msgstr "Ruĝa"

#~ msgid "Category"
#~ msgstr "Kategorio"

#~ msgid "Block"
#~ msgstr "Bloki"

#~ msgid "original context"
#~ msgstr "originala kunteksto"

#~ msgid "Plugins"
#~ msgstr "Aldonaĵoj"

#~ msgid "Answerers"
#~ msgstr "Respondiloj"

#~ msgid "Avg. time"
#~ msgstr "Mezkvanta tempo"

#~ msgid "show details"
#~ msgstr "montri detalojn"

#~ msgid "hide details"
#~ msgstr "kaŝi detalojn"

#~ msgid "Load more..."
#~ msgstr "Ŝarĝi pli..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Ŝanĝi fasonon de Searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Prokuri bildrezultojn per searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Tio ĉi estas listo de tuje respondantaj moduloj de Searx."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Ĉi tio estas listo de kuketoj kaj"
#~ " iliaj valoroj, kiujn searx konservas "
#~ "en via komputilo."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Kun tiu listo, vi povas kontroli la travideblecon de searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Ŝajnas, ke ĉi tio estas via unua fojo, kiam vi uzas searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Bonvolu provi ĝin poste aŭ trovi aliajn searx-instancon."

#~ msgid "Themes"
#~ msgstr "Temoj"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metodo"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Altgradaj agordoj"

#~ msgid "Close"
#~ msgstr "Fermi"

#~ msgid "Language"
#~ msgstr "Lingvo"

#~ msgid "broken"
#~ msgstr "rompita"

#~ msgid "supported"
#~ msgstr "subtenata"

#~ msgid "not supported"
#~ msgstr "nesubtenata"

#~ msgid "about"
#~ msgstr "pri"

#~ msgid "Avg."
#~ msgstr "Meze"

#~ msgid "User Interface"
#~ msgstr "Grafika fasado"

#~ msgid "Choose style for this theme"
#~ msgstr "Elekti stilon por ĉi tiu temo"

#~ msgid "Style"
#~ msgstr "Stilo"

#~ msgid "Show advanced settings"
#~ msgstr "Montru detalaj agordoj"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Montru detalajn agordojn en la hejmpaĝo defaŭlte"

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "Elekti lingvon"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "konservi"

#~ msgid "back"
#~ msgstr "antaŭen"

#~ msgid "Links"
#~ msgstr "Ligiloj"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Serĉrezultoj"

#~ msgid "next page"
#~ msgstr "sekva paĝo"

#~ msgid "previous page"
#~ msgstr "antaŭa paĝo"

#~ msgid "Start search"
#~ msgstr "Komenci serĉon"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "statistikoj"

#~ msgid "Heads up!"
#~ msgstr "Atentu!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Bonfarite!"

#~ msgid "Settings saved successfully."
#~ msgstr "Agordoj konservitaj sukcese."

#~ msgid "Oh snap!"
#~ msgstr "Ho ve!"

#~ msgid "Something went wrong."
#~ msgstr "Io fuŝiĝis."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Akiri bildon"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "agordoj"

#~ msgid "Scores per result"
#~ msgstr "Poentaroj por unu rezulto"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "kodumebla metaserĉilo kiu respektas vian privatecon"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Neniu resumo atingeblas por tiu ĉi eldonaĵo."

#~ msgid "Self Informations"
#~ msgstr "Memaj Informoj"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ŝanĝi kiel formoj estas sendataj, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">sciu pli pri peto-"
#~ "metodoj</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Vi uzas Tor-on. Via IP-adreso ŝajnas esti: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Vi ne uzas Tor-on. Via IP-adreso ŝajnas esti: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "aliaj"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Shortcut"
#~ msgstr "Fulmoklavo"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Motoroj ne povas trovi rezultojn."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Direkti al malfermaliraj versioj de "
#~ "eldonaĵoj, se eblas (aldonaĵo necesas)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Ŝanĝu kiel oni sendas formularojn, <a"
#~ " "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">lernu pli pri petaj "
#~ "metodoj</a>"

#~ msgid "On"
#~ msgstr "Ŝaltita"

#~ msgid "Off"
#~ msgstr "Malŝaltita"

#~ msgid "Enabled"
#~ msgstr "Ŝaltita"

#~ msgid "Disabled"
#~ msgstr "Malŝaltita"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Serĉi tuj se oni elektas kategorion. "
#~ "Malŝaltu ĝin por elekti plurajn "
#~ "kategoriojn (ĜavoSkripto bezonata)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim-ŝajnaj klavkomandoj"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Tranavigi serĉrezultojn per Vim-ŝajnaj "
#~ "klavkomandoj (ĜavoSkripto bezonata). Premu "
#~ "\"h\" por helptekstaro en ĉef- aŭ "
#~ "rezultpaĝo."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "ni ne trovis rezultojn. Bonvole uzu "
#~ "alian serĉfrazon aŭ serĉu en pliaj "
#~ "kategorioj."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Reskribi rezultajn gastigajn nomojn aŭ "
#~ "forigi rezultojn bazitajn sur la gastiga"
#~ " nomo"

#~ msgid "Bytes"
#~ msgstr "Bitokoj"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Gastnomo anstataŭigas"

#~ msgid "Error!"
#~ msgstr "Eraro!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Serĉiloj ne povas retrovi rezultojn"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Komencu sendi novan numeron en GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Hazardvalora generilo"

#~ msgid "Statistics functions"
#~ msgstr "Statistikaj funkcioj"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Kalkuli {functions} de la argumentoj"

#~ msgid "Get directions"
#~ msgstr "Akiri direktojn"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Montras vian IP-adreson se la "
#~ "serĉofrazo estas \"ip\" kaj vian "
#~ "klientan aplikaĵon se la serĉofrazo "
#~ "enhavas \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Ne eblis elŝuti liston de Tor "
#~ "elirnodoj de: https://check.torproject.org/exit-"
#~ "addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Vi uzas Tor kaj ŝajnas, ke vi "
#~ "havas ĉi tiun eksteran IP-adreson: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Vi ne uzas Tor kaj vi havas ĉi tiun eksteran IP-adreson: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Ŝlosilvortoj"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Specifante kutimajn agordojn en la URL"
#~ " de preferoj povas esti uzata por "
#~ "sinkronigi preferojn tra aparatoj."

#~ msgid "proxied"
#~ msgstr "prokurata"

