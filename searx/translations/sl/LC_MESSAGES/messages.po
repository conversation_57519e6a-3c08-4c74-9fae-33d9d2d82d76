# Slovenian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2017-2018
# <PERSON> <<EMAIL>>, 2022, 2023.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <PERSON>dobni Volk <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-01-06 15:53+0000\n"
"Last-Translator: cynedex <<EMAIL>>\n"
"Language: sl\n"
"Language-Team: Slovenian "
"<https://translate.codeberg.org/projects/searxng/searxng/sl/>\n"
"Plural-Forms: nplurals=4; plural=n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 "
"|| n%100==4 ? 2 : 3;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "brez nadaljnjega razvrščanja v podskupine"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "Ostale kategorije"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "datoteke"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "splošno"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "glasba"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "družabna omrežja"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "slike"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videi"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "televizija"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "informatika"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "novice"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "zemljevid"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "čebula"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "znanost"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aplikacije"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "slovarji"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "besedilo"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketi"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "vprašanja in odgovori"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "repozitoriji"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "Dokumentacija programske opreme"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "splet"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "znanstvena publikacija"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "avtomatsko"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "svetlo"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "temno"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr ""

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Čas delovanja"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "O nas"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Povprečna temp."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Oblačnost"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Pogoji"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Trenutno stanje"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Večer"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Občuti se kot"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Vlaga"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Najvišja temp."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Najnižja temp."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Jutro"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Noč"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Opoldne"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Tlak"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Sončni vzhod"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Sončni zahod"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Temperatura"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV indeks"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Vidnost"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Veter"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "naročniki"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "objave"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktivni uporabnik"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "komentarji"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "uporabnik"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "skupnost"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "točke"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "glavni naslov"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "avtor"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr ""

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr ""

#: searx/webapp.py:292
msgid "No item found"
msgstr "Ni zadetkov"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Vir"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Napaka pri nalaganju naslednje strani"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Neveljavne nastavitve. Prosimo, preverite vašo konfiguracijo"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Neveljavne nastavitve"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "napaka pri iskanju"

#: searx/webutils.py:35
msgid "timeout"
msgstr "odmor"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "napaka pri razčlenjevanju"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "napaka protokola HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "omrežna napaka"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL-napaka: Preveritev certifikata je spodletela"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "nepričakovana zrušitev"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "napaka HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "napaka povezave HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "napaka proxyja"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "preveč prošenj"

#: searx/webutils.py:58
msgid "access denied"
msgstr "dostop zavrnjen"

#: searx/webutils.py:59
msgid "server API error"
msgstr "napaka API strežnika"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Prekinjeno"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} minut nazaj"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "pred {hours} urami in {minutes} minut"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Generiraj različne naključne vrednosti"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr ""

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (neveljaven)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Ta vnos je bil nadomeščen z"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitna hitrost"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "glasov"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "klikov"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Jezik"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} navedb od leta {firstCitationVelocityYear} do "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Ne morem prebrati slike hiperpovezave. Razlog je lahko zaradi nepodprtega"
" formata datoteke. TinEye podpira samo slikovne formate JPEG, PNG, GIF, "
"BMP, TIFF ali WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Slika je preveč preprosta, da bi lahko našel zadetke. TinEye potrebuje "
"osnovni nivo vizualnih detajlov za identifikacijo zadetkov."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Slike ni bilo mogoče prevesti."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Ocena knjige"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Kakovost datoteke"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Izačunajte matematične izraze preko iskalne vrstice"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Pretvori besede v drugo hash vrednost."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "Hash vrednost"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr ""

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Prosto dostopni DOI prepis"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Izogibanje plačilom s preusmeritvijo na prostodostopne različice "
"publikacij, ko so na voljo"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informacije o sebi"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr ""

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr ""

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Preveri Tor vtičnik"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Ta vtičnik preveri, če je naslov poizvedbe izhodni prikluček TOR in "
"informira uporabnika o njem, kot naprimer check.torproject.org ampak "
"preko SearXNG-ja."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Odstranjevalec sledilcev URL"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Odstrani argumente sledilcev iz vrnjenega URL"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Pretvarjanje med enotami"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Strani ni bilo mogoče najti"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Pojdi na %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "stran za iskanje"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Doniraj"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Nastavitve"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Omogočeno z"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "odprt metaiskalnik, ki spoštuje zasebnost"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Izvorna koda"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Sledilnik napak/problemov"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statistike iskalnika"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Javne instance"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Politika zasebnosti"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kontaktiraj vzdrževalca instance"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Kiknite na lupo za iskanje"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Dolžina"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr ""

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Avtor"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "predpomnjeno"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Začni oddajo novega hrošča na GitHub-u"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Prosim preveri že aktivne hrošče glede engine-a na GitHub-u"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Potrjujem da o problemu, na katerega sem naletel, ni že obstoječega hrošča"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Če je to javna instanca, prosim specificirajte URL v poročilu o napaki"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Predloži novo težavo na Githubu, vključno z zgornjimi informacijami"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Brez HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Oglejte si dnevnike napak in pošljite poročilo o napakah"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang za ta iskalnik"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang za njegove kategorije"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Mediana"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Neuspešno opravljen(i) preizkus(i) preverjanja: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Napake:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Splošno"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Privzete kategorije"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Uporabniški vmesnik"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Zasebnost"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Iskalniki"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Trenutno uporabljeni iskalniki"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Posebne poizvedbe"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Piškotki"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Število zadetkov"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Informacije"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Nazaj na vrh"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Prejšnja stran"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Naslednja stran"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Prikaži naslovno stran"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Poišči..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "počisti"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "Išči"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Trenutno ni podatkov na voljo."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Ime iskalnika"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Točke"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Število rezultatov"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Odzivni čas"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Zanesljivost"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Skupaj"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "obdelava"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Opozorila"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Napake in izjeme"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Izjeme"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Sporočilo"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Odstotek"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parameter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Ime datoteke"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkcija"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Koda"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Pregledovalnik"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Neuspešen preizkus"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Komentar(ji)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Primeri"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr ""

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Odgovori"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Prenesi zadetke"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Poskusite iskati:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Sporočila iskalnikov"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr ""

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Iskalni URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Kopirano"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Kopiraj"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Predlogi"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Jezik iskanja"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Privzeti jezik"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Samodejno zaznaj"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Varno iskanje"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Strogo"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Zmerno"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Brez"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Časovni razpon"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Kadarkoli"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "V zadnjem dnevu"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "V zadnjem tednu"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "V zadnjem mesecu"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "V zadnjem letu"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Informacije!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "Trenutno ni definiranih piškotkov."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Škoda!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Ni rezultatov. Lahko poskusiš:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Ni več rezultatov. Lahko poskusiš:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Osveži stran."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Išči z drugačno poizvedbo ali izberi drugo kategorijo (zgoraj)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Zamenjaj iskalnik uporabljen v nastavitvah:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Preklopi na drugo instanco:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Išči z drugačno poizvedbo ali izberi drugo kategorijo."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Pojdi na prejšnjo stran z uporabo gumba za prejšnjo stran."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Dovoli"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Ime"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Opis"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "To je seznam modulov za takojšnje javljanje SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "To je seznam vtičnikov."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Samodejni predlogi"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Iščite že med tipkanjem"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Sredinska poravnava"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Prikaže rezultate na sredini strani (postavitev Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"To je seznam piškotkov in njihovih vrednosti, ki jih SearXNG shranjuje v "
"vaš računalnik."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "S tem seznamom lahko ocenite transparentnost SearXNG."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Ime piškotka"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Vrednost"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Iskalni URL trenutno shranjenih nastavitev"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Opomba: navajanje lastnih nastavitev v iskalnem URL lahko vodi do "
"zmanjšane zasebnosti preko podajanja podatkov izbranim rezultatom."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL za obnovitev vaših nastavitev v drugem brskalniku"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Kopiraj hash nastavitev"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Vnesi kopirani hash nastavitev (brez URL) za povrnitev"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash nastavitev"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "odprto dostopni DOI razreševalec"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Izveri storitev za DOI prepisovanje"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ta kartica ne obstaja v uporabniškem vmesniku, ampak lahko iščeš v teh "
"iskalnikih z njihovimi !bangi."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "omogoči vse"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "onemogoči vse"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Podpira izbrani jezik"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Teža"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Največji čas"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr ""

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr ""

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Te nastavitve so shranjene v vaših piškotkih; to nam omogoča, da ne "
"hranimo teh podatkov o vas."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr "Ti piškotki so za boljšo izkušnjo, ne uporabljamo jih za sledenje."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Shrani"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Ponastavi na privzeto"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Nazaj"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Hitre tipke"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Kot v Vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Po rezultatih se pomikajte s hitrimi tipkami (potreben je JavaScript). Za"
" pomoč na glavni strani ali strani z rezultati pritisnite tipko \"h\"."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Posredniški strežnik za slike"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proxy rezultatov slik prek SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Neskončno drsenje"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Samodejno naloži naslednjo stran ob ogledu dna trenutne strani"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "V katerem jeziku želite iskati?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Izberi Avtomatsko zaznavanje, da lahko SearXNG samodejno zazna jezik "
"poizvedbe."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "metoda HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Spremeni način pošiljanja obrazcev"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Poizvedba v naslovu strani"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Ko je omogočeno, naslov strani z rezultati vsebuje vašo poizvedbo. Vaš "
"brskalnik lahko posname ta naslov"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Zadetki v novih zavihkih"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Odpri povezave zadetkov v novih zavihkih brskalnika"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtriraj vsebino"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Išči ob izboru kategorije"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Izvedi iskanje takoj, če je izbrana kategorija. Onemogoči za izbor več "
"kategorij"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Spremenite postavitev SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Slog teme"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Če želite slediti nastavitvam brskalnika, izberite samodejno"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Žetoni za iskalnik"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Žetoni dostopa za zasebne iskalnike"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Jezik vmesnika"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Spremeni jezik vmesnika"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr ""

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr ""

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "repozitorij"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "pokaži medijske vsebine"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "skrij medijske vsebine"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ta stran ni posredovala nobenega opisa."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Velikost"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Datum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Vrsta"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Ločljivost"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Pogon"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Ogled vira"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "Naslov"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "prikaži zemljevid"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "skrij zemljevid"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Različica"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Vzdrževalec"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Posodobljeno"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Oznake"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularnost"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licenca"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Domača stran projekta"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Datum objave"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Revija"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Urejevalnik"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Založnik"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet povezava"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent datoteka"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Sejalec"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Odjemalec"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Število datotek"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "pokaži video"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "skrij video"

#~ msgid "Engine time (sec)"
#~ msgstr "Čas iskanja (sek.)"

#~ msgid "Page loads (sec)"
#~ msgstr "Čas nalaganja (sek.)"

#~ msgid "Errors"
#~ msgstr "Napake"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Prepisovanje HTTP povezav v HTTPS, ko je to mogoče"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Zadetki so privzeto odprti v istem "
#~ "oknu. Ta vstavek spremeni privzeto "
#~ "obnašanje tako, da se povezave odprejo"
#~ " v novih zavihkih/oknih. (Potrebuje "
#~ "JavaScript)"

#~ msgid "Color"
#~ msgstr "Barva"

#~ msgid "Blue (default)"
#~ msgstr "Modra (privzeto)"

#~ msgid "Violet"
#~ msgstr "Vijolična"

#~ msgid "Green"
#~ msgstr "Zelena"

#~ msgid "Cyan"
#~ msgstr "Cian modra"

#~ msgid "Orange"
#~ msgstr "Oranžna"

#~ msgid "Red"
#~ msgstr "Rdeča"

#~ msgid "Category"
#~ msgstr "Kategorija"

#~ msgid "Block"
#~ msgstr "Blokiraj"

#~ msgid "original context"
#~ msgstr "originalna stran"

#~ msgid "Plugins"
#~ msgstr "Vtičniki"

#~ msgid "Answerers"
#~ msgstr "Ponudniki odgovorov"

#~ msgid "Avg. time"
#~ msgstr "Povprečni čas"

#~ msgid "show details"
#~ msgstr "prikaži podrobnosti"

#~ msgid "hide details"
#~ msgstr "skrij podrobnosti"

#~ msgid "Load more..."
#~ msgstr "Naloži več..."

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr "Spremeni izgled searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Uporaba searx kot posredniški strežnik za slike"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "To je seznam modulov searx za takojšnje odgovore."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "To je seznam piškotkov in pripadajočih"
#~ " vrednosti, ki jih searx hrani na "
#~ "vašem računalniku."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "S tem seznamom lahko ocenite transparentnost searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Prvič uporabljate searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Prosimo, poskusite kasneje tu ali na drugi instanci searx."

#~ msgid "Themes"
#~ msgstr "Teme"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Metoda"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Napredne nastavitve"

#~ msgid "Close"
#~ msgstr "Zapri"

#~ msgid "Language"
#~ msgstr ""

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "podprto"

#~ msgid "not supported"
#~ msgstr "ni podprto"

#~ msgid "about"
#~ msgstr "več o"

#~ msgid "Avg."
#~ msgstr "Povprečje"

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Izberite stil za trenutno temo"

#~ msgid "Style"
#~ msgstr "Stil"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "Izbrani jezik"

#~ msgid "Query"
#~ msgstr "Poizvedba"

#~ msgid "save"
#~ msgstr "shrani"

#~ msgid "back"
#~ msgstr "nazaj"

#~ msgid "Links"
#~ msgstr "Povezave"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr "Zadetki iskanja"

#~ msgid "next page"
#~ msgstr "naslednja stran"

#~ msgid "previous page"
#~ msgstr "prejšnja stran"

#~ msgid "Start search"
#~ msgstr "Začni iskati"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "statistike"

#~ msgid "Heads up!"
#~ msgstr "Pozor!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Opravljeno!"

#~ msgid "Settings saved successfully."
#~ msgstr "Nastavitve so bile uspešno shranjene."

#~ msgid "Oh snap!"
#~ msgstr "Ojej!"

#~ msgid "Something went wrong."
#~ msgstr "Nekaj je bilo narobe."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Pridobi sliko"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "nastavitve"

#~ msgid "Scores per result"
#~ msgstr "Točke na zadetek"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "razširljiv metaiskalnik, ki spoštuje vašo zasebnost"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Povzetek za to publikacijo ni na voljo."

#~ msgid "Self Informations"
#~ msgstr "Informacije o sebi"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Spremeni, kako se pošiljajo obrazci, <a"
#~ " "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">, več o metodah za "
#~ "zahtevke </a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Ta vtičnik preveri, če je naslov "
#~ "poizvedbe izhodni prikluček TOR in "
#~ "informira uporabnika o njem, kot "
#~ "naprimer check.torproject.org ampak preko "
#~ "searxng-ja."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "Seznam izhodnih priključkov TOR "
#~ "(https://check.torproject.org/exit-addresses) je "
#~ "nedosegljiv."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Uporabljaš TOR. Tvoj IP naslov naj bi bil: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Ne uporabljaš TOR-a. Tvoj IP naslov naj bi bil: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Samodejno zaznavanje jezika iskanja"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Samodejno zazna jezik iskanja poizvedbe in preklopi nanj."

#~ msgid "others"
#~ msgstr "Ostali"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Ta zavihek se ne prikaže pri "
#~ "rezultatih iskanja, lahko pa iščete "
#~ "iskalnike, navedene tukaj, prek bangs."

#~ msgid "Shortcut"
#~ msgstr "Bližnjica"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Iskalniki ne morejo pridobiti rezultatov."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Prosim poizkusite kasneje, ali poiščite drugo SearXNG istanco."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Preusmeri na prosto dostopne različice "
#~ "publikacij, ko so na voljo (zahtevan "
#~ "vtičnik)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Spremenite način oddaje obrazcev, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">izvedite več o metodah "
#~ "zahtevka</a>"

#~ msgid "On"
#~ msgstr "Vklopljeno"

#~ msgid "Off"
#~ msgstr "Izklopljeno"

#~ msgid "Enabled"
#~ msgstr "Omogočeno"

#~ msgid "Disabled"
#~ msgstr "Onemogočeno"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Če je kategorija izbrana, takoj izvedi"
#~ " iskanje. Za izbor več kategorij "
#~ "onemogocite. (Potrebna je JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Tipkovne bližnjice Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Premikanje po zadetkih z tipkovnimi "
#~ "bližnjicami Vim (zahtevan JavaScript). "
#~ "Pritisnite tipko \"h\" na glavni strani"
#~ " ali strani z zadetki za pomoč."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Nismo našli zadetkov. Uporabite drugo "
#~ "poizvedbo ali pa razširite nabor "
#~ "kategorij za iskanje."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Prepiši rezultate strežniških imen ali "
#~ "odstrani rezultate na bazi strežniških "
#~ "imen"

#~ msgid "Bytes"
#~ msgstr "Bajti"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Preimenuj strežniško ime"

#~ msgid "Error!"
#~ msgstr "Napaka!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Iskalniki ne morejo pridobiti rezultatov"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Začni oddajo novega hrošča na GitHub-u"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Generator naključnih števil"

#~ msgid "Statistics functions"
#~ msgstr "Statistične funkcije"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Izračunaj {functions} argumentov"

#~ msgid "Get directions"
#~ msgstr "Pridobite navodila"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Prikaže IP naslov, če je niz "
#~ "poizvedbe \"ip\", in uporabniški agent, "
#~ "če je niz \"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Seznama izhodnih točk Tor ni bilo "
#~ "mogoče prenesti s https://check.torproject.org"
#~ "/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Uporabljate Tor in kot kaže imate ta zunanji IP naslov: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Ne uporabljate Tor in imate tale zunanji IP naslov: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Ključne besede"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Določanje nastavitev po meri v URL-"
#~ "ju z nastavitvami se lahko uporabi "
#~ "za sinhronizacijo nastavitev med napravami."

#~ msgid "proxied"
#~ msgstr "preko posredniškega strežnika"

