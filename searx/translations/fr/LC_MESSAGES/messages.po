# French translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2017-2018, 2022.
# <PERSON> <<EMAIL>>, 2014
# Cqoicebordel, 2014
# Cqoicebordel, 2014-2017,2020
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# rike, 2014
# <PERSON> <<EMAIL>>, 2022.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024.
# <PERSON><PERSON> <<EMAIL>>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>,
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-03 03:33+0000\n"
"Last-Translator: wags07 <<EMAIL>>\n"
"Language-Team: French <https://translate.codeberg.org/projects/searxng/"
"searxng/fr/>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "sans autre sous-groupe"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "autre"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "fichiers"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "général"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "musique"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "réseaux sociaux"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "images"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "vidéos"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radio"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "tv"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "informatique"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "actualités"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "carte"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onions"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "science"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "applications"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "dictionnaires"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "paroles"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paquets"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "questions/réponses"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "dépôts"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "documentations d'applications"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "publications scientifiques"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "auto"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "clair"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "sombre"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "noir"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Temps de fonctionnement"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "À propos"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Temp. moyenne"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Couvert nuageux"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Condition"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Condition actuelle"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Soir"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Ressenti"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Humidité"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Temp. maximale"

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Temp. minimale"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Matin"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Nuit"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Midi"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Pression"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Lever du soleil"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Coucher de soleil"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Température"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Indice UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Visibilité"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Le vent"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Ciel dégagé"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Abonnés"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "Posts"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "utilisateurs actifs"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "Commentaires"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "utilisateur"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "Communauté"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "points"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "Titre"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "Auteur"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Ouvert"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Fermé"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "répondu"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Pas d'élément trouvé"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Source"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Erreur lors du chargement de la page suivante"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Paramètres non valides, veuillez éditer vos préférences"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Paramètres non valides"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "erreur de recherche"

#: searx/webutils.py:35
msgid "timeout"
msgstr "délai dépassé"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "erreur d'analyse"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "erreur de protocole HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "Erreur de réseau"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Erreur SSL : La vérification du certificat a échoué"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "erreur inattendue"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "erreur HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "erreur de connexion HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "Erreur proxy"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "trop de requêtes"

#: searx/webutils.py:58
msgid "access denied"
msgstr "accès refusé"

#: searx/webutils.py:59
msgid "server API error"
msgstr "erreur API du serveur"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Suspendu"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "il y a {minutes} minute(s)"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "il y a {hours} heure(s), {minutes} minute(s)"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Crée des valeurs aléatoires différentes"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Calcule les {func} des arguments"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Montrer la route sur la carte .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (OBSOLÈTE)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Cet item a été remplacé par"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Chaîne"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "débit"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "voix"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "clics"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Langue"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} citations de l'année {firstCitationVelocityYear} à "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Impossible de lire l'url de l'image. Cela peut être dû à un format de "
"fichier non pris en charge. TinEye ne prend en charge que les images au "
"format JPEG, PNG, GIF, BMP, TIFF ou WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"L'image est trop simple pour trouver des correspondances. TinEye a besoin"
" d'un niveau de détail visuel minimum pour réussir à identifier les "
"correspondances."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "L'image n'a pas pu être téléchargée."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Évaluation du livre"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Qualité du fichier"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia liste noire"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Filtrer les résultat d’onion qui apparaissent dans la liste noire d’Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Calculatrice de Base"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Calculer des expressions mathématiques dans la barre de recherche"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Plugin de hachage"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Convertit les chaînes de caractères en différents condensés de hachage."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "Valeur de hachage"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Plugin de noms d’hôtes"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Réécrire les noms de domaines, supprimer des résultats ou les prioriser "
"en se basant sur les domaines"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Utiliser Open Access DOI"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Contourne les verrous payants de certaines publications scientifiques en "
"redirigeant vers la version ouverte de ces papiers si elle est disponible"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Informations sur le navigateur"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Affiche votre IP si la requête est « ip » et votre agent utilisateur si "
"la requête est « agent-utilisateur »."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Votre IP est : "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Votre agent-utilisateur est : "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Plugin de vérification de Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Ce plugin vérifie si l’adresse de la requête est un nœud de sortie Tor, "
"et informe l’utilisateur si c’en est un ; par exemple "
"check.torproject.org, mais depuis SearXNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Impossible de télécharger la liste des nœuds de sortie Tor depuis"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Vous utilisez Tor et il semble que vous ayez l'adresse IP externe"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Vous n'utilisez pas Tor et vous avez l'adresse IP externe"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Nettoyeur d'URL de suivis"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Retire les arguments utilisés pour vous pister des URL retournées"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Plugin de conversion d'unités"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Convertit entre les unités"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Page non trouvée"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Aller à %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "la page d'accueil"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Faire un don"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Préférences"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Propulsé par"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "un métamoteur ouvert et respectueux de la vie privée"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Code source"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Suivi des problèmes"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Statistiques des moteurs"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Instances publiques"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Politique de confidentialité"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Contacter le responsable de l'instance"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Cliquez sur la loupe pour effectuer une recherche"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Durée"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "vues"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Auteur"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "en cache"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Soumettre un nouveau problème sur GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Merci de vérifier l’existence de bugs sur ce moteur de recherche sur "
"GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Je confirme qu'il n'existe pas de bug pour le problème que j'ai rencontré"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Si c'est une instance public, merci de spécifier l'URL dans le rapport de"
" bug"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Soumettre un nouveau ticket sur Github incluant l'information ci-dessus"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Pas de HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Afficher les journaux d'erreurs et soumettre un rapport de bogue"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang pour ce moteur de recherche"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang pour ses catégories"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Médiane"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Test(s) du checker échoué(s) : "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Erreurs :"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Général"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Catégories par défaut"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Interface utilisateur"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Vie privée"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Moteurs"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Moteurs de recherche actuellement utilisés"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Requêtes spéciales"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Cookies"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Nombre de résultats"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Infos"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Retour en haut de page"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Page précédente"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "page suivante"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Afficher la page d'accueil"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Rechercher..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "effacer"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "chercher"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Aucune donnée disponible pour l'instant. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Nom du moteur"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Score"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Nombre de résultats"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Temps de réponse"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Fiabilité"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Total"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Traitement"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Attention"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Erreurs et exceptions"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Exception"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Message"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Pourcentage"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Paramètre"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Nom de fichier"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Fonction"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Code"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Checker"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Test échoué"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Commentaire(s)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Exemples"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Définitions"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Synonymes"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Température ressentie"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Réponses"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Télécharger les résultats"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Essayez de chercher :"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Messages des moteurs de recherche"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "secondes"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "URL de recherche"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Copié"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Copier"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Suggestions"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Langue de recherche"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Langue par défaut"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Détection automatique"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Recherche sécurisée"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Stricte"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Modérée"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Désactivé"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Intervalle de temps"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "À tout moment"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Dernières 24h"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Semaine précédente"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Mois précédent"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Année précédente"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Information !"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "il n'y a pas de cookies définis pour le moment."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Désolé !"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Aucun résultat trouvé. Vous pouvez essayer de :"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Il n'y a plus d'autres résultats. Vous pouvez essayer de :"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Rafraîchir la page."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Faire une autre requête ou sélectionnez une autre catégorie (ci-dessus)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Changez le moteur de recherche utilisé dans les préférences :"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Basculer sur une autre instance :"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Cherchez avec une autre requête ou sélectionnez une autre catégorie."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Retour à la page précédente en utilisant le bouton de page précédente."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Autoriser"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Mots-clés (premier mot dans la requête)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Nom"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Description"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Liste des modules de réponse instantanée de SearXNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Voici la liste des plugins."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Complétion automatique"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Chercher au fil de la saisie"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Centrer"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Affiche les résultats au centre de la page (similaire au thème Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Les cookies et leurs valeurs que SearXNG stocke sur votre ordinateur sont"
" énumérés ci-dessous."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Avec cette liste, vous pouvez juger de la transparence de searx."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Nom du cookie"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Valeur"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Adresse de recherche des réglages actuels"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Note : utiliser des réglages personnalisés dans l'adresse de recherche "
"peut réduire la vie privée en donnant accès à certaines données aux sites"
" des résultats sélectionnés."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL pour restaurer vos préférences dans un autre navigateur"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Un lien contenant vos préférences. Ce lien peut être utilisé pour "
"restaurer vos paramètres sur un autre appareil."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Copier le hash des préférences"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Insérer le hash de préférences copié à restaurer (sans l'URL)"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hash des préférences"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Identifiant d'objet numérique (ION)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Résolveur Open Access DOI"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Sélectionner le service utilisé pour la réécriture par DOI"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Cet onglet n'existe pas dans l'interface utilisateur, mais vous pouvez "
"effectuer des recherches dans ces moteurs grâce à ses !bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Activer tout"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Désactiver tout"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Supporte la langue sélectionnée"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Poids"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Temps max"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Résolveur de Favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Affiche les favicons à côté des résultats de recherche"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Ces paramètres sont stockés dans vos cookies ; ceci nous permet de ne pas"
" collecter vos données."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Ces cookies existent pour votre confort d'utilisation, nous ne les "
"utilisons pas pour vous espionner."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Enregistrer"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Remettre les valeurs par défaut"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Retour"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Raccourcis clavier"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Comme-vim"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Parcourir les résultats avec les raccourcis clavier (nécessite "
"Javascript). Pressez \"h\" sur la page principale pour obtenir de l'aide."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy d'images"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Proxifier les images à travers SearXNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Défilement infini"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Charge automatiquement la page suivante quand vous arrivez en bas de la "
"page"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Dans quelle langue préférez-vous effectuer la recherche ?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Choisissez détection automatique pour laisser SearXNG détecter la langue "
"de votre recherche."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Méthode HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Modifier le mode de soumission des formulaires"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Requête dans le titre de la page"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Activer pour inclure la requête utilisateur dans le titre de la page "
"HTML. Votre navigateur peut enregistrer ce titre de page"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Résultats dans de nouveaux onglets"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Ouvrir les liens de résultats dans un nouvel onglet"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Filtrer le contenu"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Lancer la recherche lors du choix d'une catégorie"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Effectuer la recherche immédiatement si une catégorie est sélectionnée. "
"Désactiver pour sélectionner de plusieurs catégories"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Thème"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Change l'apparence de SearXNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Style du thème"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Choisissez auto pour respecter les paramètres de votre navigateur"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Jetons de moteur"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Jetons d'accès pour les moteurs privés"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Langue de l'interface"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Changer la langue d'affichage"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "formatage d'URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Jolie"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Complete"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Hote"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Modifier le formatage de l'URL du résultat"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "dépôts"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "afficher le média"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "cacher le media"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ce site n'a pas fourni de description."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Taille du fichier"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Date"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Type"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Résolution"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Moteur"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Voir la source"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adresse"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "afficher la carte"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "cacher la carte"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Version"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Mainteneur"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Mis à jour le"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Tags"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popularité"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licence"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projet"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Page d'accueil du projet"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Date de publication"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Journal"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Rédacteur·rice en chef"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Éditeur"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "lien magnet"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "fichier torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Seeder"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Nombre de fichiers"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "afficher la vidéo"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "cacher la vidéo"

#~ msgid "Engine time (sec)"
#~ msgstr "Temps du moteur (sec)"

#~ msgid "Page loads (sec)"
#~ msgstr "Chargement de la page (sec)"

#~ msgid "Errors"
#~ msgstr "Erreur"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA nécessaire"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Réécrire les liens HTTP en HTTPS si possible"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Les résultats sont ouvert dans la "
#~ "même fenêtre par défaut. Cette extension"
#~ " change le comportement par défaut "
#~ "pour ouvrir les liens dans des "
#~ "nouveaux onglets ou fenêtres (Javascript "
#~ "est nécessaire)"

#~ msgid "Color"
#~ msgstr "Couleur"

#~ msgid "Blue (default)"
#~ msgstr "Bleu (défaut)"

#~ msgid "Violet"
#~ msgstr "Violet"

#~ msgid "Green"
#~ msgstr "Vert"

#~ msgid "Cyan"
#~ msgstr "Cyan"

#~ msgid "Orange"
#~ msgstr "Orange"

#~ msgid "Red"
#~ msgstr "Rouge"

#~ msgid "Category"
#~ msgstr "Catégorie"

#~ msgid "Block"
#~ msgstr "Bloquer"

#~ msgid "original context"
#~ msgstr "contexte original"

#~ msgid "Plugins"
#~ msgstr "Plugins"

#~ msgid "Answerers"
#~ msgstr "Réponses instantanées"

#~ msgid "Avg. time"
#~ msgstr "Temps moy."

#~ msgid "show details"
#~ msgstr "afficher les détails"

#~ msgid "hide details"
#~ msgstr "cacher les détails"

#~ msgid "Load more..."
#~ msgstr "Afficher plus..."

#~ msgid "Loading..."
#~ msgstr "Chargement…"

#~ msgid "Change searx layout"
#~ msgstr "Modifier l'affichage de searx"

#~ msgid "Proxying image results through searx"
#~ msgstr "Proxifier les images de résultats à travers searx"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Voici la liste des module de searx produisant une réponse instantanée."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "C'est une liste de cookies et de"
#~ " leurs valeurs que searx enregistre "
#~ "sur votre ordinateur."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Avec cette liste, vous pouvez juger de la transparence de searx."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Il semble que ce soit la première fois que vous utilisez searx."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""
#~ "Veuillez réessayer ultérieurement, ou utiliser"
#~ " une instance différente de searx."

#~ msgid "Themes"
#~ msgstr "Thème"

#~ msgid "Reliablity"
#~ msgstr "Fiabilité"

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Méthode"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Paramètres avancés"

#~ msgid "Close"
#~ msgstr "Fermer"

#~ msgid "Language"
#~ msgstr "Langue"

#~ msgid "broken"
#~ msgstr "non fonctionnel"

#~ msgid "supported"
#~ msgstr "pris en charge"

#~ msgid "not supported"
#~ msgstr "non pris en charge"

#~ msgid "about"
#~ msgstr "À propos"

#~ msgid "Avg."
#~ msgstr "Moy."

#~ msgid "User Interface"
#~ msgstr "Interface utilisateur"

#~ msgid "Choose style for this theme"
#~ msgstr "Choisir un style pour ce thème"

#~ msgid "Style"
#~ msgstr "Style"

#~ msgid "Show advanced settings"
#~ msgstr "Afficher les paramètres avancés"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Par défaut, afficher les paramètres avancés sur la page d'accueil"

#~ msgid "Allow all"
#~ msgstr "Tout autoriser"

#~ msgid "Disable all"
#~ msgstr "Tout désactiver"

#~ msgid "Selected language"
#~ msgstr "Langue choisie"

#~ msgid "Query"
#~ msgstr "Requête"

#~ msgid "save"
#~ msgstr "enregistrer"

#~ msgid "back"
#~ msgstr "retour"

#~ msgid "Links"
#~ msgstr "Liens"

#~ msgid "RSS subscription"
#~ msgstr "Abonnement RSS"

#~ msgid "Search results"
#~ msgstr "Résultats de recherche"

#~ msgid "next page"
#~ msgstr "page suivante"

#~ msgid "previous page"
#~ msgstr "page précédente"

#~ msgid "Start search"
#~ msgstr "Lancer une recherche"

#~ msgid "Clear search"
#~ msgstr "Effacer la recherche"

#~ msgid "Clear"
#~ msgstr "Effacer"

#~ msgid "stats"
#~ msgstr "statistiques"

#~ msgid "Heads up!"
#~ msgstr "Astuces !"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr "Il semblerait que vous utilisez SearXNG pour la première fois."

#~ msgid "Well done!"
#~ msgstr "Bravo !"

#~ msgid "Settings saved successfully."
#~ msgstr "Les préférences ont été sauvegardées avec succès."

#~ msgid "Oh snap!"
#~ msgstr "Oups !"

#~ msgid "Something went wrong."
#~ msgstr "Il y a un problème."

#~ msgid "Date"
#~ msgstr "Date"

#~ msgid "Type"
#~ msgstr "Type"

#~ msgid "Get image"
#~ msgstr "Voir l'image"

#~ msgid "Center Alignment"
#~ msgstr "Centré"

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""
#~ "Affiche les résultats au centre de "
#~ "la page (mise en page du thème "
#~ "Oscar)."

#~ msgid "preferences"
#~ msgstr "préférences"

#~ msgid "Scores per result"
#~ msgstr "Score par résultat"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "un métamoteur de recherche hackable et respectueux de la vie privée"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Aucun résumé disponible pour cette publication."

#~ msgid "Self Informations"
#~ msgstr "Informations sur le navigateur"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Permet de choisir comment la recherche"
#~ " est envoyée, <a "
#~ "href=\"https://fr.wikipedia.org/wiki/Hypertext_Transfer_Protocol#M.C3.A9thodes\""
#~ " rel=\"external\">en savoir plus sur les"
#~ " méthodes HTTP</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Ce plugin vérifie si l'adresse de "
#~ "la requête est un nœud de sortie"
#~ " TOR, et informe l'utilisateur si "
#~ "c'est le cas, comme check.torproject.org "
#~ "mais depuis searxng."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "La liste des nœuds de sortie TOR"
#~ " (https://check.torproject.org/exit-addresses) est "
#~ "inaccessible."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Vous utilisez TOR. Votre adresse IP semble être : {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Vous n'utilisez pas TOR. Votre adresse ip semble être : {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""
#~ "Vous utilisez Tor. Il semble que "
#~ "vous avez cette adresse IP externe "
#~ ": {ip_address}."

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""
#~ "Vous n’utilisez pas Tor. Vous avez "
#~ "cette adresse IP externe : {ip_address}."

#~ msgid "Autodetect search language"
#~ msgstr "Détecter automatiquement la langue de la recherche"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Détecter automatiquement la langue de la recherche et y passer."

#~ msgid "others"
#~ msgstr "autres"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Cet onglet n'apparaît pas dans les "
#~ "résultats de recherche, mais vous pouvez"
#~ " effectuer des recherches dans les "
#~ "moteurs répertoriés ici via bangs."

#~ msgid "Shortcut"
#~ msgstr "Raccourci"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Cet onglet n'existe pas dans l'interface"
#~ " utilisateur, mais vous pouvez effectuer"
#~ " des recherches dans ces moteurs "
#~ "grâce à ses !bangs."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Les moteurs ne peuvent récupérer de résultats."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "Veuillez réessayer ultérieurement ou trouver"
#~ " une autre instance SearXNG."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Rediriger vers les versions des articles"
#~ " en libre accès lorsqu'elles sont "
#~ "disponibles (nécessite un plugin)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Permet de choisir comment la recherche"
#~ " est envoyée, <a "
#~ "href=\"https://fr.wikipedia.org/wiki/Hypertext_Transfer_Protocol#M.C3.A9thodes\""
#~ " rel=\"external\">en savoir plus sur les"
#~ " méthodes HTTP</a>"

#~ msgid "On"
#~ msgstr "Activé"

#~ msgid "Off"
#~ msgstr "Désactivé"

#~ msgid "Enabled"
#~ msgstr "Activé"

#~ msgid "Disabled"
#~ msgstr "Désactivé"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Exécute la recherche immédiatement si "
#~ "une catégorie est sélectionnée. Désactiver "
#~ "pour sélectionner plusieurs catégories "
#~ "(nécessite JavaScript)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Raccourcis clavier comme Vim"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Parcourez les résultats de recherche "
#~ "avec des raccourcis clavier similaires à"
#~ " Vim (Javascript est nécessaire. Appuyez"
#~ " sur \"h\" dans la fenêtre principale"
#~ " de résultats pour afficher de "
#~ "l'aide."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "nous n'avons trouvé aucun résultat. "
#~ "Effectuez une autre recherche ou changez"
#~ " de catégorie."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr "Réécrit ou supprime les résultats en se basant sur les noms de domaine"

#~ msgid "Bytes"
#~ msgstr "octets"

#~ msgid "kiB"
#~ msgstr "kio"

#~ msgid "MiB"
#~ msgstr "Mio"

#~ msgid "GiB"
#~ msgstr "Gio"

#~ msgid "TiB"
#~ msgstr "Tio"

#~ msgid "Hostname replace"
#~ msgstr "Remplacer les noms de domaine"

#~ msgid "Error!"
#~ msgstr "Erreur !"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Les moteurs ne peuvent pas récupérer de résultats"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Soumettre un nouveau problème sur GitHub"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Générateur de valeur aléatoire"

#~ msgid "Statistics functions"
#~ msgstr "Fonctions statistiques"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Calcule les {functions} des arguments"

#~ msgid "Get directions"
#~ msgstr "Obtenir l'itinéraire"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Affiche votre adresse IP si la "
#~ "requête est \"ip\", et affiche votre "
#~ "user-agent si la requête contient "
#~ "\"user agent\"."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Erreur lors du téléchargement des noeuds"
#~ " de sortie Tor depuis : "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Vous utilisez Tor et votre adresse "
#~ "IP externe semble être : {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Vous n'utilisez pas Tor et votre adresse IP externe est : {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Mots clés"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "La spécification de paramètres personnalisés"
#~ " dans l'URL des préférences peut être"
#~ " utilisée pour synchroniser les préférences"
#~ " entre les appareils."

#~ msgid "proxied"
#~ msgstr "proxifié"
