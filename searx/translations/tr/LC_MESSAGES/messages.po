# Turkish translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
# BouRock, 2020
# <PERSON><PERSON> <<EMAIL>>, 2014-2016
# <AUTHOR> <EMAIL>, 2014
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <PERSON><PERSON> <<EMAIL>>, 2022.
# em<PERSON><PERSON> <<EMAIL>>, 2023.
# <AUTHOR> <EMAIL>, 2023, 2024.
# <AUTHOR> <EMAIL>, 2023.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-04-02 11:50+0000\n"
"Last-Translator: return42 <<EMAIL>>\n"
"Language: tr\n"
"Language-Team: Turkish "
"<https://translate.codeberg.org/projects/searxng/searxng/tr/>\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "daha fazla alt grup olmadan"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "diğer"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "dosyalar"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "genel"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "müzik"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "sosyal medya"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "görseller"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "görüntüler"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "radyo"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "televizyon"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "bilişim"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "haberler"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "harita"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onion lar"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "bilim"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "uygulamalar"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "sözlükler"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "şarkı sözleri"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "paketler"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "soru ve cevap"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "depolar"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "yazılım vikileri"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "ağ"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "bilimsel yayınlar"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "otomatik"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "aydınlık"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "karanlık"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "siyah"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Çalışma Süresi"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Hakkında"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Ortalama sıcaklık."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Bulut örtüsü"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Durum"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Şimdiki durum"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Akşam"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "hissettiren"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Rutubet"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Max Sıcaklık.."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Min Sıcaklık"

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Gündüz"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Gece"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Öğlen"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Basınç"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "gündoğumu"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Gün batımı"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Sıcaklık"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV Endeksi"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Görünürlük"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Rüzgâr"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "aboneler"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "gönderiler"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktif kullanıcılar"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "yorumlar"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "kullanıcı"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "topluluk"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "puanlar"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "başlık"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "yazar"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "açık"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "kapalı"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "yanıtlandı"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Öğe bulunamadı"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Kaynak"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Sonraki sayfa yüklenemedi"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Geçersiz ayarlar, lütfen tercihlerinizi düzenleyin"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Geçersiz ayarlar"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "arama hatası"

#: searx/webutils.py:35
msgid "timeout"
msgstr "zaman aşımı"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "ayrıştırma hatası"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP protokol hatası"

#: searx/webutils.py:38
msgid "network error"
msgstr "bağlantı hatası"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL Hatası: Sertifika doğrulaması başarısız oldu"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "beklenmeyen çökme"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP hatası"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP bağlantı hatası"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxy hatası"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "çok fazla istek"

#: searx/webutils.py:58
msgid "access denied"
msgstr "erişim engellendi"

#: searx/webutils.py:59
msgid "server API error"
msgstr "sunucu API hatası"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Askıya alındı"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} dakika önce"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} saat, {minutes} dakika önce"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Farklı rastgele değerler üret"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Argümanların değerini hesapla {func}"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Yol tarifini haritada goster .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ESKİ)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Bu girişin yerini alan"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Kanal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bit hızı"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "oylar"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "tıklamalar"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Dil"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{firstCitationVelocityYear} yılından {lastCitationVelocityYear} yılına "
"kadar {numCitations} alıntı(lar)"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Görsel bağlantısı okunamadı. Bu desteklenmeyen bir dosya uzantısı "
"yüzünden olabilir. TinEye sadece JPEG, PNG, GIF, BMP, TIFF veya WebP "
"uzantılı görselleri destekliyor."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Fotoğraf aranmak için fazla basit. TinEye 'ın başarılı şekilde çalışması "
"için basit detaylar gereklidir."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Görsel indirilemedi."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Kitap değerlendirmesi"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Dosya kalitesi"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia kara listesi"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Ahmia kara listesinde görünen onion sonuçlarını filtrele"

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Temel Hesap Makinesi"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Arama çubuğunu kullanarak matematiksel ifadeleri hesaplayın"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash eklentisi"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Dizileri farklı özdeğerlerine çevirir."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "özdeğer"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Sunucu adı eklentisi"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr "Sunucu adını tekrar yaz, sonuçları sil veya sunucu adına göre öncelik ver"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Açık Erişim DOI yeniden yazma"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Mevcut olduğunda yayınların açık erişim sürümlerine yeniden yönlendirerek"
" ödeme ekranlarını önle"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "kişisel bilgi"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Sorgu \"ip\" ise IP adresinizi, \"user-agent\" ise kullanıcı aracınızı "
"gösterir."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "IP adresiniz: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Kullanıcı-ajanınız: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor kontrol eklentisi"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Bu eklenti, isteğin adresinin bir Tor çıkış düğümü olup olmadığını "
"kontrol eder ve varsa kullanıcıyı bilgilendirir; check.torproject.org "
"gibi, ancak SearXNG'den."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Tor çıkış noktalarinin listesini indiremiyor"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Su anda Tor'a baglisiniz ve dış IP adresiniz var gibi görünüyor"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Tor kullanmıyorsunuz ve harici IP adresiniz var"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Takip URL kaldırıcı"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Takip parametrelerini URL'den kaldır"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Birim dönüştürme eklentisi"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Birimler arasında dönüştürme"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Sayfa bulunamadı"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "%(search_page)s sayfasına git."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "arama sayfası"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Bağış"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Tercihler"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Destekleyen"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "Gizliliğiniz önemseyen, açık meta arama motoru"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Kaynak kodu"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Sorun izleyici"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Motor istatistikleri"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Açık sunucular"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Gizlilik politikası"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Sunucu sahibi ile iletişime geçin"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Arama yapmak için büyütece tıklayın"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Uzunluk"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Görüntülemeler"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Hazırlayan"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "önbellek"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "GitHub'a Yeni bir hata göndeymeye başlayin"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Lütfen Github üzerinden bu tarayıcı hakkında devam eden sorunları kontrol"
" ediniz"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Karşılaştığım hata ile ilgili süregelen bir hata bulunmadığını onaylıyorum"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr ""
"Eğer bu bir bulut üzerinde bir bilgisayar ise lütfen URL'yi hata "
"raporunda belirtin"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Yukarıdaki bilgilerle Github'da bir sorun bildirin"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "HTTPS Yok"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Hata kayıtlarını incele ve bir hata raporu gönder"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "bu motor için"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "bu kategoriler için"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "ortalama"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Başarısız kontrol deneme(leri) "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Hatalar:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Genel"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Varsayılan kategoriler"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Kullanıcı arayüzü"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Gizlilik"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Motorlar"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Şu anda kullanılan arama motorları"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Özel Arama Sorguları"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Tanımlama Bilgileri"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Sonuç sayısı"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Bilgi"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Yukarıya dön"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Önceki sayfa"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Sonraki sayfa"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Ön sayfayı göster"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Aranan..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "temizle"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "ara"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Şu anda mevcut veri yok. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Motor adı"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Skor"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Sonuç sayısı"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Yanıt süresi"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Güvenilirlik"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Toplam"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "İşlem"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Uyarılar"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Hatalar ve istisnalar"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "İstisna"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Mesaj"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Yüzde"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Parametre"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Dosya adı"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "İşlev"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kod"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Denetleyici"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Başarısız deneme"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Yorum"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Örnekler"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Tanımlar"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Eş Anlamlılar"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Yanıtlar"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Sonuçlarını indir"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Aramaya çalışılan:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Arama motorlarından gelen mesajlar"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Arama URL'si"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "kopyalandı"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "kopyala"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Öneriler"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Arama dili"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Varsayılan dil"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Özdevimli algılama"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Güvenli Arama"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Sıkı"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Orta"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Yok"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Zaman aralığı"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Herhangi bir zaman"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Geçen gün"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Geçen hafta"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Geçen ay"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Geçen yıl"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Bilgiler!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "şu anda, tanımlanmış tanımlama bilgileri yok."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Üzgünüz!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Hiç sonuç bulunamadı. Bunları deneyebilirsin:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Daha fazla sonuç yok. Şunu deneyebilirsin:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Sayfayı yenile."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Başka bir sorguyu ara veya başka bir kategori seç (yukarıda)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Ayarlardan kullanılan arama motorunu değiştir:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Başka bir sağlayıcıya geçiş yapın:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Başka bir sorgu arayın veya başka bir kategori seçin."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Önceki sayfa butonunu kullanarak bir önceki sayfaya geri dön."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "İzin ver"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Anahtar kelimeler (sorgudaki ilk kelime)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Ad"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Açıklama"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Bu, SearXNG'in anlık cevap modüllerinin listesidir."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Bu eklentilerin listesidir."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Otomatik tamamlama"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Yazarken bir şeyler bulun"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Ortaya hizalama"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Sonuçları sayfanın ortasında görüntüler (Oscar düzeni)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Bu, SearXNG'nin bilgasayarında sakladığı çerezlerin ve çerezlerin "
"değerlerinin bir listesidir."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Bu listeyle SeaXNG şeffaflığına ulaşbilirsiniz."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Tanımlama bilgisi adı"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Değer"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Şu anda kaydedilmiş tercihlerin arama URL'si"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Not: Arama URL'sinde özel ayarların belirtilmesi, tıklanan sonuç "
"sitelerine veri sızdırarak gizliliği azaltabilir."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "Seçeneklerinizi farklı bir tarayıcıda yükleme URL'i"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"Tercihlerinizi içeren bir URL. Bu URL, ayarlarınızı farklı bir cihazda "
"geri yüklemek için kullanılabilir."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Ayarlar hash değerini kopyala"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Geri yüklemek için kopyalanan tercihleri hash (URL olmadan) ekleyin"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Hah tercihleri"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Dijital Nesne Tanımlayıcı (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Açık Erişim DOI çözümleyicisi"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "DOI rewrite tarafından kullanılan hizmeti seçin"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Bu sekme kullanıcı arayüzünde yoktur, ancak bu motorlarda ona göre arama "
"yapabilirsiniz."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Hepsini etkinleştir"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Hepsini hızmet dışı bırak"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Seçili dili destekler"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Ağırlık"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "En fazla zaman"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Favicon Çözümleyicisi"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Arama sonuçlarının yanında favsimgelerini göster"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Bu ayarlar tanımlama bilgilerinde saklanır, bu sizin hakkınızda bu "
"verileri saklamamamıza izin verir."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Bu tanımlama bilgileri size kolaylık sağlar, sizi izlemek için bu "
"çerezleri kullanmayız."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Kaydet"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Varsayılanları sıfırla"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Geri"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Kısayollar"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-tarzı"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Kısayollar ile arama sonuçlarını hareket ettirin (JavaScript gerektirir)."
" Ana sayfada veya sonuç sayfasında yardım almak için \"h\" tuşuna basın."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Görsel vekil sunucu"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Resim sonuçları SearXNG üzerinden vekil sunucu ile iletiliyor"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Sonsuz kaydırma"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Şu anki sayfanın en altına kaydırıldığında sonraki sayfayı otomatik "
"olarak yükle"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Arama için hangi dili tercih edersiniz?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"SearXNG'nin sorgunuzun dilini algılamasına izin vermek için Özdevimli "
"algıla'yı seçin."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP Metodu"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Formların nasıl gönderildiğini değiştir"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Sayfanın başındaki arama sorgusu"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Bu etkinleştirildiği zaman sonuç sayfasının başlığı arama sonuçlarınızı "
"da içerir. Tarayıcınız bu başlığı kaydedebilir"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Sonuçlar yeni sekmelerde"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Yeni tarayıcı sekmelerinde sonuçta ortaya çıkan bağlantıları aç"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "İçeriği süzün"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Kategori seçimine göre ara"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Kategori seçildikten sonra aramayı hemen uygulayın. Birden fazla kategori"
" seçmek için devre dışı bırakın"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Tema"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "SearXNG düzenini değiştir"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Tema stili"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "İnternet tarayıcınızın ayarlarını kullanmak için otomatik modu seçin"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Motor belirteçleri"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Özel motorlar için erişim belirteçleri"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Arayüz dili"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Düzen dilini değiştirin"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL Formatı"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Tatlı"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Tam dolu"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Host"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Sonuç URL biçimlendirmesini değiştir"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "depo"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "medyayı göster"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "medyayı gizle"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "BU site herhangi bir açıklama sağlamadı."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Dosya boyutu"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Gün"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Yaz"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Çözünürlük"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Format"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Kaynağı göster"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "adres"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "haritayı göster"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "haritayı gizle"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Sürüm"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Sahibi"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Güncellenme tarihi"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Etiketler"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Popülerlik"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Lisans"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Proje"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Proje ana sayfası"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Yayınlanma tarihi"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Günlük"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Editör"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Yayımlayıcı"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "magnet bağlantısı"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent dosyası"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Gönderenler"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Çekenler"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Dosya Sayısı"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "görüntüyü göster"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "görüntüyü gizle"

#~ msgid "Engine time (sec)"
#~ msgstr "Motor süresi (san)"

#~ msgid "Page loads (sec)"
#~ msgstr "Sayfa yüklemeleri (san)"

#~ msgid "Errors"
#~ msgstr "Hatalar"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA gerekli"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "Mümkünse HTTP bağlantıları HTTPS olarak yeniden yaz"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "Sonuçlar varsayılan olarak aynı pencerede "
#~ "açılır. Bu eklenti, bağlantıları yeni "
#~ "sekmelerde/pencerelerde açmak için varsayılan "
#~ "davranışın üzerine yazar. (JavaScript gerekli)"

#~ msgid "Color"
#~ msgstr "Renk"

#~ msgid "Blue (default)"
#~ msgstr "Mavi (varsayılan)"

#~ msgid "Violet"
#~ msgstr "Mor"

#~ msgid "Green"
#~ msgstr "Yeşil"

#~ msgid "Cyan"
#~ msgstr "Camgöbeği"

#~ msgid "Orange"
#~ msgstr "Turuncu"

#~ msgid "Red"
#~ msgstr "Kırmızı"

#~ msgid "Category"
#~ msgstr "Kategori"

#~ msgid "Block"
#~ msgstr "Engelle"

#~ msgid "original context"
#~ msgstr "orijinal içerik"

#~ msgid "Plugins"
#~ msgstr "Eklentiler"

#~ msgid "Answerers"
#~ msgstr "Yanıtlayanlar"

#~ msgid "Avg. time"
#~ msgstr "Ort. zaman"

#~ msgid "show details"
#~ msgstr "ayrıntıları göster"

#~ msgid "hide details"
#~ msgstr "ayrıntıları gizle"

#~ msgid "Load more..."
#~ msgstr "Daha fazla yükle..."

#~ msgid "Loading..."
#~ msgstr "Yükleniyor..."

#~ msgid "Change searx layout"
#~ msgstr "Searx düzenini değiştirin"

#~ msgid "Proxying image results through searx"
#~ msgstr "Searx aracılığıyla görsel sonuçlarını vekil sunucusu üzerinden geçirin"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Bu, searx'in anında yanıtlama modüllerinin listesidir."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""
#~ "Bu, searx'in bilgisayarınızda depoladığı "
#~ "tanımlama bilgileri ve değerleri listesidir."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Bu listeyle searx şeffaflığını değerlendirebilirsiniz."

#~ msgid "It look like you are using searx first time."
#~ msgstr "İlk defa searx kullanıyor gibi görünüyorsunuz."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Lütfen daha sonra tekrar deneyin veya başka bir searx örneği bulun."

#~ msgid "Themes"
#~ msgstr "Temalar"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Yöntem"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Gelişmiş ayarlar"

#~ msgid "Close"
#~ msgstr "Kapat"

#~ msgid "Language"
#~ msgstr "Dil"

#~ msgid "broken"
#~ msgstr "bozuk"

#~ msgid "supported"
#~ msgstr "desteklenir"

#~ msgid "not supported"
#~ msgstr "desteklenmez"

#~ msgid "about"
#~ msgstr "hakkında"

#~ msgid "Avg."
#~ msgstr "ortalama"

#~ msgid "User Interface"
#~ msgstr "kullanıcı arayüzü"

#~ msgid "Choose style for this theme"
#~ msgstr "Bu tema için stil seçin"

#~ msgid "Style"
#~ msgstr "Stil"

#~ msgid "Show advanced settings"
#~ msgstr "gelişmiş ayarları göster"

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr "Gelişmiş ayarlar panelini ana sayfada varsayılan olarak göster"

#~ msgid "Allow all"
#~ msgstr "Tümüne izin ver"

#~ msgid "Disable all"
#~ msgstr "Tümünü etkisizleştir"

#~ msgid "Selected language"
#~ msgstr "Seçilen dil"

#~ msgid "Query"
#~ msgstr "Sorgu"

#~ msgid "save"
#~ msgstr "kaydet"

#~ msgid "back"
#~ msgstr "geri"

#~ msgid "Links"
#~ msgstr "Bağlantılar"

#~ msgid "RSS subscription"
#~ msgstr "RSS aboneliği"

#~ msgid "Search results"
#~ msgstr "Arama sonuçları"

#~ msgid "next page"
#~ msgstr "sonraki sayfa"

#~ msgid "previous page"
#~ msgstr "önceki sayfa"

#~ msgid "Start search"
#~ msgstr "Aramayı başlat"

#~ msgid "Clear search"
#~ msgstr "Aramayı temizle"

#~ msgid "Clear"
#~ msgstr "Temizle"

#~ msgid "stats"
#~ msgstr "istatistikler"

#~ msgid "Heads up!"
#~ msgstr "Dikkat et!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Aferin!"

#~ msgid "Settings saved successfully."
#~ msgstr "Ayarlar başarılı olarak kaydedildi."

#~ msgid "Oh snap!"
#~ msgstr "Hay aksi!"

#~ msgid "Something went wrong."
#~ msgstr "Bir şeyler ters gitti."

#~ msgid "Date"
#~ msgstr "Tarih"

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Görseli al"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "tercihler"

#~ msgid "Scores per result"
#~ msgstr "Sonuç başına skor"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "gizliliğe saygılı, kurcalanabilir bir meta arama motoru"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Bu yayın için özet mevcut değil."

#~ msgid "Self Informations"
#~ msgstr "kişisel bilgileri"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Formların nasıl gönderildiğini değiştirin, <a"
#~ " "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">istek yöntemleri hakkında daha"
#~ " fazla bilgi edinin</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Bu eklenti istek adresinin TOR çıkış "
#~ "düğümü olup olmadığını kontrol eder ve"
#~ " öyleyse kullanıcıya haber verir, "
#~ "check.torproject.org benzeri ama searxng'den."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "TOR cıkış düğümü listesine "
#~ "(https://check.torproject.org/exit-addresses) "
#~ "ulaşılamıyor."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "TOR kullanıyorsunuz. Gözüken IP adresiniz: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "TOR kullanmıyorsunuz. Gözüken IP adresiniz: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr "Arama dilini otomatik algıla"

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr "Arama dilini otomatik algıla ve değiştir."

#~ msgid "others"
#~ msgstr "diğer"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Bu sekme arama sonuçlarında görünmüyor, "
#~ "ama listelenen motorlarını \"bang\"ler ile "
#~ "arayabilirsiniz."

#~ msgid "Shortcut"
#~ msgstr "Kısayol"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Bu sekme kullanıcı arayüzünde yoktur, "
#~ "ancak bu motorlarda ona göre arama "
#~ "yapabilirsiniz."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Motorlar sonuçları alamıyor."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Lütfen daha sonra tekrar deneyin veya başka bir SearXNG örneği bulun."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Mümkün olduğunda yayınların açık erişimli "
#~ "sürümlerine yeniden yönlendirir (eklenti "
#~ "gerekli)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Formların gönderilme şeklini değiştirin, <a"
#~ " "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">istek yöntemleri hakkında daha"
#~ " fazlasını öğrenin</a>"

#~ msgid "On"
#~ msgstr "Açık"

#~ msgid "Off"
#~ msgstr "Kapalı"

#~ msgid "Enabled"
#~ msgstr "Etkinleştirildi"

#~ msgid "Disabled"
#~ msgstr "Etkisizleştirildi"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Bir kategori seçilirse hemen arama yap."
#~ " Birden çok kategori seçmek için "
#~ "devre dışı bırak. (JavaScript gerekli)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim benzeri kısayol tuşları"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Vim benzeri kısayol tuşlarıyla arama "
#~ "sonuçlarında gezinin (JavaScript gerekli). "
#~ "Yardım almak için ana sayfada veya "
#~ "sonuç sayfasında \"h\" tuşuna basın."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "herhangi bir sonuç bulamadık. Lütfen, "
#~ "başka bir sorgu kullanın veya daha "
#~ "fazla kategoride arama yapın."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Sonuçların sunucu adlarını tekrar yaz ya"
#~ " da sunucu adına göre sonuçları sil"

#~ msgid "Bytes"
#~ msgstr "Bayt"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Sunucu adını değiştir"

#~ msgid "Error!"
#~ msgstr "Hata!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Motorlar sonuçları alamıyor"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "GitHub'a Yeni bir hata göndeymeye başlayin"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Rastgele değer üreteci"

#~ msgid "Statistics functions"
#~ msgstr "İstatistik fonksiyonları"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Bağımsız değişkenlerin {functions} değerini hesapla"

#~ msgid "Get directions"
#~ msgstr "Yönleri al"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Sorgu \"ip\" ise IP'nizi ve sorgu "
#~ "\"kullanıcı tanıtıcısı\" içeriyorsa kullanıcı "
#~ "tanıtıcınızı görüntüler."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Tor çıkış düğümlerinin listesi şu "
#~ "adresten indirilemedi: https://check.torproject.org"
#~ "/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Tor kullanıyorsunuz ve şu harici IP "
#~ "adresine sahip olduğunuz anlaşılıyor: "
#~ "{ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Tor kullanmıyorsunuz ve şu harici IP adresine sahipsiniz: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Anahtar kelimeler"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Tercihler URL'sinde özel ayarları belirtmek,"
#~ " cihazlar arasında senkronize etmek için"
#~ " kullanılabilir."

#~ msgid "proxied"
#~ msgstr "proxylendi"

