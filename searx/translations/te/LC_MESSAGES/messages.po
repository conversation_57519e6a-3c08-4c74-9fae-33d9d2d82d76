# Telugu translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-01-09 07:08+0000\n"
"Last-Translator: Harshith-10 "
"<<EMAIL>>\n"
"Language: te\n"
"Language-Team: Telugu "
"<https://translate.codeberg.org/projects/searxng/searxng/te/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "తదుపరి ఉపసమితి లేకుండా"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "ఇతర"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "ఫైళ్ళు"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "సాధారణ"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "సంగీతం"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "సోషల్ మీడియా"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "చిత్రాలు"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "వీడియోలు"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "రేడియో"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "టీవీ"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "ఐటి"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "వార్తలు"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "పటము"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "ఉల్లిపాయ"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "విజ్ఞానశాస్త్రం"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "యాప్‌లు"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "నిఘంటువులు"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "సాహిత్యం"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "ప్యాకేజీలు"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "ప్రశ్నలు మరియు సమాధానాలు"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "రెపోలు"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "సాఫ్ట్‌వేర్ వికీ"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "వెబ్"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "శాస్త్రీయ ప్రచురణలు"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "ఆటో"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "వెలుగు"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "చీకటి"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "నలుపు"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "సేవ లభ్యత సమయం"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "గురించి"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "సగటు ఉష్ణోగ్రత"

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "మేఘం కమ్మటం"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "పరిస్థితి"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "ప్రస్తుత పరిస్థితి"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "సాయంత్రం"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "అనిపిస్తుంది"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "తేమ"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "గరిష్ట ఉష్ణోగ్రత."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "కనిష్ట ఉష్ణోగ్రత."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "ఉదయం"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "రాత్రి"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "మధ్యాహ్నం"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "వాతావరణ పీడనం"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "సూర్యోదయం"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "సూర్యాస్తమయం"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "ఉష్ణోగ్రత"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV సూచిక"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "విసిబిలిటీ"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "గాలి"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "సుబ్స్చ్రిబెర్ లు"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "పోస్ట్ లు"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "ఆక్టివ్ యూసర్ లు"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "కామెంట్ లు"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "యూసర్"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "కమ్యూనిటీ"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "పాయింట్ లు"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "టైటిల్"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "రచయిత"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "ఓపెన్"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "క్లోస్డ్"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "సమాధానమిచ్చారు"

#: searx/webapp.py:292
msgid "No item found"
msgstr "ఏమీ దొరకలేదు"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "మూలం"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "తదుపరి పేజీని లోడ్ చేయడంలో లోపం"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "చెల్లని సెట్టింగ్‌లు, దయచేసి మీ ప్రాధాన్యతలను సవరించండి"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "చెల్లని అమరికలు"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "శోధనలో లోపము"

#: searx/webutils.py:35
msgid "timeout"
msgstr "సమయం ముగిసినది"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "పార్సింగ్ లోపం"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP నియమాలలో లోపం"

#: searx/webutils.py:38
msgid "network error"
msgstr "నెట్వర్క్ లోపం"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL లోపం: సర్టిఫికేట్ ధ్రువీకరణ విఫలమైంది"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "ఊహించని లోపం"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP లోపం"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP కనెక్షన్ లోపం"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "ప్రాక్సీ లోపం"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "క్యాప్చా"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "చాలా అభ్యర్థనలు"

#: searx/webutils.py:58
msgid "access denied"
msgstr "అనుమతి లేదు"

#: searx/webutils.py:59
msgid "server API error"
msgstr "సర్వర్ API లోపం"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "రద్ధు చెయ్యబడింది"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} నిమిషము(ల) క్రిందట"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} గంట(లు), {minutes} నిమిషం(లు) క్రితం"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "విభిన్న యాదృచ్ఛిక విలువలను రూపొందించండి"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr ""

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr ""

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (వాడుకలో లేదు)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "దీని ద్వారా ఈ ఎంట్రీ భర్తీ చేయబడింది"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "ఛానెల్"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "బిట్రేట్"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "ఓట్లు"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "క్లిక్‌లు"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "భాష"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{firstCitationVelocityYear} సంవత్సరం నుండి {lastCitationVelocityYear} "
"వరకు {numCitations}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"ఆ చిత్ర urlని చదవడం సాధ్యపడలేదు. ఇది సపోర్ట్ లేని ఫైల్ ఫార్మాట్ వల్ల అయి "
"ఉండవచ్చు. TinEye JPEG, PNG, GIF, BMP, TIFF లేదా WebP చిత్రాలకు మాత్రమే "
"సపోర్ట్ ఇస్తుంది."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"చిత్రం సరిపోలికలను కనుగొనడానికి చాలా సాధారణంగా ఉంది. మ్యాచ్‌లను సక్రమంగా "
"గుర్తించడానికి TinEyeకి ప్రాథమిక స్థాయి దృశ్య వివరాలు అవసరం."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "చిత్రాన్ని డౌన్‌లోడ్ చేయడం సాధ్యపడలేదు."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "పుస్తకం రేటింగు"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "ఫైలు క్వాలిటీ"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr ""

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr ""

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr ""

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "సెర్చ్ బార్ ద్వారా గణిత సమీకరణలను లెక్కించండి"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr ""

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "స్ట్రింగ్‌లను విభిన్న హాష్ డైజెస్ట్‌లుగా మారుస్తుంది."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "హాష్ డైజెస్ట్"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "హోస్ట్ పేర్ల ప్లగిన్"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"హోస్ట్ పేర్లను తిరిగి వ్రాయండి, ఫలితాలను తొలగించండి లేదా హోస్ట్ పేరు "
"ఆధారంగా వాటికి ప్రాధాన్యత ఇవ్వండి"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "ఓపెన్ యాక్సెస్ DOI రీరైట్"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"అందుబాటులో ఉన్నప్పుడు ప్రచురణల యొక్క ఓపెన్-యాక్సెస్ వెర్షన్‌లకు దారి "
"మళ్లించడం ద్వారా పేవాల్‌లను నివారించండి"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "స్వీయ సమాచారం"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "మీ ఐపీ: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "మీ యూజర్-ఏజెంట్: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "టోర్ చెక్ ప్లగిన్"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"ఈ ప్లగిన్ అభ్యర్థన చిరునామా టోర్ ఎగ్జిట్-నోడ్ అవునా కాదా అని తనిఖీ "
"చేస్తుంది మరియు అది check.torproject.org లాగా, కానీ SearXNG నుండి "
"వినియోగదారుకు తెలియజేస్తుంది."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr ""

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr ""

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "ట్రాకర్ URL రిమూవర్"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "తిరిగి వచ్చిన URL నుండి ట్రాకర్స్ ఆర్గ్యుమెంట్‌లను తీసివేయండి"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr ""

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "యూనిట్లను మార్చండి"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "పుట దొరకలేదు"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "%(search_page)sకు వెళ్ళు"

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "శోధన పుట"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "దానం చేయండి"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "అభిరుచులు"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "ద్వారా ఆధారితం"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "గోప్యతను గౌరవించే, ఓపెన్ మెటా సెర్చ్ ఇంజిన్"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "సోర్స్ కోడ్"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "ఇష్యూ ట్రాకర్"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "ఇంజిన్ గణాంకాలు"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "పబ్లిక్ ఇన్స్తంచెస్"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "గోప్యతా విధానం"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "నిర్వహించేవాడిని సంప్రదించండి"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "శోధనను నిర్వహించడానికి మాగ్నిఫైయర్‌పై క్లిక్ చేయండి"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "పొడవు"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "వ్యూస్"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "రచయిత"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "కాష్ చేయబడింది"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "GitHub లో కొత్త సంచికను సమర్పించడం ప్రారంభించండి"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "దయచేసి ఈ ఇంజిన్ గురించి ఇప్పటికే ఉన్న బగ్‌ల కోసం GitHubలో తనిఖీ చేయండి"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "నేను ఎదుర్కొన్న సమస్యకు సంబంధించి బగ్ ఏదీ లేదని నేను ధృవీకరిస్తున్నాను"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "ఇది పబ్లిక్ ఉదాహరణ అయితే, దయచేసి బగ్ నివేదికలో URL ని పేర్కొనండి"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "పై సమాచారంతో సహా Github పై కొత్త సంచికను సమర్పించండి"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "HTTPS లేదు"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "ఎర్రర్ లాగ్‌లను వీక్షించండి మరియు బగ్ నివేదికను సమర్పించండి"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "ఈ ఇంజిన్ కి !bang"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "దాని వర్గాలకు !bang"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "మధ్యస్థ"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "విఫలమైన చెకర్ పరీక్ష(లు): "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "లోపాలు:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "సాధారణ"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "నిష్క్రియ వర్గాలు"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "వినియోగ మార్గము"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "ఆంతరంగికత"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "యంత్రాలు"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "ప్రస్తుతం ఉపయోగించబడుతున్న శోధన యంత్రాలు"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "ప్రత్యేక ప్రశ్నలు"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "కుకీలు"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "ఫలితముల సంఖ్య"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "సమాచారం"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "తిరిగి పైకి"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "ముందు పేజి"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "తరువాతి పేజీ"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "మొదటి పేజీని ప్రదర్శించండి"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "శోధించు..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "తొలగించండి"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "వెతకండి"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "ప్రస్తుతం డేటా అందుబాటులో లేదు."

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "యంత్రం పేరు"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "స్కోర్లు"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "ఫలితాల గణన"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "ప్రతిస్పందన సమయం"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "విశ్వసనీయత"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "మొత్తం"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "ప్రాసెసింగ్"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "హెచ్చరికలు"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "లోపాలు మరియు మినహాయింపులు"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "మినహాయింపు"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "సందేశం"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "శాతం"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "పరిమితి"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "ఫైల్ పేరు"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "ఫంక్షన్"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "కోడ్"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "చెకర్"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "పరీక్ష విఫలమైంది"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "వ్యాఖ్య(లు)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "ఉదాహరణలు"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr ""

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "పర్యాయపదాలు"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "జవాబులు"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "ఫలితాలను దింపుకోండి"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "దీనికొరకు శోధించండి:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "శోధన ఇంజిన్ల నుండి సందేశాలు"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "క్షణాలు"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "శోధన URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "కాపీ చేయబడింది"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "కాపీ"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "సూచనలు"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "శోధన భాష"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "నిష్క్రియ భాష"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "ఆటో-డిటెక్ట్"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "సురక్షితశోధన"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "కఠినమైన"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "మితమైన"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "ఏమీ లేదు"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "కాల శ్రేణి"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "ఎప్పుడైనా"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "క్రిందటి రోజు"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "క్రిందటి వారం"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "క్రిందటి నెల"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "క్రిందటి సంవత్సరం"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "సమాచారం!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "ప్రస్తుతం, కుకీలు ఏవీ నిర్వచించబడలేదు."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "క్షమించండి!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "ఫలితాలు ఏవీ కనుగొనబడలేదు. మీరు వీటిని ప్రయత్నించవచ్చు:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "ఇక ఫలితాలు లేవు. మీరు వీటిని ప్రయత్నించవచ్చు:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "పేజీని రిఫ్రెష్ చేయండి."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "మరొక ప్రశ్న కోసం శోధించండి లేదా మరొక వర్గాన్ని ఎంచుకోండి (పైన)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "ప్రాధాన్యతలలో ఉపయోగించే శోధన ఇంజిన్‌ను మార్చండి:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "మరొక ఇంస్టాన్సుకు మారండి:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "మరొక ప్రశ్న కోసం శోధించండి లేదా మరొక వర్గాన్ని ఎంచుకోండి."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "మునుపటి పేజీ బటన్‌ను ఉపయోగించి మునుపటి పేజీకి తిరిగి వెళ్ళండి."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "అనుమతించు"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr ""

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "పేరు"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "వర్ణన"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "ఇది SearXNG యొక్క తక్షణ సమాధాన మాడ్యూల్‌ల జాబితా."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "ఇది ప్లగిన్‌ల జాబితా."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "ఆటోకంప్లేటే"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "టైపు చేస్తూ శోధించు"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "మధ్య అమరిక"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "పేజీ మధ్యలో ఫలితాలను ప్రదర్శిస్తుంది (ఆస్కార్ లేఅవుట్)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"ఇది మీ కంప్యూటర్‌లో SearXNG నిల్వ చేస్తున్న కుక్కీల జాబితా మరియు వాటి "
"విలువలు."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "ఆ జాబితాతో, మీరు SearXNG పారదర్శకతను అంచనా వేయవచ్చు."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "కుకీ పేరు"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "విలువ"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "ప్రస్తుతం సేవ్ చేయబడిన ప్రాధాన్యతల URLని శోధించండి"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"గమనిక: శోధన URLలో అనుకూల సెట్టింగ్‌లను పేర్కొనడం క్లిక్ చేసిన ఫలితాల "
"సైట్‌లకు డేటాను లీక్ చేయడం ద్వారా గోప్యతను తగ్గిస్తుంది."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "మరొక బ్రౌజర్‌లో మీ ప్రాధాన్యతలను పునరుద్ధరించడానికి URL"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "ప్రాధాన్యతల హాష్‌ను కాపీ చేయి"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""
"పునరుద్ధరించడానికి కాపీ చేయబడిన ప్రాధాన్యతల హాష్ (URL లేకుండా) నమోదు "
"చేయండి"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "ప్రాధాన్యతల హాష్"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr ""

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "యాక్సెస్ DOI పరిష్కరిణిని తెరవండి"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "DOI తిరిగి వ్రాయడానికి ఉపయోగించే సేవను ఎంచుకోండి"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"ఈ ట్యాబ్ యూజర్ ఇంటర్‌ఫేస్‌లో లేదు, కానీ మీరు ఈ ఇంజిన్‌లలో దీని ద్వారా "
"శోధించవచ్చు: !bangs"

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "అన్నిటిని ఎనేబుల్ చేయి"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "అన్నిటిని డిసేబుల్ చేయి"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "ఎంచుకున్న భాషకు మద్దతు ఇస్తుంది"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "బరువు"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "గరిష్ఠ సమయం"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "ఫేవికాన్ రిసాల్వర్"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "శోధన ఫలితాల దగ్గర ఫేవికాన్‌లను ప్రదర్శించు"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"ఈ సెట్టింగ్‌లు మీ కుక్కీలలో నిల్వ చేయబడ్డాయి, ఇది మీ గురించిన ఈ డేటాను "
"నిల్వ చేయకుండా ఉండటానికి మాకు అనుమతిస్తుంది."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"ఈ కుక్కీలు మీ సౌకర్యార్థం, మిమ్మల్ని ట్రాక్ చేయడానికి మేము ఈ కుక్కీలను "
"ఉపయోగించము."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "సేవ్ చేయండి"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "నిష్క్రియాలకు అమర్చు"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "వెనుకకు"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "హాట్‌కీలు"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "విమ్-లాంటిది"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"హాట్‌కీలతో శోధన ఫలితాలను నావిగేట్ చేయండి (జావాస్క్రిప్ట్ అవసరం). సహాయం "
"పొందడానికి ప్రధాన లేదా ఫలిత పేజీలో \"h\" కీని నొక్కండి."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "చిత్రం ప్రాక్సీ"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "SearXNG ద్వారా ఇమేజ్ ఫలితాలను ప్రాక్సీ చేస్తోంది"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "అనంతమైన స్క్రోల్"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"ప్రస్తుత పేజీ దిగువకు స్క్రోల్ చేస్తున్నప్పుడు తదుపరి పేజీని స్వయంచాలకంగా"
" లోడ్ చేయండి"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "శోధన కోసం మీరు ఏ భాషను ఇష్టపడతారు?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"మీ ప్రశ్న యొక్క భాషను SearXNG గుర్తించేలా చేయడానికి ఆటో-డిటెక్ట్‌ను "
"ఎంచుకోండి."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP పద్ధతి"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "ఫారమ్‌లను ఎలా సమర్పించాలో మార్చండి"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "పేజీ శీర్షికలో ప్రశ్న"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"ప్రారంభించబడినప్పుడు, ఫలిత పేజీ యొక్క శీర్షిక మీ ప్రశ్నను కలిగి ఉంటుంది. "
"మీ బ్రౌజర్ ఈ శీర్షికను రికార్డ్ చేయగలదు"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "కొత్త ట్యాబ్‌లలో ఫలితాలు"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "కొత్త బ్రౌజర్ ట్యాబ్‌లలో ఫలితాల లింక్‌లను తెరవండి"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "విషయాలను వడకట్టు"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "వర్గం ఎంపికపై శోధించండి"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"ఒక వర్గం ఎంచుకోబడితే వెంటనే శోధనను నిర్వహించండి. బహుళ వర్గాలను ఎంచుకోవడం "
"కోసం నిలిపివేయండి"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "థీమ్"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "SearXNG లేఅవుట్‌ని మార్చండి"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "థీమ్ శైలి"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "మీ బ్రౌజర్ సెట్టింగ్‌లను అనుసరించడానికి ఆటో ఎంచుకోండి"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "ఇంజిన్ టోకెన్లు"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "ప్రైవేట్ ఇంజిన్ల కోసం యాక్సెస్ టోకెన్లు"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "వినిమయసీమ భాష"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "వినిమయసీమ యొక్క భాషను మార్చు"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "URL ఫార్మాటింగ్"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "ప్రెట్టి"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "పూర్తి"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "హోస్ట్"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "ఫలిత URL ఫార్మాటింగ్‌ను మార్చండి"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "రిపోజిటరీ"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "మీడియా చూపించు"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "మీడియాను దాచండి"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "ఈ సైట్ ఎలాంటి వివరణను అందించలేదు."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "ఫైల్ పరిమాణం"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "తేదీ"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "రకం"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "స్పష్టత"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "ఫార్మాట్"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "ఇంజిన్"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "మూలాన్ని వీక్షించండి"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "చిరునామా"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "మ్యాప్ చూపించు"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "మ్యాప్‌ను దాచండి"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "వెర్షన్"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "నిర్వహణదారు"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "నవీకరించబడిన సమయం"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "ట్యాగ్‌లు"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "ప్రజాదరణ"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "లైసెన్స్"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "ప్రాజెక్ట్"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "ప్రాజెక్ట్ హోమ్‌పేజీ"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "ప్రచురించబడిన తేదీ"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "జర్నల్"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "ఎడిటర్"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "ప్రచురణకర్త"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "మాగ్నెట్ లింక్"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "టోరెంట్ ఫైల్"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "సీడర్"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "లీచర్"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "ఫైళ్ళ సంఖ్య"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "వీడియో చూపించు"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "వీడియోను దాచిపెట్టు"

#~ msgid "Engine time (sec)"
#~ msgstr ""

#~ msgid "Page loads (sec)"
#~ msgstr ""

#~ msgid "Errors"
#~ msgstr "దోషములు"

#~ msgid "CAPTCHA required"
#~ msgstr ""

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr ""

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""

#~ msgid "Color"
#~ msgstr "రంగు"

#~ msgid "Blue (default)"
#~ msgstr "నీలం (నిష్క్రియం)"

#~ msgid "Violet"
#~ msgstr "ఊదారంగు"

#~ msgid "Green"
#~ msgstr "ఆకుపచ్చ"

#~ msgid "Cyan"
#~ msgstr " ముదురు నీలం"

#~ msgid "Orange"
#~ msgstr "నారింజ"

#~ msgid "Red"
#~ msgstr "ఎరుపు"

#~ msgid "Category"
#~ msgstr "వర్గము"

#~ msgid "Block"
#~ msgstr "అడ్డగించు"

#~ msgid "original context"
#~ msgstr ""

#~ msgid "Plugins"
#~ msgstr "ప్లగిన్లు"

#~ msgid "Answerers"
#~ msgstr "జవాబులు"

#~ msgid "Avg. time"
#~ msgstr "సగటు సమయం"

#~ msgid "show details"
#~ msgstr ""

#~ msgid "hide details"
#~ msgstr ""

#~ msgid "Load more..."
#~ msgstr ""

#~ msgid "Loading..."
#~ msgstr ""

#~ msgid "Change searx layout"
#~ msgstr ""

#~ msgid "Proxying image results through searx"
#~ msgstr ""

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr ""

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr ""

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr ""

#~ msgid "It look like you are using searx first time."
#~ msgstr ""

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr ""

#~ msgid "Themes"
#~ msgstr ""

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "విధానం"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr ""

#~ msgid "Close"
#~ msgstr "మూసివేయు"

#~ msgid "Language"
#~ msgstr ""

#~ msgid "broken"
#~ msgstr ""

#~ msgid "supported"
#~ msgstr "ఆదరించబడిన"

#~ msgid "not supported"
#~ msgstr "ఆదరణ లేని"

#~ msgid "about"
#~ msgstr "గురించి"

#~ msgid "Avg."
#~ msgstr ""

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr ""

#~ msgid "Style"
#~ msgstr "శైలి"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr ""

#~ msgid "Disable all"
#~ msgstr ""

#~ msgid "Selected language"
#~ msgstr "ఎంచుకున్న భాష"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "దాచు"

#~ msgid "back"
#~ msgstr "వెనక్కి"

#~ msgid "Links"
#~ msgstr "లంకెలు"

#~ msgid "RSS subscription"
#~ msgstr ""

#~ msgid "Search results"
#~ msgstr ""

#~ msgid "next page"
#~ msgstr "తర్వాతి పుట"

#~ msgid "previous page"
#~ msgstr "పూర్వపు పుట"

#~ msgid "Start search"
#~ msgstr "శోధన ప్రారంభించు"

#~ msgid "Clear search"
#~ msgstr ""

#~ msgid "Clear"
#~ msgstr ""

#~ msgid "stats"
#~ msgstr "స్థితి వివరణ లెక్కలు"

#~ msgid "Heads up!"
#~ msgstr "జాగ్రత్త!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "భళా!"

#~ msgid "Settings saved successfully."
#~ msgstr "ఆమరికలు విజయవంతంగా పొందుపరచబడ్డాయి."

#~ msgid "Oh snap!"
#~ msgstr "అయ్యో!"

#~ msgid "Something went wrong."
#~ msgstr "ఏదో తప్పు జరిగింది."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr ""

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "అభిరుచులు"

#~ msgid "Scores per result"
#~ msgstr "ఒక్కో ఫలితానికి స్కోర్లు"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "గోప్యతను గౌరవించే, హ్యాక్ చేయదగిన మెటా సెర్చ్ ఇంజిన్"

#~ msgid "No abstract is available for this publication."
#~ msgstr "ఈ ప్రచురణకు సంగ్రహం అందుబాటులో లేదు."

#~ msgid "Self Informations"
#~ msgstr "స్వీయ సమాచారం"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "ఫారమ్‌లను ఎలా సమర్పించాలో మార్చండి, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">అభ్యర్థన పద్ధతుల గురించి మరింత"
#~ " తెలుసుకోండి</a>"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr ""

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "ఇతర"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "ఈ ట్యాబ్ శోధన ఫలితాల కోసం చూపబడదు, "
#~ "కానీ మీరు ఇక్కడ జాబితా చేయబడిన "
#~ "ఇంజిన్‌లను బ్యాంగ్స్ ద్వారా శోధించవచ్చు."

#~ msgid "Shortcut"
#~ msgstr "సత్వరమార్గం"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""

#~ msgid "Engines cannot retrieve results."
#~ msgstr "యంత్రాలు ఫలితాలను రాబట్టలేకపోయాయి."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr ""
#~ "దయచేసి, తర్వాత మళ్లీ ప్రయత్నించండి లేదా "
#~ "మరొక SearXNG ఇన్స్తంచె కనుగొనండి."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "అందుబాటులో ఉన్నప్పుడు ప్రచురణల యొక్క "
#~ "ఓపెన్-యాక్సెస్ వెర్షన్‌లకు దారి మళ్లించండి "
#~ "(ప్లగ్ఇన్ అవసరం)"

#~ msgid "Bang"
#~ msgstr ""

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""

#~ msgid "On"
#~ msgstr "పై"

#~ msgid "Off"
#~ msgstr "ఆఫ్"

#~ msgid "Enabled"
#~ msgstr "ఎనేబుల్డ్"

#~ msgid "Disabled"
#~ msgstr "దిశల్డ్"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "వర్గం ఎంపిక చేయబడితే వెంటనే శోధనను "
#~ "నిర్వహించండి. బహుళ వర్గాలను ఎంచుకోవడానికి "
#~ "నిలిపివేయండి. (జావాస్క్రిప్ట్ అవసరం)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim లాంటి హాట్‌కీలు"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Vim లాంటి హాట్‌కీలతో శోధన ఫలితాలను "
#~ "నావిగేట్ చేయండి (జావాస్క్రిప్ట్ అవసరం). సహాయం"
#~ " పొందడానికి ప్రధాన లేదా ఫలితాల పేజీలో "
#~ "\"h\" కీని నొక్కండి."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "మేము ఏ ఫలితాలను కనుగొనలేదు. దయచేసి మరొక"
#~ " ప్రశ్నను ఉపయోగించండి లేదా మరిన్ని "
#~ "వర్గాల్లో శోధించండి."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "ఫలితాల హోస్ట్ పేర్లను తిరిగి వ్రాయండి "
#~ "లేదా హోస్ట్ పేరు ఆధారంగా ఫలితాలను "
#~ "తీసివేయండి"

#~ msgid "Bytes"
#~ msgstr "బైట్లు"

#~ msgid "kiB"
#~ msgstr "kiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "హోస్ట్ పేరు భర్తీ"

#~ msgid "Error!"
#~ msgstr "దోషం!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "యంత్రాలు ఫలితాలను రాబట్టలేకపోతున్నాయి"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "GitHub లో కొత్త సంచికను సమర్పించడం ప్రారంభించండి"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "యాదృచ్ఛిక విలువ ఉత్పత్తిదారు"

#~ msgid "Statistics functions"
#~ msgstr "సాంఖ్యకశాస్త్ర ప్రమేయాలు"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "ఆర్గ్యుమెంట్‌ల {functions} గణించండి"

#~ msgid "Get directions"
#~ msgstr "దిశలను పొందండి"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "ప్రశ్న \"ip\" అయితే మీ IPని మరియు "
#~ "ప్రశ్నలో \"యూజర్ ఏజెంట్\" ఉంటే మీ యూజర్"
#~ " ఏజెంట్‌ని ప్రదర్శిస్తుంది."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "టోర్ ఎగ్జిట్-నోడ్‌ల జాబితాను "
#~ "https://check.torproject.org/exit-addresses నుండి "
#~ "డౌన్‌లోడ్ చేయలేకపోయాము"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "మీరు Tor ఉపయోగిస్తున్నారు మరియు మీకు ఈ"
#~ " బాహ్య IP చిరునామా ఉన్నట్లు కనిపిస్తోంది:"
#~ " {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "మీరు Tor ని ఉపయోగించడం లేదు మరియు "
#~ "మీకు ఈ బాహ్య IP చిరునామా ఉంది: "
#~ "{ip_address}"

#~ msgid "Keywords"
#~ msgstr "కీలకపదాలు"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "ప్రాధాన్యతల URLలో అనుకూల సెట్టింగ్‌లను "
#~ "పేర్కొనడం ద్వారా పరికరాల్లో ప్రాధాన్యతలను "
#~ "సమకాలీకరించడానికి ఉపయోగించవచ్చు."

#~ msgid "proxied"
#~ msgstr "ప్రాక్సీడ్"

