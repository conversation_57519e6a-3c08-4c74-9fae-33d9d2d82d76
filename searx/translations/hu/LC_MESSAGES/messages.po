# Hungarian translations for .
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the  project.
#
# Translators:
# <PERSON> <<EMAIL>>, 2014-2017,2020
# <AUTHOR> <EMAIL>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016-2017
# <PERSON><PERSON> <<EMAIL>>, 2022.
# <PERSON> <<EMAIL>>, 2022, 2023.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022.
# <AUTHOR> <EMAIL>, 2023.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024,
# 2025.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2024, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version:  searx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-03-30 18:03+0000\n"
"Last-Translator: kratos <<EMAIL>>\n"
"Language: hu\n"
"Language-Team: Hungarian "
"<https://translate.codeberg.org/projects/searxng/searxng/hu/>\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "további alcsoportosítás nélkül"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "egyéb"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "fájlok"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "általános"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "zene"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "közösségi média"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "képek"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "videók"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "rádió"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "TV"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "hírek"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "térkép"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "onion hivatkozások"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "tudomány"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "alkalmazások"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "szótárak"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "dalszöveg"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "csomagok"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "kérdések és válaszok"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "tárolók"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "szoftveres wikik"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "web"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "tudományos publikációk"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "automatikus"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "világos"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "sötét"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "fekete"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Üzemidő"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Névjegy"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Átlagos hőm."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Felhő borította"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Állapot"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Jelenlegi állapot"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Este"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "érzetre"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Páratartalom"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Maximum hőm."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Minimum hőm."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Reggel"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Éjszaka"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Dél"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Nyomás"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Napfelkelte"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Naplemente"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Hőmérséklet"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "UV terhelés"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Láthatóság"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Szél"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr ""

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr ""

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "Feliratkozók"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "bejegyzések"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "aktív felhasználók"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "kommentek"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "felhasználó"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "közösség"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "pontok"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "cím"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "szerző"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "Megnyitás"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "Lezárt"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "megválaszolt"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Nincs találat"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Forrás"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Hiba a következő oldal betöltése során"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Érvénytelen beállítások, módosítsa őket"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Érvénytelen beállítások"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "keresési hiba"

#: searx/webutils.py:35
msgid "timeout"
msgstr "időtúllépés"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "feldolgozási hiba"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "HTTP protokollhiba"

#: searx/webutils.py:38
msgid "network error"
msgstr "hálózati hiba"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "SSL hiba: a tanúsítvány ellenőrzése nem sikerült"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "váratlan összeomlás"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "HTTP hiba"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "HTTP csatlakozási hiba"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "proxy hiba"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "túl sok kérés"

#: searx/webutils.py:58
msgid "access denied"
msgstr "hozzáférés megtagadva"

#: searx/webutils.py:59
msgid "server API error"
msgstr "kiszolgáló API hiba"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Felfüggesztve"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} perce"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} óra, {minutes} perce"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Különböző véletlen értékek előállítása"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "A(z) {func} értékének kiszámítása az argumentumokból"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Útvonal megjelenítése a térképen .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (elavult)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Ezt a bejegyzést leváltotta:"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Csatorna"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "bitráta:"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "szavazatok:"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "kattintások"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Nyelv"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} idézet ebben az évben: {firstCitationVelocityYear} és "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Ennek a képnek az webcíme nem olvasható. Ennek az oka a nem támogatott "
"fájlformátum lehet. A TinEye által támogatott formátumok: JPEG, PNG, GIF,"
" BMP, TIFF vagy WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"A kép túl egyszerű a kereséshez. A TinEye-nak szüksége van egy alapvető "
"vizuális részletességre a sikeres kereséshez."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "A kép nem tölthető le."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Könyv értékelése"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Fájlminőség"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Ahmia tiltólista"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Szűrje ki az Ahmia titlólistáján szereplő onion hivatkozásokat."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Alapvető számológép"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Végezzen el matematikai műveleteket a keresősávban"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Hash bővítmény"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "A szöveget különböző hash értékekké alakítja."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "hash érték"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Kiszolgálónév modul"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Írd át a kiszolgálóneveket, távolítsd el az eredményeket vagy rangsorold "
"őket a kiszolgálónév alapján"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Szabad DOI használata"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Ha lehetséges, elkerüli a fizetőkapukat azáltal, hogy a kiadvány szabadon"
" elérhető változatára irányítja át"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Személyes információk"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Megjeleníti az IP címet, ha a lekérdezés „ip”, valamint a felhasználói "
"ügynököt, ha a lekérdezés „user-agent”."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Az IP címed: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "A felhasználói ügynököd: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Tor ellenőrző kiegészítő"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Ez a beépülő modul ellenőrzi, hogy a kérés címe Tor kilépési csomópont-e,"
" és értesíti a felhasználót, ha igen; mint a check.torproject.org, de a "
"SearXNG-től."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Nem sikerült letölteni a Tor kilépési csomópontok listáját innen"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr "Ön a Tor-t használja, és úgy tűnik, hogy külső IP-címmel rendelkezik"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Ön nem használja a Tor-t és külső IP-címmel rendelkezik"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Követők eltávolítása a webcímekből"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr ""
"Eltávolítja a felhasználók követéshez használt argumentumokat a találatok"
" webcíméből"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Egységváltó bővítmény"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Váltson mértékegységek között"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr ""

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Az oldal nem található"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Ugrás a %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "keresőoldalra"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Adományozás"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Beállítások"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Az oldalt kiszolgálja:"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "egy adatvédelmet tiszteletben tartó, nyílt forráskódú metakereső"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Forráskód"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Hibajegykövető"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Keresőstatisztikák"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Publikus példányok"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Adatvédelmi irányelvek"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Kapcsolatfelvétel a példány karbantartójával"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "A kereséshez kattintson a nagyítóra"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Hossz"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Nézetek"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Szerző"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "tárolt"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Probléma bejelentése a GitHubon"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr "Ellenőrizze a keresőszolgáltatás bejelentett hibáit a GitHubon"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Megerősítem, hogy nincs meglévő bejelentés a problémámról"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Ha ez egy nyilvános példány, adja meg a webcímét a hibajelentésben"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr ""
"Jelentsen be egy új problémát a GitHubon, amely tartalmazza a fenti "
"információkat"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Nincs HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Nézze meg a hibanaplókat, és küldjön hibajelentést"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang ehhez a keresőmotorhoz"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang a kategóriáihoz"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Medián"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Elbukott ellenőrzőtesztek: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Hibák:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Általános"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Alapértelmezett kategóriák"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Felhasználói felület"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Adatvédelem"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Keresőmotorok"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Jelenleg használt keresőmotorok"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Speciális lekérdezések"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Sütik"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Találatok száma"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Információ"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Vissza a lap tetejére"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Előző oldal"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "Következő oldal"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Kezdőlap megjelenítése"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Keresés…"

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "törlés"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "keresés"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Jelenleg nincs megjeleníthető adat. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Keresőmotor neve"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Pontszámok"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Találatok száma"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Válaszidő"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Megbízhatóság"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Összesen"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Feldolgozás"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Figyelmeztetések"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Hibák és kivételek"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Kivétel"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Üzenet"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Százalék"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Paraméter"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Fájlnév"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Funkció"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Kód"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Ellenőrző"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Elbukott teszt"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Megjegyzések"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Példák"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Meghatározások"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Szinonimák"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr ""

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Válaszok"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Találatok letöltése"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Keresés erre:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "A keresőmotorok üzenetei"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "s"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Keresés webcíme"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Másolva"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Másolás"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Javaslatok"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Keresés nyelve"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Alapértelmezett nyelv"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Automatikus felismerés"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Felnőtt tartalom szűrése"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Erős"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Enyhe"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Nincs"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Időintervallum"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Bármikor"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "Legutóbbi nap"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "Legutóbbi hét"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "Legutóbbi hónap"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "Előző év"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Figyelem!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "jelenleg nincs megadva süti."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Elnézést!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Nincs találat. Megpróbálhatja:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Nincs több eredmény. Megpróbálhatja:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Az oldal frissítése."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Keressen másra, vagy válasszon másik kategóriát (fent)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Módosítsa a beállításokban használt keresőmotort:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Váltás egy másik példányra:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Keressen egy másik lekérdezést, vagy válasszon másik kategóriát."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr "Az előző oldal gomb használatával ugorjon vissza az előző oldalra."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Engedélyezés"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Kulcsszavak (a lekérdezés első szava)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Név"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Leírás"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Ez a SearXNG „azonnal válaszoló\" moduljainak listája."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Ez a beépülő modulok listája."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Automatikus kiegészítés"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Keresés gépelés közben"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Középre rendezés"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "A találatokat a lap közepén jeleníti meg (Oscar elrendezés)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr "Ez a SearXNG által tárolt sütik és azok értékeinek listája."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "A listával felmérheti a SearXNG átláthatóságát."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Süti neve"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Érték"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "A jelenleg mentett beállítások keresési webcíme"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Megjegyzés: a webcímben megadott egyéni beállítások csökkenthetik az "
"adatvédelmét, mert adatokat szivárogtatnak a találatok felé, melyekre "
"rákattint."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "Webcím, mely segítségével átviheti a beállításait egy másik böngészőbe"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"A beállításokat tartalmazó webcím. Ez a webcím használható a beállítások "
"visszaállítására egy másik eszközön."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Beállítások kivonatának másolása"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr ""
"A helyreállításhoz illessze be a kimásolt beállítások kivonatát (webcím "
"nélkül)"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Beállítások kivonatai"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Digital Object Identifier (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Szabad DOI feloldó"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Válassza ki a DOI újraírásához használt szolgáltatást"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Ez a lap nem létezik a felhasználói felületen, de ezekben a "
"keresőmotorokban a !bang parancsok segítségével kereshet."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Minden engedélyezése"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Minden tiltása"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Támogatja a kiválasztott nyelvet"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Súly"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Maximális idő"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Weblapikon-feloldó"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Weblapikonok megjelenítése a keresési eredmények mellett"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Ezek a beállítások böngészősütikben vannak tárolva, így nem kell adatokat"
" tárolnunk Önről."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Ezek a sütik csak kényelmi funkciókat látnak el, nem használjuk arra, "
"hogy kövessük Önt."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Mentés"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Alaphelyzetbe állítás"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Vissza"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Gyorsbillentyűk"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "Vim-szerű"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Gyorsbillentyűkkel navigálhat a keresési eredmények között (JavaScript "
"szükséges). Segítségét nyomja meg a „h” gombot a fő vagy a találati "
"oldalon."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Kép proxy"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Képtalálatok proxyzása a SearXNG-n keresztül"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Végtelen görgetés"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr "Görgetéskor automatikusan betölti a következő oldalt, ha a lap aljára ér"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Milyen nyelven keres?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Válassza az „Automatikus” lehetőséget, hogy a SearXNG ismerje fel a "
"keresési nyelvet."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "HTTP metódus"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Az űrlapok beküldési módjának módosítása"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Lekérdezés az oldal címében"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Ha be van kapcsolva, akkor a találati oldal fejléce tartalmazza a "
"keresést. A böngésző így elmentheti a címét."

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Találatok megjelenítése új lapon"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Találatok megnyitása új böngészőlapokon"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Tartalomszűrés"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Keresés kategóriaválasztással"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Azonnali keresés egy kategória kiválasztásakor. Több kategória "
"kiválasztásához kapcsolja ki"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Téma"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "A SearXNG elrendezésének megváltoztatása"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Téma stílusa"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "A böngésző beállításainak követéséhez válassza az „automatikus” beállítást"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Keresőmotor-tokenek"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Hozzáférési tokenek a privát keresőmotorokhoz"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Felület nyelve"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "A felület nyelvének megváltoztatása"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Webcímformázás"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Szép"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Teljes"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Kiszolgáló"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Az eredmény webcím formázásának módosítása"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "tároló"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "média megjelenítése"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "média elrejtése"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Ennek a weblapnak nincsen leírása."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Fájlméret"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dátum"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Típus"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Felbontás"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formátum"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Motor"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Forrás megtekintése"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "cím"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "térkép megjelenítése"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "térkép elrejtése"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Verzió"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Karbantartó"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Frissítve:"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Címkék"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Népszerűség"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Licenc"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Projekt"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Projekt honlapja"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Közzététel dátuma"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Folyóirat"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Szerkesztő"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Kiadó"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "mágneslink"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "torrent fájl"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Feltöltő"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Letöltő"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Fájlok száma"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "videó megjelenítése"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "videó elrejtése"

#~ msgid "Engine time (sec)"
#~ msgstr "Keresési idő (másodperc)"

#~ msgid "Page loads (sec)"
#~ msgstr "Válaszidők (sec)"

#~ msgid "Errors"
#~ msgstr "Hibák"

#~ msgid "CAPTCHA required"
#~ msgstr "CAPTCHA hiba"

#~ msgid "Rewrite HTTP links to HTTPS if possible"
#~ msgstr "HTTP linkek lecserélése HTTPS-re"

#~ msgid ""
#~ "Results are opened in the same "
#~ "window by default. This plugin "
#~ "overwrites the default behaviour to open"
#~ " links on new tabs/windows. (JavaScript "
#~ "required)"
#~ msgstr ""
#~ "A találatok az aktuális oldalon nyílnak"
#~ " meg alapértelmezetten. Ez a plugin "
#~ "megváltoztatja ezt a működést és új "
#~ "lapra nyitja meg a találatokat. (ez "
#~ "a funkció JavaScript-et igényel)"

#~ msgid "Color"
#~ msgstr "Szín"

#~ msgid "Blue (default)"
#~ msgstr "Kék"

#~ msgid "Violet"
#~ msgstr "Ibolya"

#~ msgid "Green"
#~ msgstr "Zöld"

#~ msgid "Cyan"
#~ msgstr "Türkiz"

#~ msgid "Orange"
#~ msgstr "Narancs"

#~ msgid "Red"
#~ msgstr "Piros"

#~ msgid "Category"
#~ msgstr "Kategória"

#~ msgid "Block"
#~ msgstr "Tiltás"

#~ msgid "original context"
#~ msgstr "eredeti kontextus"

#~ msgid "Plugins"
#~ msgstr "Pluginek"

#~ msgid "Answerers"
#~ msgstr "Válaszok"

#~ msgid "Avg. time"
#~ msgstr "Átlag idő"

#~ msgid "show details"
#~ msgstr "Részletek"

#~ msgid "hide details"
#~ msgstr "Részletek elrejtése"

#~ msgid "Load more..."
#~ msgstr "További találatok betöltése"

#~ msgid "Loading..."
#~ msgstr "Töltés..."

#~ msgid "Change searx layout"
#~ msgstr "Megjelenés"

#~ msgid "Proxying image results through searx"
#~ msgstr "Kép találatok betöltése searx-ön keresztül"

#~ msgid "This is the list of searx's instant answering modules."
#~ msgstr "Az alábbi lista tartalmazza searx instant válaszoló moduljait."

#~ msgid ""
#~ "This is the list of cookies and"
#~ " their values searx is storing on "
#~ "your computer."
#~ msgstr "Searx által használt sütik listája."

#~ msgid "With that list, you can assess searx transparency."
#~ msgstr "Ez a lista a kereső transzparenciáját hivatott megmutatni."

#~ msgid "It look like you are using searx first time."
#~ msgstr "Úgy tűnik először használod a keresőt."

#~ msgid "Please, try again later or find another searx instance."
#~ msgstr "Kérjük próbáld újra, vagy használj egy másik searx-t."

#~ msgid "Themes"
#~ msgstr "Megjelenés"

#~ msgid "Reliablity"
#~ msgstr ""

#~ msgid ""
#~ "When enabled, the result page's title"
#~ " contains your query. Your browser "
#~ "can record this title."
#~ msgstr ""

#~ msgid "Method"
#~ msgstr "Method"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""

#~ msgid "Advanced settings"
#~ msgstr "Keresés beállításai"

#~ msgid "Close"
#~ msgstr "Bezár"

#~ msgid "Language"
#~ msgstr "Nyelv"

#~ msgid "broken"
#~ msgstr "törött"

#~ msgid "supported"
#~ msgstr "támogatott"

#~ msgid "not supported"
#~ msgstr "nem támogatott"

#~ msgid "about"
#~ msgstr "rólunk"

#~ msgid "Avg."
#~ msgstr "Átl."

#~ msgid "User Interface"
#~ msgstr ""

#~ msgid "Choose style for this theme"
#~ msgstr "Válassz megjelenést ehhez a témához"

#~ msgid "Style"
#~ msgstr "Megjelenés"

#~ msgid "Show advanced settings"
#~ msgstr ""

#~ msgid "Show advanced settings panel in the home page by default"
#~ msgstr ""

#~ msgid "Allow all"
#~ msgstr "Mindent engedélyez"

#~ msgid "Disable all"
#~ msgstr "Mindent tilt"

#~ msgid "Selected language"
#~ msgstr "Kiválasztott nyelv"

#~ msgid "Query"
#~ msgstr ""

#~ msgid "save"
#~ msgstr "mentés"

#~ msgid "back"
#~ msgstr "vissza"

#~ msgid "Links"
#~ msgstr "Linkek"

#~ msgid "RSS subscription"
#~ msgstr "RSS feliratkozás"

#~ msgid "Search results"
#~ msgstr "Keresési eredmények"

#~ msgid "next page"
#~ msgstr "következő oldal"

#~ msgid "previous page"
#~ msgstr "előző oldal"

#~ msgid "Start search"
#~ msgstr "Keresés indítása"

#~ msgid "Clear search"
#~ msgstr "Keresés törlése"

#~ msgid "Clear"
#~ msgstr "Törlés"

#~ msgid "stats"
#~ msgstr "statisztikák"

#~ msgid "Heads up!"
#~ msgstr "Figyelem!"

#~ msgid "It look like you are using SearXNG first time."
#~ msgstr ""

#~ msgid "Well done!"
#~ msgstr "Siker!"

#~ msgid "Settings saved successfully."
#~ msgstr "Beállítások mentve."

#~ msgid "Oh snap!"
#~ msgstr "Oh!"

#~ msgid "Something went wrong."
#~ msgstr "Hiba történt."

#~ msgid "Date"
#~ msgstr ""

#~ msgid "Type"
#~ msgstr ""

#~ msgid "Get image"
#~ msgstr "Kép megjelenítése"

#~ msgid "Center Alignment"
#~ msgstr ""

#~ msgid "Displays results in the center of the page (Oscar layout)."
#~ msgstr ""

#~ msgid "preferences"
#~ msgstr "beállítások"

#~ msgid "Scores per result"
#~ msgstr "Pontszámok találatonként"

#~ msgid "a privacy-respecting, hackable metasearch engine"
#~ msgstr "egy magánszféra tisztelő, könnyen módosítható metakereső"

#~ msgid "No abstract is available for this publication."
#~ msgstr "Nem elérhető absztrakt a publikációhoz."

#~ msgid "Self Informations"
#~ msgstr "Saját információ"

#~ msgid ""
#~ "Change how forms are submited, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Keresés metódusa (<a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">bővebben</a>)"

#~ msgid ""
#~ "This plugin checks if the address "
#~ "of the request is a TOR exit "
#~ "node, and informs the user if it"
#~ " is, like check.torproject.org but from "
#~ "searxng."
#~ msgstr ""
#~ "Ez a kiegeszítő ellenőrzi, hogy a "
#~ "kérés címe az egy TOR kilépő "
#~ "nodé-e, és téjákoztatja erről a "
#~ "felhasználót. Olyan, mint a "
#~ "check.torproject.org, de a searxng-től."

#~ msgid ""
#~ "The TOR exit node list "
#~ "(https://check.torproject.org/exit-addresses) is "
#~ "unreachable."
#~ msgstr ""
#~ "A TOR kilébő node listája "
#~ "(https://check.torproject.org/exit-addresses) "
#~ "elérhetetlen."

#~ msgid "You are using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "TOR-t használsz. Az IP címed ennek tűnik: {ip_address}."

#~ msgid "You are not using TOR. Your IP address seems to be: {ip_address}."
#~ msgstr "Nem használsz TOR-t. Az IP címed ennek tűnik: {ip_address}."

#~ msgid ""
#~ "The could not download the list of"
#~ " Tor exit-nodes from "
#~ "https://check.torproject.org/exit-addresses."
#~ msgstr ""

#~ msgid ""
#~ "You are using Tor. It looks like"
#~ " you have this external IP address:"
#~ " {ip_address}."
#~ msgstr ""

#~ msgid "You are not using Tor. You have this external IP address: {ip_address}."
#~ msgstr ""

#~ msgid "Autodetect search language"
#~ msgstr ""

#~ msgid "Automatically detect the query search language and switch to it."
#~ msgstr ""

#~ msgid "others"
#~ msgstr "mások"

#~ msgid ""
#~ "This tab does not show up for "
#~ "search results, but you can search "
#~ "the engines listed here via bangs."
#~ msgstr ""
#~ "Ez az oldal nem jelenik meg a "
#~ "keresés eredményében, de te tudsz "
#~ "keresni keresőmotorokat a \"bangs\"-el."

#~ msgid "Shortcut"
#~ msgstr "Rövidítés"

#~ msgid "!bang"
#~ msgstr ""

#~ msgid ""
#~ "This tab dues not exists in the"
#~ " user interface, but you can search"
#~ " in these engines by its !bangs."
#~ msgstr ""
#~ "Ez a fül nem létezik a "
#~ "felhasználói felületen, de ezekben a "
#~ "keresőmotorokban a !bang-jai segítségével "
#~ "kereshetsz."

#~ msgid "Engines cannot retrieve results."
#~ msgstr "Nincs találat a keresőmotortól."

#~ msgid "Please, try again later or find another SearXNG instance."
#~ msgstr "Kérlek próbáld újra, vagy keress egy másik SearXNG oldalt."

#~ msgid ""
#~ "Redirect to open-access versions of "
#~ "publications when available (plugin required)"
#~ msgstr ""
#~ "Átirányítás a publikáció szabadon elérhető "
#~ "változatára (plugin szükséges)"

#~ msgid "Bang"
#~ msgstr "!bang"

#~ msgid ""
#~ "Change how forms are submitted, <a "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">learn more about request "
#~ "methods</a>"
#~ msgstr ""
#~ "Módosítsa az űrlapok benyújtásának módját,<a"
#~ " "
#~ "href=\"http://en.wikipedia.org/wiki/Hypertext_Transfer_Protocol#Request_methods\""
#~ " rel=\"external\">tudjon meg többet a "
#~ "kérési módszerekről</a>"

#~ msgid "On"
#~ msgstr "Be"

#~ msgid "Off"
#~ msgstr "Ki"

#~ msgid "Enabled"
#~ msgstr "Engedélyez"

#~ msgid "Disabled"
#~ msgstr "Inaktivál"

#~ msgid ""
#~ "Perform search immediately if a category"
#~ " selected. Disable to select multiple "
#~ "categories. (JavaScript required)"
#~ msgstr ""
#~ "Keresés megkezdése, ha a kategória ki"
#~ " van választva. Több kategória "
#~ "kiválasztásához tiltsd le ezt. (JavaScript "
#~ "szükséges)"

#~ msgid "Vim-like hotkeys"
#~ msgstr "Vim jellegű billentyűzetes navigáció"

#~ msgid ""
#~ "Navigate search results with Vim-like"
#~ " hotkeys (JavaScript required). Press \"h\""
#~ " key on main or result page to"
#~ " get help."
#~ msgstr ""
#~ "Navigálj Vim stílusú gombnyomásokkal a "
#~ "találatok között. Aktiválás után a \"h\""
#~ " betű lenyomásával jeleníthető meg "
#~ "részletes segítség a használatról. (Ez a"
#~ " funkció JavaScriptet igényel)."

#~ msgid ""
#~ "we didn't find any results. Please "
#~ "use another query or search in "
#~ "more categories."
#~ msgstr ""
#~ "Nincs megjeleníthető találat. Kérlek, hogy "
#~ "használj másik kifejezést vagy keress "
#~ "több kategóriában."

#~ msgid "Rewrite result hostnames or remove results based on the hostname"
#~ msgstr ""
#~ "Találatok kiszolgálónevének átírása, vagy a"
#~ " találatok eltávolítása gépnév alapján"

#~ msgid "Bytes"
#~ msgstr "Bájt"

#~ msgid "kiB"
#~ msgstr "KiB"

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "GiB"
#~ msgstr "GiB"

#~ msgid "TiB"
#~ msgstr "TiB"

#~ msgid "Hostname replace"
#~ msgstr "Kiszolgálónév cseréje"

#~ msgid "Error!"
#~ msgstr "Hiba!"

#~ msgid "Engines cannot retrieve results"
#~ msgstr "Nincs találat a keresőmotortól"

#~ msgid "Start submiting a new issue on GitHub"
#~ msgstr "Probléma bejelentése a GitHubon"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Véletlenérték-generátor"

#~ msgid "Statistics functions"
#~ msgstr "Statisztikai függvények"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "{functions} alkalmazása az argumentumokon"

#~ msgid "Get directions"
#~ msgstr "Útvonaltervezés"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Megjeleníti a saját IP-címét és "
#~ "felhasználói ügynökét, ha a keresése "
#~ "ezeket tartalmazza: „ip” és „user "
#~ "agent”."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Nem sikerült letölteni a Tor kilépési"
#~ " csomópontok listáját innen: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr "Ön Tort használ, és úgy tűnik, ez a külső IP-címe: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr "Nem használ Tor kapcsolatot, és ez a külső IP-címe: {ip_address}"

#~ msgid "Keywords"
#~ msgstr "Kulcsszavak"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "A beállítási webcímben megadott egyéni "
#~ "beállítások az eszközök közti szinkronizációra"
#~ " használhatók."

#~ msgid "proxied"
#~ msgstr "proxy nézet"

