# Irish translations for PROJECT.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2024.
# <AUTHOR> <EMAIL>, 2025.
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-05-31 18:38+0000\n"
"PO-Revision-Date: 2025-06-02 14:58+0000\n"
"Last-Translator: aindriu80 <<EMAIL>>\n"
"Language-Team: Irish <https://translate.codeberg.org/projects/searxng/"
"searxng/ga/>\n"
"Language: ga\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=5; plural=n==1 ? 0 : n==2 ? 1 : (n>2 && n<7) ? 2 :("
"n>6 && n<11) ? 3 : 4;\n"
"X-Generator: Weblate 5.11.4\n"
"Generated-By: Babel 2.17.0\n"

#. CONSTANT_NAMES['NO_SUBGROUPING']
#: searx/searxng.msg
msgid "without further subgrouping"
msgstr "gan foghrúpáil bhreise"

#. CONSTANT_NAMES['DEFAULT_CATEGORY']
#: searx/searxng.msg
msgid "other"
msgstr "eile"

#. CATEGORY_NAMES['FILES']
#: searx/searxng.msg
msgid "files"
msgstr "comhaid"

#. CATEGORY_NAMES['GENERAL']
#: searx/searxng.msg
msgid "general"
msgstr "ginearálta"

#. CATEGORY_NAMES['MUSIC']
#: searx/searxng.msg
msgid "music"
msgstr "ceol"

#. CATEGORY_NAMES['SOCIAL_MEDIA']
#: searx/searxng.msg
msgid "social media"
msgstr "meáin shóisialta"

#. CATEGORY_NAMES['IMAGES']
#: searx/searxng.msg
msgid "images"
msgstr "íomhánna"

#. CATEGORY_NAMES['VIDEOS']
#: searx/searxng.msg
msgid "videos"
msgstr "físeáin"

#. CATEGORY_NAMES['RADIO']
#: searx/engines/radio_browser.py:151 searx/searxng.msg
msgid "radio"
msgstr "raidió"

#. CATEGORY_NAMES['TV']
#: searx/searxng.msg
msgid "tv"
msgstr "teilifíse"

#. CATEGORY_NAMES['IT']
#: searx/searxng.msg
msgid "it"
msgstr "IT"

#. CATEGORY_NAMES['NEWS']
#: searx/searxng.msg
msgid "news"
msgstr "nuacht"

#. CATEGORY_NAMES['MAP']
#: searx/searxng.msg
msgid "map"
msgstr "léarscáil"

#. CATEGORY_NAMES['ONIONS']
#: searx/searxng.msg
msgid "onions"
msgstr "oinniúin"

#. CATEGORY_NAMES['SCIENCE']
#: searx/searxng.msg
msgid "science"
msgstr "eolaíocht"

#. CATEGORY_GROUPS['APPS']
#: searx/searxng.msg
msgid "apps"
msgstr "aipeanna"

#. CATEGORY_GROUPS['DICTIONARIES']
#: searx/searxng.msg
msgid "dictionaries"
msgstr "foclóirí"

#. CATEGORY_GROUPS['LYRICS']
#: searx/searxng.msg
msgid "lyrics"
msgstr "liricí"

#. CATEGORY_GROUPS['PACKAGES']
#: searx/searxng.msg
msgid "packages"
msgstr "pacáistí"

#. CATEGORY_GROUPS['Q_A']
#: searx/searxng.msg
msgid "q&a"
msgstr "q&a"

#. CATEGORY_GROUPS['REPOS']
#: searx/searxng.msg
msgid "repos"
msgstr "stórais"

#. CATEGORY_GROUPS['SOFTWARE_WIKIS']
#: searx/searxng.msg
msgid "software wikis"
msgstr "bogearraí wikis"

#. CATEGORY_GROUPS['WEB']
#: searx/searxng.msg
msgid "web"
msgstr "gréasáin"

#. CATEGORY_GROUPS['SCIENTIFIC PUBLICATIONS']
#: searx/searxng.msg
msgid "scientific publications"
msgstr "foilseacháin eol"

#. STYLE_NAMES['AUTO']
#: searx/searxng.msg
msgid "auto"
msgstr "uath"

#. STYLE_NAMES['LIGHT']
#: searx/searxng.msg
msgid "light"
msgstr "solas"

#. STYLE_NAMES['DARK']
#: searx/searxng.msg
msgid "dark"
msgstr "dorcha"

#. STYLE_NAMES['BLACK']
#: searx/searxng.msg
msgid "black"
msgstr "dubh"

#. BRAND_CUSTOM_LINKS['UPTIME']
#: searx/searxng.msg
msgid "Uptime"
msgstr "Aga fónaimh"

#. BRAND_CUSTOM_LINKS['ABOUT']
#: searx/searxng.msg searx/templates/simple/base.html:49
msgid "About"
msgstr "Maidir"

#. WEATHER_TERMS['AVERAGE TEMP.']
#: searx/engines/wttr.py:32 searx/searxng.msg
msgid "Average temp."
msgstr "Meán-teocht."

#. WEATHER_TERMS['CLOUD COVER']
#: searx/searxng.msg
msgid "Cloud cover"
msgstr "Clúdach scamall"

#. WEATHER_TERMS['CONDITION']
#: searx/engines/duckduckgo_weather.py:45 searx/engines/wttr.py:51
#: searx/searxng.msg
msgid "Condition"
msgstr "Coinníoll"

#. WEATHER_TERMS['CURRENT CONDITION']
#: searx/engines/duckduckgo_weather.py:118 searx/engines/wttr.py:104
#: searx/searxng.msg
msgid "Current condition"
msgstr "Coinníoll reatha"

#. WEATHER_TERMS['EVENING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Evening"
msgstr "Tráthnóna"

#. WEATHER_TERMS['FEELS LIKE']
#: searx/engines/duckduckgo_weather.py:53 searx/engines/wttr.py:59
#: searx/searxng.msg
msgid "Feels like"
msgstr "Mothaíonn sé"

#. WEATHER_TERMS['HUMIDITY']
#: searx/engines/duckduckgo_weather.py:64 searx/engines/wttr.py:68
#: searx/searxng.msg searx/templates/simple/answer/weather.html:29
msgid "Humidity"
msgstr "Bogthaise"

#. WEATHER_TERMS['MAX TEMP.']
#: searx/engines/duckduckgo_weather.py:77 searx/engines/wttr.py:34
#: searx/searxng.msg
msgid "Max temp."
msgstr "Teocht uasta."

#. WEATHER_TERMS['MIN TEMP.']
#: searx/engines/duckduckgo_weather.py:73 searx/engines/wttr.py:33
#: searx/searxng.msg
msgid "Min temp."
msgstr "Teocht íosta."

#. WEATHER_TERMS['MORNING']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Morning"
msgstr "Maidin"

#. WEATHER_TERMS['NIGHT']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Night"
msgstr "Oíche"

#. WEATHER_TERMS['NOON']
#: searx/engines/wttr.py:100 searx/searxng.msg
msgid "Noon"
msgstr "Meán lae"

#. WEATHER_TERMS['PRESSURE']
#: searx/searxng.msg searx/templates/simple/answer/weather.html:25
msgid "Pressure"
msgstr "Brú"

#. WEATHER_TERMS['SUNRISE']
#: searx/engines/duckduckgo_weather.py:81 searx/engines/wttr.py:36
#: searx/searxng.msg
msgid "Sunrise"
msgstr "Éirí na gréine"

#. WEATHER_TERMS['SUNSET']
#: searx/engines/duckduckgo_weather.py:82 searx/engines/wttr.py:37
#: searx/searxng.msg
msgid "Sunset"
msgstr "Luí na gréine"

#. WEATHER_TERMS['TEMPERATURE']
#: searx/engines/duckduckgo_weather.py:48 searx/engines/wttr.py:55
#: searx/searxng.msg searx/templates/simple/answer/weather.html:17
msgid "Temperature"
msgstr "Teocht"

#. WEATHER_TERMS['UV INDEX']
#: searx/engines/duckduckgo_weather.py:80 searx/engines/wttr.py:35
#: searx/searxng.msg
msgid "UV index"
msgstr "Innéacs UV"

#. WEATHER_TERMS['VISIBILITY']
#: searx/engines/duckduckgo_weather.py:62 searx/engines/wttr.py:66
#: searx/searxng.msg
msgid "Visibility"
msgstr "Infheictheacht"

#. WEATHER_TERMS['WIND']
#: searx/engines/duckduckgo_weather.py:58 searx/engines/wttr.py:62
#: searx/searxng.msg searx/templates/simple/answer/weather.html:23
msgid "Wind"
msgstr "Gaoth"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Clear sky"
msgstr "Spéir shoiléir"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Cloudy"
msgstr "Scamallach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fair"
msgstr "Breá"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Fog"
msgstr "Ceo"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain and thunder"
msgstr "Báisteach throm agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers and thunder"
msgstr "Cithfholcadáin throma báistí agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain showers"
msgstr "Cithfholcadáin throma báistí"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy rain"
msgstr "Báisteach throm"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet and thunder"
msgstr "Fliuchshneachta agus toirneach trom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers and thunder"
msgstr "Cithfholcadáin throma sneachta agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet showers"
msgstr "Cithfholcadáin throma sneachta"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy sleet"
msgstr "Fliuchshneachta trom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow and thunder"
msgstr "Sneachta trom agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers and thunder"
msgstr "Cithfholcadáin sneachta trom agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow showers"
msgstr "Cithfholcadáin sneachta troma"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Heavy snow"
msgstr "Sneachta trom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain and thunder"
msgstr "Báisteach éadrom agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers and thunder"
msgstr "Cithfholcadáin éadroma báistí agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain showers"
msgstr "Cithfholcadáin bháistí éadroma"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light rain"
msgstr "Báisteach éadrom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet and thunder"
msgstr "Fliuchshneachta agus toirneach éadrom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers and thunder"
msgstr "Cithfholcadáin éadroma agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet showers"
msgstr "Cithfholcadáin éadroma"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light sleet"
msgstr "Fliuchshneachta éadrom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow and thunder"
msgstr "Sneachta éadrom agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers and thunder"
msgstr "Cithfholcadáin sneachta éadroma agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow showers"
msgstr "Cithfholcadáin sneachta éadroma"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Light snow"
msgstr "Sneachta éadrom"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Partly cloudy"
msgstr "Scamallach go páirteach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain and thunder"
msgstr "Báisteach agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers and thunder"
msgstr "Cithfholcadáin agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain showers"
msgstr "Cithfholcadáin báistí"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Rain"
msgstr "Báisteach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet and thunder"
msgstr "Fliuchshneachta agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers and thunder"
msgstr "Cithfholcadáin flichshneachta agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet showers"
msgstr "Cithfholcadáin shneachta"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Sleet"
msgstr "Fliuchshneachta"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow and thunder"
msgstr "Sneachta agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers and thunder"
msgstr "Cithfholcadáin sneachta agus toirneach"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow showers"
msgstr "Cithfholcadáin sneachta"

#. WEATHER_CONDITIONS
#: searx/searxng.msg
msgid "Snow"
msgstr "Sneachta"

#. SOCIAL_MEDIA_TERMS['SUBSCRIBERS']
#: searx/engines/lemmy.py:85 searx/searxng.msg
msgid "subscribers"
msgstr "síntiúsóirí"

#. SOCIAL_MEDIA_TERMS['POSTS']
#: searx/engines/lemmy.py:86 searx/searxng.msg
msgid "posts"
msgstr "poist"

#. SOCIAL_MEDIA_TERMS['ACTIVE USERS']
#: searx/engines/lemmy.py:87 searx/searxng.msg
msgid "active users"
msgstr "úsáideoirí gníomhacha"

#. SOCIAL_MEDIA_TERMS['COMMENTS']
#: searx/engines/discourse.py:157 searx/engines/hackernews.py:82
#: searx/engines/lemmy.py:130 searx/searxng.msg
msgid "comments"
msgstr "tráchtanna"

#. SOCIAL_MEDIA_TERMS['USER']
#: searx/engines/lemmy.py:129 searx/engines/lemmy.py:164 searx/searxng.msg
msgid "user"
msgstr "úsáideoir"

#. SOCIAL_MEDIA_TERMS['COMMUNITY']
#: searx/engines/lemmy.py:131 searx/engines/lemmy.py:165 searx/searxng.msg
msgid "community"
msgstr "pobal"

#. SOCIAL_MEDIA_TERMS['POINTS']
#: searx/engines/hackernews.py:82 searx/searxng.msg
msgid "points"
msgstr "pointí"

#. SOCIAL_MEDIA_TERMS['TITLE']
#: searx/searxng.msg
msgid "title"
msgstr "teideal"

#. SOCIAL_MEDIA_TERMS['AUTHOR']
#: searx/engines/hackernews.py:85 searx/searxng.msg
msgid "author"
msgstr "údar"

#. SOCIAL_MEDIA_TERMS['THREAD OPEN']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "open"
msgstr "oscailte"

#. SOCIAL_MEDIA_TERMS['THREAD CLOSED']
#: searx/engines/discourse.py:149 searx/searxng.msg
msgid "closed"
msgstr "dúnta"

#. SOCIAL_MEDIA_TERMS['THREAD ANSWERED']
#: searx/engines/discourse.py:160 searx/searxng.msg
msgid "answered"
msgstr "freagraí"

#: searx/webapp.py:292
msgid "No item found"
msgstr "Níor aimsíodh aon rud"

#: searx/engines/qwant.py:291
#: searx/templates/simple/result_templates/images.html:23 searx/webapp.py:294
msgid "Source"
msgstr "Foinse"

#: searx/webapp.py:296
msgid "Error loading the next page"
msgstr "Earráid ag luchtú an chéad leathanach eile"

#: searx/webapp.py:447 searx/webapp.py:845
msgid "Invalid settings, please edit your preferences"
msgstr "Socruithe neamhbhailí, cuir do chuid roghanna in"

#: searx/webapp.py:463
msgid "Invalid settings"
msgstr "Socruithe neamhbhaintí"

#: searx/webapp.py:540 searx/webapp.py:630
msgid "search error"
msgstr "earráid cuardaigh"

#: searx/webutils.py:35
msgid "timeout"
msgstr "amuigh"

#: searx/webutils.py:36
msgid "parsing error"
msgstr "earráid parsála"

#: searx/webutils.py:37
msgid "HTTP protocol error"
msgstr "Earráid prótacal HTTP"

#: searx/webutils.py:38
msgid "network error"
msgstr "earráid líonra"

#: searx/webutils.py:39
msgid "SSL error: certificate validation has failed"
msgstr "Earráid SSL: theip ar bhailíochtú teastais"

#: searx/webutils.py:41
msgid "unexpected crash"
msgstr "timpiste gan choinne"

#: searx/webutils.py:48
msgid "HTTP error"
msgstr "Earráid HTTP"

#: searx/webutils.py:49
msgid "HTTP connection error"
msgstr "Earráid nasc HTTP"

#: searx/webutils.py:55
msgid "proxy error"
msgstr "earráid seachfhá"

#: searx/webutils.py:56
msgid "CAPTCHA"
msgstr "CAPTCHA"

#: searx/webutils.py:57
msgid "too many requests"
msgstr "an iomarca iarratais"

#: searx/webutils.py:58
msgid "access denied"
msgstr "rochtain diúltaithe"

#: searx/webutils.py:59
msgid "server API error"
msgstr "earráid API freastalaí"

#: searx/webutils.py:78
msgid "Suspended"
msgstr "Ar fionraí"

#: searx/webutils.py:313
#, python-brace-format
msgid "{minutes} minute(s) ago"
msgstr "{minutes} nóiméad ó shin"

#: searx/webutils.py:314
#, python-brace-format
msgid "{hours} hour(s), {minutes} minute(s) ago"
msgstr "{hours} uair(eanta), {minutes} nóiméad ó shin"

#: searx/answerers/random.py:69
msgid "Generate different random values"
msgstr "Cruthaigh luachanna randamacha éag"

#: searx/answerers/statistics.py:36
#, python-brace-format
msgid "Compute {func} of the arguments"
msgstr "Ríomh {func} na n-argóintí"

#: searx/engines/openstreetmap.py:158
msgid "Show route in map .."
msgstr "Taispeáin an bealach ar an léarscáil .."

#: searx/engines/pdbe.py:96
#, python-brace-format
msgid "{title} (OBSOLETE)"
msgstr "{title} (ÚSÁIDEACH)"

#: searx/engines/pdbe.py:103
msgid "This entry has been superseded by"
msgstr "Cuireadh an iontráil seo in ionad ag"

#: searx/engines/qwant.py:293
msgid "Channel"
msgstr "Cainéal"

#: searx/engines/radio_browser.py:153
msgid "bitrate"
msgstr "ráta giotán"

#: searx/engines/radio_browser.py:154
msgid "votes"
msgstr "vótaí"

#: searx/engines/radio_browser.py:155
msgid "clicks"
msgstr "cliceáil"

#: searx/engines/seekr.py:193 searx/engines/yummly.py:71
#: searx/engines/zlibrary.py:137
msgid "Language"
msgstr "Teanga"

#: searx/engines/semantic_scholar.py:101
#, python-brace-format
msgid ""
"{numCitations} citations from the year {firstCitationVelocityYear} to "
"{lastCitationVelocityYear}"
msgstr ""
"{numCitations} lua ón mbliain {firstCitationVelocityYear} go "
"{lastCitationVelocityYear}"

#: searx/engines/tineye.py:48
msgid ""
"Could not read that image url. This may be due to an unsupported file "
"format. TinEye only supports images that are JPEG, PNG, GIF, BMP, TIFF or"
" WebP."
msgstr ""
"Ní fhéadfaí an url íomhá sin a léamh. D'fhéadfadh sé seo a bheith mar "
"gheall ar fhormáid comhaid gan tacaíocht. Ní thacaíonn TinEye ach le "
"híomhánna atá JPEG, PNG, GIF, BMP, TIFF nó WebP."

#: searx/engines/tineye.py:54
msgid ""
"The image is too simple to find matches. TinEye requires a basic level of"
" visual detail to successfully identify matches."
msgstr ""
"Tá an íomhá ró-simplí chun cluichí a aimsiú. Éilíonn TinEye leibhéal "
"bunúsach sonraí amhairc chun cluichí a aithint go rathúil."

#: searx/engines/tineye.py:59
msgid "The image could not be downloaded."
msgstr "Ní fhéadfaí an íomhá a íoslódáil."

#: searx/engines/zlibrary.py:138
msgid "Book rating"
msgstr "Rátáil leabhar"

#: searx/engines/zlibrary.py:139
msgid "File quality"
msgstr "Cáilíocht comhad"

#: searx/plugins/ahmia_filter.py:32
msgid "Ahmia blacklist"
msgstr "Liosta dubh Ahmia"

#: searx/plugins/ahmia_filter.py:33
msgid "Filter out onion results that appear in Ahmia's blacklist."
msgstr "Scag amach torthaí oinniún atá le feiceáil ar liosta dubh Ahmia."

#: searx/plugins/calculator.py:38
msgid "Basic Calculator"
msgstr "Áireamhán Bunúsach"

#: searx/plugins/calculator.py:39
msgid "Calculate mathematical expressions via the search bar"
msgstr "Ríomh nathanna matamaiticiúla tríd an mbarra cu"

#: searx/plugins/hash_plugin.py:34
msgid "Hash plugin"
msgstr "Breiseán hais"

#: searx/plugins/hash_plugin.py:35
msgid "Converts strings to different hash digests."
msgstr "Athraíonn sé teaghráin go díleá hash éagsúla."

#: searx/plugins/hash_plugin.py:62
msgid "hash digest"
msgstr "díleá hash"

#: searx/plugins/hostnames.py:123
msgid "Hostnames plugin"
msgstr "Breiseán Óstainmneacha"

#: searx/plugins/hostnames.py:124
msgid "Rewrite hostnames, remove results or prioritize them based on the hostname"
msgstr ""
"Athscríobh óstainmneacha, bain torthaí nó tosaíocht a thabhairt dóibh "
"bunaithe ar an óstainm"

#: searx/plugins/oa_doi_rewrite.py:55
msgid "Open Access DOI rewrite"
msgstr "Athscríobh DOI Rochtana Oscailte"

#: searx/plugins/oa_doi_rewrite.py:56
msgid ""
"Avoid paywalls by redirecting to open-access versions of publications "
"when available"
msgstr ""
"Seachain ballaí pá trí athreorú chuig leaganacha rochtana oscailte de "
"fhoilseacháin"

#: searx/plugins/self_info.py:37
msgid "Self Information"
msgstr "Féin-fhaisnéis"

#: searx/plugins/self_info.py:39
msgid ""
"Displays your IP if the query is \"ip\" and your user agent if the query "
"is \"user-agent\"."
msgstr ""
"Taispeáin do IP más \"ip\" an cheist agus do ghníomhaire úsáideora más "
"\"úsáideoir-gníomhaire\" an cheist."

#: searx/plugins/self_info.py:52
msgid "Your IP is: "
msgstr "Is é do IP: "

#: searx/plugins/self_info.py:55
msgid "Your user-agent is: "
msgstr "Is é do ghníomhaire úsáideora: "

#: searx/plugins/tor_check.py:42
msgid "Tor check plugin"
msgstr "Breiseán seiceála Tor"

#: searx/plugins/tor_check.py:44
msgid ""
"This plugin checks if the address of the request is a Tor exit-node, and "
"informs the user if it is; like check.torproject.org, but from SearXNG."
msgstr ""
"Seiceálann an breiseán seo an nód amach Tor é seoladh an iarratais, agus "
"cuireann sé in iúl don úsáideoir más é; cosúil le check.torproject.org, "
"ach ó SearxNG."

#: searx/plugins/tor_check.py:65
msgid "Could not download the list of Tor exit-nodes from"
msgstr "Níorbh fhéidir liosta na nóid scoir Tor a íoslódáil ó"

#: searx/plugins/tor_check.py:72
msgid "You are using Tor and it looks like you have the external IP address"
msgstr ""
"Tá tú ag úsáid Tor agus tá an chuma ar an scéal go bhfuil an seoladh IP "
"seachtrach agat"

#: searx/plugins/tor_check.py:76
msgid "You are not using Tor and you have the external IP address"
msgstr "Níl tú ag úsáid Tor agus tá an seoladh IP seachtrach agat"

#: searx/plugins/tracker_url_remover.py:37
msgid "Tracker URL remover"
msgstr "Aistritheoir URL rianaithe"

#: searx/plugins/tracker_url_remover.py:38
msgid "Remove trackers arguments from the returned URL"
msgstr "Bain argóintí rianaithe ón URL ar ais"

#: searx/plugins/unit_converter.py:49
msgid "Unit converter plugin"
msgstr "Breiseán tiontaire aonad"

#: searx/plugins/unit_converter.py:50
msgid "Convert between units"
msgstr "Tiontaigh idir aonaid"

#: searx/result_types/answer.py:224
#, python-brace-format
msgid "{location}: {temperature}, {condition}"
msgstr "{location}: {temperature}, {condition}"

#: searx/templates/simple/404.html:4
msgid "Page not found"
msgstr "Níor aimsíodh an leathanach"

#: searx/templates/simple/404.html:6
#, python-format
msgid "Go to %(search_page)s."
msgstr "Téigh chuig %(search_page)s."

#: searx/templates/simple/404.html:6
msgid "search page"
msgstr "leathanach cuardaigh"

#: searx/templates/simple/base.html:53
msgid "Donate"
msgstr "Deontas"

#: searx/templates/simple/base.html:57
#: searx/templates/simple/preferences.html:156
msgid "Preferences"
msgstr "Roghanna"

#: searx/templates/simple/base.html:67
msgid "Powered by"
msgstr "Cumhachtaithe ag"

#: searx/templates/simple/base.html:67
msgid "a privacy-respecting, open metasearch engine"
msgstr "inneall metaschuardaigh oscailte a bhfuil meas ar phríobháideacht"

#: searx/templates/simple/base.html:68
#: searx/templates/simple/result_templates/packages.html:59
msgid "Source code"
msgstr "Cód foinse"

#: searx/templates/simple/base.html:69
msgid "Issue tracker"
msgstr "Rianóir saincheisteanna"

#: searx/templates/simple/base.html:70 searx/templates/simple/stats.html:18
msgid "Engine stats"
msgstr "Staitisticí innill"

#: searx/templates/simple/base.html:72
msgid "Public instances"
msgstr "Cásanna poiblí"

#: searx/templates/simple/base.html:75
msgid "Privacy policy"
msgstr "Beartas príobháideachta"

#: searx/templates/simple/base.html:78
msgid "Contact instance maintainer"
msgstr "Déan teagmháil le cothabhálaí sampla"

#: searx/templates/simple/categories.html:30
msgid "Click on the magnifier to perform search"
msgstr "Cliceáil ar an mhéadaitheoir chun cuardach a dhéanamh"

#: searx/templates/simple/macros.html:40
msgid "Length"
msgstr "Fad"

#: searx/templates/simple/macros.html:41
msgid "Views"
msgstr "Amharcanna"

#: searx/templates/simple/macros.html:42
#: searx/templates/simple/result_templates/files.html:34
#: searx/templates/simple/result_templates/images.html:19
#: searx/templates/simple/result_templates/paper.html:6
msgid "Author"
msgstr "Údar"

#: searx/templates/simple/macros.html:50
msgid "cached"
msgstr "taisceáilte"

#: searx/templates/simple/new_issue.html:64
msgid "Start submitting a new issue on GitHub"
msgstr "Tosaigh eisiúint nua a chur isteach ar GitHub"

#: searx/templates/simple/new_issue.html:66
msgid "Please check for existing bugs about this engine on GitHub"
msgstr ""
"Seiceáil le do thoil le haghaidh fabhtanna atá ann cheana faoin inneall "
"seo ar GitHub"

#: searx/templates/simple/new_issue.html:69
msgid "I confirm there is no existing bug about the issue I encounter"
msgstr "Deimhním nach bhfuil aon fhabht ann cheana faoin gceist a bhíonn agam"

#: searx/templates/simple/new_issue.html:71
msgid "If this is a public instance, please specify the URL in the bug report"
msgstr "Más cás poiblí é seo, sonraigh an URL sa tuarascáil fabht"

#: searx/templates/simple/new_issue.html:72
msgid "Submit a new issue on Github including the above information"
msgstr "Cuir isteach eisiúint nua ar Github lena n-áirítear an fhaisnéis thuas"

#: searx/templates/simple/preferences.html:65
msgid "No HTTPS"
msgstr "Gan HTTPS"

#: searx/templates/simple/elements/engines_msg.html:14
#: searx/templates/simple/preferences.html:69
#: searx/templates/simple/preferences.html:70
msgid "View error logs and submit a bug report"
msgstr "Féach ar logaí earráide agus cuir isteach tuarascáil fabht"

#: searx/templates/simple/preferences.html:74
msgid "!bang for this engine"
msgstr "!bang don inneall seo"

#: searx/templates/simple/preferences.html:80
msgid "!bang for its categories"
msgstr "!bang dá chatagóirí"

#: searx/templates/simple/preferences.html:102
#: searx/templates/simple/stats.html:64
msgid "Median"
msgstr "Meánmhéid"

#: searx/templates/simple/preferences.html:103
#: searx/templates/simple/stats.html:70
msgid "P80"
msgstr "P80"

#: searx/templates/simple/preferences.html:104
#: searx/templates/simple/stats.html:76
msgid "P95"
msgstr "P95"

#: searx/templates/simple/preferences.html:136
msgid "Failed checker test(s): "
msgstr "Tástáil(í) seiceála ar theip orthu: "

#: searx/templates/simple/preferences.html:138
msgid "Errors:"
msgstr "Earráidí:"

#: searx/templates/simple/preferences.html:163
msgid "General"
msgstr "Ginearálta"

#: searx/templates/simple/preferences.html:166
msgid "Default categories"
msgstr "Catagóirí réamhshoc"

#: searx/templates/simple/preferences.html:194
msgid "User interface"
msgstr "Comhéadan úsáideora"

#: searx/templates/simple/preferences.html:217
msgid "Privacy"
msgstr "Príobháideacht"

#: searx/templates/simple/preferences.html:232
msgid "Engines"
msgstr "Innill"

#: searx/templates/simple/preferences.html:234
msgid "Currently used search engines"
msgstr "Innill chuardaigh á n-úsáidtear"

#: searx/templates/simple/preferences.html:243
msgid "Special Queries"
msgstr "Ceisteanna Speisialta"

#: searx/templates/simple/preferences.html:251
msgid "Cookies"
msgstr "Fianáin"

#: searx/templates/simple/results.html:30
msgid "Number of results"
msgstr "Líon na dtorthaí"

#: searx/templates/simple/results.html:36
msgid "Info"
msgstr "Eolas"

#: searx/templates/simple/results.html:77
msgid "Back to top"
msgstr "Ar ais go dtí an barr"

#: searx/templates/simple/results.html:95
msgid "Previous page"
msgstr "Leathanach roimhe seo"

#: searx/templates/simple/results.html:113
msgid "Next page"
msgstr "An chéad leathanach eile"

#: searx/templates/simple/search.html:3
msgid "Display the front page"
msgstr "Taispeáin an leathanach tosaigh"

#: searx/templates/simple/search.html:9
#: searx/templates/simple/simple_search.html:5
msgid "Search for..."
msgstr "Cuardaigh do..."

#: searx/templates/simple/search.html:10
#: searx/templates/simple/simple_search.html:6
msgid "clear"
msgstr "soiléir"

#: searx/templates/simple/search.html:11
#: searx/templates/simple/simple_search.html:7
msgid "search"
msgstr "cuardaigh"

#: searx/templates/simple/stats.html:21
msgid "There is currently no data available. "
msgstr "Níl aon sonraí ar fáil faoi láthair. "

#: searx/templates/simple/preferences/engines.html:24
#: searx/templates/simple/stats.html:25
msgid "Engine name"
msgstr "Ainm an innill"

#: searx/templates/simple/stats.html:26
msgid "Scores"
msgstr "Scóir"

#: searx/templates/simple/stats.html:27
msgid "Result count"
msgstr "Comhaireamh torthaí"

#: searx/templates/simple/elements/engines_msg.html:7
#: searx/templates/simple/preferences/engines.html:31
#: searx/templates/simple/stats.html:28
msgid "Response time"
msgstr "Am freagartha"

#: searx/templates/simple/preferences/engines.html:35
#: searx/templates/simple/stats.html:29
msgid "Reliability"
msgstr "Iontaofacht"

#: searx/templates/simple/stats.html:59
msgid "Total"
msgstr "Iomlán"

#: searx/templates/simple/stats.html:60
msgid "HTTP"
msgstr "HTTP"

#: searx/templates/simple/stats.html:61
msgid "Processing"
msgstr "Próiseáil"

#: searx/templates/simple/stats.html:99
msgid "Warnings"
msgstr "Rabhadh"

#: searx/templates/simple/stats.html:99
msgid "Errors and exceptions"
msgstr "Earráidí agus eisceachtaí"

#: searx/templates/simple/stats.html:105
msgid "Exception"
msgstr "Eisceacht"

#: searx/templates/simple/stats.html:107
msgid "Message"
msgstr "Teachtaireacht"

#: searx/templates/simple/stats.html:109
msgid "Percentage"
msgstr "Céatadán"

#: searx/templates/simple/stats.html:111
msgid "Parameter"
msgstr "Paraiméadar"

#: searx/templates/simple/result_templates/files.html:36
#: searx/templates/simple/stats.html:119
msgid "Filename"
msgstr "Ainm comhaid"

#: searx/templates/simple/stats.html:120
msgid "Function"
msgstr "Feidhm"

#: searx/templates/simple/stats.html:121
msgid "Code"
msgstr "Cód"

#: searx/templates/simple/stats.html:128
msgid "Checker"
msgstr "Seiceálaí"

#: searx/templates/simple/stats.html:131
msgid "Failed test"
msgstr "Thástáil theip"

#: searx/templates/simple/stats.html:132
msgid "Comment(s)"
msgstr "Trácht(anna)"

#: searx/templates/simple/answer/translations.html:12
#: searx/templates/simple/preferences/answerers.html:8
msgid "Examples"
msgstr "Samplaí"

#: searx/templates/simple/answer/translations.html:21
msgid "Definitions"
msgstr "Sainmhínithe"

#: searx/templates/simple/answer/translations.html:30
msgid "Synonyms"
msgstr "Comhchiallaigh"

#: searx/templates/simple/answer/weather.html:19
msgid "Feels Like"
msgstr "Mothaíonn Cosúil"

#: searx/templates/simple/elements/answers.html:2
msgid "Answers"
msgstr "Freagraí"

#: searx/templates/simple/elements/apis.html:3
msgid "Download results"
msgstr "Íoslódáil torthaí"

#: searx/templates/simple/elements/corrections.html:2
msgid "Try searching for:"
msgstr "Bain triail as cuardach a dhéanamh:"

#: searx/templates/simple/elements/engines_msg.html:4
msgid "Messages from the search engines"
msgstr "Teachtaireachtaí ó na hinnill chuardaigh"

#: searx/templates/simple/elements/engines_msg.html:7
msgid "seconds"
msgstr "soicind"

#: searx/templates/simple/elements/search_url.html:3
msgid "Search URL"
msgstr "Cuardaigh URL"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copied"
msgstr "Cóipeáladh"

#: searx/templates/simple/elements/search_url.html:4
#: searx/templates/simple/preferences/cookies.html:54
msgid "Copy"
msgstr "Cóipeáil"

#: searx/templates/simple/elements/suggestions.html:3
msgid "Suggestions"
msgstr "Moltaí"

#: searx/templates/simple/filters/languages.html:1
#: searx/templates/simple/preferences/language.html:2
msgid "Search language"
msgstr "Teanga cuardaigh"

#: searx/templates/simple/filters/languages.html:4
#: searx/templates/simple/preferences/language.html:7
msgid "Default language"
msgstr "Teanga réamhshocraithe"

#: searx/templates/simple/filters/languages.html:8
#: searx/templates/simple/preferences/language.html:11
msgid "Auto-detect"
msgstr "Uathoibríoch"

#: searx/templates/simple/filters/safesearch.html:1
#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/engines.html:27
#: searx/templates/simple/preferences/safesearch.html:2
msgid "SafeSearch"
msgstr "Cuardach Sábháilte"

#: searx/templates/simple/filters/safesearch.html:2
#: searx/templates/simple/preferences/safesearch.html:7
msgid "Strict"
msgstr "Docht"

#: searx/templates/simple/filters/safesearch.html:3
#: searx/templates/simple/preferences/safesearch.html:11
msgid "Moderate"
msgstr "Measartha"

#: searx/templates/simple/filters/safesearch.html:4
#: searx/templates/simple/preferences/safesearch.html:15
msgid "None"
msgstr "Níl aon"

#: searx/templates/simple/filters/time_range.html:1
#: searx/templates/simple/preferences/engines.html:28
msgid "Time range"
msgstr "Raon ama"

#: searx/templates/simple/filters/time_range.html:3
msgid "Anytime"
msgstr "Am ar bith"

#: searx/templates/simple/filters/time_range.html:6
msgid "Last day"
msgstr "An lá deireanach"

#: searx/templates/simple/filters/time_range.html:9
msgid "Last week"
msgstr "An tseachtain seo caite"

#: searx/templates/simple/filters/time_range.html:12
msgid "Last month"
msgstr "An mhí seo caite"

#: searx/templates/simple/filters/time_range.html:15
msgid "Last year"
msgstr "An bhliain seo caite"

#: searx/templates/simple/messages/no_cookies.html:3
msgid "Information!"
msgstr "Eolas!"

#: searx/templates/simple/messages/no_cookies.html:4
msgid "currently, there are no cookies defined."
msgstr "faoi láthair, níl aon fhianáin sainmhínithe ann."

#: searx/templates/simple/messages/no_results.html:6
msgid "Sorry!"
msgstr "Tá brón orm!"

#: searx/templates/simple/messages/no_results.html:12
msgid "No results were found. You can try to:"
msgstr "Níor aimsíodh aon torthaí. Is féidir leat iarracht a dhéanamh:"

#: searx/templates/simple/messages/no_results.html:14
msgid "There are no more results. You can try to:"
msgstr "Níl aon torthaí níos mó ann. Is féidir leat iarracht a dhéanamh:"

#: searx/templates/simple/messages/no_results.html:19
msgid "Refresh the page."
msgstr "Athnuachan an leathanach."

#: searx/templates/simple/messages/no_results.html:20
msgid "Search for another query or select another category (above)."
msgstr "Cuardaigh ceist eile nó roghnaigh catagóir eile (thuas)."

#: searx/templates/simple/messages/no_results.html:21
msgid "Change the search engine used in the preferences:"
msgstr "Athraigh an t-inneall cuardaigh a úsáidtear sna roghanna:"

#: searx/templates/simple/messages/no_results.html:22
msgid "Switch to another instance:"
msgstr "Athraigh go cás eile:"

#: searx/templates/simple/messages/no_results.html:24
msgid "Search for another query or select another category."
msgstr "Cuardaigh ceist eile nó roghnaigh catagóir eile."

#: searx/templates/simple/messages/no_results.html:25
msgid "Go back to the previous page using the previous page button."
msgstr ""
"Téigh ar ais go dtí an leathanach roimhe seo ag baint úsáide as an "
"gcnaipe leathanach roimhe seo."

#: searx/templates/simple/preferences/answerers.html:4
#: searx/templates/simple/preferences/engines.html:23
msgid "Allow"
msgstr "Ceadaigh"

#: searx/templates/simple/preferences/answerers.html:5
msgid "Keywords (first word in query)"
msgstr "Eochairfhocail (an chéad fhocal sa cheist)"

#: searx/templates/simple/preferences/answerers.html:6
#: searx/templates/simple/result_templates/packages.html:7
msgid "Name"
msgstr "Ainm"

#: searx/templates/simple/preferences/answerers.html:7
msgid "Description"
msgstr "Cur síos"

#: searx/templates/simple/preferences/answerers.html:13
msgid "This is the list of SearXNG's instant answering modules."
msgstr "Seo an liosta de mhodúil freagartha láithreach SearxNG."

#: searx/templates/simple/preferences/answerers.html:29
msgid "This is the list of plugins."
msgstr "Seo liosta na mbreiseáin."

#: searx/templates/simple/preferences/autocomplete.html:2
msgid "Autocomplete"
msgstr "Uathchomhlánú"

#: searx/templates/simple/preferences/autocomplete.html:15
msgid "Find stuff as you type"
msgstr "Faigh rudaí agus tú ag clóscríobh"

#: searx/templates/simple/preferences/center_alignment.html:2
msgid "Center Alignment"
msgstr "Ailíniú Ionaid"

#: searx/templates/simple/preferences/center_alignment.html:14
msgid "Displays results in the center of the page (Oscar layout)."
msgstr "Taispeánann torthaí i lár an leathanaigh (leagan amach Oscar)."

#: searx/templates/simple/preferences/cookies.html:2
msgid ""
"This is the list of cookies and their values SearXNG is storing on your "
"computer."
msgstr ""
"Seo an liosta fianáin agus a luachanna atá ag stóráil SearxNG ar do "
"ríomhaire."

#: searx/templates/simple/preferences/cookies.html:3
msgid "With that list, you can assess SearXNG transparency."
msgstr "Leis an liosta sin, is féidir leat trédhearcacht SearxNG a mheas."

#: searx/templates/simple/preferences/cookies.html:9
msgid "Cookie name"
msgstr "Ainm fianán"

#: searx/templates/simple/preferences/cookies.html:10
msgid "Value"
msgstr "Luach"

#: searx/templates/simple/preferences/cookies.html:23
msgid "Search URL of the currently saved preferences"
msgstr "Cuardaigh URL na roghanna atá sábháilte faoi láthair"

#: searx/templates/simple/preferences/cookies.html:32
msgid ""
"Note: specifying custom settings in the search URL can reduce privacy by "
"leaking data to the clicked result sites."
msgstr ""
"Tabhair faoi deara: má shonraítear socruithe saincheaptha sa URL "
"cuardaigh is féidir an phríobháideachas a laghdú trí shonraí a sceitheadh"
" chuig na suíomhanna toraidh a chliceáiltear."

#: searx/templates/simple/preferences/cookies.html:35
msgid "URL to restore your preferences in another browser"
msgstr "URL chun do roghanna a athbhunú i mbrabhsálaí eile"

#: searx/templates/simple/preferences/cookies.html:43
msgid ""
"A URL containing your preferences. This URL can be used to restore your "
"settings on a different device."
msgstr ""
"URL ina bhfuil do shainroghanna. Is féidir an URL seo a úsáid chun do "
"shocruithe a chur ar ais ar ghléas eile."

#: searx/templates/simple/preferences/cookies.html:46
msgid "Copy preferences hash"
msgstr "Cóipeáil roghanna hash"

#: searx/templates/simple/preferences/cookies.html:57
msgid "Insert copied preferences hash (without URL) to restore"
msgstr "Cuir isteach hash roghanna cóipeáilte (gan URL) chun athbhunú"

#: searx/templates/simple/preferences/cookies.html:59
msgid "Preferences hash"
msgstr "Roghanna hais"

#: searx/templates/simple/preferences/doi_resolver.html:1
msgid "Digital Object Identifier (DOI)"
msgstr "Aitheantóir Oibiachta Digiteach (DOI)"

#: searx/templates/simple/preferences/doi_resolver.html:6
msgid "Open Access DOI resolver"
msgstr "Réiteach DOI Rochtana Oscailte"

#: searx/templates/simple/preferences/doi_resolver.html:18
msgid "Select service used by DOI rewrite"
msgstr "Roghnaigh seirbhís a úsáideann DOI athscríobh"

#: searx/templates/simple/preferences/engines.html:9
msgid ""
"This tab does not exists in the user interface, but you can search in "
"these engines by its !bangs."
msgstr ""
"Níl an cluaisín seo ann sa chomhéadan úsáideora, ach is féidir leat "
"cuardach a dhéanamh sna hinnill seo de réir a! bangs."

#: searx/templates/simple/preferences/engines.html:15
msgid "Enable all"
msgstr "Cumasaigh gach duine"

#: searx/templates/simple/preferences/engines.html:16
msgid "Disable all"
msgstr "Díchumasaigh gach"

#: searx/templates/simple/preferences/engines.html:25
msgid "!bang"
msgstr "!bang"

#: searx/templates/simple/preferences/engines.html:26
msgid "Supports selected language"
msgstr "Tacaíonn le teanga roghnai"

#: searx/templates/simple/preferences/engines.html:29
msgid "Weight"
msgstr "Meáchan"

#: searx/templates/simple/preferences/engines.html:33
msgid "Max time"
msgstr "Uasmhéid ama"

#: searx/templates/simple/preferences/favicon.html:2
msgid "Favicon Resolver"
msgstr "Réiteach Favicon"

#: searx/templates/simple/preferences/favicon.html:15
msgid "Display favicons near search results"
msgstr "Taispeáin favicons in aice le torthaí cuardaigh"

#: searx/templates/simple/preferences/footer.html:2
msgid ""
"These settings are stored in your cookies, this allows us not to store "
"this data about you."
msgstr ""
"Stóráiltear na socruithe seo i do chuid fianáin, tugann sé seo deis dúinn"
" gan na sonraí seo a stóráil fút."

#: searx/templates/simple/preferences/footer.html:3
msgid ""
"These cookies serve your sole convenience, we don't use these cookies to "
"track you."
msgstr ""
"Freastalaíonn na fianáin seo ar d'aon áisiúlacht, ní úsáidimid na fianáin"
" seo chun tú a rianú."

#: searx/templates/simple/preferences/footer.html:6
msgid "Save"
msgstr "Sábháil"

#: searx/templates/simple/preferences/footer.html:9
msgid "Reset defaults"
msgstr "Athshocraigh réamhshocruithe"

#: searx/templates/simple/preferences/footer.html:13
msgid "Back"
msgstr "Ar ais"

#: searx/templates/simple/preferences/hotkeys.html:2
msgid "Hotkeys"
msgstr "Eochracha te"

#: searx/templates/simple/preferences/hotkeys.html:13
msgid "Vim-like"
msgstr "VIM-cosúil"

#: searx/templates/simple/preferences/hotkeys.html:18
msgid ""
"Navigate search results with hotkeys (JavaScript required). Press \"h\" "
"key on main or result page to get help."
msgstr ""
"Nascleanúint a dhéanamh ar thorthaí cuardaigh le hotkeys (JavaScript ag "
"teastáil). Brúigh eochair “h” ar an bpríomh-leathanach nó ar leathanach "
"torthaí chun cabhair a fháil."

#: searx/templates/simple/preferences/image_proxy.html:2
msgid "Image proxy"
msgstr "Proxy íomhá"

#: searx/templates/simple/preferences/image_proxy.html:14
msgid "Proxying image results through SearXNG"
msgstr "Torthaí íomhá seirbhíseach trí SearxNG"

#: searx/templates/simple/preferences/infinite_scroll.html:2
msgid "Infinite scroll"
msgstr "Scroll gan teorainn"

#: searx/templates/simple/preferences/infinite_scroll.html:14
msgid "Automatically load next page when scrolling to bottom of current page"
msgstr ""
"Déan an chéad leathanach eile a luchtú go huathoibríoch agus tú ag "
"scrollú go bun"

#: searx/templates/simple/preferences/language.html:24
msgid "What language do you prefer for search?"
msgstr "Cén teanga is fearr leat le haghaidh cuardaigh?"

#: searx/templates/simple/preferences/language.html:25
msgid "Choose Auto-detect to let SearXNG detect the language of your query."
msgstr ""
"Roghnaigh Auto-bhrath chun ligean do SearXNG teanga d'fhiosrúcháin a "
"bhrath."

#: searx/templates/simple/preferences/method.html:2
msgid "HTTP Method"
msgstr "Modh HTTP"

#: searx/templates/simple/preferences/method.html:14
msgid "Change how forms are submitted"
msgstr "Athraigh conas a chuirtear foirmeacha"

#: searx/templates/simple/preferences/query_in_title.html:2
msgid "Query in the page's title"
msgstr "Fiosrú i dteideal an leathanaigh"

#: searx/templates/simple/preferences/query_in_title.html:14
msgid ""
"When enabled, the result page's title contains your query. Your browser "
"can record this title"
msgstr ""
"Nuair a bheidh sé cumasaithe, beidh do cheist i dteideal leathanach an "
"toraidh. Is féidir le do bhrabhsálaí an teideal seo a thaifeadadh"

#: searx/templates/simple/preferences/results_on_new_tab.html:2
msgid "Results on new tabs"
msgstr "Torthaí ar chluaisíní nua"

#: searx/templates/simple/preferences/results_on_new_tab.html:14
msgid "Open result links on new browser tabs"
msgstr "Oscail naisc torthaí ar chluaisíní brabhsála"

#: searx/templates/simple/preferences/safesearch.html:20
msgid "Filter content"
msgstr "Ábhar scagaire"

#: searx/templates/simple/preferences/search_on_category_select.html:2
msgid "Search on category select"
msgstr "Cuardaigh ar roghnaigh catagóir"

#: searx/templates/simple/preferences/search_on_category_select.html:14
msgid ""
"Perform search immediately if a category selected. Disable to select "
"multiple categories"
msgstr ""
"Déan cuardach láithreach má roghnaíodh catagóir. Díchumasaigh chun "
"catagóirí iolracha"

#: searx/templates/simple/preferences/theme.html:2
msgid "Theme"
msgstr "Téama"

#: searx/templates/simple/preferences/theme.html:14
msgid "Change SearXNG layout"
msgstr "Athraigh leagan amach SearxNG"

#: searx/templates/simple/preferences/theme.html:19
msgid "Theme style"
msgstr "Stíl téama"

#: searx/templates/simple/preferences/theme.html:31
msgid "Choose auto to follow your browser settings"
msgstr "Roghnaigh uathoibríoch chun socruithe do bhra"

#: searx/templates/simple/preferences/tokens.html:2
msgid "Engine tokens"
msgstr "Comharthaí innill"

#: searx/templates/simple/preferences/tokens.html:9
msgid "Access tokens for private engines"
msgstr "Comharthaí rochtana d’innill phríobháideacha"

#: searx/templates/simple/preferences/ui_locale.html:2
msgid "Interface language"
msgstr "Teanga comhéadain"

#: searx/templates/simple/preferences/ui_locale.html:14
msgid "Change the language of the layout"
msgstr "Athraigh teanga an leagan amach"

#: searx/templates/simple/preferences/urlformatting.html:2
msgid "URL formatting"
msgstr "Formáidiú URL"

#: searx/templates/simple/preferences/urlformatting.html:8
msgid "Pretty"
msgstr "Deas"

#: searx/templates/simple/preferences/urlformatting.html:13
msgid "Full"
msgstr "Lán"

#: searx/templates/simple/preferences/urlformatting.html:18
msgid "Host"
msgstr "Óstach"

#: searx/templates/simple/preferences/urlformatting.html:23
msgid "Change result URL formatting"
msgstr "Athraigh formáidiú URL torthaí"

#: searx/templates/simple/result_templates/code.html:13
msgid "repo"
msgstr "stóras"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
#: searx/templates/simple/result_templates/files.html:11
msgid "show media"
msgstr "meáin taispeána"

#: searx/templates/simple/result_templates/default.html:6
#: searx/templates/simple/result_templates/files.html:8
msgid "hide media"
msgstr "na meáin a cheilt"

#: searx/templates/simple/result_templates/default.html:14
#: searx/templates/simple/result_templates/videos.html:14
msgid "This site did not provide any description."
msgstr "Níor thug an suíomh seo aon tuairisc ar fáil."

#: searx/templates/simple/result_templates/files.html:38
#: searx/templates/simple/result_templates/images.html:22
#: searx/templates/simple/result_templates/torrent.html:18
msgid "Filesize"
msgstr "Méid na Comhad"

#: searx/templates/simple/result_templates/files.html:40
msgid "Date"
msgstr "Dáta"

#: searx/templates/simple/result_templates/files.html:42
#: searx/templates/simple/result_templates/paper.html:24
msgid "Type"
msgstr "Cineál"

#: searx/templates/simple/result_templates/images.html:20
msgid "Resolution"
msgstr "Réiteach"

#: searx/templates/simple/result_templates/images.html:21
msgid "Format"
msgstr "Formáid"

#: searx/templates/simple/result_templates/images.html:24
msgid "Engine"
msgstr "Inneall"

#: searx/templates/simple/result_templates/images.html:25
msgid "View source"
msgstr "Féach foinse"

#: searx/templates/simple/result_templates/map.html:12
msgid "address"
msgstr "seoladh"

#: searx/templates/simple/result_templates/map.html:43
msgid "show map"
msgstr "taispeáin léarscáil"

#: searx/templates/simple/result_templates/map.html:43
msgid "hide map"
msgstr "folaigh léarscáil"

#: searx/templates/simple/result_templates/packages.html:12
msgid "Version"
msgstr "Leagan"

#: searx/templates/simple/result_templates/packages.html:18
msgid "Maintainer"
msgstr "Cothabhálaí"

#: searx/templates/simple/result_templates/packages.html:24
msgid "Updated at"
msgstr "Nuashonraithe ag"

#: searx/templates/simple/result_templates/packages.html:30
#: searx/templates/simple/result_templates/paper.html:25
msgid "Tags"
msgstr "Clibeanna"

#: searx/templates/simple/result_templates/packages.html:36
msgid "Popularity"
msgstr "Coitianta"

#: searx/templates/simple/result_templates/packages.html:42
msgid "License"
msgstr "Ceadúnas"

#: searx/templates/simple/result_templates/packages.html:52
msgid "Project"
msgstr "Tionscadal"

#: searx/templates/simple/result_templates/packages.html:55
msgid "Project homepage"
msgstr "Leathanach baile tionscad"

#: searx/templates/simple/result_templates/paper.html:5
msgid "Published date"
msgstr "Dáta foilsithe"

#: searx/templates/simple/result_templates/paper.html:9
msgid "Journal"
msgstr "Dialann"

#: searx/templates/simple/result_templates/paper.html:22
msgid "Editor"
msgstr "Eagarthóir"

#: searx/templates/simple/result_templates/paper.html:23
msgid "Publisher"
msgstr "Foilsitheoir"

#: searx/templates/simple/result_templates/paper.html:26
msgid "DOI"
msgstr "DOI"

#: searx/templates/simple/result_templates/paper.html:27
msgid "ISSN"
msgstr "ISSN"

#: searx/templates/simple/result_templates/paper.html:28
msgid "ISBN"
msgstr "ISBN"

#: searx/templates/simple/result_templates/paper.html:33
msgid "PDF"
msgstr "PDF"

#: searx/templates/simple/result_templates/paper.html:34
msgid "HTML"
msgstr "HTML"

#: searx/templates/simple/result_templates/torrent.html:7
msgid "magnet link"
msgstr "nasc maighnéad"

#: searx/templates/simple/result_templates/torrent.html:8
msgid "torrent file"
msgstr "comhad torrent"

#: searx/templates/simple/result_templates/torrent.html:13
msgid "Seeder"
msgstr "Síoltóir"

#: searx/templates/simple/result_templates/torrent.html:14
msgid "Leecher"
msgstr "Leecher"

#: searx/templates/simple/result_templates/torrent.html:19
msgid "Number of Files"
msgstr "Líon na gComhaid"

#: searx/templates/simple/result_templates/videos.html:6
msgid "show video"
msgstr "taispeáin físeán"

#: searx/templates/simple/result_templates/videos.html:6
msgid "hide video"
msgstr "físeán a cheilt"

#~ msgid "dummy"
#~ msgstr ""

#~ msgid "Random value generator"
#~ msgstr "Gineadóir luacha randamach"

#~ msgid "Statistics functions"
#~ msgstr "Feidhmeanna staitisticí"

#~ msgid "Compute {functions} of the arguments"
#~ msgstr "Ríomh {functions} na n-argóintí"

#~ msgid "Get directions"
#~ msgstr "Faigh treoracha"

#~ msgid ""
#~ "Displays your IP if the query is"
#~ " \"ip\" and your user agent if "
#~ "the query contains \"user agent\"."
#~ msgstr ""
#~ "Taispeánann sé do IP más “ip” an"
#~ " cheist agus do ghníomhaire úsáideora "
#~ "má tá “gníomhaire úsáideora” sa cheist."

#~ msgid ""
#~ "Could not download the list of Tor"
#~ " exit-nodes from: https://check.torproject.org"
#~ "/exit-addresses"
#~ msgstr ""
#~ "Ní fhéadfaí liosta na nóid imeachta "
#~ "Tor a íoslódáil ó: "
#~ "https://check.torproject.org/exit-addresses"

#~ msgid ""
#~ "You are using Tor and it looks "
#~ "like you have this external IP "
#~ "address: {ip_address}"
#~ msgstr ""
#~ "Tá Tor á úsáid agat agus is "
#~ "cosúil go bhfuil an seoladh IP "
#~ "seachtrach seo agat: {ip_address}"

#~ msgid ""
#~ "You are not using Tor and you "
#~ "have this external IP address: "
#~ "{ip_address}"
#~ msgstr ""
#~ "Níl Tor á úsáid agat agus tá "
#~ "an seoladh IP seachtrach seo agat: "
#~ "{ip_address}"

#~ msgid "Keywords"
#~ msgstr "Eochairfhocal"

#~ msgid "/"
#~ msgstr ""

#~ msgid ""
#~ "Specifying custom settings in the "
#~ "preferences URL can be used to "
#~ "sync preferences across devices."
#~ msgstr ""
#~ "Is féidir socruithe saincheaptha a "
#~ "shonrú sna roghanna URL a úsáid "
#~ "chun roghanna a shioncronú ar fud "
#~ "feistí."

#~ msgid "proxied"
#~ msgstr "trí sheachvótálaí"
