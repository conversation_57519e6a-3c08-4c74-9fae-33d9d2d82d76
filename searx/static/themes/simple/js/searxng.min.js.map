{"version": 3, "file": "searxng.min.js", "sources": ["../../../../../client/simple/src/js/main/00_toolkit.js", "../../../../../client/simple/src/js/main/infinite_scroll.js", "../../../../../client/simple/src/js/main/keyboard.js", "../../../../../client/simple/src/js/main/mapresult.js", "../../../../../client/simple/src/js/main/preferences.js", "../../../../../client/simple/node_modules/swiped-events/src/swiped-events.js", "../../../../../client/simple/src/js/main/results.js", "../../../../../client/simple/src/js/main/search.js"], "sourcesContent": ["/**\n * @license\n * (C) Copyright Contributors to the SearXNG project.\n * (C) Copyright Contributors to the searx project (2014 - 2021).\n * SPDX-License-Identifier: AGPL-3.0-or-later\n */\nwindow.searxng = (function (w, d) {\n\n  'use strict';\n\n  // not invented here toolkit with bugs fixed elsewhere\n  // purposes : be just good enough and as small as possible\n\n  // from https://plainjs.com/javascript/events/live-binding-event-handlers-14/\n  if (w.Element) {\n    (function (ElementPrototype) {\n      ElementPrototype.matches = ElementPrototype.matches ||\n      ElementPrototype.matchesSelector ||\n      ElementPrototype.webkitMatchesSelector ||\n      ElementPrototype.msMatchesSelector ||\n      function (selector) {\n        var node = this, nodes = (node.parentNode || node.document).querySelectorAll(selector), i = -1;\n        while (nodes[++i] && nodes[i] != node);\n        return !!nodes[i];\n      };\n    })(Element.prototype);\n  }\n\n  function callbackSafe (callback, el, e) {\n    try {\n      callback.call(el, e);\n    } catch (exception) {\n      console.log(exception);\n    }\n  }\n\n  var searxng = window.searxng || {};\n\n  searxng.on = function (obj, eventType, callback, useCapture) {\n    useCapture = useCapture || false;\n    if (typeof obj !== 'string') {\n      // obj HTMLElement, HTMLDocument\n      obj.addEventListener(eventType, callback, useCapture);\n    } else {\n      // obj is a selector\n      d.addEventListener(eventType, function (e) {\n        var el = e.target || e.srcElement, found = false;\n        while (el && el.matches && el !== d && !(found = el.matches(obj))) el = el.parentElement;\n        if (found) callbackSafe(callback, el, e);\n      }, useCapture);\n    }\n  };\n\n  searxng.ready = function (callback) {\n    if (document.readyState != 'loading') {\n      callback.call(w);\n    } else {\n      w.addEventListener('DOMContentLoaded', callback.bind(w));\n    }\n  };\n\n  searxng.http = function (method, url, data = null) {\n    return new Promise(function (resolve, reject) {\n      try {\n        var req = new XMLHttpRequest();\n        req.open(method, url, true);\n        req.timeout = 20000;\n\n        // On load\n        req.onload = function () {\n          if (req.status == 200) {\n            resolve(req.response, req.responseType);\n          } else {\n            reject(Error(req.statusText));\n          }\n        };\n\n        // Handle network errors\n        req.onerror = function () {\n          reject(Error(\"Network Error\"));\n        };\n\n        req.onabort = function () {\n          reject(Error(\"Transaction is aborted\"));\n        };\n\n        req.ontimeout = function () {\n          reject(Error(\"Timeout\"));\n        }\n\n        // Make the request\n        if (data) {\n          req.send(data)\n        } else {\n          req.send();\n        }\n      } catch (ex) {\n        reject(ex);\n      }\n    });\n  };\n\n  searxng.loadStyle = function (src) {\n    var path = searxng.settings.theme_static_path + \"/\" + src,\n      id = \"style_\" + src.replace('.', '_'),\n      s = d.getElementById(id);\n    if (s === null) {\n      s = d.createElement('link');\n      s.setAttribute('id', id);\n      s.setAttribute('rel', 'stylesheet');\n      s.setAttribute('type', 'text/css');\n      s.setAttribute('href', path);\n      d.body.appendChild(s);\n    }\n  };\n\n  searxng.loadScript = function (src, callback) {\n    var path = searxng.settings.theme_static_path + \"/\" + src,\n      id = \"script_\" + src.replace('.', '_'),\n      s = d.getElementById(id);\n    if (s === null) {\n      s = d.createElement('script');\n      s.setAttribute('id', id);\n      s.setAttribute('src', path);\n      s.onload = callback;\n      s.onerror = function () {\n        s.setAttribute('error', '1');\n      };\n      d.body.appendChild(s);\n    } else if (!s.hasAttribute('error')) {\n      try {\n        callback.apply(s, []);\n      } catch (exception) {\n        console.log(exception);\n      }\n    } else {\n      console.log(\"callback not executed : script '\" + path + \"' not loaded.\");\n    }\n  };\n\n  searxng.insertBefore = function (newNode, referenceNode) {\n    referenceNode.parentNode.insertBefore(newNode, referenceNode);\n  };\n\n  searxng.insertAfter = function (newNode, referenceNode) {\n    referenceNode.parentNode.insertAfter(newNode, referenceNode.nextSibling);\n  };\n\n  searxng.on('.close', 'click', function () {\n    this.parentNode.classList.add('invisible');\n  });\n\n  function getEndpoint () {\n    for (var className of d.getElementsByTagName('body')[0].classList.values()) {\n      if (className.endsWith('_endpoint')) {\n        return className.split('_')[0];\n      }\n    }\n    return '';\n  }\n\n  searxng.endpoint = getEndpoint();\n\n  return searxng;\n})(window, document);\n", "// SPDX-License-Identifier: AGPL-3.0-or-later\n\n/* global searxng */\n\nsearxng.ready(function () {\n  'use strict';\n\n  searxng.infinite_scroll_supported = (\n    'IntersectionObserver' in window &&\n    'IntersectionObserverEntry' in window &&\n    'intersectionRatio' in window.IntersectionObserverEntry.prototype);\n\n  if (searxng.endpoint !== 'results') {\n    return;\n  }\n\n  if (!searxng.infinite_scroll_supported) {\n    console.log('IntersectionObserver not supported');\n    return;\n  }\n\n  let d = document;\n  var onlyImages = d.getElementById('results').classList.contains('only_template_images');\n\n  function newLoadSpinner () {\n    var loader = d.createElement('div');\n    loader.classList.add('loader');\n    return loader;\n  }\n\n  function replaceChildrenWith (element, children) {\n    element.textContent = '';\n    children.forEach(child => element.appendChild(child));\n  }\n\n  function loadNextPage (callback) {\n    var form = d.querySelector('#pagination form.next_page');\n    if (!form) {\n      return\n    }\n    replaceChildrenWith(d.querySelector('#pagination'), [ newLoadSpinner() ]);\n    var formData = new FormData(form);\n    searxng.http('POST', d.querySelector('#search').getAttribute('action'), formData).then(\n      function (response) {\n        var nextPageDoc = new DOMParser().parseFromString(response, 'text/html');\n        var articleList = nextPageDoc.querySelectorAll('#urls article');\n        var paginationElement = nextPageDoc.querySelector('#pagination');\n        d.querySelector('#pagination').remove();\n        if (articleList.length > 0 && !onlyImages) {\n          // do not add <hr> element when there are only images\n          d.querySelector('#urls').appendChild(d.createElement('hr'));\n        }\n        articleList.forEach(articleElement => {\n          d.querySelector('#urls').appendChild(articleElement);\n        });\n        if (paginationElement) {\n          d.querySelector('#results').appendChild(paginationElement);\n          callback();\n        }\n      }\n    ).catch(\n      function (err) {\n        console.log(err);\n        var e = d.createElement('div');\n        e.textContent = searxng.settings.translations.error_loading_next_page;\n        e.classList.add('dialog-error');\n        e.setAttribute('role', 'alert');\n        replaceChildrenWith(d.querySelector('#pagination'), [ e ]);\n      }\n    )\n  }\n\n  if (searxng.settings.infinite_scroll && searxng.infinite_scroll_supported) {\n    const intersectionObserveOptions = {\n      rootMargin: \"20rem\",\n    };\n    const observedSelector = 'article.result:last-child';\n    const observer = new IntersectionObserver(entries => {\n      const paginationEntry = entries[0];\n      if (paginationEntry.isIntersecting) {\n        observer.unobserve(paginationEntry.target);\n        loadNextPage(() => observer.observe(d.querySelector(observedSelector), intersectionObserveOptions));\n      }\n    });\n    observer.observe(d.querySelector(observedSelector), intersectionObserveOptions);\n  }\n\n});\n", "/* SPDX-License-Identifier: AGPL-3.0-or-later */\n/* global searxng */\n\nsearxng.ready(function () {\n\n  function isElementInDetail (el) {\n    while (el !== undefined) {\n      if (el.classList.contains('detail')) {\n        return true;\n      }\n      if (el.classList.contains('result')) {\n        // we found a result, no need to go to the root of the document:\n        // el is not inside a <div class=\"detail\"> element\n        return false;\n      }\n      el = el.parentNode;\n    }\n    return false;\n  }\n\n  function getResultElement (el) {\n    while (el !== undefined) {\n      if (el.classList.contains('result')) {\n        return el;\n      }\n      el = el.parentNode;\n    }\n    return undefined;\n  }\n\n  function isImageResult (resultElement) {\n    return resultElement && resultElement.classList.contains('result-images');\n  }\n\n  searxng.on('.result', 'click', function (e) {\n    if (!isElementInDetail(e.target)) {\n      highlightResult(this)(true, true);\n      let resultElement = getResultElement(e.target);\n      if (isImageResult(resultElement)) {\n        e.preventDefault();\n        searxng.selectImage(resultElement);\n      }\n    }\n  });\n\n  searxng.on('.result a', 'focus', function (e) {\n    if (!isElementInDetail(e.target)) {\n      let resultElement = getResultElement(e.target);\n      if (resultElement && resultElement.getAttribute(\"data-vim-selected\") === null) {\n        highlightResult(resultElement)(true);\n      }\n      if (isImageResult(resultElement)) {\n        searxng.selectImage(resultElement);\n      }\n    }\n  }, true);\n\n  /* common base for layouts */\n  var baseKeyBinding = {\n    'Escape': {\n      key: 'ESC',\n      fun: removeFocus,\n      des: 'remove focus from the focused input',\n      cat: 'Control'\n    },\n    'c': {\n      key: 'c',\n      fun: copyURLToClipboard,\n      des: 'copy url of the selected result to the clipboard',\n      cat: 'Results'\n    },\n    'h': {\n      key: 'h',\n      fun: toggleHelp,\n      des: 'toggle help window',\n      cat: 'Other'\n    },\n    'i': {\n      key: 'i',\n      fun: searchInputFocus,\n      des: 'focus on the search input',\n      cat: 'Control'\n    },\n    'n': {\n      key: 'n',\n      fun: GoToNextPage(),\n      des: 'go to next page',\n      cat: 'Results'\n    },\n    'o': {\n      key: 'o',\n      fun: openResult(false),\n      des: 'open search result',\n      cat: 'Results'\n    },\n    'p': {\n      key: 'p',\n      fun: GoToPreviousPage(),\n      des: 'go to previous page',\n      cat: 'Results'\n    },\n    'r': {\n      key: 'r',\n      fun: reloadPage,\n      des: 'reload page from the server',\n      cat: 'Control'\n    },\n    't': {\n      key: 't',\n      fun: openResult(true),\n      des: 'open the result in a new tab',\n      cat: 'Results'\n    },\n  };\n  var keyBindingLayouts = {\n\n    \"default\": Object.assign(\n      { /* SearXNG layout */\n        'ArrowLeft': {\n          key: '←',\n          fun: highlightResult('up'),\n          des: 'select previous search result',\n          cat: 'Results'\n        },\n        'ArrowRight': {\n          key: '→',\n          fun: highlightResult('down'),\n          des: 'select next search result',\n          cat: 'Results'\n        },\n      }, baseKeyBinding),\n\n    'vim': Object.assign(\n      { /* Vim-like Key Layout. */\n        'b': {\n          key: 'b',\n          fun: scrollPage(-window.innerHeight),\n          des: 'scroll one page up',\n          cat: 'Navigation'\n        },\n        'f': {\n          key: 'f',\n          fun: scrollPage(window.innerHeight),\n          des: 'scroll one page down',\n          cat: 'Navigation'\n        },\n        'u': {\n          key: 'u',\n          fun: scrollPage(-window.innerHeight / 2),\n          des: 'scroll half a page up',\n          cat: 'Navigation'\n        },\n        'd': {\n          key: 'd',\n          fun: scrollPage(window.innerHeight / 2),\n          des: 'scroll half a page down',\n          cat: 'Navigation'\n        },\n        'g': {\n          key: 'g',\n          fun: scrollPageTo(-document.body.scrollHeight, 'top'),\n          des: 'scroll to the top of the page',\n          cat: 'Navigation'\n        },\n        'v': {\n          key: 'v',\n          fun: scrollPageTo(document.body.scrollHeight, 'bottom'),\n          des: 'scroll to the bottom of the page',\n          cat: 'Navigation'\n        },\n        'k': {\n          key: 'k',\n          fun: highlightResult('up'),\n          des: 'select previous search result',\n          cat: 'Results'\n        },\n        'j': {\n          key: 'j',\n          fun: highlightResult('down'),\n          des: 'select next search result',\n          cat: 'Results'\n        },\n        'y': {\n          key: 'y',\n          fun: copyURLToClipboard,\n          des: 'copy url of the selected result to the clipboard',\n          cat: 'Results'\n        },\n      }, baseKeyBinding)\n  }\n\n  var keyBindings = keyBindingLayouts[searxng.settings.hotkeys] || keyBindingLayouts.default;\n\n  searxng.on(document, \"keydown\", function (e) {\n    // check for modifiers so we don't break browser's hotkeys\n    if (\n      Object.prototype.hasOwnProperty.call(keyBindings, e.key)\n        && !e.ctrlKey && !e.altKey\n        && !e.shiftKey && !e.metaKey\n    ) {\n      var tagName = e.target.tagName.toLowerCase();\n      if (e.key === 'Escape') {\n        keyBindings[e.key].fun(e);\n      } else {\n        if (e.target === document.body || tagName === 'a' || tagName === 'button') {\n          e.preventDefault();\n          keyBindings[e.key].fun();\n        }\n      }\n    }\n  });\n\n  function highlightResult (which) {\n    return function (noScroll, keepFocus) {\n      var current = document.querySelector('.result[data-vim-selected]'),\n        effectiveWhich = which;\n      if (current === null) {\n        // no selection : choose the first one\n        current = document.querySelector('.result');\n        if (current === null) {\n          // no first one : there are no results\n          return;\n        }\n        // replace up/down actions by selecting first one\n        if (which === \"down\" || which === \"up\") {\n          effectiveWhich = current;\n        }\n      }\n\n      var next, results = document.querySelectorAll('.result');\n      results = Array.from(results);  // convert NodeList to Array for further use\n\n      if (typeof effectiveWhich !== 'string') {\n        next = effectiveWhich;\n      } else {\n        switch (effectiveWhich) {\n        case 'visible':\n          var top = document.documentElement.scrollTop || document.body.scrollTop;\n          var bot = top + document.documentElement.clientHeight;\n\n          for (var i = 0; i < results.length; i++) {\n            next = results[i];\n            var etop = next.offsetTop;\n            var ebot = etop + next.clientHeight;\n\n            if ((ebot <= bot) && (etop > top)) {\n              break;\n            }\n          }\n          break;\n        case 'down':\n          next = results[results.indexOf(current) + 1] || current;\n          break;\n        case 'up':\n          next = results[results.indexOf(current) - 1] || current;\n          break;\n        case 'bottom':\n          next = results[results.length - 1];\n          break;\n        case 'top':\n          /* falls through */\n        default:\n          next = results[0];\n        }\n      }\n\n      if (next) {\n        current.removeAttribute('data-vim-selected');\n        next.setAttribute('data-vim-selected', 'true');\n        if (!keepFocus) {\n          var link = next.querySelector('h3 a') || next.querySelector('a');\n          if (link !== null) {\n            link.focus();\n          }\n        }\n        if (!noScroll) {\n          scrollPageToSelected();\n        }\n      }\n    };\n  }\n\n  function reloadPage () {\n    document.location.reload(true);\n  }\n\n  function removeFocus (e) {\n    const tagName = e.target.tagName.toLowerCase();\n    if (document.activeElement && (tagName === 'input' || tagName === 'select' || tagName === 'textarea')) {\n      document.activeElement.blur();\n    } else {\n      searxng.closeDetail();\n    }\n  }\n\n  function pageButtonClick (css_selector) {\n    return function () {\n      var button = document.querySelector(css_selector);\n      if (button) {\n        button.click();\n      }\n    };\n  }\n\n  function GoToNextPage () {\n    return pageButtonClick('nav#pagination .next_page button[type=\"submit\"]');\n  }\n\n  function GoToPreviousPage () {\n    return pageButtonClick('nav#pagination .previous_page button[type=\"submit\"]');\n  }\n\n  function scrollPageToSelected () {\n    var sel = document.querySelector('.result[data-vim-selected]');\n    if (sel === null) {\n      return;\n    }\n    var wtop = document.documentElement.scrollTop || document.body.scrollTop,\n      wheight = document.documentElement.clientHeight,\n      etop = sel.offsetTop,\n      ebot = etop + sel.clientHeight,\n      offset = 120;\n    // first element ?\n    if ((sel.previousElementSibling === null) && (ebot < wheight)) {\n      // set to the top of page if the first element\n      // is fully included in the viewport\n      window.scroll(window.scrollX, 0);\n      return;\n    }\n    if (wtop > (etop - offset)) {\n      window.scroll(window.scrollX, etop - offset);\n    } else {\n      var wbot = wtop + wheight;\n      if (wbot < (ebot + offset)) {\n        window.scroll(window.scrollX, ebot - wheight + offset);\n      }\n    }\n  }\n\n  function scrollPage (amount) {\n    return function () {\n      window.scrollBy(0, amount);\n      highlightResult('visible')();\n    };\n  }\n\n  function scrollPageTo (position, nav) {\n    return function () {\n      window.scrollTo(0, position);\n      highlightResult(nav)();\n    };\n  }\n\n  function searchInputFocus () {\n    window.scrollTo(0, 0);\n    var q = document.querySelector('#q');\n    q.focus();\n    if (q.setSelectionRange) {\n      var len = q.value.length;\n      q.setSelectionRange(len, len);\n    }\n  }\n\n  function openResult (newTab) {\n    return function () {\n      var link = document.querySelector('.result[data-vim-selected] h3 a');\n      if (link === null) {\n        link = document.querySelector('.result[data-vim-selected] > a');\n      }\n      if (link !== null) {\n        var url = link.getAttribute('href');\n        if (newTab) {\n          window.open(url);\n        } else {\n          window.location.href = url;\n        }\n      }\n    };\n  }\n\n  function initHelpContent (divElement) {\n    var categories = {};\n\n    for (var k in keyBindings) {\n      var key = keyBindings[k];\n      categories[key.cat] = categories[key.cat] || [];\n      categories[key.cat].push(key);\n    }\n\n    var sorted = Object.keys(categories).sort(function (a, b) {\n      return categories[b].length - categories[a].length;\n    });\n\n    if (sorted.length === 0) {\n      return;\n    }\n\n    var html = '<a href=\"#\" class=\"close\" aria-label=\"close\" title=\"close\">×</a>';\n    html += '<h3>How to navigate SearXNG with hotkeys</h3>';\n    html += '<table>';\n\n    for (var i = 0; i < sorted.length; i++) {\n      var cat = categories[sorted[i]];\n\n      var lastCategory = i === (sorted.length - 1);\n      var first = i % 2 === 0;\n\n      if (first) {\n        html += '<tr>';\n      }\n      html += '<td>';\n\n      html += '<h4>' + cat[0].cat + '</h4>';\n      html += '<ul class=\"list-unstyled\">';\n\n      for (var cj in cat) {\n        html += '<li><kbd>' + cat[cj].key + '</kbd> ' + cat[cj].des + '</li>';\n      }\n\n      html += '</ul>';\n      html += '</td>'; // col-sm-*\n\n      if (!first || lastCategory) {\n        html += '</tr>'; // row\n      }\n    }\n\n    html += '</table>';\n\n    divElement.innerHTML = html;\n  }\n\n  function toggleHelp () {\n    var helpPanel = document.querySelector('#vim-hotkeys-help');\n    if (helpPanel === undefined || helpPanel === null) {\n      // first call\n      helpPanel = document.createElement('div');\n      helpPanel.id = 'vim-hotkeys-help';\n      helpPanel.className = 'dialog-modal';\n      initHelpContent(helpPanel);\n      var body = document.getElementsByTagName('body')[0];\n      body.appendChild(helpPanel);\n    } else {\n      // toggle hidden\n      helpPanel.classList.toggle('invisible');\n      return;\n    }\n  }\n\n  function copyURLToClipboard () {\n    var currentUrlElement = document.querySelector('.result[data-vim-selected] h3 a');\n    if (currentUrlElement === null) return;\n\n    const url = currentUrlElement.getAttribute('href');\n    navigator.clipboard.writeText(url);\n  }\n\n  searxng.scrollPageToSelected = scrollPageToSelected;\n  searxng.selectNext = highlightResult('down');\n  searxng.selectPrevious = highlightResult('up');\n});\n", "/* SPDX-License-Identifier: AGPL-3.0-or-later */\n/* global L */\n(function (w, d, searxng) {\n  'use strict';\n\n  searxng.ready(function () {\n    searxng.on('.searxng_init_map', 'click', function (event) {\n      // no more request\n      this.classList.remove(\"searxng_init_map\");\n\n      //\n      var leaflet_target = this.dataset.leafletTarget;\n      var map_lon = parseFloat(this.dataset.mapLon);\n      var map_lat = parseFloat(this.dataset.mapLat);\n      var map_zoom = parseFloat(this.dataset.mapZoom);\n      var map_boundingbox = JSON.parse(this.dataset.mapBoundingbox);\n      var map_geojson = JSON.parse(this.dataset.mapGeojson);\n\n      searxng.loadStyle('css/leaflet.css');\n      searxng.loadScript('js/leaflet.js', function () {\n        var map_bounds = null;\n        if (map_boundingbox) {\n          var southWest = L.latLng(map_boundingbox[0], map_boundingbox[2]);\n          var northEast = L.latLng(map_boundingbox[1], map_boundingbox[3]);\n          map_bounds = L.latLngBounds(southWest, northEast);\n        }\n\n        // init map\n        var map = L.map(leaflet_target);\n        // create the tile layer with correct attribution\n        var osmMapnikUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';\n        var osmMapnikAttrib = 'Map data © <a href=\"https://openstreetmap.org\">OpenStreetMap</a> contributors';\n        var osmMapnik = new L.TileLayer(osmMapnikUrl, {minZoom: 1, maxZoom: 19, attribution: osmMapnikAttrib});\n        var osmWikimediaUrl = 'https://maps.wikimedia.org/osm-intl/{z}/{x}/{y}.png';\n        var osmWikimediaAttrib = 'Wikimedia maps | Maps data © <a href=\"https://openstreetmap.org\">OpenStreetMap contributors</a>';\n        var osmWikimedia = new L.TileLayer(osmWikimediaUrl, {minZoom: 1, maxZoom: 19, attribution: osmWikimediaAttrib});\n        // init map view\n        if (map_bounds) {\n          // TODO hack: https://github.com/Leaflet/Leaflet/issues/2021\n          // Still useful ?\n          setTimeout(function () {\n            map.fitBounds(map_bounds, {\n              maxZoom: 17\n            });\n          }, 0);\n        } else if (map_lon && map_lat) {\n          if (map_zoom) {\n            map.setView(new L.latLng(map_lat, map_lon), map_zoom);\n          } else {\n            map.setView(new L.latLng(map_lat, map_lon), 8);\n          }\n        }\n\n        map.addLayer(osmMapnik);\n\n        var baseLayers = {\n          \"OSM Mapnik\": osmMapnik,\n          \"OSM Wikimedia\": osmWikimedia,\n        };\n\n        L.control.layers(baseLayers).addTo(map);\n\n        if (map_geojson) {\n          L.geoJson(map_geojson).addTo(map);\n        } /* else if(map_bounds) {\n          L.rectangle(map_bounds, {color: \"#ff7800\", weight: 3, fill:false}).addTo(map);\n        } */\n      });\n\n      // this event occur only once per element\n      event.preventDefault();\n    });\n  });\n})(window, document, window.searxng);\n", "/* SPDX-License-Identifier: AGPL-3.0-or-later */\n(function (w, d, searxng) {\n  'use strict';\n\n  if (searxng.endpoint !== 'preferences') {\n    return;\n  }\n\n  searxng.ready(function () {\n    let engine_descriptions = null;\n    function load_engine_descriptions () {\n      if (engine_descriptions == null) {\n        searxng.http(\"GET\", \"engine_descriptions.json\").then(function (content) {\n          engine_descriptions = JSON.parse(content);\n          for (const [engine_name, description] of Object.entries(engine_descriptions)) {\n            let elements = d.querySelectorAll('[data-engine-name=\"' + engine_name + '\"] .engine-description');\n            for (const element of elements) {\n              let source = ' (<i>' + searxng.settings.translations.Source + ':&nbsp;' + description[1] + '</i>)';\n              element.innerHTML = description[0] + source;\n            }\n          }\n        });\n      }\n    }\n\n    for (const el of d.querySelectorAll('[data-engine-name]')) {\n      searxng.on(el, 'mouseenter', load_engine_descriptions);\n    }\n\n    const enableAllEngines = d.querySelectorAll(\".enable-all-engines\");\n    const disableAllEngines = d.querySelectorAll(\".disable-all-engines\");\n    const engineToggles = d.querySelectorAll('tbody input[type=checkbox][class~=checkbox-onoff]');\n    const toggleEngines = (enable) => {\n      for (const el of engineToggles) {\n        // check if element visible, so that only engines of the current category are modified\n        if (el.offsetParent !== null) el.checked = !enable;\n      }\n    };\n    for (const el of enableAllEngines) {\n      searxng.on(el, 'click', () => toggleEngines(true));\n    }\n    for (const el of disableAllEngines) {\n      searxng.on(el, 'click', () => toggleEngines(false));\n    }\n\n    const copyHashButton = d.querySelector(\"#copy-hash\");\n    searxng.on(copyHashButton, 'click', (e) => {\n      e.preventDefault();\n      navigator.clipboard.writeText(copyHashButton.dataset.hash);\n      copyHashButton.innerText = copyHashButton.dataset.copiedText;\n    });\n  });\n})(window, document, window.searxng);\n", "/*!\n * swiped-events.js - v@version@\n * Pure JavaScript swipe events\n * https://github.com/john-doherty/swiped-events\n * @inspiration https://stackoverflow.com/questions/16348031/disable-scrolling-when-touch-moving-certain-element\n * <AUTHOR> <www.johndoherty.info>\n * @license MIT\n */\n(function (window, document) {\n\n    'use strict';\n\n    // patch CustomEvent to allow constructor creation (IE/Chrome)\n    if (typeof window.CustomEvent !== 'function') {\n\n        window.CustomEvent = function (event, params) {\n\n            params = params || { bubbles: false, cancelable: false, detail: undefined };\n\n            var evt = document.createEvent('CustomEvent');\n            evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);\n            return evt;\n        };\n\n        window.CustomEvent.prototype = window.Event.prototype;\n    }\n\n    document.addEventListener('touchstart', handleTouchStart, false);\n    document.addEventListener('touchmove', handleTouchMove, false);\n    document.addEventListener('touchend', handleTouchEnd, false);\n\n    var xDown = null;\n    var yDown = null;\n    var xDiff = null;\n    var yDiff = null;\n    var timeDown = null;\n    var startEl = null;\n    var touchCount = 0;\n\n    /**\n     * Fires swiped event if swipe detected on touchend\n     * @param {object} e - browser event object\n     * @returns {void}\n     */\n    function handleTouchEnd(e) {\n\n        // if the user released on a different target, cancel!\n        if (startEl !== e.target) return;\n\n        var swipeThreshold = parseInt(getNearestAttribute(startEl, 'data-swipe-threshold', '20'), 10); // default 20 units\n        var swipeUnit = getNearestAttribute(startEl, 'data-swipe-unit', 'px'); // default px\n        var swipeTimeout = parseInt(getNearestAttribute(startEl, 'data-swipe-timeout', '500'), 10);    // default 500ms\n        var timeDiff = Date.now() - timeDown;\n        var eventType = '';\n        var changedTouches = e.changedTouches || e.touches || [];\n\n        if (swipeUnit === 'vh') {\n            swipeThreshold = Math.round((swipeThreshold / 100) * document.documentElement.clientHeight); // get percentage of viewport height in pixels\n        }\n        if (swipeUnit === 'vw') {\n            swipeThreshold = Math.round((swipeThreshold / 100) * document.documentElement.clientWidth); // get percentage of viewport height in pixels\n        }\n\n        if (Math.abs(xDiff) > Math.abs(yDiff)) { // most significant\n            if (Math.abs(xDiff) > swipeThreshold && timeDiff < swipeTimeout) {\n                if (xDiff > 0) {\n                    eventType = 'swiped-left';\n                }\n                else {\n                    eventType = 'swiped-right';\n                }\n            }\n        }\n        else if (Math.abs(yDiff) > swipeThreshold && timeDiff < swipeTimeout) {\n            if (yDiff > 0) {\n                eventType = 'swiped-up';\n            }\n            else {\n                eventType = 'swiped-down';\n            }\n        }\n\n        if (eventType !== '') {\n\n            var eventData = {\n                dir: eventType.replace(/swiped-/, ''),\n                touchType: (changedTouches[0] || {}).touchType || 'direct',\n                fingers: touchCount, // Number of fingers used\n                xStart: parseInt(xDown, 10),\n                xEnd: parseInt((changedTouches[0] || {}).clientX || -1, 10),\n                yStart: parseInt(yDown, 10),\n                yEnd: parseInt((changedTouches[0] || {}).clientY || -1, 10)\n            };\n\n            // fire `swiped` event event on the element that started the swipe\n            startEl.dispatchEvent(new CustomEvent('swiped', { bubbles: true, cancelable: true, detail: eventData }));\n\n            // fire `swiped-dir` event on the element that started the swipe\n            startEl.dispatchEvent(new CustomEvent(eventType, { bubbles: true, cancelable: true, detail: eventData }));\n        }\n\n        // reset values\n        xDown = null;\n        yDown = null;\n        timeDown = null;\n    }\n    /**\n     * Records current location on touchstart event\n     * @param {object} e - browser event object\n     * @returns {void}\n     */\n    function handleTouchStart(e) {\n\n        // if the element has data-swipe-ignore=\"true\" we stop listening for swipe events\n        if (e.target.getAttribute('data-swipe-ignore') === 'true') return;\n\n        startEl = e.target;\n\n        timeDown = Date.now();\n        xDown = e.touches[0].clientX;\n        yDown = e.touches[0].clientY;\n        xDiff = 0;\n        yDiff = 0;\n        touchCount = e.touches.length;\n    }\n\n    /**\n     * Records location diff in px on touchmove event\n     * @param {object} e - browser event object\n     * @returns {void}\n     */\n    function handleTouchMove(e) {\n\n        if (!xDown || !yDown) return;\n\n        var xUp = e.touches[0].clientX;\n        var yUp = e.touches[0].clientY;\n\n        xDiff = xDown - xUp;\n        yDiff = yDown - yUp;\n    }\n\n    /**\n     * Gets attribute off HTML element or nearest parent\n     * @param {object} el - HTML element to retrieve attribute from\n     * @param {string} attributeName - name of the attribute\n     * @param {any} defaultValue - default value to return if no match found\n     * @returns {any} attribute value or defaultValue\n     */\n    function getNearestAttribute(el, attributeName, defaultValue) {\n\n        // walk up the dom tree looking for attributeName\n        while (el && el !== document.documentElement) {\n\n            var attributeValue = el.getAttribute(attributeName);\n\n            if (attributeValue) {\n                return attributeValue;\n            }\n\n            el = el.parentNode;\n        }\n\n        return defaultValue;\n    }\n\n}(window, document));\n", "/* SPDX-License-Identifier: AGPL-3.0-or-later */\n\nimport \"../../../node_modules/swiped-events/src/swiped-events.js\";\n\n(function (w, d, searxng) {\n  'use strict';\n\n  if (searxng.endpoint !== 'results') {\n    return;\n  }\n\n  searxng.ready(function () {\n    d.querySelectorAll('#urls img').forEach(\n      img =>\n        img.addEventListener(\n          'error', () => {\n            // console.log(\"ERROR can't load: \" + img.src);\n            img.src = window.searxng.settings.theme_static_path + \"/img/img_load_error.svg\";\n          },\n          {once: true}\n        ));\n\n    if (d.querySelector('#search_url button#copy_url')) {\n      d.querySelector('#search_url button#copy_url').style.display = \"block\";\n    }\n\n    searxng.on('.btn-collapse', 'click', function () {\n      var btnLabelCollapsed = this.getAttribute('data-btn-text-collapsed');\n      var btnLabelNotCollapsed = this.getAttribute('data-btn-text-not-collapsed');\n      var target = this.getAttribute('data-target');\n      var targetElement = d.querySelector(target);\n      var html = this.innerHTML;\n      if (this.classList.contains('collapsed')) {\n        html = html.replace(btnLabelCollapsed, btnLabelNotCollapsed);\n      } else {\n        html = html.replace(btnLabelNotCollapsed, btnLabelCollapsed);\n      }\n      this.innerHTML = html;\n      this.classList.toggle('collapsed');\n      targetElement.classList.toggle('invisible');\n    });\n\n    searxng.on('.media-loader', 'click', function () {\n      var target = this.getAttribute('data-target');\n      var iframe_load = d.querySelector(target + ' > iframe');\n      var srctest = iframe_load.getAttribute('src');\n      if (srctest === null || srctest === undefined || srctest === false) {\n        iframe_load.setAttribute('src', iframe_load.getAttribute('data-src'));\n      }\n    });\n\n    searxng.on('#copy_url', 'click', function () {\n      var target = this.parentElement.querySelector('pre');\n      navigator.clipboard.writeText(target.innerText);\n      this.innerText = this.dataset.copiedText;\n    });\n\n    // searxng.selectImage (gallery)\n    // -----------------------------\n\n    // setTimeout() ID, needed to cancel *last* loadImage\n    let imgTimeoutID;\n\n    // progress spinner, while an image is loading\n    const imgLoaderSpinner = d.createElement('div');\n    imgLoaderSpinner.classList.add('loader');\n\n    // singleton image object, which is used for all loading processes of a\n    // detailed image\n    const imgLoader = new Image();\n\n    const loadImage = (imgSrc, onSuccess) => {\n      // if defered image load exists, stop defered task.\n      if (imgTimeoutID) clearTimeout(imgTimeoutID);\n\n      // defer load of the detail image for 1 sec\n      imgTimeoutID = setTimeout(() => {\n        imgLoader.src = imgSrc;\n      }, 1000);\n\n      // set handlers in the on-properties\n      imgLoader.onload = () => {\n        onSuccess();\n        imgLoaderSpinner.remove();\n      };\n      imgLoader.onerror = () => {\n        imgLoaderSpinner.remove();\n      };\n    };\n\n    searxng.selectImage = (resultElement) => {\n\n      // add a class that can be evaluated in the CSS and indicates that the\n      // detail view is open\n      d.getElementById('results').classList.add('image-detail-open');\n\n      // add a hash to the browser history so that pressing back doesn't return\n      // to the previous page this allows us to dismiss the image details on\n      // pressing the back button on mobile devices\n      window.location.hash = '#image-viewer';\n\n      searxng.scrollPageToSelected();\n\n      // if there is none element given by the caller, stop here\n      if (!resultElement) return;\n\n      // find <img> object in the element, if there is none, stop here.\n      const img = resultElement.querySelector('.result-images-source img');\n      if (!img) return;\n\n      // <img src=\"\" data-src=\"http://example.org/image.jpg\">\n      const src = img.getAttribute('data-src');\n\n      // already loaded high-res image or no high-res image available\n      if (!src) return;\n\n      // use the image thumbnail until the image is fully loaded\n      const thumbnail = resultElement.querySelector('.image_thumbnail');\n      img.src = thumbnail.src;\n\n      // show a progress spinner\n      const detailElement = resultElement.querySelector('.detail');\n      detailElement.appendChild(imgLoaderSpinner);\n\n      // load full size image in background\n      loadImage(src, () => {\n        // after the singelton loadImage has loaded the detail image into the\n        // cache, it can be used in the origin <img> as src property.\n        img.src = src;\n        img.removeAttribute('data-src');\n      });\n    };\n\n    searxng.closeDetail = function () {\n      d.getElementById('results').classList.remove('image-detail-open');\n      // remove #image-viewer hash from url by navigating back\n      if (window.location.hash == '#image-viewer') window.history.back();\n      searxng.scrollPageToSelected();\n    };\n    searxng.on('.result-detail-close', 'click', e => {\n      e.preventDefault();\n      searxng.closeDetail();\n    });\n    searxng.on('.result-detail-previous', 'click', e => {\n      e.preventDefault();\n      searxng.selectPrevious(false);\n    });\n    searxng.on('.result-detail-next', 'click', e => {\n      e.preventDefault();\n      searxng.selectNext(false);\n    });\n\n    // listen for the back button to be pressed and dismiss the image details when called\n    window.addEventListener('hashchange', () => {\n      if (window.location.hash != '#image-viewer') searxng.closeDetail();\n    });\n\n    d.querySelectorAll('.swipe-horizontal').forEach(\n      obj => {\n        obj.addEventListener('swiped-left', function () {\n          searxng.selectNext(false);\n        });\n        obj.addEventListener('swiped-right', function () {\n          searxng.selectPrevious(false);\n        });\n      }\n    );\n\n    w.addEventListener('scroll', function () {\n      var e = d.getElementById('backToTop'),\n        scrollTop = document.documentElement.scrollTop || document.body.scrollTop,\n        results = d.getElementById('results');\n      if (e !== null) {\n        if (scrollTop >= 100) {\n          results.classList.add('scrolling');\n        } else {\n          results.classList.remove('scrolling');\n        }\n      }\n    }, true);\n\n  });\n\n})(window, document, window.searxng);\n", "/* SPDX-License-Identifier: AGPL-3.0-or-later */\n/* exported AutoComplete */\n\n(function (w, d, searxng) {\n  'use strict';\n\n  var qinput_id = \"q\", qinput;\n\n  const isMobile = window.matchMedia(\"only screen and (max-width: 50em)\").matches;\n  const isResultsPage = document.querySelector(\"main\").id == \"main_results\";\n\n  function submitIfQuery () {\n    if (qinput.value.length  > 0) {\n      var search = document.getElementById('search');\n      setTimeout(search.submit.bind(search), 0);\n    }\n  }\n\n  function createClearButton (qinput) {\n    var cs = document.getElementById('clear_search');\n    var updateClearButton = function () {\n      if (qinput.value.length === 0) {\n        cs.classList.add(\"empty\");\n      } else {\n        cs.classList.remove(\"empty\");\n      }\n    };\n\n    // update status, event listener\n    updateClearButton();\n    cs.addEventListener('click', function (ev) {\n      qinput.value = '';\n      qinput.focus();\n      updateClearButton();\n      ev.preventDefault();\n    });\n    qinput.addEventListener('input', updateClearButton, false);\n  }\n\n  const fetchResults = async (query) => {\n    let request;\n    if (searxng.settings.method === 'GET') {\n      const reqParams = new URLSearchParams();\n      reqParams.append(\"q\", query);\n      request = fetch(\"./autocompleter?\" + reqParams.toString());\n    } else {\n      const formData = new FormData();\n      formData.append(\"q\", query);\n      request = fetch(\"./autocompleter\", {\n        method: 'POST',\n        body: formData,\n      });\n    }\n\n    request.then(async function (response) {\n      const results = await response.json();\n\n      if (!results) return;\n\n      const autocomplete = d.querySelector(\".autocomplete\");\n      const autocompleteList = d.querySelector(\".autocomplete ul\");\n      autocomplete.classList.add(\"open\");\n      autocompleteList.innerHTML = \"\";\n\n      // show an error message that no result was found\n      if (!results[1] || results[1].length == 0) {\n        const noItemFoundMessage = document.createElement(\"li\");\n        noItemFoundMessage.classList.add('no-item-found');\n        noItemFoundMessage.innerHTML = searxng.settings.translations.no_item_found;\n        autocompleteList.appendChild(noItemFoundMessage);\n        return;\n      }\n\n      for (let result of results[1]) {\n        const li = document.createElement(\"li\");\n        li.innerText = result;\n\n        searxng.on(li, 'mousedown', () => {\n          qinput.value = result;\n          const form = d.querySelector(\"#search\");\n          form.submit();\n          autocomplete.classList.remove('open');\n        });\n        autocompleteList.appendChild(li);\n      }\n    });\n  };\n\n  searxng.ready(function () {\n    // focus search input on large screens\n    if (!isMobile && !isResultsPage) document.getElementById(\"q\").focus();\n\n    qinput = d.getElementById(qinput_id);\n    const autocomplete = d.querySelector(\".autocomplete\");\n    const autocompleteList = d.querySelector(\".autocomplete ul\");\n\n    if (qinput !== null) {\n      // clear button\n      createClearButton(qinput);\n\n      // autocompleter\n      if (searxng.settings.autocomplete) {\n        searxng.on(qinput, 'input', () => {\n          const query = qinput.value;\n          if (query.length < searxng.settings.autocomplete_min) return;\n\n          setTimeout(() => {\n            if (query == qinput.value) fetchResults(query);\n          }, 300);\n        });\n\n        searxng.on(qinput, 'keyup', (e) => {\n          let currentIndex = -1;\n          const listItems = autocompleteList.children;\n          for (let i = 0; i < listItems.length; i++) {\n            if (listItems[i].classList.contains('active')) {\n              currentIndex = i;\n              break;\n            }\n          }\n\n          let newCurrentIndex = -1;\n          if (e.key === \"ArrowUp\") {\n            if (currentIndex >= 0) listItems[currentIndex].classList.remove('active');\n            // we need to add listItems.length to the index calculation here because the JavaScript modulos\n            // operator doesn't work with negative numbers\n            newCurrentIndex = (currentIndex - 1 + listItems.length) % listItems.length;\n          } else if (e.key === \"ArrowDown\") {\n            if (currentIndex >= 0) listItems[currentIndex].classList.remove('active');\n            newCurrentIndex = (currentIndex + 1) % listItems.length;\n          } else if (e.key === \"Tab\" || e.key === \"Enter\") {\n            autocomplete.classList.remove('open');\n          }\n\n          if (newCurrentIndex != -1) {\n            const selectedItem = listItems[newCurrentIndex];\n            selectedItem.classList.add('active');\n\n            if (!selectedItem.classList.contains('no-item-found')) qinput.value = selectedItem.innerText;\n          }\n        });\n      }\n    }\n\n    // Additionally to searching when selecting a new category, we also\n    // automatically start a new search request when the user changes a search\n    // filter (safesearch, time range or language) (this requires JavaScript\n    // though)\n    if (\n      qinput !== null\n        && searxng.settings.search_on_category_select\n      // If .search_filters is undefined (invisible) we are on the homepage and\n      // hence don't have to set any listeners\n        && d.querySelector(\".search_filters\") != null\n    ) {\n      searxng.on(d.getElementById('safesearch'), 'change', submitIfQuery);\n      searxng.on(d.getElementById('time_range'), 'change', submitIfQuery);\n      searxng.on(d.getElementById('language'), 'change', submitIfQuery);\n    }\n\n    const categoryButtons = d.querySelectorAll(\"button.category_button\");\n    for (let button of categoryButtons) {\n      searxng.on(button, 'click', (event) => {\n        if (event.shiftKey) {\n          event.preventDefault();\n          button.classList.toggle(\"selected\");\n          return;\n        }\n\n        // manually deselect the old selection when a new category is selected\n        const selectedCategories = d.querySelectorAll(\"button.category_button.selected\");\n        for (let categoryButton of selectedCategories) {\n          categoryButton.classList.remove(\"selected\");\n        }\n        button.classList.add(\"selected\");\n      });\n    }\n\n    // override form submit action to update the actually selected categories\n    const form = d.querySelector(\"#search\");\n    if (form != null) {\n      searxng.on(form, 'submit', (event) => {\n        event.preventDefault();\n        const categoryValuesInput = d.querySelector(\"#selected-categories\");\n        if (categoryValuesInput) {\n          let categoryValues = [];\n          for (let categoryButton of categoryButtons) {\n            if (categoryButton.classList.contains(\"selected\")) {\n              categoryValues.push(categoryButton.name.replace(\"category_\", \"\"));\n            }\n          }\n          categoryValuesInput.value = categoryValues.join(\",\");\n        }\n        form.submit();\n      });\n    }\n  });\n\n})(window, document, window.searxng);\n"], "names": ["w", "d", "ElementPrototype", "selector", "node", "nodes", "i", "callbackSafe", "callback", "el", "e", "exception", "searxng", "obj", "eventType", "useCapture", "found", "method", "url", "data", "resolve", "reject", "req", "ex", "src", "path", "id", "s", "newNode", "referenceNode", "getEndpoint", "className", "onlyImages", "newLoadSpinner", "loader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "children", "child", "loadNextPage", "form", "formData", "response", "nextPageDoc", "articleList", "paginationElement", "articleElement", "err", "intersectionObserveOptions", "observedSelector", "observer", "entries", "paginationEntry", "isElementInDetail", "getResultElement", "isImageResult", "resultElement", "highlightResult", "baseKeyBinding", "removeFocus", "copyURLToClipboard", "toggleHelp", "searchInputFocus", "GoToNextPage", "openResult", "GoToPreviousPage", "reloadPage", "keyBindingLayouts", "scrollPage", "scrollPageTo", "keyBindings", "tagName", "which", "noScroll", "keepFocus", "current", "effectiveWhich", "next", "results", "top", "bot", "etop", "ebot", "link", "scrollPageToSelected", "pageButtonClick", "css_selector", "button", "sel", "wtop", "wheight", "offset", "wbot", "amount", "position", "nav", "q", "len", "newTab", "initHelpContent", "divElement", "categories", "k", "key", "sorted", "a", "b", "html", "cat", "lastCategory", "first", "cj", "helpPanel", "body", "currentUrlElement", "event", "leaflet_target", "map_lon", "map_lat", "map_zoom", "map_boundingbox", "map_geojson", "map_bounds", "southWest", "northEast", "map", "osmMapnikUrl", "osmMapnikAttrib", "osmMapnik", "osmWikimediaUrl", "osmWikimediaAttrib", "osmWikimedia", "baseLayers", "engine_descriptions", "load_engine_descriptions", "content", "engine_name", "description", "elements", "source", "enableAllEngines", "disableAllEngines", "engineToggles", "toggleEngines", "enable", "copyHashButton", "window", "document", "params", "evt", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "xDown", "yDown", "xDiff", "yDiff", "timeDown", "startEl", "touchCount", "swipe<PERSON><PERSON><PERSON><PERSON>", "getNearestAttribute", "swipeUnit", "swipeTimeout", "timeDiff", "changedTouches", "eventData", "xUp", "yUp", "attributeName", "defaultValue", "attributeValue", "img", "btnLabelCollapsed", "btnLabelNotCollapsed", "target", "targetElement", "iframe_load", "srctest", "imgTimeoutID", "imgLoaderSpinner", "imgL<PERSON>der", "loadImage", "imgSrc", "onSuccess", "thumbnail", "scrollTop", "qinput_id", "qinput", "isMobile", "isResultsPage", "submitIfQuery", "search", "createClearButton", "cs", "updateClearButton", "ev", "fetchResults", "query", "request", "reqParams", "autocomplete", "autocompleteList", "noItemFoundMessage", "result", "li", "currentIndex", "listItems", "newCurrentIndex", "selectedItem", "categoryButtons", "selectedCategories", "categoryButton", "categoryValuesInput", "categoryValues"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAMA,OAAO,QAAW,SAAUA,EAAGC,EAAG,CAQ5BD,EAAE,SACH,SAAUE,EAAkB,CAC3BA,EAAiB,QAAUA,EAAiB,SAC5CA,EAAiB,iBACjBA,EAAiB,uBACjBA,EAAiB,mBACjB,SAAUC,EAAU,CAElB,QADIC,EAAO,KAAMC,GAASD,EAAK,YAAcA,EAAK,UAAU,iBAAiBD,CAAQ,EAAGG,EAAI,GACrFD,EAAM,EAAEC,CAAC,GAAKD,EAAMC,CAAC,GAAKF,GAAK,CACtC,MAAO,CAAC,CAACC,EAAMC,CAAC,CACjB,CACP,EAAO,QAAQ,SAAS,EAGtB,SAASC,EAAcC,EAAUC,EAAIC,EAAG,CACtC,GAAI,CACFF,EAAS,KAAKC,EAAIC,CAAC,CACpB,OAAQC,EAAW,CAClB,QAAQ,IAAIA,CAAS,CAC3B,CACA,CAEE,IAAIC,EAAU,OAAO,SAAW,CAAE,EAElCA,EAAQ,GAAK,SAAUC,EAAKC,EAAWN,EAAUO,EAAY,CAC3DA,EAAaA,GAAc,GACvB,OAAOF,GAAQ,SAEjBA,EAAI,iBAAiBC,EAAWN,EAAUO,CAAU,EAGpDd,EAAE,iBAAiBa,EAAW,SAAUJ,EAAG,CAEzC,QADID,EAAKC,EAAE,QAAUA,EAAE,WAAYM,EAAQ,GACpCP,GAAMA,EAAG,SAAWA,IAAOR,GAAK,EAAEe,EAAQP,EAAG,QAAQI,CAAG,IAAIJ,EAAKA,EAAG,cACvEO,GAAOT,EAAaC,EAAUC,EAAIC,CAAC,CACxC,EAAEK,CAAU,CAEhB,EAEDH,EAAQ,MAAQ,SAAUJ,EAAU,CAC9B,SAAS,YAAc,UACzBA,EAAS,KAAKR,CAAC,EAEfA,EAAE,iBAAiB,mBAAoBQ,EAAS,KAAKR,CAAC,CAAC,CAE1D,EAEDY,EAAQ,KAAO,SAAUK,EAAQC,EAAKC,EAAO,KAAM,CACjD,OAAO,IAAI,QAAQ,SAAUC,EAASC,EAAQ,CAC5C,GAAI,CACF,IAAIC,EAAM,IAAI,eACdA,EAAI,KAAKL,EAAQC,EAAK,EAAI,EAC1BI,EAAI,QAAU,IAGdA,EAAI,OAAS,UAAY,CACnBA,EAAI,QAAU,IAChBF,EAAQE,EAAI,SAAUA,EAAI,YAAY,EAEtCD,EAAO,MAAMC,EAAI,UAAU,CAAC,CAE/B,EAGDA,EAAI,QAAU,UAAY,CACxBD,EAAO,MAAM,eAAe,CAAC,CAC9B,EAEDC,EAAI,QAAU,UAAY,CACxBD,EAAO,MAAM,wBAAwB,CAAC,CACvC,EAEDC,EAAI,UAAY,UAAY,CAC1BD,EAAO,MAAM,SAAS,CAAC,CACjC,EAGYF,EACFG,EAAI,KAAKH,CAAI,EAEbG,EAAI,KAAM,CAEb,OAAQC,EAAI,CACXF,EAAOE,CAAE,CACjB,CACA,CAAK,CACF,EAEDX,EAAQ,UAAY,SAAUY,EAAK,CACjC,IAAIC,EAAOb,EAAQ,SAAS,kBAAoB,IAAMY,EACpDE,EAAK,SAAWF,EAAI,QAAQ,IAAK,GAAG,EACpCG,EAAI1B,EAAE,eAAeyB,CAAE,EACrBC,IAAM,OACRA,EAAI1B,EAAE,cAAc,MAAM,EAC1B0B,EAAE,aAAa,KAAMD,CAAE,EACvBC,EAAE,aAAa,MAAO,YAAY,EAClCA,EAAE,aAAa,OAAQ,UAAU,EACjCA,EAAE,aAAa,OAAQF,CAAI,EAC3BxB,EAAE,KAAK,YAAY0B,CAAC,EAEvB,EAEDf,EAAQ,WAAa,SAAUY,EAAKhB,EAAU,CAC5C,IAAIiB,EAAOb,EAAQ,SAAS,kBAAoB,IAAMY,EACpDE,EAAK,UAAYF,EAAI,QAAQ,IAAK,GAAG,EACrCG,EAAI1B,EAAE,eAAeyB,CAAE,EACzB,GAAIC,IAAM,KACRA,EAAI1B,EAAE,cAAc,QAAQ,EAC5B0B,EAAE,aAAa,KAAMD,CAAE,EACvBC,EAAE,aAAa,MAAOF,CAAI,EAC1BE,EAAE,OAASnB,EACXmB,EAAE,QAAU,UAAY,CACtBA,EAAE,aAAa,QAAS,GAAG,CAC5B,EACD1B,EAAE,KAAK,YAAY0B,CAAC,UACVA,EAAE,aAAa,OAAO,EAOhC,QAAQ,IAAI,mCAAqCF,EAAO,eAAe,MANvE,IAAI,CACFjB,EAAS,MAAMmB,EAAG,EAAE,CACrB,OAAQhB,EAAW,CAClB,QAAQ,IAAIA,CAAS,CAC7B,CAIG,EAEDC,EAAQ,aAAe,SAAUgB,EAASC,EAAe,CACvDA,EAAc,WAAW,aAAaD,EAASC,CAAa,CAC7D,EAEDjB,EAAQ,YAAc,SAAUgB,EAASC,EAAe,CACtDA,EAAc,WAAW,YAAYD,EAASC,EAAc,WAAW,CACxE,EAEDjB,EAAQ,GAAG,SAAU,QAAS,UAAY,CACxC,KAAK,WAAW,UAAU,IAAI,WAAW,CAC7C,CAAG,EAED,SAASkB,GAAe,CACtB,QAASC,KAAa9B,EAAE,qBAAqB,MAAM,EAAE,CAAC,EAAE,UAAU,SAChE,GAAI8B,EAAU,SAAS,WAAW,EAChC,OAAOA,EAAU,MAAM,GAAG,EAAE,CAAC,EAGjC,MAAO,EACX,CAEE,OAAAnB,EAAQ,SAAWkB,EAAa,EAEzBlB,CACT,EAAG,OAAQ,QAAQ,EChKnB,QAAQ,MAAM,UAAY,CAQxB,GALA,QAAQ,0BACN,yBAA0B,QAC1B,8BAA+B,QAC/B,sBAAuB,OAAO,0BAA0B,UAEtD,QAAQ,WAAa,UACvB,OAGF,GAAI,CAAC,QAAQ,0BAA2B,CACtC,QAAQ,IAAI,oCAAoC,EAChD,MACJ,CAEE,IAAIX,EAAI,SACR,IAAI+B,EAAa/B,EAAE,eAAe,SAAS,EAAE,UAAU,SAAS,sBAAsB,EAEtF,SAASgC,GAAkB,CACzB,IAAIC,EAASjC,EAAE,cAAc,KAAK,EAClC,OAAAiC,EAAO,UAAU,IAAI,QAAQ,EACtBA,CACX,CAEE,SAASC,EAAqBC,EAASC,EAAU,CAC/CD,EAAQ,YAAc,GACtBC,EAAS,QAAQC,GAASF,EAAQ,YAAYE,CAAK,CAAC,CACxD,CAEE,SAASC,EAAc/B,EAAU,CAC/B,IAAIgC,EAAOvC,EAAE,cAAc,4BAA4B,EACvD,GAAKuC,EAGL,CAAAL,EAAoBlC,EAAE,cAAc,aAAa,EAAG,CAAEgC,EAAc,EAAI,EACxE,IAAIQ,EAAW,IAAI,SAASD,CAAI,EAChC,QAAQ,KAAK,OAAQvC,EAAE,cAAc,SAAS,EAAE,aAAa,QAAQ,EAAGwC,CAAQ,EAAE,KAChF,SAAUC,EAAU,CAClB,IAAIC,EAAc,IAAI,UAAS,EAAG,gBAAgBD,EAAU,WAAW,EACnEE,EAAcD,EAAY,iBAAiB,eAAe,EAC1DE,EAAoBF,EAAY,cAAc,aAAa,EAC/D1C,EAAE,cAAc,aAAa,EAAE,OAAQ,EACnC2C,EAAY,OAAS,GAAK,CAACZ,GAE7B/B,EAAE,cAAc,OAAO,EAAE,YAAYA,EAAE,cAAc,IAAI,CAAC,EAE5D2C,EAAY,QAAQE,GAAkB,CACpC7C,EAAE,cAAc,OAAO,EAAE,YAAY6C,CAAc,CAC7D,CAAS,EACGD,IACF5C,EAAE,cAAc,UAAU,EAAE,YAAY4C,CAAiB,EACzDrC,EAAU,EAEpB,CACA,EAAM,MACA,SAAUuC,EAAK,CACb,QAAQ,IAAIA,CAAG,EACf,IAAIrC,EAAIT,EAAE,cAAc,KAAK,EAC7BS,EAAE,YAAc,QAAQ,SAAS,aAAa,wBAC9CA,EAAE,UAAU,IAAI,cAAc,EAC9BA,EAAE,aAAa,OAAQ,OAAO,EAC9ByB,EAAoBlC,EAAE,cAAc,aAAa,EAAG,CAAES,CAAC,CAAE,CACjE,CACA,EACA,CAEE,GAAI,QAAQ,SAAS,iBAAmB,QAAQ,0BAA2B,CACzE,MAAMsC,EAA6B,CACjC,WAAY,OACb,EACKC,EAAmB,4BACnBC,EAAW,IAAI,qBAAqBC,GAAW,CACnD,MAAMC,EAAkBD,EAAQ,CAAC,EAC7BC,EAAgB,iBAClBF,EAAS,UAAUE,EAAgB,MAAM,EACzCb,EAAa,IAAMW,EAAS,QAAQjD,EAAE,cAAcgD,CAAgB,EAAGD,CAA0B,CAAC,EAE1G,CAAK,EACDE,EAAS,QAAQjD,EAAE,cAAcgD,CAAgB,EAAGD,CAA0B,CAClF,CAEA,CAAC,ECpFD,QAAQ,MAAM,UAAY,CAExB,SAASK,EAAmB5C,EAAI,CAC9B,KAAOA,IAAO,QAAW,CACvB,GAAIA,EAAG,UAAU,SAAS,QAAQ,EAChC,MAAO,GAET,GAAIA,EAAG,UAAU,SAAS,QAAQ,EAGhC,MAAO,GAETA,EAAKA,EAAG,UACd,CACI,MAAO,EACX,CAEE,SAAS6C,EAAkB7C,EAAI,CAC7B,KAAOA,IAAO,QAAW,CACvB,GAAIA,EAAG,UAAU,SAAS,QAAQ,EAChC,OAAOA,EAETA,EAAKA,EAAG,UACd,CAEA,CAEE,SAAS8C,EAAeC,EAAe,CACrC,OAAOA,GAAiBA,EAAc,UAAU,SAAS,eAAe,CAC5E,CAEE,QAAQ,GAAG,UAAW,QAAS,SAAU,EAAG,CAC1C,GAAI,CAACH,EAAkB,EAAE,MAAM,EAAG,CAChCI,EAAgB,IAAI,EAAE,GAAM,EAAI,EAChC,IAAID,EAAgBF,EAAiB,EAAE,MAAM,EACzCC,EAAcC,CAAa,IAC7B,EAAE,eAAgB,EAClB,QAAQ,YAAYA,CAAa,EAEzC,CACA,CAAG,EAED,QAAQ,GAAG,YAAa,QAAS,SAAU,EAAG,CAC5C,GAAI,CAACH,EAAkB,EAAE,MAAM,EAAG,CAChC,IAAIG,EAAgBF,EAAiB,EAAE,MAAM,EACzCE,GAAiBA,EAAc,aAAa,mBAAmB,IAAM,MACvEC,EAAgBD,CAAa,EAAE,EAAI,EAEjCD,EAAcC,CAAa,GAC7B,QAAQ,YAAYA,CAAa,CAEzC,CACG,EAAE,EAAI,EAGP,IAAIE,EAAiB,CACnB,OAAU,CACR,IAAK,MACL,IAAKC,EACL,IAAK,sCACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EACL,IAAK,mDACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EACL,IAAK,qBACL,IAAK,OACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EACL,IAAK,4BACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EAAc,EACnB,IAAK,kBACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EAAW,EAAK,EACrB,IAAK,qBACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EAAkB,EACvB,IAAK,sBACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EACL,IAAK,8BACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKF,EAAW,EAAI,EACpB,IAAK,+BACL,IAAK,SACN,CACF,EACGG,EAAoB,CAEtB,QAAW,OAAO,OAChB,CACE,UAAa,CACX,IAAK,IACL,IAAKV,EAAgB,IAAI,EACzB,IAAK,gCACL,IAAK,SACN,EACD,WAAc,CACZ,IAAK,IACL,IAAKA,EAAgB,MAAM,EAC3B,IAAK,4BACL,IAAK,SACN,CACF,EAAEC,CAAc,EAEnB,IAAO,OAAO,OACZ,CACE,EAAK,CACH,IAAK,IACL,IAAKU,EAAW,CAAC,OAAO,WAAW,EACnC,IAAK,qBACL,IAAK,YACN,EACD,EAAK,CACH,IAAK,IACL,IAAKA,EAAW,OAAO,WAAW,EAClC,IAAK,uBACL,IAAK,YACN,EACD,EAAK,CACH,IAAK,IACL,IAAKA,EAAW,CAAC,OAAO,YAAc,CAAC,EACvC,IAAK,wBACL,IAAK,YACN,EACD,EAAK,CACH,IAAK,IACL,IAAKA,EAAW,OAAO,YAAc,CAAC,EACtC,IAAK,0BACL,IAAK,YACN,EACD,EAAK,CACH,IAAK,IACL,IAAKC,EAAa,CAAC,SAAS,KAAK,aAAc,KAAK,EACpD,IAAK,gCACL,IAAK,YACN,EACD,EAAK,CACH,IAAK,IACL,IAAKA,EAAa,SAAS,KAAK,aAAc,QAAQ,EACtD,IAAK,mCACL,IAAK,YACN,EACD,EAAK,CACH,IAAK,IACL,IAAKZ,EAAgB,IAAI,EACzB,IAAK,gCACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKA,EAAgB,MAAM,EAC3B,IAAK,4BACL,IAAK,SACN,EACD,EAAK,CACH,IAAK,IACL,IAAKG,EACL,IAAK,mDACL,IAAK,SACN,CACT,EAASF,CAAc,CACvB,EAEMY,EAAcH,EAAkB,QAAQ,SAAS,OAAO,GAAKA,EAAkB,QAEnF,QAAQ,GAAG,SAAU,UAAW,SAAU,EAAG,CAE3C,GACE,OAAO,UAAU,eAAe,KAAKG,EAAa,EAAE,GAAG,GAClD,CAAC,EAAE,SAAW,CAAC,EAAE,QACjB,CAAC,EAAE,UAAY,CAAC,EAAE,QACvB,CACA,IAAIC,EAAU,EAAE,OAAO,QAAQ,YAAa,EACxC,EAAE,MAAQ,SACZD,EAAY,EAAE,GAAG,EAAE,IAAI,CAAC,GAEpB,EAAE,SAAW,SAAS,MAAQC,IAAY,KAAOA,IAAY,YAC/D,EAAE,eAAgB,EAClBD,EAAY,EAAE,GAAG,EAAE,IAAK,EAGlC,CACA,CAAG,EAED,SAASb,EAAiBe,EAAO,CAC/B,OAAO,SAAUC,EAAUC,EAAW,CACpC,IAAIC,EAAU,SAAS,cAAc,4BAA4B,EAC/DC,EAAiBJ,EACnB,GAAIG,IAAY,KAAM,CAGpB,GADAA,EAAU,SAAS,cAAc,SAAS,EACtCA,IAAY,KAEd,QAGEH,IAAU,QAAUA,IAAU,QAChCI,EAAiBD,EAE3B,CAEM,IAAIE,EAAMC,EAAU,SAAS,iBAAiB,SAAS,EAGvD,GAFAA,EAAU,MAAM,KAAKA,CAAO,EAExB,OAAOF,GAAmB,SAC5BC,EAAOD,MAEP,QAAQA,EAAc,CACtB,IAAK,UAIH,QAHIG,EAAM,SAAS,gBAAgB,WAAa,SAAS,KAAK,UAC1DC,EAAMD,EAAM,SAAS,gBAAgB,aAEhCzE,EAAI,EAAGA,EAAIwE,EAAQ,OAAQxE,IAAK,CACvCuE,EAAOC,EAAQxE,CAAC,EAChB,IAAI2E,EAAOJ,EAAK,UACZK,EAAOD,EAAOJ,EAAK,aAEvB,GAAKK,GAAQF,GAASC,EAAOF,EAC3B,KAEd,CACU,MACF,IAAK,OACHF,EAAOC,EAAQA,EAAQ,QAAQH,CAAO,EAAI,CAAC,GAAKA,EAChD,MACF,IAAK,KACHE,EAAOC,EAAQA,EAAQ,QAAQH,CAAO,EAAI,CAAC,GAAKA,EAChD,MACF,IAAK,SACHE,EAAOC,EAAQA,EAAQ,OAAS,CAAC,EACjC,MACF,IAAK,MAEL,QACED,EAAOC,EAAQ,CAAC,CAC1B,CAGM,GAAID,EAAM,CAGR,GAFAF,EAAQ,gBAAgB,mBAAmB,EAC3CE,EAAK,aAAa,oBAAqB,MAAM,EACzC,CAACH,EAAW,CACd,IAAIS,EAAON,EAAK,cAAc,MAAM,GAAKA,EAAK,cAAc,GAAG,EAC3DM,IAAS,MACXA,EAAK,MAAO,CAExB,CACaV,GACHW,EAAsB,CAEhC,CACK,CACL,CAEE,SAASlB,GAAc,CACrB,SAAS,SAAS,OAAO,EAAI,CACjC,CAEE,SAASP,EAAa,EAAG,CACvB,MAAMY,EAAU,EAAE,OAAO,QAAQ,YAAa,EAC1C,SAAS,gBAAkBA,IAAY,SAAWA,IAAY,UAAYA,IAAY,YACxF,SAAS,cAAc,KAAM,EAE7B,QAAQ,YAAa,CAE3B,CAEE,SAASc,EAAiBC,EAAc,CACtC,OAAO,UAAY,CACjB,IAAIC,EAAS,SAAS,cAAcD,CAAY,EAC5CC,GACFA,EAAO,MAAO,CAEjB,CACL,CAEE,SAASxB,GAAgB,CACvB,OAAOsB,EAAgB,iDAAiD,CAC5E,CAEE,SAASpB,GAAoB,CAC3B,OAAOoB,EAAgB,qDAAqD,CAChF,CAEE,SAASD,GAAwB,CAC/B,IAAII,EAAM,SAAS,cAAc,4BAA4B,EAC7D,GAAIA,IAAQ,KAGZ,KAAIC,EAAO,SAAS,gBAAgB,WAAa,SAAS,KAAK,UAC7DC,EAAU,SAAS,gBAAgB,aACnCT,EAAOO,EAAI,UACXN,EAAOD,EAAOO,EAAI,aAClBG,EAAS,IAEX,GAAKH,EAAI,yBAA2B,MAAUN,EAAOQ,EAAU,CAG7D,OAAO,OAAO,OAAO,QAAS,CAAC,EAC/B,MACN,CACI,GAAID,EAAQR,EAAOU,EACjB,OAAO,OAAO,OAAO,QAASV,EAAOU,CAAM,MACtC,CACL,IAAIC,EAAOH,EAAOC,EACdE,EAAQV,EAAOS,GACjB,OAAO,OAAO,OAAO,QAAST,EAAOQ,EAAUC,CAAM,CAE7D,EACA,CAEE,SAASvB,EAAYyB,EAAQ,CAC3B,OAAO,UAAY,CACjB,OAAO,SAAS,EAAGA,CAAM,EACzBpC,EAAgB,SAAS,EAAG,CAC7B,CACL,CAEE,SAASY,EAAcyB,EAAUC,EAAK,CACpC,OAAO,UAAY,CACjB,OAAO,SAAS,EAAGD,CAAQ,EAC3BrC,EAAgBsC,CAAG,EAAG,CACvB,CACL,CAEE,SAASjC,GAAoB,CAC3B,OAAO,SAAS,EAAG,CAAC,EACpB,IAAIkC,EAAI,SAAS,cAAc,IAAI,EAEnC,GADAA,EAAE,MAAO,EACLA,EAAE,kBAAmB,CACvB,IAAIC,EAAMD,EAAE,MAAM,OAClBA,EAAE,kBAAkBC,EAAKA,CAAG,CAClC,CACA,CAEE,SAASjC,EAAYkC,EAAQ,CAC3B,OAAO,UAAY,CACjB,IAAIf,EAAO,SAAS,cAAc,iCAAiC,EAInE,GAHIA,IAAS,OACXA,EAAO,SAAS,cAAc,gCAAgC,GAE5DA,IAAS,KAAM,CACjB,IAAIjE,EAAMiE,EAAK,aAAa,MAAM,EAC9Be,EACF,OAAO,KAAKhF,CAAG,EAEf,OAAO,SAAS,KAAOA,CAEjC,CACK,CACL,CAEE,SAASiF,EAAiBC,EAAY,CACpC,IAAIC,EAAa,CAAE,EAEnB,QAASC,KAAKhC,EAAa,CACzB,IAAIiC,EAAMjC,EAAYgC,CAAC,EACvBD,EAAWE,EAAI,GAAG,EAAIF,EAAWE,EAAI,GAAG,GAAK,CAAE,EAC/CF,EAAWE,EAAI,GAAG,EAAE,KAAKA,CAAG,CAClC,CAEI,IAAIC,EAAS,OAAO,KAAKH,CAAU,EAAE,KAAK,SAAUI,EAAGC,EAAG,CACxD,OAAOL,EAAWK,CAAC,EAAE,OAASL,EAAWI,CAAC,EAAE,MAClD,CAAK,EAED,GAAID,EAAO,SAAW,EAItB,KAAIG,EAAO,mEACXA,GAAQ,gDACRA,GAAQ,UAER,QAASrG,EAAI,EAAGA,EAAIkG,EAAO,OAAQlG,IAAK,CACtC,IAAIsG,EAAMP,EAAWG,EAAOlG,CAAC,CAAC,EAE1BuG,EAAevG,IAAOkG,EAAO,OAAS,EACtCM,EAAQxG,EAAI,IAAM,EAElBwG,IACFH,GAAQ,QAEVA,GAAQ,OAERA,GAAQ,OAASC,EAAI,CAAC,EAAE,IAAM,QAC9BD,GAAQ,6BAER,QAASI,KAAMH,EACbD,GAAQ,YAAcC,EAAIG,CAAE,EAAE,IAAM,UAAYH,EAAIG,CAAE,EAAE,IAAM,QAGhEJ,GAAQ,QACRA,GAAQ,SAEJ,CAACG,GAASD,KACZF,GAAQ,QAEhB,CAEIA,GAAQ,WAERP,EAAW,UAAYO,EAC3B,CAEE,SAAS9C,GAAc,CACrB,IAAImD,EAAY,SAAS,cAAc,mBAAmB,EAC1D,GAA+BA,GAAc,KAAM,CAEjDA,EAAY,SAAS,cAAc,KAAK,EACxCA,EAAU,GAAK,mBACfA,EAAU,UAAY,eACtBb,EAAgBa,CAAS,EACzB,IAAIC,EAAO,SAAS,qBAAqB,MAAM,EAAE,CAAC,EAClDA,EAAK,YAAYD,CAAS,CAChC,KAAW,CAELA,EAAU,UAAU,OAAO,WAAW,EACtC,MACN,CACA,CAEE,SAASpD,GAAsB,CAC7B,IAAIsD,EAAoB,SAAS,cAAc,iCAAiC,EAChF,GAAIA,IAAsB,KAAM,OAEhC,MAAMhG,EAAMgG,EAAkB,aAAa,MAAM,EACjD,UAAU,UAAU,UAAUhG,CAAG,CACrC,CAEE,QAAQ,qBAAuBkE,EAC/B,QAAQ,WAAa3B,EAAgB,MAAM,EAC3C,QAAQ,eAAiBA,EAAgB,IAAI,CAC/C,CAAC,GC1cA,SAAUzD,EAAGC,EAAGW,EAAS,CAGxBA,EAAQ,MAAM,UAAY,CACxBA,EAAQ,GAAG,oBAAqB,QAAS,SAAUuG,EAAO,CAExD,KAAK,UAAU,OAAO,kBAAkB,EAGxC,IAAIC,EAAiB,KAAK,QAAQ,cAC9BC,EAAU,WAAW,KAAK,QAAQ,MAAM,EACxCC,EAAU,WAAW,KAAK,QAAQ,MAAM,EACxCC,EAAW,WAAW,KAAK,QAAQ,OAAO,EAC1CC,EAAkB,KAAK,MAAM,KAAK,QAAQ,cAAc,EACxDC,EAAc,KAAK,MAAM,KAAK,QAAQ,UAAU,EAEpD7G,EAAQ,UAAU,iBAAiB,EACnCA,EAAQ,WAAW,gBAAiB,UAAY,CAC9C,IAAI8G,EAAa,KACjB,GAAIF,EAAiB,CACnB,IAAIG,EAAY,EAAE,OAAOH,EAAgB,CAAC,EAAGA,EAAgB,CAAC,CAAC,EAC3DI,EAAY,EAAE,OAAOJ,EAAgB,CAAC,EAAGA,EAAgB,CAAC,CAAC,EAC/DE,EAAa,EAAE,aAAaC,EAAWC,CAAS,CAC1D,CAGQ,IAAIC,EAAM,EAAE,IAAIT,CAAc,EAE1BU,EAAe,qDACfC,EAAkB,gFAClBC,EAAY,IAAI,EAAE,UAAUF,EAAc,CAAC,QAAS,EAAG,QAAS,GAAI,YAAaC,CAAe,CAAC,EACjGE,EAAkB,sDAClBC,EAAqB,kGACrBC,EAAe,IAAI,EAAE,UAAUF,EAAiB,CAAC,QAAS,EAAG,QAAS,GAAI,YAAaC,CAAkB,CAAC,EAE1GR,EAGF,WAAW,UAAY,CACrBG,EAAI,UAAUH,EAAY,CACxB,QAAS,EACvB,CAAa,CACF,EAAE,CAAC,EACKL,GAAWC,IAChBC,EACFM,EAAI,QAAQ,IAAI,EAAE,OAAOP,EAASD,CAAO,EAAGE,CAAQ,EAEpDM,EAAI,QAAQ,IAAI,EAAE,OAAOP,EAASD,CAAO,EAAG,CAAC,GAIjDQ,EAAI,SAASG,CAAS,EAEtB,IAAII,EAAa,CACf,aAAcJ,EACd,gBAAiBG,CAClB,EAED,EAAE,QAAQ,OAAOC,CAAU,EAAE,MAAMP,CAAG,EAElCJ,GACF,EAAE,QAAQA,CAAW,EAAE,MAAMI,CAAG,CAI1C,CAAO,EAGDV,EAAM,eAAgB,CAC5B,CAAK,CACL,CAAG,CACH,GAAG,OAAQ,SAAU,OAAO,OAAO,GCxElC,SAAUnH,EAAGC,EAAGW,EAAS,CAGpBA,EAAQ,WAAa,eAIzBA,EAAQ,MAAM,UAAY,CACxB,IAAIyH,EAAsB,KAC1B,SAASC,GAA4B,CAC/BD,GAAuB,MACzBzH,EAAQ,KAAK,MAAO,0BAA0B,EAAE,KAAK,SAAU2H,EAAS,CACtEF,EAAsB,KAAK,MAAME,CAAO,EACxC,SAAW,CAACC,EAAaC,CAAW,IAAK,OAAO,QAAQJ,CAAmB,EAAG,CAC5E,IAAIK,EAAWzI,EAAE,iBAAiB,sBAAwBuI,EAAc,wBAAwB,EAChG,UAAWpG,KAAWsG,EAAU,CAC9B,IAAIC,EAAS,QAAU/H,EAAQ,SAAS,aAAa,OAAS,UAAY6H,EAAY,CAAC,EAAI,QAC3FrG,EAAQ,UAAYqG,EAAY,CAAC,EAAIE,CACnD,CACA,CACA,CAAS,CAET,CAEI,UAAWlI,KAAMR,EAAE,iBAAiB,oBAAoB,EACtDW,EAAQ,GAAGH,EAAI,aAAc6H,CAAwB,EAGvD,MAAMM,EAAmB3I,EAAE,iBAAiB,qBAAqB,EAC3D4I,EAAoB5I,EAAE,iBAAiB,sBAAsB,EAC7D6I,EAAgB7I,EAAE,iBAAiB,mDAAmD,EACtF8I,EAAiBC,GAAW,CAChC,UAAWvI,KAAMqI,EAEXrI,EAAG,eAAiB,OAAMA,EAAG,QAAU,CAACuI,EAE/C,EACD,UAAWvI,KAAMmI,EACfhI,EAAQ,GAAGH,EAAI,QAAS,IAAMsI,EAAc,EAAI,CAAC,EAEnD,UAAWtI,KAAMoI,EACfjI,EAAQ,GAAGH,EAAI,QAAS,IAAMsI,EAAc,EAAK,CAAC,EAGpD,MAAME,EAAiBhJ,EAAE,cAAc,YAAY,EACnDW,EAAQ,GAAGqI,EAAgB,QAAUvI,GAAM,CACzCA,EAAE,eAAgB,EAClB,UAAU,UAAU,UAAUuI,EAAe,QAAQ,IAAI,EACzDA,EAAe,UAAYA,EAAe,QAAQ,UACxD,CAAK,CACL,CAAG,CACH,GAAG,OAAQ,SAAU,OAAO,OAAO,ECpDnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQC,SAAUC,EAAQC,EAAU,CAKrB,OAAOD,EAAO,aAAgB,aAE9BA,EAAO,YAAc,SAAU/B,EAAOiC,EAAQ,CAE1CA,EAASA,GAAU,CAAE,QAAS,GAAO,WAAY,GAAO,OAAQ,MAAW,EAE3E,IAAIC,EAAMF,EAAS,YAAY,aAAa,EAC5C,OAAAE,EAAI,gBAAgBlC,EAAOiC,EAAO,QAASA,EAAO,WAAYA,EAAO,MAAM,EACpEC,CACV,EAEDH,EAAO,YAAY,UAAYA,EAAO,MAAM,WAGhDC,EAAS,iBAAiB,aAAcG,EAAkB,EAAK,EAC/DH,EAAS,iBAAiB,YAAaI,EAAiB,EAAK,EAC7DJ,EAAS,iBAAiB,WAAYK,EAAgB,EAAK,EAE3D,IAAIC,EAAQ,KACRC,EAAQ,KACRC,EAAQ,KACRC,EAAQ,KACRC,EAAW,KACXC,EAAU,KACVC,EAAa,EAOjB,SAASP,EAAe9I,EAAG,CAGvB,GAAIoJ,IAAYpJ,EAAE,OAElB,KAAIsJ,EAAiB,SAASC,EAAoBH,EAAS,uBAAwB,IAAI,EAAG,EAAE,EACxFI,EAAYD,EAAoBH,EAAS,kBAAmB,IAAI,EAChEK,EAAe,SAASF,EAAoBH,EAAS,qBAAsB,KAAK,EAAG,EAAE,EACrFM,EAAW,KAAK,IAAG,EAAKP,EACxB/I,EAAY,GACZuJ,EAAiB3J,EAAE,gBAAkBA,EAAE,SAAW,CAAE,EA4BxD,GA1BIwJ,IAAc,OACdF,EAAiB,KAAK,MAAOA,EAAiB,IAAOb,EAAS,gBAAgB,YAAY,GAE1Fe,IAAc,OACdF,EAAiB,KAAK,MAAOA,EAAiB,IAAOb,EAAS,gBAAgB,WAAW,GAGzF,KAAK,IAAIQ,CAAK,EAAI,KAAK,IAAIC,CAAK,EAC5B,KAAK,IAAID,CAAK,EAAIK,GAAkBI,EAAWD,IAC3CR,EAAQ,EACR7I,EAAY,cAGZA,EAAY,gBAIf,KAAK,IAAI8I,CAAK,EAAII,GAAkBI,EAAWD,IAChDP,EAAQ,EACR9I,EAAY,YAGZA,EAAY,eAIhBA,IAAc,GAAI,CAElB,IAAIwJ,EAAY,CACZ,IAAKxJ,EAAU,QAAQ,UAAW,EAAE,EACpC,WAAYuJ,EAAe,CAAC,GAAK,CAAE,GAAE,WAAa,SAClD,QAASN,EACT,OAAQ,SAASN,EAAO,EAAE,EAC1B,KAAM,UAAUY,EAAe,CAAC,GAAK,IAAI,SAAW,GAAI,EAAE,EAC1D,OAAQ,SAASX,EAAO,EAAE,EAC1B,KAAM,UAAUW,EAAe,CAAC,GAAK,IAAI,SAAW,GAAI,EAAE,CAC7D,EAGDP,EAAQ,cAAc,IAAI,YAAY,SAAU,CAAE,QAAS,GAAM,WAAY,GAAM,OAAQQ,CAAW,CAAA,CAAC,EAGvGR,EAAQ,cAAc,IAAI,YAAYhJ,EAAW,CAAE,QAAS,GAAM,WAAY,GAAM,OAAQwJ,CAAW,CAAA,CAAC,CACpH,CAGQb,EAAQ,KACRC,EAAQ,KACRG,EAAW,KACnB,CAMI,SAASP,EAAiB5I,EAAG,CAGrBA,EAAE,OAAO,aAAa,mBAAmB,IAAM,SAEnDoJ,EAAUpJ,EAAE,OAEZmJ,EAAW,KAAK,IAAK,EACrBJ,EAAQ/I,EAAE,QAAQ,CAAC,EAAE,QACrBgJ,EAAQhJ,EAAE,QAAQ,CAAC,EAAE,QACrBiJ,EAAQ,EACRC,EAAQ,EACRG,EAAarJ,EAAE,QAAQ,OAC/B,CAOI,SAAS6I,EAAgB7I,EAAG,CAExB,GAAI,GAAC+I,GAAS,CAACC,GAEf,KAAIa,EAAM7J,EAAE,QAAQ,CAAC,EAAE,QACnB8J,EAAM9J,EAAE,QAAQ,CAAC,EAAE,QAEvBiJ,EAAQF,EAAQc,EAChBX,EAAQF,EAAQc,EACxB,CASI,SAASP,EAAoBxJ,EAAIgK,EAAeC,EAAc,CAG1D,KAAOjK,GAAMA,IAAO0I,EAAS,iBAAiB,CAE1C,IAAIwB,EAAiBlK,EAAG,aAAagK,CAAa,EAElD,GAAIE,EACA,OAAOA,EAGXlK,EAAKA,EAAG,UACpB,CAEQ,OAAOiK,CACf,CAEA,GAAE,OAAQ,QAAQ,GClKjB,SAAU1K,EAAGC,EAAGW,EAAS,CAGpBA,EAAQ,WAAa,WAIzBA,EAAQ,MAAM,UAAY,CACxBX,EAAE,iBAAiB,WAAW,EAAE,QAC9B2K,GACEA,EAAI,iBACF,QAAS,IAAM,CAEbA,EAAI,IAAM,OAAO,QAAQ,SAAS,kBAAoB,yBACvD,EACD,CAAC,KAAM,EAAI,CACrB,CAAS,EAED3K,EAAE,cAAc,6BAA6B,IAC/CA,EAAE,cAAc,6BAA6B,EAAE,MAAM,QAAU,SAGjEW,EAAQ,GAAG,gBAAiB,QAAS,UAAY,CAC/C,IAAIiK,EAAoB,KAAK,aAAa,yBAAyB,EAC/DC,EAAuB,KAAK,aAAa,6BAA6B,EACtEC,EAAS,KAAK,aAAa,aAAa,EACxCC,EAAgB/K,EAAE,cAAc8K,CAAM,EACtCpE,EAAO,KAAK,UACZ,KAAK,UAAU,SAAS,WAAW,EACrCA,EAAOA,EAAK,QAAQkE,EAAmBC,CAAoB,EAE3DnE,EAAOA,EAAK,QAAQmE,EAAsBD,CAAiB,EAE7D,KAAK,UAAYlE,EACjB,KAAK,UAAU,OAAO,WAAW,EACjCqE,EAAc,UAAU,OAAO,WAAW,CAChD,CAAK,EAEDpK,EAAQ,GAAG,gBAAiB,QAAS,UAAY,CAC/C,IAAImK,EAAS,KAAK,aAAa,aAAa,EACxCE,EAAchL,EAAE,cAAc8K,EAAS,WAAW,EAClDG,EAAUD,EAAY,aAAa,KAAK,GACxCC,GAAY,MAAiCA,IAAY,KAC3DD,EAAY,aAAa,MAAOA,EAAY,aAAa,UAAU,CAAC,CAE5E,CAAK,EAEDrK,EAAQ,GAAG,YAAa,QAAS,UAAY,CAC3C,IAAImK,EAAS,KAAK,cAAc,cAAc,KAAK,EACnD,UAAU,UAAU,UAAUA,EAAO,SAAS,EAC9C,KAAK,UAAY,KAAK,QAAQ,UACpC,CAAK,EAMD,IAAII,EAGJ,MAAMC,EAAmBnL,EAAE,cAAc,KAAK,EAC9CmL,EAAiB,UAAU,IAAI,QAAQ,EAIvC,MAAMC,EAAY,IAAI,MAEhBC,EAAY,CAACC,EAAQC,IAAc,CAEnCL,GAAc,aAAaA,CAAY,EAG3CA,EAAe,WAAW,IAAM,CAC9BE,EAAU,IAAME,CACjB,EAAE,GAAI,EAGPF,EAAU,OAAS,IAAM,CACvBG,EAAW,EACXJ,EAAiB,OAAQ,CAC1B,EACDC,EAAU,QAAU,IAAM,CACxBD,EAAiB,OAAQ,CAC1B,CACF,EAEDxK,EAAQ,YAAe4C,GAAkB,CAcvC,GAVAvD,EAAE,eAAe,SAAS,EAAE,UAAU,IAAI,mBAAmB,EAK7D,OAAO,SAAS,KAAO,gBAEvBW,EAAQ,qBAAsB,EAG1B,CAAC4C,EAAe,OAGpB,MAAMoH,EAAMpH,EAAc,cAAc,2BAA2B,EACnE,GAAI,CAACoH,EAAK,OAGV,MAAMpJ,EAAMoJ,EAAI,aAAa,UAAU,EAGvC,GAAI,CAACpJ,EAAK,OAGV,MAAMiK,EAAYjI,EAAc,cAAc,kBAAkB,EAChEoH,EAAI,IAAMa,EAAU,IAGEjI,EAAc,cAAc,SAAS,EAC7C,YAAY4H,CAAgB,EAG1CE,EAAU9J,EAAK,IAAM,CAGnBoJ,EAAI,IAAMpJ,EACVoJ,EAAI,gBAAgB,UAAU,CACtC,CAAO,CACF,EAEDhK,EAAQ,YAAc,UAAY,CAChCX,EAAE,eAAe,SAAS,EAAE,UAAU,OAAO,mBAAmB,EAE5D,OAAO,SAAS,MAAQ,iBAAiB,OAAO,QAAQ,KAAM,EAClEW,EAAQ,qBAAsB,CAC/B,EACDA,EAAQ,GAAG,uBAAwB,QAASF,GAAK,CAC/CA,EAAE,eAAgB,EAClBE,EAAQ,YAAa,CAC3B,CAAK,EACDA,EAAQ,GAAG,0BAA2B,QAASF,GAAK,CAClDA,EAAE,eAAgB,EAClBE,EAAQ,eAAe,EAAK,CAClC,CAAK,EACDA,EAAQ,GAAG,sBAAuB,QAASF,GAAK,CAC9CA,EAAE,eAAgB,EAClBE,EAAQ,WAAW,EAAK,CAC9B,CAAK,EAGD,OAAO,iBAAiB,aAAc,IAAM,CACtC,OAAO,SAAS,MAAQ,iBAAiBA,EAAQ,YAAa,CACxE,CAAK,EAEDX,EAAE,iBAAiB,mBAAmB,EAAE,QACtCY,GAAO,CACLA,EAAI,iBAAiB,cAAe,UAAY,CAC9CD,EAAQ,WAAW,EAAK,CAClC,CAAS,EACDC,EAAI,iBAAiB,eAAgB,UAAY,CAC/CD,EAAQ,eAAe,EAAK,CACtC,CAAS,CACT,CACK,EAEDZ,EAAE,iBAAiB,SAAU,UAAY,CACvC,IAAIU,EAAIT,EAAE,eAAe,WAAW,EAClCyL,EAAY,SAAS,gBAAgB,WAAa,SAAS,KAAK,UAChE5G,EAAU7E,EAAE,eAAe,SAAS,EAClCS,IAAM,OACJgL,GAAa,IACf5G,EAAQ,UAAU,IAAI,WAAW,EAEjCA,EAAQ,UAAU,OAAO,WAAW,EAGzC,EAAE,EAAI,CAEX,CAAG,CAEH,GAAG,OAAQ,SAAU,OAAO,OAAO,GCpLlC,SAAU9E,EAAGC,EAAGW,EAAS,CAGxB,IAAI+K,EAAY,IAAKC,EAErB,MAAMC,EAAW,OAAO,WAAW,mCAAmC,EAAE,QAClEC,EAAgB,SAAS,cAAc,MAAM,EAAE,IAAM,eAE3D,SAASC,GAAiB,CACxB,GAAIH,EAAO,MAAM,OAAU,EAAG,CAC5B,IAAII,EAAS,SAAS,eAAe,QAAQ,EAC7C,WAAWA,EAAO,OAAO,KAAKA,CAAM,EAAG,CAAC,CAC9C,CACA,CAEE,SAASC,EAAmBL,EAAQ,CAClC,IAAIM,EAAK,SAAS,eAAe,cAAc,EAC3CC,EAAoB,UAAY,CAC9BP,EAAO,MAAM,SAAW,EAC1BM,EAAG,UAAU,IAAI,OAAO,EAExBA,EAAG,UAAU,OAAO,OAAO,CAE9B,EAGDC,EAAmB,EACnBD,EAAG,iBAAiB,QAAS,SAAUE,EAAI,CACzCR,EAAO,MAAQ,GACfA,EAAO,MAAO,EACdO,EAAmB,EACnBC,EAAG,eAAgB,CACzB,CAAK,EACDR,EAAO,iBAAiB,QAASO,EAAmB,EAAK,CAC7D,CAEE,MAAME,EAAe,MAAOC,GAAU,CACpC,IAAIC,EACJ,GAAI3L,EAAQ,SAAS,SAAW,MAAO,CACrC,MAAM4L,EAAY,IAAI,gBACtBA,EAAU,OAAO,IAAKF,CAAK,EAC3BC,EAAU,MAAM,mBAAqBC,EAAU,SAAQ,CAAE,CAC/D,KAAW,CACL,MAAM/J,EAAW,IAAI,SACrBA,EAAS,OAAO,IAAK6J,CAAK,EAC1BC,EAAU,MAAM,kBAAmB,CACjC,OAAQ,OACR,KAAM9J,CACd,CAAO,CACP,CAEI8J,EAAQ,KAAK,eAAgB7J,EAAU,CACrC,MAAMoC,EAAU,MAAMpC,EAAS,KAAM,EAErC,GAAI,CAACoC,EAAS,OAEd,MAAM2H,EAAexM,EAAE,cAAc,eAAe,EAC9CyM,EAAmBzM,EAAE,cAAc,kBAAkB,EAK3D,GAJAwM,EAAa,UAAU,IAAI,MAAM,EACjCC,EAAiB,UAAY,GAGzB,CAAC5H,EAAQ,CAAC,GAAKA,EAAQ,CAAC,EAAE,QAAU,EAAG,CACzC,MAAM6H,EAAqB,SAAS,cAAc,IAAI,EACtDA,EAAmB,UAAU,IAAI,eAAe,EAChDA,EAAmB,UAAY/L,EAAQ,SAAS,aAAa,cAC7D8L,EAAiB,YAAYC,CAAkB,EAC/C,MACR,CAEM,QAASC,KAAU9H,EAAQ,CAAC,EAAG,CAC7B,MAAM+H,EAAK,SAAS,cAAc,IAAI,EACtCA,EAAG,UAAYD,EAEfhM,EAAQ,GAAGiM,EAAI,YAAa,IAAM,CAChCjB,EAAO,MAAQgB,EACF3M,EAAE,cAAc,SAAS,EACjC,OAAQ,EACbwM,EAAa,UAAU,OAAO,MAAM,CAC9C,CAAS,EACDC,EAAiB,YAAYG,CAAE,CACvC,CACA,CAAK,CACF,EAEDjM,EAAQ,MAAM,UAAY,CAEpB,CAACiL,GAAY,CAACC,GAAe,SAAS,eAAe,GAAG,EAAE,MAAO,EAErEF,EAAS3L,EAAE,eAAe0L,CAAS,EACnC,MAAMc,EAAexM,EAAE,cAAc,eAAe,EAC9CyM,EAAmBzM,EAAE,cAAc,kBAAkB,EAEvD2L,IAAW,OAEbK,EAAkBL,CAAM,EAGpBhL,EAAQ,SAAS,eACnBA,EAAQ,GAAGgL,EAAQ,QAAS,IAAM,CAChC,MAAMU,EAAQV,EAAO,MACjBU,EAAM,OAAS1L,EAAQ,SAAS,kBAEpC,WAAW,IAAM,CACX0L,GAASV,EAAO,OAAOS,EAAaC,CAAK,CAC9C,EAAE,GAAG,CAChB,CAAS,EAED1L,EAAQ,GAAGgL,EAAQ,QAAUlL,GAAM,CACjC,IAAIoM,EAAe,GACnB,MAAMC,EAAYL,EAAiB,SACnC,QAASpM,EAAI,EAAGA,EAAIyM,EAAU,OAAQzM,IACpC,GAAIyM,EAAUzM,CAAC,EAAE,UAAU,SAAS,QAAQ,EAAG,CAC7CwM,EAAexM,EACf,KACd,CAGU,IAAI0M,EAAkB,GAatB,GAZItM,EAAE,MAAQ,WACRoM,GAAgB,GAAGC,EAAUD,CAAY,EAAE,UAAU,OAAO,QAAQ,EAGxEE,GAAmBF,EAAe,EAAIC,EAAU,QAAUA,EAAU,QAC3DrM,EAAE,MAAQ,aACfoM,GAAgB,GAAGC,EAAUD,CAAY,EAAE,UAAU,OAAO,QAAQ,EACxEE,GAAmBF,EAAe,GAAKC,EAAU,SACxCrM,EAAE,MAAQ,OAASA,EAAE,MAAQ,UACtC+L,EAAa,UAAU,OAAO,MAAM,EAGlCO,GAAmB,GAAI,CACzB,MAAMC,EAAeF,EAAUC,CAAe,EAC9CC,EAAa,UAAU,IAAI,QAAQ,EAE9BA,EAAa,UAAU,SAAS,eAAe,IAAGrB,EAAO,MAAQqB,EAAa,UAC/F,CACA,CAAS,IASHrB,IAAW,MACNhL,EAAQ,SAAS,2BAGjBX,EAAE,cAAc,iBAAiB,GAAK,OAE3CW,EAAQ,GAAGX,EAAE,eAAe,YAAY,EAAG,SAAU8L,CAAa,EAClEnL,EAAQ,GAAGX,EAAE,eAAe,YAAY,EAAG,SAAU8L,CAAa,EAClEnL,EAAQ,GAAGX,EAAE,eAAe,UAAU,EAAG,SAAU8L,CAAa,GAGlE,MAAMmB,EAAkBjN,EAAE,iBAAiB,wBAAwB,EACnE,QAASsF,KAAU2H,EACjBtM,EAAQ,GAAG2E,EAAQ,QAAU4B,GAAU,CACrC,GAAIA,EAAM,SAAU,CAClBA,EAAM,eAAgB,EACtB5B,EAAO,UAAU,OAAO,UAAU,EAClC,MACV,CAGQ,MAAM4H,EAAqBlN,EAAE,iBAAiB,iCAAiC,EAC/E,QAASmN,KAAkBD,EACzBC,EAAe,UAAU,OAAO,UAAU,EAE5C7H,EAAO,UAAU,IAAI,UAAU,CACvC,CAAO,EAIH,MAAM/C,EAAOvC,EAAE,cAAc,SAAS,EAClCuC,GAAQ,MACV5B,EAAQ,GAAG4B,EAAM,SAAW2E,GAAU,CACpCA,EAAM,eAAgB,EACtB,MAAMkG,EAAsBpN,EAAE,cAAc,sBAAsB,EAClE,GAAIoN,EAAqB,CACvB,IAAIC,EAAiB,CAAE,EACvB,QAASF,KAAkBF,EACrBE,EAAe,UAAU,SAAS,UAAU,GAC9CE,EAAe,KAAKF,EAAe,KAAK,QAAQ,YAAa,EAAE,CAAC,EAGpEC,EAAoB,MAAQC,EAAe,KAAK,GAAG,CAC7D,CACQ9K,EAAK,OAAQ,CACrB,CAAO,CAEP,CAAG,CAEH,GAAG,OAAQ,SAAU,OAAO,OAAO", "x_google_ignoreList": [5]}