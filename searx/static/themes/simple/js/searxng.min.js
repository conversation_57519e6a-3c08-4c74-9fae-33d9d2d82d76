/**
 * @license
 * (C) Copyright Contributors to the SearXNG project.
 * (C) Copyright Contributors to the searx project (2014 - 2021).
 * SPDX-License-Identifier: AGPL-3.0-or-later
 */window.searxng=function(h,s){h.Element&&function(n){n.matches=n.matches||n.matchesSelector||n.webkitMatchesSelector||n.msMatchesSelector||function(l){for(var t=this,r=(t.parentNode||t.document).querySelectorAll(l),o=-1;r[++o]&&r[o]!=t;);return!!r[o]}}(Element.prototype);function a(n,l,t){try{n.call(l,t)}catch(r){console.log(r)}}var f=window.searxng||{};f.on=function(n,l,t,r){r=r||!1,typeof n!="string"?n.addEventListener(l,t,r):s.addEventListener(l,function(o){for(var i=o.target||o.srcElement,p=!1;i&&i.matches&&i!==s&&!(p=i.matches(n));)i=i.parentElement;p&&a(t,i,o)},r)},f.ready=function(n){document.readyState!="loading"?n.call(h):h.addEventListener("DOMContentLoaded",n.bind(h))},f.http=function(n,l,t=null){return new Promise(function(r,o){try{var i=new XMLHttpRequest;i.open(n,l,!0),i.timeout=2e4,i.onload=function(){i.status==200?r(i.response,i.responseType):o(Error(i.statusText))},i.onerror=function(){o(Error("Network Error"))},i.onabort=function(){o(Error("Transaction is aborted"))},i.ontimeout=function(){o(Error("Timeout"))},t?i.send(t):i.send()}catch(p){o(p)}})},f.loadStyle=function(n){var l=f.settings.theme_static_path+"/"+n,t="style_"+n.replace(".","_"),r=s.getElementById(t);r===null&&(r=s.createElement("link"),r.setAttribute("id",t),r.setAttribute("rel","stylesheet"),r.setAttribute("type","text/css"),r.setAttribute("href",l),s.body.appendChild(r))},f.loadScript=function(n,l){var t=f.settings.theme_static_path+"/"+n,r="script_"+n.replace(".","_"),o=s.getElementById(r);if(o===null)o=s.createElement("script"),o.setAttribute("id",r),o.setAttribute("src",t),o.onload=l,o.onerror=function(){o.setAttribute("error","1")},s.body.appendChild(o);else if(o.hasAttribute("error"))console.log("callback not executed : script '"+t+"' not loaded.");else try{l.apply(o,[])}catch(i){console.log(i)}},f.insertBefore=function(n,l){l.parentNode.insertBefore(n,l)},f.insertAfter=function(n,l){l.parentNode.insertAfter(n,l.nextSibling)},f.on(".close","click",function(){this.parentNode.classList.add("invisible")});function m(){for(var n of s.getElementsByTagName("body")[0].classList.values())if(n.endsWith("_endpoint"))return n.split("_")[0];return""}return f.endpoint=m(),f}(window,document);searxng.ready(function(){if(searxng.infinite_scroll_supported="IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype,searxng.endpoint!=="results")return;if(!searxng.infinite_scroll_supported){console.log("IntersectionObserver not supported");return}let h=document;var s=h.getElementById("results").classList.contains("only_template_images");function a(){var n=h.createElement("div");return n.classList.add("loader"),n}function f(n,l){n.textContent="",l.forEach(t=>n.appendChild(t))}function m(n){var l=h.querySelector("#pagination form.next_page");if(l){f(h.querySelector("#pagination"),[a()]);var t=new FormData(l);searxng.http("POST",h.querySelector("#search").getAttribute("action"),t).then(function(r){var o=new DOMParser().parseFromString(r,"text/html"),i=o.querySelectorAll("#urls article"),p=o.querySelector("#pagination");h.querySelector("#pagination").remove(),i.length>0&&!s&&h.querySelector("#urls").appendChild(h.createElement("hr")),i.forEach(b=>{h.querySelector("#urls").appendChild(b)}),p&&(h.querySelector("#results").appendChild(p),n())}).catch(function(r){console.log(r);var o=h.createElement("div");o.textContent=searxng.settings.translations.error_loading_next_page,o.classList.add("dialog-error"),o.setAttribute("role","alert"),f(h.querySelector("#pagination"),[o])})}}if(searxng.settings.infinite_scroll&&searxng.infinite_scroll_supported){const n={rootMargin:"20rem"},l="article.result:last-child",t=new IntersectionObserver(r=>{const o=r[0];o.isIntersecting&&(t.unobserve(o.target),m(()=>t.observe(h.querySelector(l),n)))});t.observe(h.querySelector(l),n)}});searxng.ready(function(){function h(e){for(;e!==void 0;){if(e.classList.contains("detail"))return!0;if(e.classList.contains("result"))return!1;e=e.parentNode}return!1}function s(e){for(;e!==void 0;){if(e.classList.contains("result"))return e;e=e.parentNode}}function a(e){return e&&e.classList.contains("result-images")}searxng.on(".result","click",function(e){if(!h(e.target)){l(this)(!0,!0);let u=s(e.target);a(u)&&(e.preventDefault(),searxng.selectImage(u))}}),searxng.on(".result a","focus",function(e){if(!h(e.target)){let u=s(e.target);u&&u.getAttribute("data-vim-selected")===null&&l(u)(!0),a(u)&&searxng.selectImage(u)}},!0);var f={Escape:{key:"ESC",fun:r,des:"remove focus from the focused input",cat:"Control"},c:{key:"c",fun:A,des:"copy url of the selected result to the clipboard",cat:"Results"},h:{key:"h",fun:w,des:"toggle help window",cat:"Other"},i:{key:"i",fun:g,des:"focus on the search input",cat:"Control"},n:{key:"n",fun:i(),des:"go to next page",cat:"Results"},o:{key:"o",fun:v(!1),des:"open search result",cat:"Results"},p:{key:"p",fun:p(),des:"go to previous page",cat:"Results"},r:{key:"r",fun:t,des:"reload page from the server",cat:"Control"},t:{key:"t",fun:v(!0),des:"open the result in a new tab",cat:"Results"}},m={default:Object.assign({ArrowLeft:{key:"←",fun:l("up"),des:"select previous search result",cat:"Results"},ArrowRight:{key:"→",fun:l("down"),des:"select next search result",cat:"Results"}},f),vim:Object.assign({b:{key:"b",fun:c(-window.innerHeight),des:"scroll one page up",cat:"Navigation"},f:{key:"f",fun:c(window.innerHeight),des:"scroll one page down",cat:"Navigation"},u:{key:"u",fun:c(-window.innerHeight/2),des:"scroll half a page up",cat:"Navigation"},d:{key:"d",fun:c(window.innerHeight/2),des:"scroll half a page down",cat:"Navigation"},g:{key:"g",fun:d(-document.body.scrollHeight,"top"),des:"scroll to the top of the page",cat:"Navigation"},v:{key:"v",fun:d(document.body.scrollHeight,"bottom"),des:"scroll to the bottom of the page",cat:"Navigation"},k:{key:"k",fun:l("up"),des:"select previous search result",cat:"Results"},j:{key:"j",fun:l("down"),des:"select next search result",cat:"Results"},y:{key:"y",fun:A,des:"copy url of the selected result to the clipboard",cat:"Results"}},f)},n=m[searxng.settings.hotkeys]||m.default;searxng.on(document,"keydown",function(e){if(Object.prototype.hasOwnProperty.call(n,e.key)&&!e.ctrlKey&&!e.altKey&&!e.shiftKey&&!e.metaKey){var u=e.target.tagName.toLowerCase();e.key==="Escape"?n[e.key].fun(e):(e.target===document.body||u==="a"||u==="button")&&(e.preventDefault(),n[e.key].fun())}});function l(e){return function(u,_){var k=document.querySelector(".result[data-vim-selected]"),T=e;if(k===null){if(k=document.querySelector(".result"),k===null)return;(e==="down"||e==="up")&&(T=k)}var y,S=document.querySelectorAll(".result");if(S=Array.from(S),typeof T!="string")y=T;else switch(T){case"visible":for(var q=document.documentElement.scrollTop||document.body.scrollTop,B=q+document.documentElement.clientHeight,x=0;x<S.length;x++){y=S[x];var I=y.offsetTop,M=I+y.clientHeight;if(M<=B&&I>q)break}break;case"down":y=S[S.indexOf(k)+1]||k;break;case"up":y=S[S.indexOf(k)-1]||k;break;case"bottom":y=S[S.length-1];break;case"top":default:y=S[0]}if(y){if(k.removeAttribute("data-vim-selected"),y.setAttribute("data-vim-selected","true"),!_){var C=y.querySelector("h3 a")||y.querySelector("a");C!==null&&C.focus()}u||b()}}}function t(){document.location.reload(!0)}function r(e){const u=e.target.tagName.toLowerCase();document.activeElement&&(u==="input"||u==="select"||u==="textarea")?document.activeElement.blur():searxng.closeDetail()}function o(e){return function(){var u=document.querySelector(e);u&&u.click()}}function i(){return o('nav#pagination .next_page button[type="submit"]')}function p(){return o('nav#pagination .previous_page button[type="submit"]')}function b(){var e=document.querySelector(".result[data-vim-selected]");if(e!==null){var u=document.documentElement.scrollTop||document.body.scrollTop,_=document.documentElement.clientHeight,k=e.offsetTop,T=k+e.clientHeight,y=120;if(e.previousElementSibling===null&&T<_){window.scroll(window.scrollX,0);return}if(u>k-y)window.scroll(window.scrollX,k-y);else{var S=u+_;S<T+y&&window.scroll(window.scrollX,T-_+y)}}}function c(e){return function(){window.scrollBy(0,e),l("visible")()}}function d(e,u){return function(){window.scrollTo(0,e),l(u)()}}function g(){window.scrollTo(0,0);var e=document.querySelector("#q");if(e.focus(),e.setSelectionRange){var u=e.value.length;e.setSelectionRange(u,u)}}function v(e){return function(){var u=document.querySelector(".result[data-vim-selected] h3 a");if(u===null&&(u=document.querySelector(".result[data-vim-selected] > a")),u!==null){var _=u.getAttribute("href");e?window.open(_):window.location.href=_}}}function E(e){var u={};for(var _ in n){var k=n[_];u[k.cat]=u[k.cat]||[],u[k.cat].push(k)}var T=Object.keys(u).sort(function(M,C){return u[C].length-u[M].length});if(T.length!==0){var y='<a href="#" class="close" aria-label="close" title="close">×</a>';y+="<h3>How to navigate SearXNG with hotkeys</h3>",y+="<table>";for(var S=0;S<T.length;S++){var q=u[T[S]],B=S===T.length-1,x=S%2===0;x&&(y+="<tr>"),y+="<td>",y+="<h4>"+q[0].cat+"</h4>",y+='<ul class="list-unstyled">';for(var I in q)y+="<li><kbd>"+q[I].key+"</kbd> "+q[I].des+"</li>";y+="</ul>",y+="</td>",(!x||B)&&(y+="</tr>")}y+="</table>",e.innerHTML=y}}function w(){var e=document.querySelector("#vim-hotkeys-help");if(e==null){e=document.createElement("div"),e.id="vim-hotkeys-help",e.className="dialog-modal",E(e);var u=document.getElementsByTagName("body")[0];u.appendChild(e)}else{e.classList.toggle("invisible");return}}function A(){var e=document.querySelector(".result[data-vim-selected] h3 a");if(e===null)return;const u=e.getAttribute("href");navigator.clipboard.writeText(u)}searxng.scrollPageToSelected=b,searxng.selectNext=l("down"),searxng.selectPrevious=l("up")});(function(h,s,a){a.ready(function(){a.on(".searxng_init_map","click",function(f){this.classList.remove("searxng_init_map");var m=this.dataset.leafletTarget,n=parseFloat(this.dataset.mapLon),l=parseFloat(this.dataset.mapLat),t=parseFloat(this.dataset.mapZoom),r=JSON.parse(this.dataset.mapBoundingbox),o=JSON.parse(this.dataset.mapGeojson);a.loadStyle("css/leaflet.css"),a.loadScript("js/leaflet.js",function(){var i=null;if(r){var p=L.latLng(r[0],r[2]),b=L.latLng(r[1],r[3]);i=L.latLngBounds(p,b)}var c=L.map(m),d="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",g='Map data © <a href="https://openstreetmap.org">OpenStreetMap</a> contributors',v=new L.TileLayer(d,{minZoom:1,maxZoom:19,attribution:g}),E="https://maps.wikimedia.org/osm-intl/{z}/{x}/{y}.png",w='Wikimedia maps | Maps data © <a href="https://openstreetmap.org">OpenStreetMap contributors</a>',A=new L.TileLayer(E,{minZoom:1,maxZoom:19,attribution:w});i?setTimeout(function(){c.fitBounds(i,{maxZoom:17})},0):n&&l&&(t?c.setView(new L.latLng(l,n),t):c.setView(new L.latLng(l,n),8)),c.addLayer(v);var e={"OSM Mapnik":v,"OSM Wikimedia":A};L.control.layers(e).addTo(c),o&&L.geoJson(o).addTo(c)}),f.preventDefault()})})})(window,document,window.searxng);(function(h,s,a){a.endpoint==="preferences"&&a.ready(function(){let f=null;function m(){f==null&&a.http("GET","engine_descriptions.json").then(function(i){f=JSON.parse(i);for(const[p,b]of Object.entries(f)){let c=s.querySelectorAll('[data-engine-name="'+p+'"] .engine-description');for(const d of c){let g=" (<i>"+a.settings.translations.Source+":&nbsp;"+b[1]+"</i>)";d.innerHTML=b[0]+g}}})}for(const i of s.querySelectorAll("[data-engine-name]"))a.on(i,"mouseenter",m);const n=s.querySelectorAll(".enable-all-engines"),l=s.querySelectorAll(".disable-all-engines"),t=s.querySelectorAll("tbody input[type=checkbox][class~=checkbox-onoff]"),r=i=>{for(const p of t)p.offsetParent!==null&&(p.checked=!i)};for(const i of n)a.on(i,"click",()=>r(!0));for(const i of l)a.on(i,"click",()=>r(!1));const o=s.querySelector("#copy-hash");a.on(o,"click",i=>{i.preventDefault(),navigator.clipboard.writeText(o.dataset.hash),o.innerText=o.dataset.copiedText})})})(window,document,window.searxng);/*!
 * swiped-events.js - v@version@
 * Pure JavaScript swipe events
 * https://github.com/john-doherty/swiped-events
 * @inspiration https://stackoverflow.com/questions/16348031/disable-scrolling-when-touch-moving-certain-element
 * <AUTHOR> Doherty <www.johndoherty.info>
 * @license MIT
 */(function(h,s){typeof h.CustomEvent!="function"&&(h.CustomEvent=function(c,d){d=d||{bubbles:!1,cancelable:!1,detail:void 0};var g=s.createEvent("CustomEvent");return g.initCustomEvent(c,d.bubbles,d.cancelable,d.detail),g},h.CustomEvent.prototype=h.Event.prototype),s.addEventListener("touchstart",i,!1),s.addEventListener("touchmove",p,!1),s.addEventListener("touchend",o,!1);var a=null,f=null,m=null,n=null,l=null,t=null,r=0;function o(c){if(t===c.target){var d=parseInt(b(t,"data-swipe-threshold","20"),10),g=b(t,"data-swipe-unit","px"),v=parseInt(b(t,"data-swipe-timeout","500"),10),E=Date.now()-l,w="",A=c.changedTouches||c.touches||[];if(g==="vh"&&(d=Math.round(d/100*s.documentElement.clientHeight)),g==="vw"&&(d=Math.round(d/100*s.documentElement.clientWidth)),Math.abs(m)>Math.abs(n)?Math.abs(m)>d&&E<v&&(m>0?w="swiped-left":w="swiped-right"):Math.abs(n)>d&&E<v&&(n>0?w="swiped-up":w="swiped-down"),w!==""){var e={dir:w.replace(/swiped-/,""),touchType:(A[0]||{}).touchType||"direct",fingers:r,xStart:parseInt(a,10),xEnd:parseInt((A[0]||{}).clientX||-1,10),yStart:parseInt(f,10),yEnd:parseInt((A[0]||{}).clientY||-1,10)};t.dispatchEvent(new CustomEvent("swiped",{bubbles:!0,cancelable:!0,detail:e})),t.dispatchEvent(new CustomEvent(w,{bubbles:!0,cancelable:!0,detail:e}))}a=null,f=null,l=null}}function i(c){c.target.getAttribute("data-swipe-ignore")!=="true"&&(t=c.target,l=Date.now(),a=c.touches[0].clientX,f=c.touches[0].clientY,m=0,n=0,r=c.touches.length)}function p(c){if(!(!a||!f)){var d=c.touches[0].clientX,g=c.touches[0].clientY;m=a-d,n=f-g}}function b(c,d,g){for(;c&&c!==s.documentElement;){var v=c.getAttribute(d);if(v)return v;c=c.parentNode}return g}})(window,document);(function(h,s,a){a.endpoint==="results"&&a.ready(function(){s.querySelectorAll("#urls img").forEach(t=>t.addEventListener("error",()=>{t.src=window.searxng.settings.theme_static_path+"/img/img_load_error.svg"},{once:!0})),s.querySelector("#search_url button#copy_url")&&(s.querySelector("#search_url button#copy_url").style.display="block"),a.on(".btn-collapse","click",function(){var t=this.getAttribute("data-btn-text-collapsed"),r=this.getAttribute("data-btn-text-not-collapsed"),o=this.getAttribute("data-target"),i=s.querySelector(o),p=this.innerHTML;this.classList.contains("collapsed")?p=p.replace(t,r):p=p.replace(r,t),this.innerHTML=p,this.classList.toggle("collapsed"),i.classList.toggle("invisible")}),a.on(".media-loader","click",function(){var t=this.getAttribute("data-target"),r=s.querySelector(t+" > iframe"),o=r.getAttribute("src");(o==null||o===!1)&&r.setAttribute("src",r.getAttribute("data-src"))}),a.on("#copy_url","click",function(){var t=this.parentElement.querySelector("pre");navigator.clipboard.writeText(t.innerText),this.innerText=this.dataset.copiedText});let f;const m=s.createElement("div");m.classList.add("loader");const n=new Image,l=(t,r)=>{f&&clearTimeout(f),f=setTimeout(()=>{n.src=t},1e3),n.onload=()=>{r(),m.remove()},n.onerror=()=>{m.remove()}};a.selectImage=t=>{if(s.getElementById("results").classList.add("image-detail-open"),window.location.hash="#image-viewer",a.scrollPageToSelected(),!t)return;const r=t.querySelector(".result-images-source img");if(!r)return;const o=r.getAttribute("data-src");if(!o)return;const i=t.querySelector(".image_thumbnail");r.src=i.src,t.querySelector(".detail").appendChild(m),l(o,()=>{r.src=o,r.removeAttribute("data-src")})},a.closeDetail=function(){s.getElementById("results").classList.remove("image-detail-open"),window.location.hash=="#image-viewer"&&window.history.back(),a.scrollPageToSelected()},a.on(".result-detail-close","click",t=>{t.preventDefault(),a.closeDetail()}),a.on(".result-detail-previous","click",t=>{t.preventDefault(),a.selectPrevious(!1)}),a.on(".result-detail-next","click",t=>{t.preventDefault(),a.selectNext(!1)}),window.addEventListener("hashchange",()=>{window.location.hash!="#image-viewer"&&a.closeDetail()}),s.querySelectorAll(".swipe-horizontal").forEach(t=>{t.addEventListener("swiped-left",function(){a.selectNext(!1)}),t.addEventListener("swiped-right",function(){a.selectPrevious(!1)})}),h.addEventListener("scroll",function(){var t=s.getElementById("backToTop"),r=document.documentElement.scrollTop||document.body.scrollTop,o=s.getElementById("results");t!==null&&(r>=100?o.classList.add("scrolling"):o.classList.remove("scrolling"))},!0)})})(window,document,window.searxng);(function(h,s,a){var f="q",m;const n=window.matchMedia("only screen and (max-width: 50em)").matches,l=document.querySelector("main").id=="main_results";function t(){if(m.value.length>0){var i=document.getElementById("search");setTimeout(i.submit.bind(i),0)}}function r(i){var p=document.getElementById("clear_search"),b=function(){i.value.length===0?p.classList.add("empty"):p.classList.remove("empty")};b(),p.addEventListener("click",function(c){i.value="",i.focus(),b(),c.preventDefault()}),i.addEventListener("input",b,!1)}const o=async i=>{let p;if(a.settings.method==="GET"){const b=new URLSearchParams;b.append("q",i),p=fetch("./autocompleter?"+b.toString())}else{const b=new FormData;b.append("q",i),p=fetch("./autocompleter",{method:"POST",body:b})}p.then(async function(b){const c=await b.json();if(!c)return;const d=s.querySelector(".autocomplete"),g=s.querySelector(".autocomplete ul");if(d.classList.add("open"),g.innerHTML="",!c[1]||c[1].length==0){const v=document.createElement("li");v.classList.add("no-item-found"),v.innerHTML=a.settings.translations.no_item_found,g.appendChild(v);return}for(let v of c[1]){const E=document.createElement("li");E.innerText=v,a.on(E,"mousedown",()=>{m.value=v,s.querySelector("#search").submit(),d.classList.remove("open")}),g.appendChild(E)}})};a.ready(function(){!n&&!l&&document.getElementById("q").focus(),m=s.getElementById(f);const i=s.querySelector(".autocomplete"),p=s.querySelector(".autocomplete ul");m!==null&&(r(m),a.settings.autocomplete&&(a.on(m,"input",()=>{const d=m.value;d.length<a.settings.autocomplete_min||setTimeout(()=>{d==m.value&&o(d)},300)}),a.on(m,"keyup",d=>{let g=-1;const v=p.children;for(let w=0;w<v.length;w++)if(v[w].classList.contains("active")){g=w;break}let E=-1;if(d.key==="ArrowUp"?(g>=0&&v[g].classList.remove("active"),E=(g-1+v.length)%v.length):d.key==="ArrowDown"?(g>=0&&v[g].classList.remove("active"),E=(g+1)%v.length):(d.key==="Tab"||d.key==="Enter")&&i.classList.remove("open"),E!=-1){const w=v[E];w.classList.add("active"),w.classList.contains("no-item-found")||(m.value=w.innerText)}}))),m!==null&&a.settings.search_on_category_select&&s.querySelector(".search_filters")!=null&&(a.on(s.getElementById("safesearch"),"change",t),a.on(s.getElementById("time_range"),"change",t),a.on(s.getElementById("language"),"change",t));const b=s.querySelectorAll("button.category_button");for(let d of b)a.on(d,"click",g=>{if(g.shiftKey){g.preventDefault(),d.classList.toggle("selected");return}const v=s.querySelectorAll("button.category_button.selected");for(let E of v)E.classList.remove("selected");d.classList.add("selected")});const c=s.querySelector("#search");c!=null&&a.on(c,"submit",d=>{d.preventDefault();const g=s.querySelector("#selected-categories");if(g){let v=[];for(let E of b)E.classList.contains("selected")&&v.push(E.name.replace("category_",""));g.value=v.join(",")}c.submit()})})})(window,document,window.searxng);
//# sourceMappingURL=searxng.min.js.map
