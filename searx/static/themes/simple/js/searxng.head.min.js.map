{"version": 3, "file": "searxng.head.min.js", "sources": ["../../../../../client/simple/src/js/head/00_init.js"], "sourcesContent": ["/* SPDX-License-Identifier: AGPL-3.0-or-later */\n(function (w, d) {\n  'use strict';\n\n  // add data- properties\n  var script = d.currentScript  || (function () {\n    var scripts = d.getElementsByTagName('script');\n    return scripts[scripts.length - 1];\n  })();\n\n  w.searxng = {\n    settings: JSON.parse(atob(script.getAttribute('client_settings')))\n  };\n\n  // update the css\n  var htmlElement = d.getElementsByTagName(\"html\")[0];\n  htmlElement.classList.remove('no-js');\n  htmlElement.classList.add('js');\n\n})(window, document);\n"], "names": ["w", "d", "script", "scripts", "htmlElement"], "mappings": "CACC,SAAUA,EAAGC,EAAG,CAIf,IAAIC,EAASD,EAAE,eAAmB,UAAY,CAC5C,IAAIE,EAAUF,EAAE,qBAAqB,QAAQ,EAC7C,OAAOE,EAAQA,EAAQ,OAAS,CAAC,CACrC,EAAM,EAEJH,EAAE,QAAU,CACV,SAAU,KAAK,MAAM,KAAKE,EAAO,aAAa,iBAAiB,CAAC,CAAC,CAClE,EAGD,IAAIE,EAAcH,EAAE,qBAAqB,MAAM,EAAE,CAAC,EAClDG,EAAY,UAAU,OAAO,OAAO,EACpCA,EAAY,UAAU,IAAI,IAAI,CAEhC,GAAG,OAAQ,QAAQ"}