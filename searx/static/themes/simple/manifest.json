{"js/searxng.head.js": {"file": "js/searxng.head.min.js", "name": "js/searxng.head.min", "src": "js/searxng.head.js", "isEntry": true}, "js/searxng.js": {"file": "js/searxng.min.js", "name": "js/searxng.min", "src": "js/searxng.js", "isEntry": true}, "less/rss.less": {"file": "css/rss.min.css", "src": "less/rss.less", "isEntry": true}, "less/style-ltr.less": {"file": "css/searxng.min.css", "src": "less/style-ltr.less", "isEntry": true}, "less/style-rtl.less": {"file": "css/searxng-rtl.min.css", "src": "less/style-rtl.less", "isEntry": true}}