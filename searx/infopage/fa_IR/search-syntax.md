# نحو جستجو
SearXNG با یک متن جستجو همراه است که می توانید دسته ها را تغییر دهید،
موتورها، زبان ها و موارد دیگر. {{link('اولویت ها', 'preferences')}}  رابرای
لیست موتورها، دسته ها و زبان ها ببینید.
## `!` موتور و دسته را انتخاب کنید

برای تنظیم نام دسته و/یا موتور از پیشوند «!» استفاده کنید. برای ذکر چند مثال:

- در ویکی پدیا برای **paris** جستجو کنید

  - {{search('!wp paris')}}
  - {{search('!wikipedia paris')}}

- جستجو در دسته **نقشه** برای **پاریس**
  - {{search('!map paris')}}

- جستجوی تصویر
  - {{search('!images Wau Holland')}}

اختصارات موتورها و زبان ها نیز پذیرفته می شود. موتور/رده
اصلاح کننده ها زنجیره ای و فراگیر هستند. به عنوان مثال. با {{search('!map !ddg !wp
paris')}} در دسته بندی نقشه و DuckDuckGo و Wikipedia برای **paris** جستجو کنید..

## `:` زبان را انتخاب کنید

برای انتخاب فیلتر زبان از پیشوند «:» استفاده کنید. برای مثال زدن:

- ویکی پدیا را با یک زبان سفارشی جستجو کنید

  - {{search(':fr !wp Wau Holland')}}

## `<bang>!!` چتری خارجی

SearXNG از ضربه های خارجی پشتیبانی می کند [DuckDuckGo] .  To مستقیماً به a
صفحه جستجوی خارجی از پیشوند «!!» استفاده کنید. برای مثال زدن:

- ویکی پدیا را با یک زبان سفارشی جستجو کنید
  - {{search('!!wfr Wau Holland')}}

لطفاً توجه داشته باشید، جستجوی شما مستقیماً در جستجوی خارجی انجام می شود
موتور، SearXNG نمی تواند از حریم خصوصی شما در این مورد محافظت کند.
[DuckDuckGo]: https://duckduckgo.com/bang

## سوالات ویژه

در صفحه {{link('preferences', 'preferences')}} کلمات کلیدی را پیدا می کنید
_پرسش های ویژه_. برای ذکر چند مثال:

- ایجاد یک UUID تصادفی

  - {{search('random uuid')}}

- محاسبه میانگین

  - {{search('avg 123 548 2.04 24.2')}}

- نشان دادن _user agent_ مرورگر شما (باید فعال شود)

  - {{search('user-agent')}}

- تبدیل رشته ها به هش های مختلف (باید فعال شود)

  - {{search('md5 lorem ipsum')}}
  - {{search('sha512 lorem ipsum')}}
