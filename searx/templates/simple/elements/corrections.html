    <div id="corrections" role="complementary" aria-labelledby="corrections-title">
      <h4 id="corrections-title">{{ _('Try searching for:') }}</h4>
      {% for correction in corrections %}
      <div class="left">
	      <form method="{{ method or 'POST' }}" action="{{ url_for('search') }}" role="navigation">
          {% for category in selected_categories %}
          <input type="hidden" name="category_{{ category }}" value="1">
          {% endfor %}
          <input type="hidden" name="q" value="{{ correction.url }}">
          <input type="hidden" name="language" value="{{ current_language }}">
          <input type="hidden" name="time_range" value="{{ time_range }}">
          <input type="hidden" name="safesearch" value="{{ safesearch }}">
          <input type="hidden" name="theme" value="{{ theme }}">
          {% if timeout_limit %}<input type="hidden" name="timeout_limit" value="{{ timeout_limit }}" >{% endif %}
          <input type="submit" role="link" value="{{ correction.title }}">
	      </form>
      </div>
      {% endfor %}
    </div>
