<aside class="infobox" aria-label="{{ infobox.infobox }}">
  <h2 class="title"><bdi>{{ infobox.infobox }}</bdi></h2>
  {%- if infobox.img_src -%}<img src="{{ image_proxify(infobox.img_src) }}" title="{{ infobox.infobox|striptags }}" alt="{{ infobox.infobox|striptags }}">{%- endif -%}
  <p><bdi>{{ infobox.content | safe }}</bdi></p>
  {%- if infobox.attributes -%}
    <div class="attributes">
      {%- for attribute in infobox.attributes -%}
        <dl>
          <dt><bdi>{{ attribute.label }} :</bdi></dt>
          {%- if attribute.image -%}
            <dd><img src="{{ image_proxify(attribute.image.src) }}" alt="{{ attribute.image.alt }}"></dd>
          {%- else -%}
            <dd><bdi>{{ attribute.value }}</bdi></dd>
          {%- endif -%}
        </dl>
      {%- endfor -%}
    </div>
  {%- endif -%}
  {%- if infobox.urls -%}
    <div class="urls">
      <ul>
        {%- for url in infobox.urls -%}
          <li class="url"><bdi><a href="{{ url.url }}" {%- if results_on_new_tab -%}target="_blank" rel="noopener noreferrer"{%- else -%}rel="noreferrer"{%- endif -%}>{{ url.title }}</a></bdi></li>
        {%- endfor -%}
      </ul>
    </div>
  {%- endif -%}
  {%- if infobox.relatedTopics -%}
    <div class="relatedTopics">
      {%- for topic in infobox.relatedTopics -%}
        <div>
          <h3><bdi>{{ topic.name }}</bdi></h3>
          {%- for suggestion in topic.suggestions -%}
            <form method="{{ method or 'POST' }}" action="{{ url_for('search') }}">
              <input type="hidden" name="q" value="{{ suggestion }}">
              <input type="hidden" name="time_range" value="{{ time_range }}">
              <input type="hidden" name="language" value="{{ current_language }}">
              <input type="hidden" name="safesearch" value="{{ safesearch }}">
              <input type="hidden" name="theme" value="{{ theme }}">
              {%- if timeout_limit -%}<input type="hidden" name="timeout_limit" value="{{ timeout_limit|e }}" >{%- endif -%}
              <input type="submit" value="{{ suggestion }}">
            </form>
          {%- endfor -%}
        </div>
      {%- endfor -%}
    </div>
  {%- endif -%}
</aside>
