<form id="search" method="{{ method or 'POST' }}" action="{{ url_for('search') }}" role="search">
  <div id="search_header">
    <a id="search_logo" href="{{ url_for('index') }}" tabindex="0" title="{{ _('Display the front page') }}">
      <span hidden>SearXNG</span>
      {% include 'simple/searxng-wordmark.min.svg' without context %}
    </a>
    <div id="search_view">
      <div class="search_box">
        <input id="q" name="q" type="text" placeholder="{{ _('Search for...') }}" tabindex="1" autocomplete="off" autocapitalize="none" spellcheck="false" autocorrect="off" dir="auto" value="{{ q or '' }}">
        <button id="clear_search" type="reset" aria-label="{{ _('clear') }}" class="hide_if_nojs"><span>{{ icon_big('close') }}</span><span class="show_if_nojs">{{ _('clear') }}</span></button>
        <button id="send_search" type="submit" {%- if search_on_category_select -%}name="category_{{ selected_categories[0]|replace(' ', '_') }}"{%- endif -%} aria-label="{{ _('search') }}"><span class="hide_if_nojs">{{ icon_big('search') }}</span><span class="show_if_nojs">{{ _('search') }}</span></button>
        <div class="autocomplete hide_if_nojs"><ul></ul></div>
      </div>
    </div>
    {% set display_tooltip = true %}
    {% include 'simple/categories.html' %}
  </div>
  <div class="search_filters">
    {% include 'simple/filters/languages.html' %}
    {% include 'simple/filters/time_range.html' %}
    {% include 'simple/filters/safesearch.html' %}
  </div>
  <input type="hidden" name="theme" value="{{ theme }}" >
  {% if timeout_limit %}<input type="hidden" name="timeout_limit" value="{{ timeout_limit|e }}" >{% endif %}
</form>
