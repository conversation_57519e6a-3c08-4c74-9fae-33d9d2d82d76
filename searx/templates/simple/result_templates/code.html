{% from 'simple/macros.html' import result_header, result_sub_header, result_sub_footer, result_footer with context %}

{{ result_header(result, favicons, image_proxify) -}}
{{- result_sub_header(result) -}}

{%- if result.content -%}
  <p class="content">
    {{- result.content|safe -}}
  </p>
{%- endif -%}
{%- if result.repository -%}
  <p class="content">{{- '' -}}
    {{ _('repo') }}: {{- ' ' -}}
    <a href="{{ result.repository|safe }}"{{- ' ' -}}
       {% if results_on_new_tab %}
         target="_blank" {{- ' ' -}}
         rel="noopener noreferrer"
       {%- else -%}
         rel="noreferrer"
       {%- endif -%}
       >
       {{- result.repository -}}
    </a>{{- '' -}}
  </p>
{%- endif -%}

<div dir="ltr" class="codelines">
    {{- result.codelines|code_highlighter(result.code_language)|safe -}}
</div>

{{- result_sub_footer(result) -}}
{{- result_footer(result) -}}
