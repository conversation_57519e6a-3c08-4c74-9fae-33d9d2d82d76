{% from 'simple/macros.html' import result_header, result_sub_header, result_sub_footer, result_footer, result_link with context %}

{{ result_header(result, favicons, image_proxify) -}}
{{- result_sub_header(result) -}}

<p class="altlink">
    {%- if result.magnetlink %}{{ result_link(result.magnetlink, icon_big('magnet') + _('magnet link'), "magnetlink") }}{%- endif -%}
    {%- if result.torrentfile %}{{ result_link(result.torrentfile, icon_big('download-alt') + _('torrent file'), "torrentfile") }}{%- endif -%}
</p>

<p class="stat">
    {%- if result.seed is defined %}
        {{ icon_big('seeder') }} <span class="badge">{{ result.seed }} {{ _('Seeder') }} </span>
        {{ icon_big('leecher') }} <span class="badge">{{ result.leech }} {{ _('Leecher') }}</span>
    {% endif %}
</p>
<p class="stat">
    {%- if result.filesize %}{{ icon_big('save') }} <span class="badge">{{ result.filesize }} {{ _('Filesize') }}</span>{%- endif -%}
    {%- if result.files %}{{ icon_big('file') }} <span class="badge">{{ result.files }} {{ _('Number of Files') }}</span>{%- endif -%}
</p>
{%- if result.content %}<p class="content">{{ result.content|safe }}</p>{%- endif -%}
{{- result_sub_footer(result) -}}
{{- result_footer(result) -}}
