<details class="answer-translations">
  <summary>{{ answer.translations[0].text }}</summary>
  <dl>
    {%- for item in answer.translations -%}
    <dt>{{ item.text }}</dt>
    <dd>
      {%- if item.transliteration -%}
      <div class="item-transliteration">{{ item.transliteration }}</div>
      {%- endif -%}

      {%- if item.examples -%}
      <div>{{ _('Examples') }}</div>
      <ul>
	{%- for i in item.examples -%}
	<li>{{ i }}</li>
	{%- endfor -%}
      </ul>
      {%- endif -%}

      {%- if item.definitions -%}
      <div>{{ _('Definitions') }}</div>
      <ul>
	{%- for i in item.definitions -%}
	<li>{{ i }}</li>
	{%- endfor -%}
      </ul>
      {%- endif -%}

      {%- if item.synonyms -%}
      <div>{{ _('Synonyms') }}</div>
      <ul>
	{%- for i in item.synonyms -%}
	<li>{{ i }}</li>
	{%- endfor -%}
      </ul>
      {%- endif -%}
    </dd>
    {%- endfor -%}
  </dl>
</details>

{%- if answer.url -%}
<a href="{{ answer.url }}" class="answer-url"
   {%- if results_on_new_tab %}
   target="_blank" rel="noopener noreferrer"
   {%- else -%}
   rel="noreferrer"
   {%- endif -%}
   >{{ answer.engine }}</a>
{%- else -%}
<span class="answer-url">{{ answer.engine }}</span>
{% endif -%}
