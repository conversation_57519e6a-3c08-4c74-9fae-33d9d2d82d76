{% macro new_issue(engine_name, engine_reliability) %}
<form action="{{ get_setting('brand.new_issue_url') }}" method="GET">
    <input name="title" type="hidden" value="Bug: {{ engine_name }} engine">
    <input name="labels" type="hidden" value="bug">
    <input name="template" type="hidden" value="bug-report.md">
    <textarea name="body" class="issue-hide">{{- '' -}}

**Version of SearXNG, commit number if you are using on master branch and stipulate if you forked SearXNG**
{% if searx_git_url and searx_git_url != 'unknow' %}
Repository: {{ searx_git_url }}
Branch: {{ searx_git_branch }}
Version: {{ searx_version }}
<!-- Check if these values are correct -->

{% else %}
<!-- If you are running on master branch using git execute this command
in order to fetch the latest commit ID:
```
git log -1
```
If you are using searxng-docker then look at the bottom of the SearXNG page
and check for the version after "Powered by SearXNG"

Please also stipulate if you are using a forked version of SearxNG and
include a link to the fork source code.
-->
{% endif %}
**How did you install SearXNG?**
<!-- Did you install SearXNG using the official wiki or using searxng-docker
or manually by executing the searx/webapp.py file? -->
**What happened?**
<!-- A clear and concise description of what the bug is. -->

**How To Reproduce**
<!-- How can we reproduce this issue? (as minimally and as precisely as possible) -->

**Expected behavior**
<!-- A clear and concise description of what you expected to happen. -->

**Screenshots & Logs**
<!-- If applicable, add screenshots, logs to help explain your problem. -->

**Additional context**
<!-- Add any other context about the problem here. -->

**Technical report**

{% for error in engine_reliability.errors %}
{% if secondary %}Warning{% else %}Error{% endif %}
{{'\n  '}}* Error: {{ error.exception_classname or error.log_message }}
{{'  '}}* Percentage: {{ error.percentage }}
{{'  '}}* Parameters: `{{ error.log_parameters }}`
{{'  '}}* File name: `{{ error.filename }}:{{ error.line_no }}`
{{'  '}}* Function: `{{ error.function }}`
{{'  '}}* Code: `{{ error.code }}`
{{'\n'-}}
{%- endfor -%}
{%- for test_name, results in engine_reliability.checker.items() -%}
{%- if loop.first %}Checker{% endif -%}
{{-'\n  '}}* {{ test_name }}: {% for result in results%}`{{ result }}`,{% endfor -%}
{%- endfor -%}
    </textarea>
    <input type="checkbox" id="step1">
    <label for="step1">{{ _('Start submitting a new issue on GitHub') }}</label>
    <div class="step1 step_content">
        <p><a href="{{ get_setting('brand.issue_url') }}?q=is%3Aissue+Bug:%20{{ engine_name }} {{ technical_report }}" target="_blank" rel="noreferrer noreferrer">{{ _('Please check for existing bugs about this engine on GitHub') }}</a></p>
    </div>
    <input class="step1 step1_delay" type="checkbox" id="step2">
    <label class="step1 step1_delay" for="step2" >{{ _('I confirm there is no existing bug about the issue I encounter') }}</label>
    <div class="step2 step_content">
        <p>{{ _('If this is a public instance, please specify the URL in the bug report') }}</p>
        <button type="submit" class="github-issue-button button" title="{{ get_setting('brand.new_issue_url') }}">{{ _('Submit a new issue on Github including the above information') }}</button>
    </div>
</form>
{% endmacro %}
