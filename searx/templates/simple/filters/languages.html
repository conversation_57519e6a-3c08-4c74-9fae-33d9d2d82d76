<select class="language" id="language" name="language" aria-label="{{ _('Search language') }}">{{- '' -}}
  <option value="all"
          {%- if current_language == 'all' %} selected="selected" {%- endif -%}>
          {{- _('Default language') }} [all] {{- '' -}}
  </option>{{- '' -}}
  <option value="auto"
          {%- if current_language == 'auto' %} selected="selected" {%- endif -%}>
          {{- _('Auto-detect') }} ({{ search_language }})  {{- '' -}}
  </option>{{- '' -}}
  {% for sxng_tag,lang_name,country_name,english_name,flag in sxng_locales | sort(attribute=1) -%}
    <option value="{{ sxng_tag }}"
            {%- if sxng_tag == current_language %} selected="selected" {%- endif -%}>
            {{ lang_name }}{%- if country_name -%}-{{ country_name }}{%- endif -%}
            {{- ' ' -}}[{{sxng_tag}}]{{- ' ' -}}
            {%- if flag -%}{{ flag }}{%- endif -%}
    </option>
  {%- endfor -%}
</select>
