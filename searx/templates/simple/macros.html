{% from 'simple/icons.html' import icon_small %}

<!-- Draw favicon -->
{% macro draw_favicon(favicon) -%}
    <img width="14" height="14" class="favicon" src="{{ url_for('static', filename='themes/simple/img/icons/' + favicon + '.png') }}" alt="{{ favicon }}">
{%- endmacro %}

{% macro result_open_link(url, classes='') -%}
    <a href="{{ url }}" {% if classes %}class="{{ classes }}" {% endif %}{% if results_on_new_tab %}target="_blank" rel="noopener noreferrer"{% else %}rel="noreferrer"{% endif %}>
{%- endmacro %}

{%- macro result_close_link() -%}
    </a>
{%- endmacro %}

{%- macro result_link(url, title, classes='') -%}
    {{ result_open_link(url, classes) }}{{ title }}{{ result_close_link() }}
{%- endmacro -%}

<!-- Draw result header -->
{% macro result_header(result, favicons, image_proxify) -%}
<article class="result {% if result['template'] %}result-{{ result.template|replace('.html', '') }}{% else %}result-default{% endif %} {% if result['category'] %}category-{{ result['category'] }}{% endif %}">
  {{- result_open_link(result.url, "url_header") -}}
  {%- if favicon_resolver != "" %}
  <div class="favicon"><img loading="lazy" src="{{ favicon_url(result.parsed_url.netloc) }}"></div>
  {%- endif -%}
  <div class="url_wrapper">
    {%- for part in get_pretty_url(result.parsed_url) -%}
    <span class="url_o{{loop.index}}"><span class="url_i{{loop.index}}">{{- part -}}</span></span>
    {%- endfor %}
  </div>
  {{- result_close_link() -}}
  {%- if result.thumbnail %}{{ result_open_link(result.url) }}<img class="thumbnail" src="{{ image_proxify(result.thumbnail) }}" title="{{ result.title|striptags }}" loading="lazy">{{ result_close_link() }}{% endif -%}
  <h3>{{ result_link(result.url, result.title|safe) }}</h3>
{%- endmacro -%}

<!-- Draw result sub header -->
{%- macro result_sub_header(result) -%}
  {%- if result.publishedDate %}<time class="published_date" datetime="{{ result.pubdate }}" >{{ result.publishedDate }}</time>{% endif -%}
  {%- if result.length %}<div class="result_length">{{ _('Length') }}: {{ result.length }}</div>{% endif -%}
  {%- if result.views %}<div class="result_views">{{ _('Views') }}: {{ result.views }}</div>{% endif -%}
  {%- if result.author %}<div class="result_author">{{ _('Author') }}: {{ result.author }}</div>{% endif -%}
  {%- if result.metadata %}<div class="highlight">{{ result.metadata|safe }}</div>{% endif -%}
{%- endmacro -%}

<!-- Draw result sub footer -->
{%- macro result_sub_footer(result) -%}
<div class="engines">
  {% for engine in result.engines %}<span>{{ engine }}</span>{% endfor %}
  {{ icon_small('ellipsis-vertical') + result_link(cache_url + result.url, _('cached'), "cache_link") }}
</div>{{- '' -}}
<div class="break"></div>{{- '' -}}
{%- endmacro -%}

<!-- Draw result footer -->
{%- macro result_footer(result) -%}
</article>
{%- endmacro -%}

<!-- input checkbox, on/off slider user can tap-->
{%- macro checkbox_onoff(name, checked) -%}
  <input type="checkbox" {{- ' ' -}}
         name="{{ name }}" {{- ' ' -}}
         id="{{ name }}" {{- ' ' -}}
         aria-labelledby="pref_{{ name }}"{{- ' ' -}}
         class="checkbox-onoff"{{- ' ' -}}
         {%- if checked -%} checked{%- endif -%}/>
{%- endmacro -%}
