# https://editorconfig.org/

root = true

[*]
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true
end_of_line = lf
charset = utf-8

[*.py]
# code formatter accepts length of 120, but editor should prefer 80
max_line_length = 80

[*.html]
# in the jinja templates we use indent size of 2 and we do not use tabs
indent_size = 2
indent_style = space

[*.css]
indent_size = 2

[*.less]
indent_size = 2

[*.js]
indent_size = 2

[*.json]
indent_size = 2
insert_final_newline = ignore

# Minified JavaScript files shouldn't be changed
[**.min.js]
indent_style = ignore
insert_final_newline = ignore

# Makefiles always use tabs for indentation
[Makefile]
indent_style = tab

# Batch files use tabs for indentation
[*.bat]
indent_style = tab

[docs/**.rst]
max_line_length = 79

[*.yml]
indent_size = 2
