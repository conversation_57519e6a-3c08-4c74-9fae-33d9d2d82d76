#!/usr/bin/env python3
"""
Enhanced Search Engine MCP服务器配置管理 - 修复版
"""

import os
import logging
from dataclasses import dataclass, field
from typing import Optional, List
from pathlib import Path

@dataclass
class Config:
    """MCP服务器配置类"""
    
    # 服务器配置
    HOST: str = os.getenv("MCP_HOST", "0.0.0.0")
    PORT: int = int(os.getenv("MCP_PORT", "8881"))
    
    # SearXNG配置
    SEARXNG_URL: str = os.getenv("SEARXNG_URL", "http://localhost:8888")
    SEARXNG_TIMEOUT: int = int(os.getenv("SEARXNG_TIMEOUT", "30"))
    SEARXNG_MAX_RETRIES: int = int(os.getenv("SEARXNG_MAX_RETRIES", "3"))
    
    # 缓存配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))  # 1小时
    ENABLE_CACHE: bool = os.getenv("ENABLE_CACHE", "true").lower() == "true"
    CACHE_PREFIX: str = os.getenv("CACHE_PREFIX", "enhanced_search")
    
    # 搜索配置
    MAX_CONCURRENT_SEARCHES: int = int(os.getenv("MAX_CONCURRENT_SEARCHES", "5"))
    DEFAULT_ENGINES: str = os.getenv("DEFAULT_ENGINES", "google,bing,brave")
    MAX_RESULTS_PER_QUERY: int = int(os.getenv("MAX_RESULTS_PER_QUERY", "10"))
    MAX_TOTAL_RESULTS: int = int(os.getenv("MAX_TOTAL_RESULTS", "20"))
    
    # 搜索策略配置
    ENABLE_EXPANDED_SEARCH: bool = os.getenv("ENABLE_EXPANDED_SEARCH", "true").lower() == "true"
    ENABLE_RELATED_SEARCH: bool = os.getenv("ENABLE_RELATED_SEARCH", "true").lower() == "true"
    EXPANDED_QUERIES_LIMIT: int = int(os.getenv("EXPANDED_QUERIES_LIMIT", "3"))
    RELATED_QUERIES_LIMIT: int = int(os.getenv("RELATED_QUERIES_LIMIT", "2"))
    
    # 质量评分权重配置
    TITLE_RELEVANCE_WEIGHT: float = float(os.getenv("TITLE_RELEVANCE_WEIGHT", "0.3"))
    CONTENT_QUALITY_WEIGHT: float = float(os.getenv("CONTENT_QUALITY_WEIGHT", "0.25"))
    SOURCE_AUTHORITY_WEIGHT: float = float(os.getenv("SOURCE_AUTHORITY_WEIGHT", "0.2"))
    FRESHNESS_WEIGHT: float = float(os.getenv("FRESHNESS_WEIGHT", "0.15"))
    ENGAGEMENT_WEIGHT: float = float(os.getenv("ENGAGEMENT_WEIGHT", "0.1"))
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: Optional[str] = os.getenv("LOG_FILE")
    LOG_FORMAT: str = os.getenv(
        "LOG_FORMAT", 
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    LOG_MAX_BYTES: int = int(os.getenv("LOG_MAX_BYTES", "10485760"))  # 10MB
    LOG_BACKUP_COUNT: int = int(os.getenv("LOG_BACKUP_COUNT", "5"))
    
    # 监控配置
    ENABLE_METRICS: bool = os.getenv("ENABLE_METRICS", "true").lower() == "true"
    METRICS_PORT: int = int(os.getenv("METRICS_PORT", "8882"))
    
    # 关键词配置 - 使用field(default_factory)避免可变默认值问题
    DEPTH_KEYWORDS: List[str] = field(default_factory=lambda: [
        "详细介绍", "深入分析", "全面解析", "研究报告"
    ])
    
    TIME_KEYWORDS: List[str] = field(default_factory=lambda: [
        "最新", "2024", "2025", "趋势"
    ])
    
    TYPE_KEYWORDS: List[str] = field(default_factory=lambda: [
        "原理", "方法", "技术", "应用"
    ])
    
    COMPARE_KEYWORDS: List[str] = field(default_factory=lambda: [
        "对比", "比较", "优缺点", "差异"
    ])
    
    PRACTICE_KEYWORDS: List[str] = field(default_factory=lambda: [
        "实践", "案例", "示例", "教程"
    ])
    
    # 质量关键词
    QUALITY_KEYWORDS: List[str] = field(default_factory=lambda: [
        "详细", "全面", "深入", "研究", "分析", "完整", "系统", "专业", "权威", 
        "介绍", "指南", "教程", "方法", "技术", "原理", "应用", "发展", "趋势",
        "detailed", "comprehensive", "complete", "guide", "tutorial", "analysis", 
        "research", "study", "review", "overview", "introduction", "technical", 
        "professional", "official", "latest", "update"
    ])
    
    # 权威域名
    AUTHORITY_DOMAINS: List[str] = field(default_factory=lambda: [
        "wikipedia.org", "github.com", "stackoverflow.com", "medium.com", 
        "arxiv.org", "ieee.org", "acm.org", "nature.com", "science.org", 
        "edu", "gov", "org"
    ])
    
    # 低质量指标
    LOW_QUALITY_INDICATORS: List[str] = field(default_factory=lambda: [
        "广告", "推广", "购买", "下载", "免费", "优惠", "促销",
        "ad", "ads", "advertisement", "promotion", "buy", "sale", 
        "download", "free", "discount", "offer"
    ])
    
    def __post_init__(self):
        """配置后处理"""
        # 确保日志目录存在
        if self.LOG_FILE:
            log_dir = Path(self.LOG_FILE).parent
            log_dir.mkdir(parents=True, exist_ok=True)
        
        # 从环境变量加载关键词（如果设置了的话）
        if os.getenv("DEPTH_KEYWORDS"):
            self.DEPTH_KEYWORDS = os.getenv("DEPTH_KEYWORDS").split(",")
        
        if os.getenv("TIME_KEYWORDS"):
            self.TIME_KEYWORDS = os.getenv("TIME_KEYWORDS").split(",")
        
        if os.getenv("TYPE_KEYWORDS"):
            self.TYPE_KEYWORDS = os.getenv("TYPE_KEYWORDS").split(",")
        
        if os.getenv("COMPARE_KEYWORDS"):
            self.COMPARE_KEYWORDS = os.getenv("COMPARE_KEYWORDS").split(",")
        
        if os.getenv("PRACTICE_KEYWORDS"):
            self.PRACTICE_KEYWORDS = os.getenv("PRACTICE_KEYWORDS").split(",")
        
        if os.getenv("QUALITY_KEYWORDS"):
            self.QUALITY_KEYWORDS = os.getenv("QUALITY_KEYWORDS").split(",")
        
        if os.getenv("AUTHORITY_DOMAINS"):
            self.AUTHORITY_DOMAINS = os.getenv("AUTHORITY_DOMAINS").split(",")
        
        if os.getenv("LOW_QUALITY_INDICATORS"):
            self.LOW_QUALITY_INDICATORS = os.getenv("LOW_QUALITY_INDICATORS").split(",")
    
    @classmethod
    def from_env_file(cls, env_file: str = ".env"):
        """从环境文件加载配置"""
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
        return cls()
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = getattr(logging, self.LOG_LEVEL.upper(), logging.INFO)
        
        # 基础配置
        logging.basicConfig(
            level=log_level,
            format=self.LOG_FORMAT,
            handlers=[]
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_formatter = logging.Formatter(self.LOG_FORMAT)
        console_handler.setFormatter(console_formatter)
        
        # 文件处理器（如果配置了日志文件）
        handlers = [console_handler]
        if self.LOG_FILE:
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                self.LOG_FILE,
                maxBytes=self.LOG_MAX_BYTES,
                backupCount=self.LOG_BACKUP_COUNT,
                encoding='utf-8'
            )
            file_handler.setLevel(log_level)
            file_formatter = logging.Formatter(self.LOG_FORMAT)
            file_handler.setFormatter(file_formatter)
            handlers.append(file_handler)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        for handler in handlers:
            root_logger.addHandler(handler)
        
        return root_logger
    
    def validate(self) -> List[str]:
        """验证配置有效性"""
        errors = []
        
        # 验证端口范围
        if not (1024 <= self.PORT <= 65535):
            errors.append(f"MCP_PORT must be between 1024 and 65535, got {self.PORT}")
        
        if not (1024 <= self.METRICS_PORT <= 65535):
            errors.append(f"METRICS_PORT must be between 1024 and 65535, got {self.METRICS_PORT}")
        
        # 验证权重总和
        total_weight = (
            self.TITLE_RELEVANCE_WEIGHT + 
            self.CONTENT_QUALITY_WEIGHT + 
            self.SOURCE_AUTHORITY_WEIGHT + 
            self.FRESHNESS_WEIGHT + 
            self.ENGAGEMENT_WEIGHT
        )
        if abs(total_weight - 1.0) > 0.01:
            errors.append(f"Quality score weights should sum to 1.0, got {total_weight}")
        
        # 验证URL格式
        if not self.SEARXNG_URL.startswith(('http://', 'https://')):
            errors.append(f"SEARXNG_URL must start with http:// or https://, got {self.SEARXNG_URL}")
        
        if not self.REDIS_URL.startswith('redis://'):
            errors.append(f"REDIS_URL must start with redis://, got {self.REDIS_URL}")
        
        return errors
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }

# 全局配置实例
config = Config()

def get_config() -> Config:
    """获取配置实例"""
    return config

def load_config(env_file: str = ".env") -> Config:
    """加载配置"""
    global config
    config = Config.from_env_file(env_file)
    
    # 验证配置
    errors = config.validate()
    if errors:
        raise ValueError(f"Configuration errors: {'; '.join(errors)}")
    
    # 设置日志
    config.setup_logging()
    
    return config
