/mnt/e/AI/searxng/enhanced_search_mcp/server.py:227: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
/home/<USER>/miniconda/envs/searxng/lib/python3.11/site-packages/fastapi/applications.py:4495: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  return self.router.on_event(event_type)
/mnt/e/AI/searxng/enhanced_search_mcp/server.py:237: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("shutdown")
INFO:     Started server process [64776]
INFO:     Waiting for application startup.
2025-08-04 20:44:42,548 - __main__ - INFO - 配置加载成功
2025-08-04 20:44:42,548 - __main__ - INFO - SearXNG适配器创建成功: http://localhost:8888
2025-08-04 20:44:42,549 - __main__ - INFO - 增强搜索引擎创建成功
2025-08-04 20:44:42,550 - __main__ - INFO - SearXNG连接测试成功
2025-08-04 20:44:42,551 - __main__ - INFO - MCP服务器启动完成
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8881 (Press CTRL+C to quit)
INFO:     127.0.0.1:42198 - "GET /health HTTP/1.1" 200 OK
INFO:     Shutting down
